import { createSelector } from 'reselect';

import * as constants from './constants';

export const baseSelector = (state) => state[constants.REDUCER_KEY];

export const formSelector = (state) => state[constants.STATE_FORM];

export const loadingSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA_LOADING],
);

export const errorSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA_ERROR],
);

export const dataSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA] || {},
);

export const appDataSelector = createSelector(
  baseSelector,
  (state) => state[constants.APP_DATA],
);

export const accordionSelector = createSelector(
  baseSelector,
  (state) => state[constants.SHOW_ACCORDION] || false,
);

export const columnsSelector = createSelector(
  baseSelector,
  (state) => state[constants.COLUMNS] || [],
);

export const categoryDropdownDataSelector = createSelector(
  baseSelector,
  (state) => state[constants.CATEGORY_DROPDOWN_DATA],
);

export const interfaceDropdownDataSelector = createSelector(
  baseSelector,
  (state) => state[constants.INTERFACE_DROPDOWN_DATA],
);

export const resultDropdownDataSelector = createSelector(
  baseSelector,
  (state) => state[constants.RESULT_DROPDOWN_DATA],
);

export const categoryUpdatedSelector = createSelector(
  baseSelector,
  (state) => state[constants.CATEGORY_UPDATED],
);

export const auditLogsFilterForm = createSelector(
  formSelector,
  (state) => state.auditLogsFilterForm || [],
);

export const auditLogFormValues = createSelector(
  auditLogsFilterForm,
  (state) => state.values || {},
);

// export const formServerPortFrom = createSelector(
//   dnsFiltersFormValues,
//   state => state.serverPortFrom || '',
// );

// alway return top level selector
export default baseSelector;
