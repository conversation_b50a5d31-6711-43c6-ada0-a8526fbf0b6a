import React from 'react';
import PropTypes from 'prop-types';
import { FormFieldLabel, FormSectionLabel } from 'components/label';
import { withTranslation } from 'react-i18next';
import { Field } from 'redux-form';
import Input from 'components/Input';
import { NavLink } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { setShowHelp, loadArticle } from 'ducks/help';
import { HELP_ARTICLES } from 'config';
import {
  required,
  isURL,
} from 'utils/validations';

function Url({ t, submitting }) {
  const dispatch = useDispatch();

  const handleClick = () => {
    dispatch(loadArticle(HELP_ARTICLES.ABOUT_URL_CATEGORIES));
    dispatch(setShowHelp(true));
  };

  const toolTipText = t('TOOLTIP_HELP_LOOKUP_URL_ENTER_URL').split(/{[0-9]}/g);
  const toolTipJSX = (
    <>
      {toolTipText[0]}
      <b>{toolTipText[1]}</b>
      {toolTipText[2]}
      <NavLink className="tooltip-navlink" to="?" onClick={handleClick}>
        <b>{toolTipText[3]}</b>
      </NavLink>
      {toolTipText[4]}
    </>
  );

  return (
    <>
      <FormSectionLabel text={t('LOOKUP_URL_CATEGORY')} />
      <div className="form-section">
        <div className="g-row">
          <div className="g-left">
            <FormFieldLabel text={t('ENTER_URL')} tooltip={toolTipJSX} />
            <Field
              id="urlLookup"
              name="urlLookup"
              component={Input}
              styleClass="zia"
              placeholder={t('ENTER_TEXT')}
              validate={[
                required,
                isURL,
              ]} />
          </div>
          <div className="g-left">
            <button type="submit" disabled={submitting} className="submit">{t('LOOKUP')}</button>
          </div>
        </div>
      </div>
    </>
  );
}

Url.propTypes = {
  t: PropTypes.func,
  submitting: PropTypes.bool,
};
  
Url.defaultProps = {
  t: (str) => str,
};

export default withTranslation()(Url);
