import React from 'react';
import PropTypes from 'prop-types';
import i18n from 'utils/i18n';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faRefresh } from '@fortawesome/pro-regular-svg-icons';

const noop = () => undefined;
// RowEdit is responsible to support EDIT feature of the respective row
function RowRefresh(props) {
  const {
    onClickCallBack,
    isRefreshable,
  } = props;

  if (isRefreshable) {
    return (
      <FontAwesomeIcon
        icon={faRefresh}
        title={i18n.t('REFRESH')}
        onClick={onClickCallBack}
        className="pencil-icon" />
    );
  }
  return '';
}

RowRefresh.propTypes = {
  onClickCallBack: PropTypes.func,
  isRefreshable: PropTypes.bool,
};

RowRefresh.defaultProps = {
  onClickCallBack: noop,
  isRefreshable: false,
};

export default RowRefresh;
