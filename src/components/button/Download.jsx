import React from 'react';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faDownload } from '@fortawesome/pro-solid-svg-icons';

import './index.scss';

function Download(props) {
  const {
    label,
    onActionCb,
    tabIndex,
    tooltip,
  } = props;
  return (
    <div
      className="download-button"
      tabIndex={tabIndex}
      onKeyPress={() => onActionCb()}
      onClick={() => onActionCb()}
      role="button">
      <span
        className="icon"
        data-tip={tooltip}
        data-for="TunnelLogsReactTooltip">
        <FontAwesomeIcon icon={faDownload} />
      </span>
      {label}
    </div>
  );
}

Download.propTypes = {
  label: PropTypes.string,
  onActionCb: PropTypes.func,
  tabIndex: PropTypes.number,
  tooltip: PropTypes.string,
};

Download.defaultProps = {
  label: '',
  onActionCb: null,
  tabIndex: 0,
  tooltip: '',
};

export default Download;
