/* eslint-disable jsx-a11y/label-has-for */
/* eslint-disable jsx-a11y/label-has-associated-control */
import React, { useEffect, useMemo } from 'react';
import PropTypes from 'prop-types';

function SingleDropdownPanel(props) {
  const {
    // eslint-disable-next-line react/prop-types
    options, selectedValues, setSelectedValues, hideDropdown, searchText, dropdownRef, designType,
  } = props;

  useEffect(() => {
    const listener = (e) => {
      if (!dropdownRef.current.contains(e.target)) {
        hideDropdown(selectedValues);
      }
    };
    document.addEventListener('click', listener);
    return () => {
      document.removeEventListener('click', listener);
    };
  }, [selectedValues]);

  const optionsWithSelection = useMemo(() => {
    return options.map((x) => ({
      ...x,
      selected: selectedValues.map((item) => item.value).includes(x.value),
    }));
  }, [options, selectedValues]);

  const filteredOptions = useMemo(() => {
    return optionsWithSelection?.filter(
      (x) => x?.label?.toLowerCase().includes(searchText?.toLowerCase()),
    );
  }, [searchText, optionsWithSelection]);

  const handleOnChange = (val) => {
    setSelectedValues([val]);
    setTimeout(() => { hideDropdown([val]); }, 0);
  };

  function SingleDropdown({
    // eslint-disable-next-line react/prop-types
    value, label, selected, onChange,
  }) {
    return (
      <div
        className={`option-row-dropdown ${(selected && designType === 'normal') ? 'selected-value' : ''}`}
        role="button"
        tabIndex={0}
        style={{ paddingTop: '7px', paddingBottom: '7px', minHeight: '33px' }}
        key={value}
        onClick={() => onChange({ value, label })}
        onKeyPress={() => onChange({ value, label })}>
        {(designType !== 'normal') && <input type="radio" className="dropdown-radiobox" id={`chk-${value}`} defaultChecked={selected} />}
        <label id={`chk-${value}`} style={{ cursor: 'pointer' }}>
          {label}
        </label>
      </div>
    );
  }

  return (
    <>
      {filteredOptions.length > 0 ? (
        <div key={filteredOptions} className="dropdown-options-container" style={{ paddingLeft: '4.8px' }}>
          {filteredOptions.map((option) => (
            <SingleDropdown
              key={option.value}
              value={option.value}
              label={option.label}
              selected={option.selected}
              onChange={(val) => handleOnChange(val)} />
          ))}
        </div>
      )
        : <p className="dropdown-noOptions">No Options</p>}
    </>
  );
}

SingleDropdownPanel.propTypes = {
  options: PropTypes.arrayOf(),
  selectedValues: PropTypes.arrayOf(),
  setSelectedValues: PropTypes.func,
  hideDropdown: PropTypes.func,
  searchText: PropTypes.string,
  dropdownRef: PropTypes.shape({
    current: PropTypes.arrayOf(),
  }),
  designType: PropTypes.string,
};

SingleDropdownPanel.defaultProps = {
  searchText: '',
  designType: '',
};

export default SingleDropdownPanel;
