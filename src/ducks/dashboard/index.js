import {
  createAction,
  loadSuccess,
} from 'ducks/generics';
import * as constants from './constants';
import actionTypes from './action-types';
// import * as http from './http';

// we want this to exported for to be used in store and other places
// from index so...elsewhere we can say import {REDUCER_KEY as widget} from 'ducks/widget'
// for exmaple
export { REDUCER_KEY } from './constants';

const actionTypeExists = Object.keys(actionTypes).reduce((hash, key) => ({
  ...hash,
  [actionTypes[key]]: true,
}), {});

// unlike other reducer, we will use
export const reducer = (state = constants.DEFAULT_STATE, action = {}) => {
  // We will merge payload from actions to the state for this widget/page/component
  // that only matches to our actions in actiontypes
  // for example once data is loaded our action below will dispatch
  // action and data
  if (actionTypeExists[action.type]) {
    return {
      ...state,
      ...action.payload,
    };
  }
  return state;
};
export default reducer;

// const boundLoading = loading(actionTypes.DATA_LOADING);

const boundSuccess = loadSuccess(actionTypes.DATA_LOAD_SUCCESS);
const dataChanged = createAction(actionTypes.DATA_CHANGED);

// const boundError = loadError(actionTypes.DATA_LOAD_ERROR);

export const load = () => (dispatch) => {
  dispatch(boundSuccess({
    [constants.DATA_LOADING]: false,
  }));

  // dispatch(boundLoading(constants.DEFAULTS));

  // return http.load()
  //   .then(response => dispatch(boundSuccess('data', response.data)))
  //   .catch(error => dispatch(boundError(error)));
};

export const handleTrafficFlow = () => (dispatch) => {
  dispatch(dataChanged({
    [constants.SHOW_TRAFFIC_FLOW]: true,
  }));
};

export const onClickToDashboard = () => (dispatch) => {
  dispatch(dataChanged({
    [constants.SHOW_TRAFFIC_FLOW]: false,
  }));
};

export const toggleFamily = (family) => (dispatch, getState) => {
  const currentState = getState();
  const stateData = currentState[constants.REDUCER_KEY];

  if (family === constants.INCOMING_TRAFFIC) {
    dispatch(dataChanged({
      [constants.INCOMING_TRAFFIC]: !stateData[constants.INCOMING_TRAFFIC],
    }));
  } else {
    dispatch(dataChanged({
      [constants.OUTGOING_TRAFFIC]: !stateData[[constants.OUTGOING_TRAFFIC]],
    }));
  }
};
