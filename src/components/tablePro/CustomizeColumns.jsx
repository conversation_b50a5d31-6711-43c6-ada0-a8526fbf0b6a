/* eslint-disable react/no-unknown-property */
import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faBars, faRedo } from '@fortawesome/pro-solid-svg-icons';
import { faTimes, faSortAmountDown } from '@fortawesome/pro-regular-svg-icons';
import { SortableContainer, SortableElement } from 'react-sortable-hoc';
import { swapObject } from 'utils/tableHelper';
import { withTranslation } from 'react-i18next';

const noop = () => undefined;
// CustomizeColumns will handle the Menu that holds the columns that can be swapped or hidden
function CustomizeColumns(props) {
  const {
    initialItems,
    showComponent,
    hasSelectAll,
    dragnDrop,
    t,
    handleLeave,
  } = props;
  const [items, setItems] = useState(initialItems);
  useEffect(() => setItems(initialItems));
  const disableCheck = (initialItems.filter((x) => !['idx'].includes(x.key) && x.checkbox)).length <= 1;
   
  const handleCheckBox = (event) => {
    const updateItem = initialItems;
    if (!event.target.checked) {
      if (disableCheck) return;
      updateItem[event.target.value].visible = false;
      updateItem[event.target.value].checkbox = false;
    } else {
      updateItem[event.target.value].visible = true;
      updateItem[event.target.value].checkbox = true;
    }
    setItems(updateItem);
    dragnDrop(updateItem);
  };
  // The elements for the sortable list will be created
  // along with a checkbox (to support hide/unhide)
  const SortableItem = SortableElement(({ value, index, checkbox }) => {
    return (
      <div className="form-input-row ecui-sortable-handle table-form">
        <span className="form-input -js-toggle" name-attribute="USER_ID_NAME">
          <span className="bars">
            <FontAwesomeIcon icon={faBars} size="xs" />
          </span>
          <span className="check-box form-error">
            <span><input type="checkbox" className="r-check-box" disabled={disableCheck && checkbox} defaultChecked={checkbox} value={index} onClick={handleCheckBox} /></span>
            <span className="check-box-label"><li className="noLi">{t(value)}</li></span>
          </span>
        </span>
      </div>
    );
  });
  // A sortable list will be created in accordance to the columns
  const SortableList = SortableContainer(({ listItems }) => {
    return (
      <div className="sortable-container">
        {listItems.map((index, key) => (
          index.draggable
            ? <SortableItem key={`item-${index.name}`} index={key} value={index.name} checkbox={index.checkbox} />
            : ''
        ))}
      </div>
    );
  });

  const handleSortEnd = ({ oldIndex, newIndex }) => {
    setItems(swapObject(initialItems, initialItems[oldIndex].key, initialItems[newIndex].key));
    dragnDrop(items);
  };

  const handleReload = () => {
    // reload initial dataset
    dragnDrop(items);
  };

  const handleReset = () => {
    setItems(initialItems);
    handleLeave();
  };

  const selectAll = () => {
    const updateItem = initialItems.map((x) => ({ ...x, visible: true, checkbox: true }));
    setItems(updateItem);
    dragnDrop(updateItem);
  };

  const deselectAll = () => {
    const firstSelected = initialItems.find((x) => x.checkbox && x.key !== 'idx') || {};
    const updateItem = initialItems.map((x) => (['idx', firstSelected.key].includes(x.key)
      ? x
      : { ...x, visible: false, checkbox: false }
    ));
    setItems(updateItem);
    dragnDrop(updateItem);
  };

  return (
    <div className={`rdg-menu-editor ${showComponent ? 'showComponent' : ''} `}>
      <div className="table-column-menu-container">
        <div className="table-column-menu-header header">
          {!hasSelectAll
        && <span className="table-column-menu-header-text">{t('TABLE_OPTIONS')}</span>}
          {hasSelectAll && (
            <>
              <button
                type="button"
                onClick={selectAll}
                className="table-column-menu-header-text control-button-reports">
                <FontAwesomeIcon icon={faSortAmountDown} />
                {' '}
                {t('SELECT_ALL')}
              </button>
              <button
                type="button"
                onClick={deselectAll}
                className="table-column-menu-header-text control-button-reports">
                <FontAwesomeIcon icon={faTimes} />
                {' '}
                {t('DESELECT_ALL')}
              </button>
            </>
          )}
          {/* <i className="fa fa-undo" onClick={handleReload} onKeyPress={handleReload} role="presentation"></i> */}
          <FontAwesomeIcon className="column-reset" icon={faRedo} onClick={handleReload} onKeyPress={handleReload} role="presentation" />
        </div>
        <div className="table-column-menu table-column-menu-body ui-sortable">
          <SortableList listItems={items} onSortEnd={handleSortEnd} onClick={handleCheckBox} />
        </div>
        <section className="actions table-column-menu-footer">
          <div className="table-column-menu-footer-buttons">
            <button
              onClick={handleLeave}
              type="button"
              className="cancel">
              {t('CANCEL')}
            </button>
            {/* <button
              onClick={handleReset}
              type="button"
              className="cancel">
              {t('RESET')}
            </button> */}
          </div>
        </section>
      </div>
    </div>
  );
}

CustomizeColumns.propTypes = {
  initialItems: PropTypes.arrayOf(PropTypes.shape({})),
  showComponent: PropTypes.bool,
  hasSelectAll: PropTypes.bool,
  dragnDrop: PropTypes.func,
  t: PropTypes.func,
  handleLeave: PropTypes.func,
};

CustomizeColumns.defaultProps = {
  initialItems: [],
  showComponent: false,
  hasSelectAll: false,
  dragnDrop: noop,
  t: (str) => str,
  handleLeave: null,
};
export default withTranslation()(CustomizeColumns);
