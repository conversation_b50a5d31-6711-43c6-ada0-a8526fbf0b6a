@import 'scss/mixins.scss';
@import 'scss/colors.scss';


.password-banner {
    @include DisplayFlex;
        cursor: pointer;
        color: $red5;
        padding: 4px 16px;
        position: absolute;
        top: 30%;
        width: auto;
        border-radius: 3px;
        left: 30%;

    .fa-exclamation-triangle {
        color: $grey1;
        font-size: 14px;
        margin-right: 5px;
    }
    .fa-times-circle {
        color: $red5;
        font-size: 14px;
        margin-right: 5px;
    }

    .text-label {
        font-size: 12px;
        cursor: pointer;
    }

    .toggle-label-button {
        border: none;
        background: $transparent;
        color: var(--semantic-color-content-interactive-primary-default);
        font-size: 10px;
        font-weight: bold;
        margin-left: 5px;
    }
}