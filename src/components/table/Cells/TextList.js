// @flow

import React from 'react';
import PropTypes from 'prop-types';
import './index.scss';

function TextList({ value }) {
  return (
    <div className="cell-table-text-list">
      <div className="icon-cont">
        {value.join(' , ')}
      </div>
    </div>
  );
}

TextList.propTypes = {
  value: PropTypes.arrayOf(PropTypes.string),
};

TextList.defaultProps = {
  value: [],
};

export default TextList;
