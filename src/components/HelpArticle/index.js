import React from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import PropTypes from 'prop-types';
import { loadArticle } from 'ducks/help';
import noop from 'lodash/noop';

export class HelpArticle extends React.Component {
  static propTypes = {
    article: PropTypes.string,
    actions: PropTypes.shape({
      load: PropTypes.func,
    }),
  };

  static defaultProps = {
    article: 'about-cloud-connector-admin-portal',
    actions: {
      load: noop,
    },
  };

  componentDidMount() {
    const { article, actions } = this.props;
    const { load } = actions;
    load(article);
  }

  render() {
    return null;
  }
}

export const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    load: loadArticle,
  }, dispatch);
  return {
    actions,
  };
};

export default connect(
  null,
  mapDispatchToProps,
)(HelpArticle);
