@import 'scss/mixins.scss';
@import 'scss/colors.scss';

.ec-root-page {
.multi-select-drop-down-container {
  width: 100%;
  .multi-select-drop-down {
    width: 100%;
    position: relative;
    min-width: 150px;
    .multi-select-drop-down-selection {
      @include DisplayFlex;
      cursor: pointer;
      justify-content: space-between;
      align-items: center;
      min-height: 30px;
      min-width: 150px;
      border-bottom: 1px solid var(--semantic-color-border-base-primary);

      .selection-icon, .selected-label {
        color: var(--semantic-color-content-interactive-primary-default);
      }

      .selection-icon {
        .fa-angle-down, .fa-angle-up {
          font-size: 19px;
        }
      }
    }

    .multi-select-drop-down-modal-parent {
      border: 1px solid var(--semantic-color-border-base-primary);
      border-radius: 3px;
      position: absolute;
      width: 100%;
      min-width: 100%;
      resize: both;
      overflow: auto;
      min-height: 180px;
      height: 180px;
      background-color: var(--semantic-color-background-primary);
      z-index: 9;
      .multi-select-drop-down-modal-cont {
        .no-data{
          text-align: center;
          padding-top: 20px;
          color: $grey1;
        }
        & {
          position: relative;
          width: 100%;
          height: 100%;
        }

        .modal-header {
          background-color: var(--semantic-color-background-primary);
          color: var(--semantic-color-content-base-primary);
          padding-bottom: 15px;
          line-height: 20px;
          font-size: 12px;

          .dropdown-search {
            @include DisplayFlex;
            margin: 0 10px;
            border-bottom: 1px solid var(--semantic-color-border-base-primary);
            justify-content: space-between;
            padding: 5px 0;
            position: relative;
            input {
              border: unset;
              outline: none;
              background: var(--semantic-color-content-inverted-base-secondary);
              width: 100%;
            }
            span {
              position: absolute;
              right: 0;
            }
            .remove-button,
            .clear-button {
              border: none;
              background: unset;
              outline: none;
              color: var(--semantic-color-content-interactive-primary-default);
            }
            .clear-button {
              margin-right: 5px;
            }
          }

        }
        .no-data {
          text-align: center;
        }
        .show-more-container {
          padding: 10px;
          .show-more {
            font-size: inherit;
          }
        }
        .load-more {
          color: var(--semantic-color-content-base-primary);
          line-height: 16px;
          margin: 0 auto;
          border: none;
          background: none;
          display: block;
          padding: 5px;
          font-size: 12px;
        }
      }
    }

    &.error-container {
      color: var(--semantic-color-content-status-danger-primary);
      padding-top: 2px;
    }

    &.error {
      input {
        border-color: var(--semantic-color-border-status-danger-active);
      }
    }
  }

  &.error {
    .multi-select-drop-down-selection {
      border-color: var(--semantic-color-border-status-danger-active);

      .fa-angle-down, .fa-angle-up {
        color: var(--semantic-color-content-status-danger-primary);
      }
    }
  }
}
}