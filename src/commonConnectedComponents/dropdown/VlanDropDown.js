import React from 'react';
import PropTypes from 'prop-types';
import i18n from 'utils/i18n';
import { EntityDropdown } from 'components/entityDropdown';

function VlanDropDown(props) {
  const LIST_ITEM = [];
  LIST_ITEM.push({
    id: 'TAGGED', name: i18n.t('TAGGED'),
  });
  LIST_ITEM.push({
    id: 'UNTAGGED', name: i18n.t('UNTAGGED'),
  });

  return (
    <EntityDropdown
      data={LIST_ITEM}
      {...props} />
  );
}

VlanDropDown.propTypes = {
  modelType: PropTypes.string,
  netInterface: PropTypes.arrayOf(),
};
VlanDropDown.defaultProps = {
  modelType: '',
  netInterface: [],
};

export default VlanDropDown;
