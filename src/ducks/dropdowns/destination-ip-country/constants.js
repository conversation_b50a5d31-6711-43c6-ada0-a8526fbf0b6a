import dropdown from 'ducks/generics/dropdown';
import Country from '@zscaler/ec-domain/json/Country.json';

export const REDUCER_KEY = 'destinationIPCountryDropdown';
export const STATIC_DATA = 'staticData';

export const DROPDOWN = dropdown.createStateConstants();
export const DROPDOWN_DEFAULT_STATE = dropdown.createDefaultState();

const formattedStaticData = Object.values(Country).map((e) => {
  e.id = e.name;
  return e;
});

export const DEFAULT_STATE = {
  ...DROPDOWN_DEFAULT_STATE,
  [STATIC_DATA]: formattedStaticData,
};

export const DUMMY_STORE_DATA = {
  [REDUCER_KEY]: {
    ...DEFAULT_STATE,
  },
};
