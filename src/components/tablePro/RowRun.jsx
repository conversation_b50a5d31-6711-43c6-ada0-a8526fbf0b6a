import React from 'react';
import PropTypes from 'prop-types';

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlay } from '@fortawesome/pro-regular-svg-icons';

const noop = () => undefined;
// RowEdit is responsible to support EDIT feature of the respective row
function RowRun(props) {
  const {
    onClickCallBack,
    isRunable,
  } = props;
  if (isRunable) {
    return (
      <>
        <FontAwesomeIcon
          icon={faPlay}
          title="Run"
          onClick={onClickCallBack}
          className="pencil-icon" />
      </>
    );
  }
  return '';
}

RowRun.propTypes = {
  onClickCallBack: PropTypes.func,
  isRunable: PropTypes.bool,
};

RowRun.defaultProps = {
  onClickCallBack: noop,
  isRunable: false,
};

export default RowRun;
