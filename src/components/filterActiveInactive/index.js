import React, {
  useEffect, useRef, useState,
} from 'react';
import PropTypes from 'prop-types';
import i18n from 'utils/i18n';
import { noop } from 'utils/lodash';

function FilterActiveInactive({
  show,
  value,
  onValueChange,
  onSearchClick,
  containerClass,
  containerStyle,
  onSearchHide,
  inputClass,
}) {
  const searchRef = useRef(null);
  const inputRef = useRef(null);

  const [inputValue, setInputValue] = useState(value);

  const resetState = () => {
    onSearchHide();
  };

  const handleClickOutside = (event) => {
    if (searchRef.current && !searchRef.current.contains(event.target)) {
      resetState();
    }
  };

  useEffect(() => {
    if (show) document.addEventListener('click', handleClickOutside);

    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [show]);

  const updateValue = (newValue) => {
    setInputValue(newValue);

    onValueChange(value);
  };

  const onChangeHandler = (evt) => {
    const { value: newValue } = evt.target;
    if (newValue === 'ALL') {
      onSearchClick(null);
      updateValue(null);
    } else {
      onSearchClick(newValue);
      updateValue(newValue);
    }
  };

  if (!show) return <></>;

  return (
    <article
      ref={searchRef}
      className={`search-container ${containerClass}`}
      style={containerStyle}>
      <section className="input-container">
        <select
          ref={inputRef}
          className={`input ${inputClass}`}
          type="text"
          value={inputValue}
          onChange={onChangeHandler}>
          <option key="ALL" value="ALL">{i18n.t('ALL')}</option>
          <option key="INACTIVE" value={i18n.t('INACTIVE')}>{i18n.t('INACTIVE')}</option>
          <option key="ACTIVE" value={i18n.t('ACTIVE')}>{i18n.t('ACTIVE')}</option>
        </select>
      </section>
    </article>
  );
}

FilterActiveInactive.propTypes = {
  show: PropTypes.bool,
  value: PropTypes.string,
  onValueChange: PropTypes.func,
  onSearchClick: PropTypes.func,
  containerClass: PropTypes.string,
  containerStyle: PropTypes.objectOf(Object),
  onSearchHide: PropTypes.func,
  inputClass: PropTypes.string,
};

FilterActiveInactive.defaultProps = {
  show: false,
  value: '',
  onValueChange: noop,
  onSearchClick: noop,
  containerClass: '',
  containerStyle: {},
  onSearchHide: noop,
  inputClass: '',
};

export default FilterActiveInactive;
