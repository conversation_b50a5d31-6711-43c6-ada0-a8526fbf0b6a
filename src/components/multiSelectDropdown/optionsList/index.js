/* eslint-disable jsx-a11y/label-has-for */
/* eslint-disable jsx-a11y/label-has-associated-control */
// @flow

import React, { Component } from 'react';
import HighlightedText from 'components/highlightedText';
import PropTypes from 'prop-types';
import ReactTooltip from 'react-tooltip';

import './index.scss';

class OptionsList extends Component {
  getItem = (item, parentId) => {
    const {
      onChange,
      searchString,
      disabledTooltip,
      isSingleSelect,
    } = this.props;
    const tooltip = item.disabled ? disabledTooltip : '';

    const isParent = !!item.children;
    const isDisabled = item.disabled || (isParent && !item.children.length);

    const parentClass = isParent ? 'parent' : '';
    const disabledClass = isDisabled ? 'item-disabled' : '';

    return (
      <div key={item.id} className={`option-item ${parentClass} ${disabledClass}`}>
        <ReactTooltip
          place="top"
          type="light"
          className="react-tooltip"
          id={`disabled-${item.id}`}
          disable={(tooltip.length === 0)}>
          {tooltip}
        </ReactTooltip>
        <label
          className="modal-item"
          data-tip
          data-for={`disabled-${item.id}`}>

          <input
            disabled={isDisabled}
            type="checkbox"
            checked={item.checked}
            onChange={(e) => onChange(e, { ...item, parentId }, isSingleSelect)} />
          <span className="checkmark">
            <span />
          </span>
          <span className="label-text">
            <HighlightedText text={item.name} searchString={searchString} />
          </span>
        </label>
      </div>
    );
  };

  getChildrenItems = (item) => {
    if (item.children) {
      return (
        <div className="children">
          {item.children.map((child) => (
            this.getItem(child, item.id)
          ))}
        </div>
      );
    }
    return null;
  };

  render() {
    const { data } = this.props;
    const hierarchyMode = data.find((item) => !!item.children);
    return (
      <div className={`multi-select-drop-down-modal ${hierarchyMode ? 'hierarchy-mode' : ''}`}>
        {data.map((item) => (
          <div className="option-item-wrapper" key={item.id}>
            {this.getItem(item)}
            {this.getChildrenItems(item)}
          </div>
        ))}

      </div>

    );
  }
}

OptionsList.propTypes = {
  data: PropTypes.arrayOf(PropTypes.shape({
    name: PropTypes.string,
    id: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.number,
    ]),
  })),
  onChange: PropTypes.func,
  searchString: PropTypes.string,
  disabledTooltip: PropTypes.string,
  isSingleSelect: PropTypes.bool,
};

OptionsList.defaultProps = {
  data: [],
  onChange: () => { },
  searchString: null,
  disabledTooltip: null,
  isSingleSelect: false,
};

export default OptionsList;
