# LeafletMap Component Guide

LeafletMap provides bindings between React and Leaflet. It does not replace Leaflet, but leverages it to abstract Leaflet layers as React components

[The properties passed to the components are used to create the relevant Leaflet instance when the component is rendered the first time and should be treated as immutable by default.

During the first render, all these properties should be supported as they are by Leaflet, however they will not be updated in the UI when they change unless they are explicitely documented as being mutable.

Mutable properties changes are compared by reference (unless stated otherwise) and are applied calling the relevant method on the Leaflet element instance.]

## Lifecycle process
1. The MapContainer renders a container `<div>` element for the map. If the placeholder prop is set, it will be rendered inside the container `<div>`.
2. The MapContainer instantiates a Leaflet Map for the created `<div>` with the component properties and creates the React context containing the map instance.
3. The MapContainer renders its children components.
4. Each child component instantiates the matching Leaflet instance for the element using the component properties and context, and adds it to the map.
5. When a child component is rendered again, changes to its supported mutable props are applied to the map.
6. When a component is removed from the render tree, it removes its layer from the map as needed.
## Limitations
- Leaflet makes direct calls to the DOM when it is loaded, therefore React Leaflet is not compatible with server-side rendering.
- The components exposed are abstractions for Leaflet layers, not DOM elements. Some of them have properties that can be updated directly by calling the setters exposed by Leaflet while others should be completely replaced, by setting an unique value on their key property so they are properly handled by React's algorithm.


## Sample

```
  <LeafletMap
    data={data}
    legendTitle="Title"
    legendDescription="Description"
    actions={actions} />
```
## Cloud Connector Reference
1. The 'cloudConnectorData' is to provide data to the tooltip popup
2. The 'showConnectorToolTip' and 'showTrafficTooltip' are used to support two different tooltips for 'Branch and Cloud Connector' and 'Traffic Forwarding' pages.
```
  <LeafletMap
    data={data}
    legendTitle="Data Centers"
    legendDescription="Cloud Connectors"
    actions={actions}
    cloudConnectorData={cloudConnectorData}
    showConnectorToolTip={showConnectorToolTip}
    showTrafficTooltip={showTrafficTooltip} />
```
## Custom Properties

```
  static propTypes = {
    data: PropTypes.arrayOf(PropTypes.shape({})),
    actions: PropTypes.shape(),
    cloudConnectorData: PropTypes.shape(),
    showConnectorToolTip: PropTypes.bool,
    showTrafficTooltip: PropTypes.bool,
  };

  static defaultProps = {
    data: [],
    actions: {},
    cloudConnectorData: {},
    showConnectorToolTip: false,
    showTrafficTooltip: false,
  };
```

## Map
1. Tilelayer - is responsible for the theme of the map
2. MarkerClusterGroup - is to create custom clustering
3. Marker - will allow us to create the custom marking. In 'Marker', the 'icon' property will helps to 
4. Popup - will provide the layout to add custom tooltip. To fetch Popup data via API call, 'popupOpen' property of Popup needs to be Overrided. The 'popupOpen' will accept function.

```
<Map
    className="markercluster-map"
    center={centerPosition}
    zoom={3}
    scrollWheelZoom={false}
    maxZoom={5}
    minZoom={3}
    dragging={false} // will disable dragging
    fitWorld>
    <TileLayer
        url="https://server.arcgisonline.com/ArcGIS/rest/services/Canvas/World_Light_Gray_Base/MapServer/tile/{z}/{y}/{x}.png" />

    <MarkerClusterGroup
        showCoverageOnHover={false}
        iconCreateFunction={GlobalMakerIcon}
        spiderLegPolylineOptions={{ weight: 0.25, opacity: 0.02 }}>
        <CircleMarker
        className={`${markerClicked ? '' : 'hide'}`}
        center={[fenceCords.latitude, fenceCords.longitude]}
        color="#F36F00"
        fillColor="orange"
        radius={100}>

            {data.map((item) => {
              const {
                geoLocation,
                deploymentType,
                status,
              } = item;
              let icon = awsIcon;
              const lat = (geoLocation && 'latitude' in geoLocation) ? formatter(geoLocation.latitude) : 0;
              const log = (geoLocation && 'longitude' in geoLocation) ? formatter(geoLocation.longitude) : 0;
              
              return (
                <Marker
                  key={`${geoLocation}`}
                  position={[
                    lat,
                    log,
                  ]}
                  icon={icon}>
                  <Popup
                    minWidth={250}
                    closeButton={false}
                    maxHeight="auto"
                    onClose={popup => console.warn('popup-close', popup)}>
                    <WrappedTrafficTooltip {...item} showTrafficTooltip={showTrafficTooltip} />
                  </Popup>
                </Marker>
              );
            })
            }
          </CircleMarker>
    </MarkerClusterGroup>
</Map>
```