/* eslint-disable react/jsx-handler-names */
/* eslint-disable no-return-assign */
import React from 'react';
import PropTypes from 'prop-types';
import { noop } from 'utils/lodash';

import MultiUnselected from './MultiUnselected';
import MultiSelected from './MultiSelected';
import DropdownFooter from './DropdownFooter';

export class MultiDropdownSelector extends React.Component {
  componentDidMount() {
    const {
      // eslint-disable-next-line react/prop-types
      categoryUpdated: resetToDefault = false,
      actions: { load },
    } = this.props;
    load(resetToDefault);
    document.addEventListener('mousedown', this.handleClick);
  }

  componentWillUnmount() {
    // remove click handler and clean up state on unmount
    document.removeEventListener('mousedown', this.handleClick);
    this.performStateCleanup();
  }

  node = null;

  handleClick = (e) => {
    if (this.node && !this.node.contains(e.target)) {
      this.onComplete();
    }
  };

  onComplete = () => {
    // Tell redux-form that we are done making changes to this
    // input. Also perform cleanup
    const {
      input: { onChange },
      selectedValues,
    } = this.props;

    return Promise.resolve()
      .then(() => {
        return onChange(selectedValues);
      })
      .then(() => {
        this.performStateCleanup();
      });
  };

  performStateCleanup = () => {
    // clean needs to be done when form closes or
    // user is done making changes (presses Done button).
    // Essentially put the state in initial state.
    const {
      actions: { cancel },
    } = this.props;
    cancel();
  };

  setNode = (node) => this.node = node;

  render() {
    const { place } = this.props;

    return (
      <div
        className={`${place} dropdown-selector`}
        ref={this.setNode}>
        <div className="dropdown-lists">
          <MultiUnselected {...this.props} />
          <MultiSelected {...this.props} />
        </div>
        <DropdownFooter
          {...this.props}
          onComplete={this.onComplete} />
      </div>
    );
  }
}

MultiDropdownSelector.propTypes = {
  actions: PropTypes.shape({
    load: PropTypes.func,
    cancel: PropTypes.func,
  }),
  place: PropTypes.string,
  input: PropTypes.shape({
    onChange: PropTypes.func,
  }),
  selectedValues: PropTypes.arrayOf(PropTypes.shape({})),
};

MultiDropdownSelector.defaultProps = {
  actions: {
    load: noop,
  },
  place: 'left',
  input: {
    onChange: noop,
  },
  selectedValues: [],
};

export default MultiDropdownSelector;
