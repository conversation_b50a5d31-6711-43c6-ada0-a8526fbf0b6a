/* no-param-reassign */
import React from 'react';
import PropTypes from 'prop-types';
import { noop, isEqual } from 'utils/lodash';
import moment from 'moment';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faAngleUp, faAngleDown } from '@fortawesome/pro-solid-svg-icons';
import { withTranslation } from 'react-i18next';
import DateTimePicker from 'components/dateTimePicker';
import './index.scss';

const format = 'MMM DD HH:mm:ss';
const formatDay = 'MM/DD/YYYY';
const formatHour = 'HH:mm:ss';

class DateDropdown extends React.Component {
  static propTypes = {
    items: PropTypes.arrayOf(
      PropTypes.shape({
        value: PropTypes.string,
        label: PropTypes.string,
      }),
    ),
    t: PropTypes.func,
    setValue: PropTypes.func,
    defaultValue: PropTypes.shape({
      value: PropTypes.string,
      label: PropTypes.string,
    }),
    currentValue: PropTypes.shape({
      value: PropTypes.string,
      label: PropTypes.string,
    }),
    setResetValue: PropTypes.func,
    showMinute: PropTypes.bool,
    showSecond: PropTypes.bool,
    dateRange: PropTypes.number,
    maxDateSelectable: PropTypes.number,
  };

  static defaultProps = {
    items: [],
    t: (str) => str,
    setValue: noop,
    defaultValue: {},
    showMinute: true,
    showSecond: true,
    dateRange: 90,
    maxDateSelectable: 90,
  };

  constructor(props) {
    super(props);
    this.state = {
      openList: false,
      isDpOpened: false,
    };
    this.wrapperRef = null;
  }

  componentDidMount() {
    document.addEventListener('mousedown', this.handleMousedown);
    const { defaultValue, currentValue, setResetValue } = this.props;
    if (
      !!defaultValue
      && defaultValue.value
      && defaultValue.value !== currentValue.value
      && !!defaultValue
    ) {
      this.handleChangeValue(defaultValue);
      if (setResetValue) {
        setResetValue(defaultValue);
      }
    }
  }

  componentWillUnmount() {
    document.removeEventListener('mousedown', this.handleMousedown);
  }

  setWrapperRef = (node) => {
    this.wrapperRef = node;
  };

  handleChangeValue = (item) => {
    const { setValue } = this.props;
    if (item.id !== 'custom') {
      setValue(item);
    } else {
      this.setState({ isDpOpened: true });
    }
    this.setState({
      openList: false,
    });
  };

  handleMousedown = (e) => {
    const containRef = this.wrapperRef && this.wrapperRef.contains(e.target);
    if (!containRef) {
      this.setState({ openList: false });
    }
  };

  handleOpenList = () => {
    this.setState((prev) => ({ openList: !prev.openList }));
  };

  applyRange = (date) => {
    const { setValue } = this.props;
    const {
      timeShift, hours, startDate, endDate,
    } = date;
    const value = {
      value: 'custom',
      label: 'CUSTOM',
      timeshift: timeShift,
      hour: hours,
      startDate,
      endDate,
    };
    setValue(value);
    this.setState({ isDpOpened: false });
  };

  handleCustomPickerClose = () => {
    this.setState({
      isDpOpened: false,
    });
  };

  renderCustomButton = (start, end) => {
    return (
      <>
        <div className="startDate">{moment(start).format(format)}</div>
        <span> - </span>
        <div className="endDate">{moment(end).format(format)}</div>
      </>
    );
  };

  renderButton = (currentValue, start, end, t) => {
    const { label = '' } = currentValue || {};
    const isMinuteLabel = label.includes('LAST');
    const startDate = start ? moment(start).format(formatDay) : moment().startOf('day').format(formatDay);
    const endDate = moment(end).format(formatDay);
    const startHour = start ? moment(start).format(formatHour) : moment().startOf('day').format(formatHour);
    const endHour = moment(end).format(formatHour);

    return (
      startDate === endDate && startHour !== endHour
        ? (
          <>
            <span>
              {label ? `${t(label)}: ` : ''}
            </span>
            <div className="startDate">
              {startDate}
              {isMinuteLabel ? ` ${startHour}` : ''}
            </div>
            {' '}
            <div className="endDate">
              {isMinuteLabel ? `- ${endHour}` : ''}
            </div>

          </>
        )
        : (
          <>
            <span>
              {label ? `${t(label)}: ` : ''}
            </span>
            <div className="startDate">{`${startDate} ${startHour === endHour ? startHour : ''}`}</div>
            <span> - </span>
            <div className="endDate">{`${endDate}  ${startHour === endHour ? startHour : ''}`}</div>
          </>
        )
    );
  };

  render() {
    const {
      items, t, currentValue, defaultValue, showMinute, showSecond, dateRange, maxDateSelectable,
    } = this.props;
    
    const { openList, isDpOpened } = this.state;
    const {
      value, endDate, startDate, label,
    } = currentValue || defaultValue;
    let labelText = (
      <span>{t(isDpOpened ? 'CUSTOM' : label)}</span>
    );

    if (value === 'custom' && endDate && startDate) {
      labelText = this.renderCustomButton(startDate, endDate);
    } else {
      labelText = this.renderButton(currentValue, startDate, endDate, t);
    }
    return (
      <div className="drop-down-container" ref={this.setWrapperRef}>
        <button
          className="drop-down-selected-value"
          onClick={this.handleOpenList}
          type="button"
          tabIndex="0">
          <span className="drop-down-selected-value-label">
            {labelText}
          </span>
          <span
            className={`dropdown-icon fa ${
              openList ? 'angle-up' : 'angle-down'
            }`}>
            {openList
              ? <FontAwesomeIcon icon={faAngleUp} className="fontStyle" pull="right" />
              : <FontAwesomeIcon icon={faAngleDown} className="fontStyle" pull="right" />}
          </span>
        </button>
        <div className="dropdown-box">
          <div className={`drop-down-list ${openList ? 'open' : ''} ${isDpOpened ? 'open-custom' : ''}`}>
            <div className="dropdown-list-content">
              {items.map((item) => (
                <div
                  key={item.id}
                  className={`dropdown-list-item ${
                    (isEqual(item.id, currentValue.value)
                        && !isDpOpened)
                      || (isEqual(item.id, 'custom') && isDpOpened)
                      ? 'dropdown-selected-value'
                      : ''
                  }`}>
                  <button
                    onClick={() => {
                      this.handleChangeValue(item);
                    }}
                    type="button">
                    <span>{t(item.name)}</span>
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
        {isDpOpened && (
          <DateTimePicker
            isOpened={isDpOpened}
            showMinute={showMinute}
            showSecond={showSecond}
            dateRange={dateRange}
            maxDateSelectable={maxDateSelectable}
            applyRage={this.applyRange}
            currentValue={currentValue}
            onClose={this.handleCustomPickerClose} />
        )}
      </div>
    );
  }
}

export default withTranslation()(DateDropdown);
