@import "scss/colors.scss";
@import "scss/mixins.scss";

.ec-root-page {
.toggle-inline-check-box-container {
  padding: 10px 0 0 0;
  label {
    display: flex;
    align-items: center;
    width: fit-content;
  }

  p {
    color: $grey1;
    font-weight: 500;
    margin: 15px 0 10px;
    text-transform: capitalize;
  }
  .check-box-container{
    input {
      display: none;
    }
  }
  
  .toggle-switch { 
    color: var(--semantic-color-content-interactive-primary-default);
    font-size: 20px;
  }
  .toggle-label { 
    .label {
      font-size: 13px;
      font-style: normal;
      font-weight: 500;
      line-height: 20px; /* 153.846% */
      color: #000000
    }
    .sub-label {
      font-size: 13px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px; /* 153.846% */
      color: #767676
    }
  }
  .toggle-icon {
    size: 15em;
  }
  .toggle-medium-icon {
    size: 15em;
    width: 3.75em;
    height: 3.75em;
  }
  input[type=checkbox] {
    display: none;
  }
}
}