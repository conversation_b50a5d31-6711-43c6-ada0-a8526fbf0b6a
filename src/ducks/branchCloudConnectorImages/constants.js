// this will be hooked onto the relative state tree.
import { BASE_API_PATH } from 'config';

export const REDUCER_KEY = 'branch-cloud-connector-images';
export const API_ENDPOINT = `${BASE_API_PATH}/v1/downloadScript/AWS/`;
export const API_DOWNLOAD_LINK = `${BASE_API_PATH}/v1/ecbuild/images?Type=ESXI&Type=KVM&Type=HYPER`;
export const API_DOWNLOAD_ENDPOINT = (name) => `${BASE_API_PATH}/v1/downloadScript/branch/${name}`;
export const API_DOWNLOAD_APP_CONNECTOR_ENDPOINT = (name) => `${BASE_API_PATH}/v1/downloadScript/branchHybrid/${name}`;

export const DATA_ERROR = 'error';
export const DATA_LOADING = 'loading';
export const DATA = 'data';
export const DATA_CLOUD_LOCAL = 'localAwsData';
export const DATA_TERRA_LOCAL = 'localTerraData';
export const IMAGES_DATA_TABLE = 'imagestabledata';
export const ESXI_LINK = 'esxiLink';
export const KVM_LINK = 'kvmLink';
export const HYPER_V_LINK = 'hyperVLink';
export const IMAGES_APP_CONNECTOR_DATA_TABLE = 'imagesAppConnectorTableData';
export const LOADED = 'loaded';
export const TOTAL_DOWNLOAD = 'totalDownload';
export const SHOW_MODAL = 'showDownloadForm';

// this will store data to create and edit form
export const APP_DATA = 'appData';

export const imagesTableData = [{
  hypervisor: 'Linux KVM',
  name: 'KVM',
  version: '1.2.1',
}, {
  hypervisor: 'VMware ESXi',
  name: 'ESXI',
  version: '1.2.1',
}, {
  hypervisor: 'Microsfot Hyper V',
  name: 'HYPER_V',
  version: '1.2.1',
}];

export const imagesAppConnectorTableData = [{
  hypervisor: 'Linux KVM',
  name: 'KVM',
  version: '1.2.1',
}, {
  hypervisor: 'VMware ESXi',
  name: 'ESXI',
  version: '1.2.1',
}, {
  hypervisor: 'Microsfot Hyper V',
  name: 'HYPER_V',
  version: '1.2.1',
}];

export const branchImagesColumns = [
  {
    id: 'dataIdx',
    accessor: (row, i) => i + 1,
    Header: 'NUMBER_ABBR',
    width: 60,
    disableReordering: true,
    disableResizing: true,
    disableSortBy: true,
    meta: {
      skipTranslation: false,
      customCellType: 'RENDER_INDEX',
    },
  },
  {
    id: 'hypervisor',
    accessor: (row) => row.hypervisor,
    Header: 'HYPERVISOR_OS',
    width: 120,
    defaultCanSort: false,
    disableSortBy: true,
    meta: {
      skipTranslation: false,
    },
  },
  {
    id: 'version',
    accessor: (row) => row.version,
    Header: 'VERSION',
    width: 120,
    defaultCanSort: false,
    disableSortBy: true,
    meta: {
      skipTranslation: false,
    },
  },
];

const editColumn = {
  id: 'editor',
  Header: '',
  width: 59,
  disableReordering: true,
  disableResizing: true,
  disableSortBy: true,
  meta: {
    customCellType: 'DOWNLOAD_FILE_BC_IMAGES',
    pageName: 'BRANCH_IMAGES',
  },
};

export const BRANCH_IMAGES_TABLE_CONFIGS = {
  columns: [...branchImagesColumns, editColumn],
  initialState: {
    sortBy: [{ id: 'name' }],
  },
  disableTableHeaderDrag: true, // disable table column drag and drop
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: true,
};

export const RESOURCE_DEFAULTS = {
  [DATA_ERROR]: null, // or error object so we can process and show to user
  [DATA_LOADING]: true,
  [APP_DATA]: {},
  [DATA]: [],
  [IMAGES_DATA_TABLE]: imagesTableData,
  [IMAGES_APP_CONNECTOR_DATA_TABLE]: imagesAppConnectorTableData,
  [LOADED]: 0,
  [TOTAL_DOWNLOAD]: 0,
  [SHOW_MODAL]: false,
};

export const DEFAULTS = {
  ...RESOURCE_DEFAULTS,
};

// default state for this page/widget/component
export const DEFAULT_STATE = {
  ...DEFAULTS,
};

// For testing...to be used later
export const DUMMY_STORE_DATA = {
  [REDUCER_KEY]: {
    ...DEFAULT_STATE,
  },
};
