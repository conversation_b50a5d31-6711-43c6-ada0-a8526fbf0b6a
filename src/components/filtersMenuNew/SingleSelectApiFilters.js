import PropTypes from 'prop-types';
import { noop } from 'utils/lodash';
// import Localized from 'utils/i18n';
import { withTranslation } from 'react-i18next';

import React from 'react';
import Spinner from '../spinner';

class SingleSelectApiDropdown extends React.Component {
  constructor(props) {
    super(props);
    const { defaultValue } = props;
    this.state = {
      search: '',
      selectedValue: defaultValue,
      openList: false,
    };
    this.wrapperRef = null;
  }

  componentDidMount() {
    document.addEventListener('mousedown', this.handleMousedown);
  }

  componentDidUpdate(prevProps) {
    const {
      defaultValue, hasApiSearch, isLoading, reset,
    } = this.props;
    const { search } = this.state;
    if (prevProps.defaultValue !== defaultValue
      || prevProps.isLoading !== isLoading
      || (reset && !prevProps.reset)) {
      // eslint-disable-next-line
      this.setState({
        selectedValue: defaultValue,
        search: hasApiSearch ? search : '',
      });
    }
  }

  componentWillUnmount() {
    document.removeEventListener('mousedown', this.handleMousedown);
  }

  setWrapperRef = (node) => {
    this.wrapperRef = node;
  };

  handleChangeValue = (item) => {
    const { setValue } = this.props;
    this.setState({
      selectedValue: item,
      openList: false,
    });
    setValue(item);
  };

  handleMousedown = (e) => {
    const containRef = this.wrapperRef.contains(e.target);
    if (!containRef) {
      this.setState({ openList: false, search: '' });
    }
  };

  handleOpenList = () => {
    const { fetchOptionsCallback } = this.props;
    const { openList } = this.state;
    this.setState({ openList: !openList });
    fetchOptionsCallback();
  };

  handleChangeInput = (event) => {
    const inputVal = event.target.value;
    const { hasApiSearch, searchCallback } = this.props;
    const { search } = this.state;
    this.setState({ search: inputVal });
    if (hasApiSearch && search !== inputVal) {
      searchCallback(inputVal.trim());
    }
  };

  render() {
    const {
      isLoading, hasApiSearch, items, placeholderSearch, t, searchCallback,
      showLabelInSelectedValue, labelSelectedValue, removeFilterCallback, hasLocalSort,
    } = this.props;
    const { search, selectedValue, openList } = this.state;
    if (hasLocalSort) {
      items.sort((a, b) => {
        return a.label.localeCompare(b.label);
      });
    }
    const dropdownOptions = (
      <div className="dropdown-list-content">
        {isLoading
          && <Spinner size="medium" />}
        {items.map((item) => (
          <div key={item.id} className={`dropdown-list-item ${(item.label === selectedValue.label) ? 'dropdown-selected-value' : ''}`}>
            <button onClick={() => { this.handleChangeValue(item); }} type="button">
              {item.secLabel && (
                <div>{t(item.secLabel)}</div>
              )}
              <div>{t(item.label)}</div>
            </button>
          </div>
        ))}
      </div>
    );

    return (
      <div>
        <div className="drop-down-container" ref={this.setWrapperRef}>
          <button
            className="drop-down-selected-value"
            onClick={this.handleOpenList}
            type="button">
            <div className="dropdown-selected-label">
              <span className="label-selected-items">
                {showLabelInSelectedValue && (
                  <span className="label-selected-value">
                    {labelSelectedValue}
                    =
                  </span>
                )}
                {selectedValue.label}
              </span>
            </div>
            {!showLabelInSelectedValue && <div className="dropdown-selected-sub-label">{t(selectedValue.label)}</div>}
          </button>
          {removeFilterCallback && (
            // eslint-disable-next-line jsx-a11y/control-has-associated-label
            <div
              className="far fa-times-circle remove-filter-icon"
              role="button"
              tabIndex={0}
              onClick={removeFilterCallback}
              onKeyPress={removeFilterCallback}>
            </div>
          )}
          <div className="dropdown-box">
            <div className={`drop-down-list ${openList ? 'open' : ''}`}>
              {(hasApiSearch)
                && (
                  <div className="dropdown-list-header">
                    <div className="dropdown-search">
                      <input
                        className="dropdown-input-search"
                        type="text"
                        value={search}
                        placeholder={t(placeholderSearch)}
                        onChange={this.handleChangeInput} />
                      <span
                        role="button"
                        tabIndex="0"
                        aria-label="search-dropdown"
                        onKeyPress={() => searchCallback(search)}
                        onClick={() => searchCallback(search)}
                        className="search-icon fa fa-search">
                      </span>
                    </div>
                  </div>
                )}
              {dropdownOptions}
            </div>
          </div>
        </div>
      </div>
    );
  }
}

SingleSelectApiDropdown.propTypes = {
  fetchOptionsCallback: PropTypes.func,
  t: PropTypes.func,
  isLoading: PropTypes.bool,
  hasApiSearch: PropTypes.bool,
  placeholderSearch: PropTypes.string,
  searchCallback: PropTypes.func,
  items: PropTypes.arrayOf(PropTypes.shape({
    value: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.number,
      PropTypes.arrayOf(PropTypes.string),
      PropTypes.shape({}),
    ]),
    label: PropTypes.string,
  })),
  setValue: PropTypes.func,
  selectedValue: PropTypes.shape({
    value: PropTypes.oneOfType([
      PropTypes.number,
      PropTypes.string,
      PropTypes.arrayOf(PropTypes.string),
      PropTypes.shape({}),
    ]),
    label: PropTypes.string,
  }),
  hasLocalSort: PropTypes.bool,
  defaultValue: PropTypes.shape({
    value: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.number,
      PropTypes.arrayOf(PropTypes.string),
      PropTypes.shape({}),
    ]),
    label: PropTypes.string,
  }),
  labelSelectedValue: PropTypes.string,
  showLabelInSelectedValue: PropTypes.bool,
  removeFilterCallback: PropTypes.func,
  reset: PropTypes.bool,
};

SingleSelectApiDropdown.defaultProps = {
  fetchOptionsCallback: noop,
  isLoading: true,
  t: (str) => str,
  items: [],
  placeholderSearch: 'DROPDOWN_SEARCH',
  selectedValue: {},
  setValue: noop,
  defaultValue: {},
  hasLocalSort: true,
  searchCallback: noop,
  hasApiSearch: false,
  showLabelInSelectedValue: false,
  removeFilterCallback: noop,
  reset: false,
};

export default withTranslation()(SingleSelectApiDropdown);
