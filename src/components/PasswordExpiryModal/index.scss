@import 'scss/mixins.scss';
@import 'scss/colors.scss';

.ec-root-page {
.modal-content {

  min-height: 18vh;
  max-height: 70vh;
  overflow: auto;
  &.no-max-height{
    max-height: none;
  }

  .ecui-waiting-overlay  {
    align-self: center;
  }

  .container {
    padding: 20px 15px;



    &-fields {
      @include DisplayFlex;
      background: $white;
      border-radius: 2px;
      background-color: $white;
      box-shadow: 0 0 11px 0 $grey14;

      >div {
        flex: 0.5;
      }
    }
  }
  .modal-footer.change-password-modal-footer {
    position: relative;
    bottom: 0;
    width: 100%;
  }
  .change-password-warning {
    color: #f69f00;
    margin: 15px;
    line-height: 18px;
    font-size: 13px;
  }
}
}

