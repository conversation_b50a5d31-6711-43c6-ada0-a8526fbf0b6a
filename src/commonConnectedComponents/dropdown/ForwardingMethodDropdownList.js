import React from 'react';
import { EntityDropdown } from 'components/entityDropdown';
import { TRAFFIC_FWD_METHOD } from 'config';
import { useSelector } from 'react-redux';
import { has5Gsku, verifyConfigData } from 'utils/helpers';
import { accessSubscriptionSelector } from 'ducks/login/selectors';
import * as loginSelectors from 'ducks/login/selectors';

const TO_OMIT = ['ECDIRECTSCTPXFORM', 'ECZPASCTPXFORM'];

export function ForwardingMethodDropdownList(props) {
  const subscriptions = useSelector(accessSubscriptionSelector);
  const configData = useSelector((state) => loginSelectors.configDataSelector(state));

  const has5G = has5Gsku(subscriptions);

  let dropdownData = has5G
    ? TRAFFIC_FWD_METHOD : TRAFFIC_FWD_METHOD.filter(({ id }) => !TO_OMIT.includes(id));

  const enableWorkloadDiscoveryService = verifyConfigData({ configData, key: 'enableWorkloadDiscoveryService' });
  const enableAzureWorkloadDiscoveryService = verifyConfigData({ configData, key: 'enableAzureWorkloadDiscoveryService' });
  const enableGcpWorkloadDiscoveryService = verifyConfigData({ configData, key: 'enableGcpWorkloadDiscoveryService' });

  const enableEastWestTrafficFwd = verifyConfigData({ configData, key: 'enableEastWestTrafficFwd' }) && (
    enableWorkloadDiscoveryService || enableAzureWorkloadDiscoveryService || enableGcpWorkloadDiscoveryService
  );
  dropdownData = enableEastWestTrafficFwd
    ? dropdownData : dropdownData.filter(({ id }) => !['LOCAL_SWITCH'].includes(id));

  return <EntityDropdown data={dropdownData} {...props} />;
}

export default ForwardingMethodDropdownList;
