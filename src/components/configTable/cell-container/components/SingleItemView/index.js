import React, { useState, useRef, useEffect } from 'react';
import PropTypes from 'prop-types';
import { isEmpty } from 'utils/lodash';
import { useTranslation } from 'react-i18next';

const getValue = (item) => {
  if (Array.isArray(item)) return !isEmpty(item) ? item.map((x) => x.name).join(', ') : '';
  return item;
};

function SingleItemView({ field, row, generalValue }) {
  const item = row[field];
  const value = getValue(item);
  const valueRef = useRef(null);
  const { t } = useTranslation();
  const [isValueEllipsisActive, setIsValueEllipsisActive] = useState(false);

  useEffect(() => {
    if (!valueRef.current) return;

    if (valueRef.current.offsetWidth < valueRef.current.scrollWidth) {
      setIsValueEllipsisActive(true);
    }
  }, [valueRef, value]);
  
  return (
    <div style={{ position: 'relative' }}>
      <div className="policy-table-criteria-item">
          
        <div className="policy-table-criteria-item-data">
          <span
            ref={valueRef}
            data-for="secondaryTooltip"
            data-tip={isValueEllipsisActive ? value : ''}
            className={`policy-table-cell-data ${isValueEllipsisActive ? 'has-elipsis' : ''}`}>
            <span className="policy-table-criteria-item-data-item">
              {!isEmpty(value) ? value : t(generalValue)}
            </span>
          </span>
        </div>
      </div>
    </div>
  );
}

SingleItemView.propTypes = {
  field: PropTypes.string,
  row: PropTypes.shape({}),
  generalValue: PropTypes.string,
};
  
SingleItemView.defaultProps = {
  field: '',
  row: {},
  generalValue: '',
};

export default SingleItemView;
