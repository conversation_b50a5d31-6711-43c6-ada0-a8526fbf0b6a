// @flow
import React from 'react';
import PropTypes from 'prop-types';
import colors from 'scss/colors.scss';
import { DATA_TIP } from '../../MapTooltip';

function NumberMarker({ number, circleColor, onMouseMove }) {
  const circleSize = 30;
  const x = '50%';
  const y = '50%';

  return (
    <svg
      onMouseMove={onMouseMove}
      height={circleSize}
      width={circleSize}
      data-tip={DATA_TIP}>
      <circle
        cx={circleSize / 2}
        cy={circleSize / 2}
        r="9"
        fill={circleColor}
        style={{
          pressed: { outline: 0 },
        }} />

      <circle
        cx={circleSize / 2}
        cy={circleSize / 2}
        r="12"
        fill={circleColor}
        stroke={circleColor}
        strokeWidth="7"
        strokeOpacity="0.3"
        fillOpacity="0.4"
        style={{
          pressed: { outline: 0 },
        }} />

      <text
        x={x}
        y={y}
        textAnchor="middle"
        fill={colors.white}
        dy="0.325em">
        {number}
      </text>
    </svg>
  );
}

NumberMarker.propTypes = {
  number: PropTypes.number,
  circleColor: PropTypes.string,
  onMouseMove: PropTypes.func,
};

NumberMarker.defaultProps = {
  number: null,
  circleColor: null,
  onMouseMove: () => {},
};

export default NumberMarker;
