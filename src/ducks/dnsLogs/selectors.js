import { createSelector } from 'reselect';
import { getFormValues, getFormSyncErrors, getFormMeta } from 'redux-form';
import * as constants from './constants';

export const baseSelector = (state) => state[constants.REDUCER_KEY];

export const formSelector = (state) => state[constants.STATE_FORM];

export const loadingSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA_LOADING],
);

export const errorSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA_ERROR],
);

export const dataSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA] || {},
);

export const appDataSelector = createSelector(
  baseSelector,
  (state) => state[constants.APP_DATA],
);

export const accordionSelector = createSelector(
  baseSelector,
  (state) => state[constants.SHOW_ACCORDION] || false,
);

export const columnsSelector = createSelector(
  baseSelector,
  (state) => state[constants.COLUMNS] || [],
);

export const showFiltersSelector = createSelector(
  baseSelector,
  (state) => state[constants.SHOW_FILTERS] || {},
);

export const addFilterDropdownSelector = createSelector(
  baseSelector,
  (state) => state[constants.FILTERS] || [],
);

export const addFilterSwitchDropdownSelector = createSelector(
  baseSelector,
  (state) => {
    if (state[constants.CURRENT_FILTERS].length > 0) {
      return state[constants.CURRENT_FILTERS];
    }
    return state[constants.FILTERS] || [];
  },
);

export const addFilterCurrentDropdownSelector = createSelector(
  baseSelector,
  (state) => state[constants.CURRENT_FILTERS] || state[constants.FILTERS] || [],
);

export const locationDropdownSelector = createSelector(
  baseSelector,
  (state) => state[constants.LOCATIONS] || [],
);

export const dnsFilersForm = createSelector(
  formSelector,
  (state) => state.dnsFilersForm || [],
);

export const dnsFiltersFormValues = createSelector(
  dnsFilersForm,
  (state) => state.values || {},
);

export const formServerPortFrom = createSelector(
  dnsFiltersFormValues,
  (state) => state.serverPortFrom || '',
);

export const formServerPortTo = createSelector(
  dnsFiltersFormValues,
  (state) => state.serverPortTo || '',
);

export const formRequestDurationFrom = createSelector(
  dnsFiltersFormValues,
  (state) => state.requestDurationFrom || '',
);

export const formRequestDurationTo = createSelector(
  dnsFiltersFormValues,
  (state) => state.requestDurationTo || '',
);

export const formInbytesFrom = createSelector(
  dnsFiltersFormValues,
  (state) => state.inbytesFrom || '',
);

export const formInbytesTo = createSelector(
  dnsFiltersFormValues,
  (state) => state.inbytesTo || '',
);

export const formOutbytesFrom = createSelector(
  dnsFiltersFormValues,
  (state) => state.outbytesFrom || '',
);

export const formOutbytesTo = createSelector(
  dnsFiltersFormValues,
  (state) => state.outbytesTo || '',
);

export const progressSelector = createSelector(
  baseSelector,
  (state) => state[constants.PROGRESS],
);

export const downloadCSVSelector = createSelector(
  baseSelector,
  (state) => state[constants.DOWNLOAD_CSV],
);

export const isDownloadSelector = createSelector(
  baseSelector,
  (state) => state[constants.IS_DONWLOAD],
);
// alway return top level selector
export const formValuesSelector = (state) => getFormValues('dnsFilersForm')(state);
export const formMetaSelector = (state) => getFormMeta('dnsFilersForm')(state);
export const formSyncErrorsSelector = (state) => getFormSyncErrors('dnsFilersForm')(state);

export default baseSelector;
