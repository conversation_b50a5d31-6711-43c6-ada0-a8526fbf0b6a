# Component Design requirements and rules

Work in Progress


[Connected Components document is here](src/pages/page-component.md)

Presentational Components
...WIP

## Component IDs
DOM elements ids are required for testing purpose. The child component will need to accept an `id` prop coming from the parent. The child component will use `this.props.id` as part of the id in its own elements. Example below:

Parent component:
```
<div>
    <ChildComponent id="demo" />
</div>
```

Child component:

```
<div>
    <form>
        <input type="text" name="client" id={`${this.props.id}-textbox-client`} />
        <input type="submit" id={`${this.props.id}-button-submit`} />
    </form>
</div>
```
