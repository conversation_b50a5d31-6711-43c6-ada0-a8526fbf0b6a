import React from 'react';
import PropTypes from 'prop-types';
import i18n from 'utils/i18n';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faBan } from '@fortawesome/pro-regular-svg-icons';

const noop = () => undefined;
// RowEdit is responsible to support EDIT feature of the respective row
function RowDisable(props) {
  const {
    onClickCallBack,
    isDisable,
  } = props;

  if (isDisable) {
    return (
      <FontAwesomeIcon
        icon={faBan}
        title={i18n.t('DISABLE')}
        onClick={onClickCallBack}
        className="pencil-icon" />
    );
  }
  return '';
}

RowDisable.propTypes = {
  onClickCallBack: PropTypes.func,
  isDisable: PropTypes.bool,
};

RowDisable.defaultProps = {
  onClickCallBack: noop,
  isDisable: false,
};

export default RowDisable;
