import React from 'react';
import { render, fireEvent } from '@testing-library/react';
import { withTranslation } from 'react-i18next';
import RadioButton from './RadioButtonNew';

jest.mock('react-i18next', () => ({
  withTranslation: () => (Component) => function translate(props) {
    return <Component t={(str) => str} {...props} />;
  },
}));

describe('RadioButton component', () => {
  const defaultProps = {
    propertyName: 'radio-button',
    actionCallback: jest.fn(),
    isReadOnly: false,
    customAttributeClass: '',
    list: [],
    disabled: false,
    label: '',
    tooltip: '',
    defaultValue: '',
    throwError: false,
    errorTooltip: '',
  };

  it('renders correctly in read-only mode', () => {
    const props = {
      ...defaultProps,
      isReadOnly: true,
      list: [
        { value: 'value1', displayText: 'displayText1', classNames: ['active'] },
        { value: 'value2', displayText: 'displayText2' },
      ],
      defaultValue: 'value1',
    };

    const { getByText } = render(<RadioButton {...props} />);
    expect(getByText('displayText1')).toBeInTheDocument();
  });

  it('renders correctly in non-read-only mode', () => {
    const props = {
      ...defaultProps,
      list: [
        { id: 'id1', value: 'value1', displayText: 'displayText1' },
        { id: 'id2', value: 'value2', displayText: 'displayText2' },
      ],
      defaultValue: 'value1',
    };

    const { getAllByRole } = render(<RadioButton {...props} />);
    const buttons = getAllByRole('button');
    expect(buttons.length).toBe(2);
    expect(buttons[0]).toHaveClass('radio-button active');
    expect(buttons[1]).not.toHaveClass('active');
  });

  it('calls actionCallback when button is clicked', () => {
    const props = {
      ...defaultProps,
      list: [
        { id: 'id1', value: 'value1', displayText: 'displayText1' },
        { id: 'id2', value: 'value2', displayText: 'displayText2' },
      ],
      defaultValue: 'value1',
      actionCallback: jest.fn(),
    };

    const { getAllByRole } = render(<RadioButton {...props} />);
    const buttons = getAllByRole('button');
    fireEvent.click(buttons[1]);
    expect(props.actionCallback).toHaveBeenCalledTimes(1);
    expect(props.actionCallback).toHaveBeenCalledWith('radio-button', { id: 'id2', value: 'value2', displayText: 'displayText2' });
  });

  it('does not call actionCallback when button is disabled', () => {
    const props = {
      ...defaultProps,
      list: [
        { id: 'id1', value: 'value1', displayText: 'displayText1' },
        {
          id: 'id2', value: 'value2', displayText: 'displayText2', disabled: true,
        },
      ],
      defaultValue: 'value1',
      actionCallback: jest.fn(),
    };

    const { getAllByRole } = render(<RadioButton {...props} />);
    const buttons = getAllByRole('button');
    fireEvent.click(buttons[1]);
    expect(props.actionCallback).not.toHaveBeenCalled();
  });

  it('renders label and tooltip correctly', () => {
    const props = {
      ...defaultProps,
      label: 'Label',
      tooltip: 'Tooltip',
      throwError: true,
      errorTooltip: 'Error Tooltip',
    };

    const { getByText } = render(<RadioButton {...props} />);
    expect(getByText('Label')).toBeInTheDocument();
    expect(getByText('Error Tooltip')).toBeInTheDocument();
  });
});
