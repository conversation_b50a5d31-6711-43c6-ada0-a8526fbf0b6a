import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import { get } from 'utils/lodash';

import DropdownWithSearch from 'components/dropDown/DropdownWithSearch';
import { dropdownActions, selector } from 'ducks/dropdowns/country';
import labelHelper from './labelHelper';

export const getUnselectedValues = (state) => {
  const { selectedValueMap, data } = selector(state).dropdown;

  // this is your unselected values
  const unselectedValues = data
    .filter((item) => {
      const countryName = get(item, 'name', '');
      return (countryName !== 'NONE' && countryName !== 'ANY');
    })
    .map((item, index) => {
      const checked = !!selectedValueMap[get(item, 'id')];
      const disabled = false;
      const countryName = get(item, 'name', '');
      return {
        original: item,
        id: get(item, 'id', index),
        name: countryName,
        checked,
        disabled,
      };
    });
  return unselectedValues;
};

export const mapStateToProps = (state, ownProps) => {
  const { t, label } = ownProps;

  const dropdownState = selector(state).dropdown;
  const { selectedValues, values } = dropdownState;
  const filteredSelectedValues = selectedValues.filter((item) => {
    const countryName = get(item, 'name', '');
    return (countryName !== 'NONE' && countryName !== 'ANY');
  });
  const dropdownLabels = labelHelper(t, filteredSelectedValues, values, 'TOOLTIP_USED_IN_EXCLUDED_CRITERIA', label || 'NONE');

  return {
    ...dropdownState,
    ...dropdownLabels,
    selectedValues: filteredSelectedValues,
    unselectedValues: getUnselectedValues(state),
  };
};

export const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    ...dropdownActions,
  }, dispatch);

  return {
    actions,
  };
};

export default withTranslation()(connect(
  mapStateToProps,
  mapDispatchToProps,
)(DropdownWithSearch));
