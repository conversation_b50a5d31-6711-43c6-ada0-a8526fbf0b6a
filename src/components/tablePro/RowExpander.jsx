import React from 'react';
import PropTypes from 'prop-types';
import i18n from 'utils/i18n';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCaretDown, faCaretRight } from '@fortawesome/pro-solid-svg-icons';
// import { faEye } from '@fortawesome/pro-regular-svg-icons';

const noop = () => undefined;

function RowExpander(props) {
  const { isExpanded, onExpand } = props;
  return (
    // eslint-disable-next-line react/destructuring-assignment
    <div className="table-row-menu-container table-cell table-expand-cell" {...props.getToggleRowExpandedProps()}>
      <div
        className="table-row-menu"
        onKeyPress={noop}
        role="button"
        // aria-label="Sort"
        tabIndex="0"
        onClick={onExpand}>
        {isExpanded ? (
          <FontAwesomeIcon
            icon={faCaretDown}
            title={i18n.t('EDIT')}
            className="pencil-icon" />
        ) : (
          <FontAwesomeIcon
            icon={faCaretRight}
            title={i18n.t('EDIT')}
            className="pencil-icon" />
        )}
      </div>
    </div>
  );
}

RowExpander.propTypes = {
  t: PropTypes.func,
  onExpand: PropTypes.func,
  isExpanded: PropTypes.bool,
  getToggleRowExpandedProps: PropTypes.func,
};

RowExpander.defaultProps = {
  t: (str) => str,
  onExpand: noop,
  isExpanded: false,
  getToggleRowExpandedProps: null,
};

export default RowExpander;
