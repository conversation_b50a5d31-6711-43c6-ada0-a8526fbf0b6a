// @flow

import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import PropTypes from 'prop-types';
import { BASE_LAYOUT } from 'config';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';
import { NavLink } from 'react-router-dom';
import * as ssoDuck from 'ducks/sso';
import * as ssoSelectors from 'ducks/sso/selectors';
import * as loginSelectors from 'ducks/login/selectors';

export class SSO extends PureComponent {
  static propTypes = {
    actions: PropTypes.shape({
      generateToken: PropTypes.func,
    }),
    t: PropTypes.func,
    accessPermissions: PropTypes.shape({}),
    authType: PropTypes.string,
    routes: PropTypes.arrayOf(PropTypes.shape()),
  };

  static defaultProps = {
    actions: {
      load: noop,
    },
    t: noop,
    accessPermissions: {},
    authType: '',
    routes: [],
  };

  render() {
    const {
      t,
      actions: {
        generateToken,
      },
      accessPermissions,
      authType,
      routes,
    } = this.props;

    return (
      <>
        {routes.map((route) => ((
          (route.perm && accessPermissions[route.perm] === 'NONE')) || authType === 'SUPPORT_ACCESS_PARTIAL'
          ? ''
          : (
            <li
              key={route.relayPath}
              className="sso-nav nav-menu-list-item"
              onClick={() => generateToken(route.relayPath)}
              onKeyPress={() => generateToken(route.relayPath)}
              aria-label={route.name}
              role="menuitem"
              tabIndex="0">
              <NavLink
                to={`${BASE_LAYOUT}/administration/loading`}>
                <span className="nav-menu-list-item-text">
                  {t(route.name)}
                </span>
              </NavLink>
            </li>
          )))}
      </>
    );
  }
}

export const mapStateToProps = (state) => ({
  loading: ssoSelectors.loadingSelector(state),
  accessPermissions: loginSelectors.accessPermissionsSelector(state),
  authType: loginSelectors.authTypeSelector(state),
});

export const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    generateToken: ssoDuck.generateToken,
  }, dispatch);

  return {
    actions,
  };
};

export default withTranslation()(connect(
  mapStateToProps,
  mapDispatchToProps,
)(SSO));
