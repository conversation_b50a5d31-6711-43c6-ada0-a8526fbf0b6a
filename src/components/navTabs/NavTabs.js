/* eslint-disable react/no-unknown-property */
import React from 'react';
import PropTypes from 'prop-types';
import { NavLink } from 'react-router-dom';
import withRouter from 'layout/withRouter';
import './NavTabs.scss';

function NavTabs(props) {
  const { tabConfiguration, isPageHeader, router } = props;
  const { location: { pathname } } = router;

  return (
    <div className="tabs">
      <div className="tabs-items">
        {tabConfiguration.map((item) => (
          <div key={`${item.to}-tab`} id={`${item.id}-tab`}>
            <NavLink to={item.to}><p className={`${pathname.includes(item.to.split('?')[0]) ? 'tabPactive' : 'tabP'}${isPageHeader ? '_XL' : ''}`} activeclassname="active-menu-item">{item.title}</p></NavLink>
            <div className={`highlighter ${pathname.includes(item.to.split('?')[0]) ? 'active' : ''}`} />
          </div>
        ))}
      </div>
      <div className="tabs-highlrighte" />
    </div>
  );
}

NavTabs.propTypes = {
  tabConfiguration: PropTypes.arrayOf(PropTypes.shape({
    id: PropTypes.string,
    to: PropTypes.string,
    title: PropTypes.string,
  })),
  location: PropTypes.shape({
    pathname: PropTypes.string,
  }),
  router: PropTypes.shape({
    location: PropTypes.shape({
      pathname: PropTypes.string,
    }),
  }),
  isPageHeader: PropTypes.bool,
};

NavTabs.defaultProps = {
  location: {},
  tabConfiguration: [],
  isPageHeader: false,
};

export default withRouter((props) => <NavTabs {...props} />);
export { NavTabs };
