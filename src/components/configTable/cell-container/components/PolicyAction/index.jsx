import React, { useState, useRef, useEffect } from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import i18n from 'utils/i18n';
  
const defaultProps = {
  value: '',
  skipTranslation: false,
};
  
function PolicyAction({ value, stateOnly, skipTranslation }) {
  const {
    action, state, zpaIpGroup, dnsGateway,
  } = value || {};
  const { name } = zpaIpGroup || {};
  const { name: dnsGatewayName } = dnsGateway || {};

  const label = (action === 'REDIR_ZPA') ? i18n.t('RESOLVE_BY_ZPA') : '';
  const valueZPA = (action === 'REDIR_ZPA') ? `${i18n.t('IP_POOL')}: ${name}` : '';
  const valueDnsGateway = (action === 'REDIR_REQ') ? `${i18n.t('DNS_GATEWAY')}: ${dnsGatewayName}` : '';
  const labelRef = useRef(null);
  const valueRef = useRef(null);
  const [isLabelEllipsisActive, setIsLabelEllipsisActive] = useState(false);
  const [isValueEllipsisActive, setIsValueEllipsisActive] = useState(false);
  
  useEffect(() => {
    if ((!labelRef.current) || (!valueRef.current)) return;
    
    if (labelRef.current.offsetWidth < labelRef.current.scrollWidth) {
      setIsLabelEllipsisActive(true);
    }
    if (valueRef.current.offsetWidth < valueRef.current.scrollWidth) {
      setIsValueEllipsisActive(true);
    }
  }, [labelRef, valueRef, label, valueZPA]);

  const ruleAction = () => {
    if (!stateOnly && action === 'ALLOW') {
      return (
        <span className="policy-table-action-allow ">
          {skipTranslation ? 'ALLOW' : i18n.t('ALLOW')}
        </span>
      );
    }

    if (!stateOnly && action === 'BLOCK') {
      return (
        <span className="policy-table-action-block ">
          {skipTranslation ? 'BLOCK' : i18n.t('BLOCK')}
        </span>
      );
    }
  
    if (!stateOnly && action === 'REDIR_REQ') {
      return (
        <>
          <span
            ref={labelRef}
            data-for="secondaryTooltip"
            data-tip={isLabelEllipsisActive ? label : ''}
            className="policy-table-action-allow ">
            {skipTranslation ? 'REDIRECT_REQUEST' : i18n.t('REDIRECT_REQUEST')}
          </span>
          <div
            ref={valueRef}
            data-for="secondaryTooltip"
            data-tip={isValueEllipsisActive ? valueDnsGateway : ''}
            className="policy-table-action-zpa-redirect">
            <span className="policy-table-action-label">{i18n.t('DNS_GATEWAY')}</span>
            <span className="policy-table-action-content">{dnsGatewayName}</span>
          </div>
        </>
      );
    }

    if (!stateOnly && action === 'REDIR_ZPA') {
      return (
        <>
          <span
            ref={labelRef}
            data-for="secondaryTooltip"
            data-tip={isLabelEllipsisActive ? label : ''}
            className="policy-table-action-allow ">
            {skipTranslation ? 'RESOLVE_BY_ZPA' : i18n.t('RESOLVE_BY_ZPA')}
          </span>
          <div
            ref={valueRef}
            data-for="secondaryTooltip"
            data-tip={isValueEllipsisActive ? valueZPA : ''}
            className="policy-table-action-zpa-redirect">
            <span className="policy-table-action-label">{i18n.t('IP_POOL')}</span>
            <span className="policy-table-action-content">{name}</span>
          </div>
        </>
      );
    }

    if (state === 'ENABLED') {
      return (
        <span className="policy-table-action-allow ">
          {skipTranslation ? 'ENABLED' : i18n.t('ENABLED')}
        </span>
      );
    }

    if (state === 'DISABLED') {
      return (
        <span className="policy-table-action-block ">
          {skipTranslation ? 'DISABLED' : i18n.t('DISABLED')}
        </span>
      );
    }

    return (
      <span className="policy-table-action-block ">
        {skipTranslation ? 'BLOCK' : i18n.t('BLOCK')}
      </span>
    );
  };

  return ruleAction();
}
  
PolicyAction.defaultProps = defaultProps;
  
PolicyAction.propTypes = {
  value: PropTypes.shape({}),
  skipTranslation: PropTypes.bool,
};
  
export default withTranslation()(PolicyAction);
