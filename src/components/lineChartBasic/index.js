/* eslint-disable no-return-assign */
/* eslint-disable react/prop-types */
// @flow

import React, { PureComponent } from 'react';
import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import GenericErrorMessage from 'components/errors/ServerError/GenericErrorMessage';

import { getFormatedNumberWithUnit } from 'utils/helpers/getFormatedData';

import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
} from 'recharts';
import './index.scss';

const strokeColor = ['#00B5BF', '#8884d8', '#82ca9d', '#8dd1e1', '#a4de6c'];

function CustomTooltip(props) {
  const {
    active, payload, label, noMoreInfo,
  } = props;
  const { t } = useTranslation();

  if (active && payload && payload.length) {
    return (
      <div id="tooltip" className="tooltip line-chart-basic-tooltip">
        <p className="label">{`${label}`}</p>
        <br />
        {payload.map((item) => {
          document.documentElement.style.setProperty('--inline-color', item.color);

          return (
            <p
              key={item.name}
              className="label inline-var-color">
              {`${item.name}: ${item.value}`}
            </p>
          );
        })}
        {/* <p className="desc">Anything you want can be displayed here.</p> */}
        {!noMoreInfo && (
          <div className="tooltip-bottom tooltip-bottom-text">
            {t('CLICK_FOR_MORE_INFO')}
          </div>
        )}
      </div>
    );
  }

  return null;
}

const getSerializedData = (data) => {
  const graphData = {};

  const dataKeys = Object.keys(data);

  dataKeys.forEach((key) => {
    const list = data[key];

    if (Array.isArray(list)) {
      list.forEach((item) => {
        if (item.orgTime) {
          graphData[item.orgTime] = { ...graphData[item.orgTime], ...item, [`${key}_META`]: { ...item } };
        }
      });
    }
  });

  const serializedData = [];
  
  Object.keys(graphData).forEach((key) => {
    serializedData.push({ ...graphData[key] });
  });

  return serializedData;
};

function CustomizedDot(props) {
  const { cx, cy } = props;
  return (
    <svg
      x={cx - 20}
      y={cy - 15}
      width={40}
      height={40}
      viewBox="0 0 200 200">
    </svg>
  );
}

export class LineChartBasic extends PureComponent {
  constructor(props) {
    super(props);

    this.serializedData = [];

    this.tooltip = null;
    this.tooltipName = null;
    this.tooltipKey = null;
    this.tooltipValue = null;
  }

  customMouseOver = (e) => {
    const { payload, dataKey } = e;
    const { height } = this.props;

    let valueToShow = payload[dataKey];

    if (payload.unit) {
      const value = payload[`${dataKey}_META`].raw;

      valueToShow = getFormatedNumberWithUnit(value, {
        type: payload.type,
      });

      if (value) {
        valueToShow += ` (exact : ${value})`;
      }
    }

    this.tooltipName.innerHTML = payload.name;
    this.tooltipKey.innerHTML = dataKey;
    this.tooltipValue.innerHTML = valueToShow;

    const x = Math.round(e.cx);
    const y = Math.round(e.cy);

    this.tooltip.style.opacity = '5';

    if (height > 500) {
      this.tooltip.style.transform = `translate(${x - 165}px, ${y - 525}px)`;
    } else {
      this.tooltip.style.transform = `translate(${x + 13}px, ${y - 340}px)`;
    }
  };

  handleOnClick = () => {
    const { clickMoreInfo } = this.props;
    const name = this.tooltipName.innerText;
    // const dataKey = this.tooltipKey.innerText;
    // const value = this.tooltipValue.innerText;
    const selected = this.serializedData.find((e) => e.name === name);
    clickMoreInfo(selected);
  };

  render() {
    const {
      data, height, clickMoreInfo, noMoreInfo,
    } = this.props;

    const yAxisProps = {
      type: 'number',
      domain: [0, 'dataMax + 25'],
      tickCount: 25,
    };

    const custHeight = height && height.length ? Number.parseInt(height, 10) : 285;
    document.documentElement.style.setProperty('--chart-height', `${custHeight}px`);

    this.serializedData = getSerializedData(data);

    if (Array.isArray(this.serializedData) && !this.serializedData.length) {
      return <GenericErrorMessage />;
    }

    if (this.serializedData[0].unit) {
      yAxisProps.label = {
        value: this.serializedData[0].unitLabel,
        angle: -90,
        position: 'insideLeft',
      };
    }

    return (
      <div className="line-chart-container inline-style-var-chart-height">
        <ResponsiveContainer>
          <LineChart
            width={600}
            onClick={clickMoreInfo}
            height={custHeight}
            data={this.serializedData}
            margin={{
              top: 5,
              right: 30,
              left: 20,
              bottom: 5,
            }}>
            <XAxis dataKey="name" />
            <YAxis {...yAxisProps} />
            <CartesianGrid stroke="#555" strokeDasharray="3 3" />
            <Tooltip
              cursor={false}
              content={<CustomTooltip clickMoreInfo={clickMoreInfo} noMoreInfo={noMoreInfo} />} />
            <Legend />
            {Object.keys(data).map((key, idx) => (
              <Line
                activeDot={{
                  onClick: (e) => this.customMouseOver(e, key),
                }}
                isAnimationActive={false}
                dataKey={key}
                dot={<CustomizedDot />}
                stroke={strokeColor[idx]}
                key={'chart' + key} />
            ))}
          </LineChart>
        </ResponsiveContainer>
        <div
          className="bubble inline-style-opacity-0"
          ref={(ref) => (this.tooltip = ref)}>
          <div className="bubble-header">
            <span>
              <div ref={(ref) => (this.tooltipName = ref)} className="value" />
            </span>
          </div>
          <div className="data">
            <div className="value">
              <span ref={(ref) => (this.tooltipKey = ref)} />
              {': '}
              <span ref={(ref) => (this.tooltipValue = ref)} />
            </div>
          </div>
          <div
            className="link"
            role="button"
            aria-label="Line Chart"
            tabIndex="0"
            onClick={this.handleOnClick}
            onKeyPress={this.handleOnClick}>
            Click for more Info
          </div>
        </div>
      </div>
    );
  }
}

LineChartBasic.propTypes = {
  clickMoreInfo: PropTypes.func,
  noMoreInfo: PropTypes.bool,
  data: PropTypes.arrayOf(PropTypes.shape({})),
  dataKeys: PropTypes.arrayOf(PropTypes.shape({})),
};

LineChartBasic.defaultProps = {
  clickMoreInfo: null,
  noMoreInfo: false,
  data: [],
  dataKeys: [],
};

export default LineChartBasic;
