// this will be hooked onto the relative state tree.
import { BASE_API_PATH } from 'config';

export const REDUCER_KEY = 'activation';

export const API_ENDPOINT = `${BASE_API_PATH}/v1/ecAdminActivateStatus`;
export const API_ACTIVATE = `${BASE_API_PATH}/v1/ecAdminActivateStatus/activate`;
export const API_FORCE_ACTIVATE = `${BASE_API_PATH}/v1/ecAdminActivateStatus/forcedActivate`;

export const DATA_ERROR = 'error';
export const DATA_LOADING = 'loading';
export const DATA = 'data';
export const TOGGLE_ACTIVATION_PANEL = 'toggleActivationPanel';
export const MY_ACTIVATE_STATUS = 'myActivateStatus';
export const CURRENTLY_EDITING = 'currentlyEditing';
export const CURRENTLY_EDITING_LENGTH = 'currentlyEditingLength';
export const USERS_QUEUED = 'usersQueued';
export const USERS_QUEUED_COUNT = 'usersQueuedCount';
export const FORCE_ACTIVATE = 'forceActivation';
export const RESOURCE_DEFAULTS = {
  [DATA_ERROR]: null, // or error object so we can process and show to user
  [DATA_LOADING]: false,
  [DATA]: {},
  [MY_ACTIVATE_STATUS]: 'NO_ACTIVATION_PENDING',
  [CURRENTLY_EDITING]: '',
  [CURRENTLY_EDITING_LENGTH]: 0,
  [USERS_QUEUED]: '',
  [USERS_QUEUED_COUNT]: 0,
  [FORCE_ACTIVATE]: false,
};

export const DEFAULTS = {
  ...RESOURCE_DEFAULTS,
};

// default state for this page/widget/component
export const DEFAULT_STATE = {
  ...DEFAULTS,
};

// For testing...to be used later
export const DUMMY_STORE_DATA = {
  [REDUCER_KEY]: {
    ...DEFAULT_STATE,
  },
};
