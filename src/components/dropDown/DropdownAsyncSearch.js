/* eslint-disable react/jsx-handler-names */
import React from 'react';
import PropTypes from 'prop-types';
import { noop, debounce } from 'utils/lodash';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSearch, faTimes } from '@fortawesome/pro-solid-svg-icons';
import { withTranslation } from 'react-i18next';

export class DropdownAsyncSearch extends React.Component {
  constructor(props) {
    super(props);
    this.debounceInput = debounce(this.performSearch, 900);
  }

  componentWillUnmount() {
    const {
      onSearchStringChange,
      searchParamName,
      actions: {
        setSearchString,
      },
    } = this.props;
    setSearchString('', searchParamName);
    onSearchStringChange('');
  }

  clearSearch = () => {
    const { onSearchStringChange } = this.props;
    onSearchStringChange('');
  };

  onChange = (event) => {
    const { onSearchStringChange } = this.props;
    const search = event.target.value;
    onSearchStringChange(search);
  };

  performSearch = () => {
    const { onPerformSearch } = this.props;
    onPerformSearch();
  };

  renderClearButton = () => {
    const { searchString } = this.props;
    if (searchString.length === 0) return null;
    return (
      <button
        onClick={this.clearSearch}
        type="button"
        className="clear-button">
        <FontAwesomeIcon icon={faTimes} />
      </button>
    );
  };

  render() {
    const { searchString } = this.props;
    const {
      tabIndex,
      placeholder,
      t,
    } = this.props;

    return (
      <div className="dropdown-search">
        <input
          className="dropdown-search-text"
          type="text"
          value={searchString}
          tabIndex={tabIndex}
          placeholder={t(placeholder)}
          onChange={this.onChange} />
        <span className="action-buttons">
          {this.renderClearButton()}
          <button
            onClick={this.performSearch}
            type="button"
            className="remove-button">
            <FontAwesomeIcon icon={faSearch} />
          </button>
        </span>
      </div>
    );
  }
}

DropdownAsyncSearch.propTypes = {
  searchString: PropTypes.string,
  searchParamName: PropTypes.string,
  actions: PropTypes.shape({
    setSearchString: PropTypes.func,
    searchData: PropTypes.func,
  }),
  onSearchStringChange: PropTypes.func,
  onPerformSearch: PropTypes.func,
  tabIndex: PropTypes.number,
  placeholder: PropTypes.string,
  t: PropTypes.func,
};

DropdownAsyncSearch.defaultProps = {
  searchString: '',
  searchParamName: 'search',
  actions: {
    setSearchString: noop,
    searchData: noop,
  },
  onSearchStringChange: null,
  onPerformSearch: null,
  tabIndex: -1,
  placeholder: 'SEARCH',
  t: (str) => str,
};

export default withTranslation()(DropdownAsyncSearch);
