import React from 'react';
import PropTypes from 'prop-types';
import { EntityDropdown } from 'components/entityDropdown';

export function TimerPickWeekdayDropDown(props) {
  const { t } = props;
  const list = [
    { id: 'MONDAY', name: t('MONDAY'), val: 2 },
    { id: 'TUESDAY', name: t('TUESDAY'), val: 3 },
    { id: 'WEDNESDAY', name: t('WEDNESDAY'), val: 4 },
    { id: 'THURSDAY', name: t('THURSDAY'), val: 5 },
    { id: 'FRIDAY', name: t('FRIDAY'), val: 6 },
    { id: 'SATURDAY', name: t('SATURDAY'), val: 7 },
    { id: 'SUNDAY', name: t('SUNDAY'), val: 1 },
  ];
  return <EntityDropdown data={list} {...props} />;
}

TimerPickWeekdayDropDown.propTypes = {
  t: PropTypes.func,
};
  
TimerPickWeekdayDropDown.defaultProps = {
  t: (str) => str,
};
export default TimerPickWeekdayDropDown;
