@import 'scss/colors.scss';
@import 'scss/mixins.scss';

.ec-root-page {
.top-right {
  position: absolute;
  &.rTooltip {
    transform: none;
    .has-info-text {
      color: rgb(36, 117, 209);
    }
    &::after {
      content: "";
      border: 5px solid transparent;
      height: 0;
      position: absolute;
      width: 0;
      border-bottom: 5px solid $white;
      bottom: 55px;
      left: 12px;
    }
    .rTooltip-container {
      max-width: 600px;
    }
  }
}

.top-left {
  .rTooltip{
    transform: none;
    .has-info-text {
      color: rgb(36, 117, 209);
    }
  }
  .rTooltip::after {
    content: "";
    border: 5px solid transparent;
    height: 0;
    position: absolute;
    width: 0;
    border-bottom: 5px solid $white;
    bottom: 55px;
    right: 12px;
    left: unset;
  }
}
}