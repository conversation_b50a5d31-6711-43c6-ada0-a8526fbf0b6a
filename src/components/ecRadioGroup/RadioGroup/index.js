import React from 'react';
import PropTypes from 'prop-types';
import { Field } from 'redux-form';
import { Trans } from 'react-i18next';
import ReactTooltip from 'react-tooltip';
import { noop } from 'utils/lodash';

import RadioButton from '../RadioButton';
import ChartButton from '../ChartButton';
import './index.scss';

function RadioGroup(props = {}) {
  const {
    name,
    options,
    label,
    disabled,
    styleClass,
    id,
    onChange,
    charts,
  } = props;
  
  return (
    <div className={`radio-button-container radio-group-container ${styleClass}`}>
      <ReactTooltip place="top" type="info" effect="solid" offset={{ left: 115 }} />
      {label && <p data-tip={`Tooltip for ${label}`} className={`${charts ? 'charts' : ''}`}><Trans>{label}</Trans></p>}
      <div className={`radio-buttons ${disabled ? 'disabled' : ''}`} id={id}>
        {options.map((item) => (
          <Field
            key={item.value}
            disabled={item.disabled || disabled}
            name={name}
            component={charts ? ChartButton : RadioButton}
            onChange={onChange}
            type="radio"
            checked={item.checked}
            value={item.value}
            label={item.label} />
        ))}
      </div>
    </div>
  );
}

RadioGroup.defaultProps = {
  label: null,
  disabled: false,
  styleClass: '',
  id: null,
  charts: false,
  onChange: noop,
};

RadioGroup.propTypes = {
  label: PropTypes.string,
  name: PropTypes.string.isRequired,
  options: PropTypes.arrayOf(PropTypes.shape({
    label: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    checked: PropTypes.bool,
  })).isRequired,
  disabled: PropTypes.bool,
  styleClass: PropTypes.string,
  id: PropTypes.string,
  charts: PropTypes.bool,
  onChange: PropTypes.func,
};

export default RadioGroup;
