@import 'scss/variables.scss';

.ec-root-page {
  .info-box-notification-box {
      box-sizing: border-box;
      display: flex;
      align-items: flex-start;
      padding: 8px;
      background: var(--semantic-color-surface-base-primary);
      border: 1px solid var(--semantic-color-border-base-primary);
      border-radius: 4px;
      flex-direction: column;
      // flex: none;
      // order: 1;
      // flex-grow: 0;
      margin-bottom: 16px;
      color: var(--semantic-color-content-base-primary);
      .info-box-notification-title{
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 13px;
        font-weight: 500;
        line-height: 20px;
        text-align: left;
        width: 100%;
        .left {
          width: fit-content;
          display: flex;
          .svg-inline--fa {
            font-style: normal;
            font-weight: 400;
            font-size: 13px;
            line-height: 20px;
            margin-right: 8px;
            color: var(--semantic-color-content-base-primary);
            &.fa-circle-info {
              height: 20px;
              width: 13px;
              font-style: normal;
              font-weight: 900;
              font-size: 13px;
              line-height: 20px;
              color: var(--semantic-color-content-status-info-primary);
            }
          }
        }
        .right {
          width: fit-content;
          cursor: pointer;
          padding: 0;
          .svg-inline--fa {
            font-style: normal;
            font-weight: 400;
            font-size: 13px;
            line-height: 20px;
            margin-right: 8px;
            color: var(--semantic-color-content-interactive-primary-default);
          }
        }
      }
      .info-box-notification-message {
        padding-left: 36px;
        line-height: 20px;
      }
  }
  .info-box-notification-box.alert {
    border-radius: 8px;
    border: 1px solid var(--semantic-color-border-status-warning-default , #FBAD7F);
    background: var(--semantic-color-surface-status-warning-default , #FFF4EE);
    .info-box-notification-title{
      .left {
        .svg-inline--fa {
          height: 20px;
          width: 13px;
          color: var(--semantic-color-content-status-warning-secondary);
        }
      }
    }
  }
}