/* eslint-disable react/jsx-handler-names */
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { PropTypes } from 'prop-types';
import { change, updateSyncErrors } from 'redux-form';
import {
  selector, perfomSearch, handleSearchStringChange,
} from 'ducks/dropdowns/azure-resource-groups';
import AdvDropdownWithAsyncSearch from 'components/dropDown/AdvDropdownWithAsyncSearch';
import { isEmpty } from 'utils/lodash';

export function AzureResourceGroupsEntity(props) {
  const dispatch = useDispatch();
  const selectedData = useSelector((state) => selector(state).dropdown);
  const {
    data, hasMorePages, searchString = '',
  } = selectedData;
  const { input, meta } = props;
  const { name, value } = input || {};
  const { form } = meta || {};

  const handleClick = (item) => {
    dispatch(change(form, name, item));
    dispatch(updateSyncErrors(form));
  };

  return (
    <AdvDropdownWithAsyncSearch
      {...props}
      styleClass="full-width margin-right-8px min-width-287px"
      items={data}
      currentItems={[]}
      hasNextPage={hasMorePages}
      defaultValue={isEmpty(value) ? [] : value}
      onPerformSearch={() => dispatch(perfomSearch())}
      onSearchStringChange={(search) => dispatch(handleSearchStringChange(search))}
      onClickCb={handleClick}
      onChange={input.onChange}
      onBlur={() => {
        return input.onBlur;
      }}
      searchString={searchString} />
  );
}

AzureResourceGroupsEntity.propTypes = {
  input: PropTypes.shape({
    onChange: PropTypes.func,
    onBlur: PropTypes.func,
  }),
  meta: PropTypes.shape({}),
};
  
AzureResourceGroupsEntity.defaultProps = {
  input: {},
  meta: {},
};

export default AzureResourceGroupsEntity;
