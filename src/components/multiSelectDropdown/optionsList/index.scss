@import 'scss/colors.scss';
@import 'scss/mixins.scss';

.ec-root-page {
.modal-body-cont {
  height: calc(100% - 80px);
  overflow-y: auto;
  overflow-x: hidden;
  padding-bottom: 40px;

  .multi-select-drop-down-modal {
    @include DisplayFlex;
    min-height: 90px;
    flex-wrap: wrap;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    padding-bottom: 10px;

    .option-item-wrapper {
      width: 100%;
      word-break: break-all;
    }

    .option-item {
      padding: 2.5px 10px;

      .modal-item {
        display: block;
        position: relative;
        padding-left: 25px;
        cursor: pointer;
        font-size: 13px;
        color: var(--semantic-color-content-interactive-primary-default);
        min-width: 200px;
        max-width: 100%;
        line-height: 20px;

        input {
          position: absolute;
          opacity: 0;
          cursor: pointer;
          height: 0;
          width: 0;

          &:checked~.checkmark {
            background-color: var(--semantic-color-content-interactive-primary-default);
           span {
              display: block;           
            }
          }
        }

        .checkmark {
          position: absolute;
          top: 2.5px;
          left: 0;
          height: 19px;
          width: 19px;
          border: 1px solid var(--semantic-color-content-base-primary);
          border-radius: 3px;

          span {
            position: absolute;
            display: none;
            left: 4px;
            top: 0;
            width: 8px;
            height: 13px;
            border: solid var(--semantic-color-content-base-primary);
            border-width: 0 2.5px 2.5px 0;
            transform: rotate(45deg);
          }
        }
      }

      &.item-disabled {
        span {
          color: $grey10;
          &.checkmark {
            border-color: $grey10;
          }
        }
      }
    }

    &.hierarchy-mode {
      display: block;
      padding: 0;

      .option-item.parent {
        background: $grey12;

        label {
          width: 100%;

          span.label-text {
            color: $grey4;
            font-weight: 700;
          }
        }
      }

      .children {
        padding-left: 20px;
      }
    }
  }
}
}