import React, { useEffect } from 'react';
import { change, getFormValues } from 'redux-form';
import { useDispatch, useSelector } from 'react-redux';
import PropTypes from 'prop-types';
import FilterDateDropdown from 'components/commonDropdown/FilterDateDropdown';
import moment from 'moment';
import { LOGS_TIME_FRAME_DATA } from 'config';
import {
  convertStartTime,
  convertEndTime,
} from 'utils/helpers';

function TimeFrame({
  meta, id, hideDropdown, handleChange,
}) {
  const { form } = meta;
  const dispatch = useDispatch();
  const options = LOGS_TIME_FRAME_DATA.map((item) => ({
    ...item,
    value: item.id,
    startDate: moment.unix((convertStartTime(item.id) / 1000)).format(),
    endDate: moment.unix((convertEndTime(item.id) / 1000)).format(),
    label: item.name,
  }));
  const { timeFrame = {} } = useSelector((state) => getFormValues(form)(state)) || {};

  const onValueChange = (input) => {
    dispatch(change(form, id, input));
    handleChange(input);
    hideDropdown();
  };

  if (timeFrame.value !== 'custom') {
    timeFrame.startDate = moment(convertStartTime(timeFrame.value)).format('MM/DD/YYYY hh:mm:ss');
    timeFrame.endDate = moment(convertEndTime(timeFrame.value)).format('MM/DD/YYYY hh:mm:ss');
    timeFrame.startTime = convertStartTime(timeFrame.value);
    timeFrame.endTime = convertEndTime(timeFrame.value);
  } else {
    timeFrame.startTime = timeFrame.startDate.unix() * 1000;
    timeFrame.endTime = timeFrame.endDate.unix() * 1000;
  }

  useEffect(() => {
    handleChange(options[8]);
  }, []);

  return (
    <div className="time-filter">
      <div className="time-filter-dropdown">
        <FilterDateDropdown
          items={options}
          hideDropdown={hideDropdown}
          dateRange={180}
          maxDateSelectable={90}
          defaultValue={options[8]}
          currentValue={timeFrame}
          setValue={onValueChange} />
      </div>
    </div>
  );
}

TimeFrame.propTypes = {
  meta: PropTypes.shape({
    form: PropTypes.string,
  }),
  id: PropTypes.string,
  hideDropdown: PropTypes.func,
  handleChange: PropTypes.func,
};

TimeFrame.defaultProps = {
  meta: {
    form: '',
  },
  id: '',
  hideDropdown: (str) => str,
  handleChange: (str) => str,
};

export default TimeFrame;
