/* eslint-disable react/jsx-handler-names */
import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { noop } from 'utils/lodash';
import { setSelectedUserId, setSelectedLocationId } from 'ducks/drillDown';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faWindowAlt } from '@fortawesome/pro-regular-svg-icons';
import { faUsers, faMapMarkerAlt } from '@fortawesome/pro-solid-svg-icons';
import { DrillDown } from '../../drillDown/index';
import './index.scss';

const UserSelectMockData = [
  { value: '1', label: '<PERSON>' },
  { value: '2', label: '<PERSON>rian <PERSON>nnister' },
  { value: '3', label: 'Daenerys Targaryen' },
  { value: '4', label: '<PERSON>' },
];

const FinancialDistrictMockData = [
  { value: '1', label: 'Winterfell' },
  { value: '2', label: 'The Eyrie' },
  { value: '3', label: 'Casterly Rock' },
  { value: '4', label: 'Pyke' },
];

class DrillDownHeader extends React.Component {
  static propTypes = {
    title: PropTypes.string,
    actions: PropTypes.shape({
      setSelectedUser: PropTypes.func, setSelectedLocation: PropTypes.func,
    }),
  };

  static defaultProps = {
    title: '',
    actions: { setSelectedUser: noop, setSelectedLocation: noop },
  };

  handleUserSetValue = (selectedValue) => {
    const { actions: { setSelectedUser } } = this.props;
    setSelectedUser(selectedValue);
  };

  handleLocationSetValue = (selectedValue) => {
    const { actions: { setSelectedLocation } } = this.props;
    setSelectedLocation(selectedValue);
  };

  render() {
    const { title } = this.props;
    return (
      <div className="drilldown-header-container">
        <div className="drilldown-name">
          <FontAwesomeIcon icon={faWindowAlt} />
          <span className="page-title">{title}</span>
        </div>
        <DrillDown setValue={this.handleUserSetValue} data={UserSelectMockData} icon={faUsers} placeholder="All Users" />
        <DrillDown setValue={this.handleLocationSetValue} data={FinancialDistrictMockData} icon={faMapMarkerAlt} placeholder="Financial District" />
      </div>
    );
  }
}

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    setSelectedUser: setSelectedUserId, setSelectedLocation: setSelectedLocationId,
  }, dispatch);

  return {
    actions,
  };
};

export default connect(
  null,
  mapDispatchToProps,
)(DrillDownHeader);
