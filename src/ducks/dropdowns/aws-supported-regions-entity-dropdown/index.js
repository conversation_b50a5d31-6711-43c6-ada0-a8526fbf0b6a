import { genericInterface } from 'utils/http';
import { createAction, loading } from 'ducks/generics';
import genericDropdown from 'ducks/generics/dropdown';
import { notifyError } from 'ducks/notification';
import {
  get, isEmpty, uniqBy,
} from 'utils/lodash';
import i18n from 'utils/i18n';
import actionTypes from './action-types';
import selector, * as selectors from './selectors';
import * as constants from './constants';

export { REDUCER_KEY } from './constants';

const actionTypeExists = Object.keys(actionTypes).reduce((hash, key) => ({
  ...hash,
  [actionTypes[key]]: true,
}), {});

export const reducer = (state = constants.DEFAULT_STATE, action = {}) => {
  // Simplifyeverything: actions provide a payload, and we merge that payload.
  if (actionTypeExists[action.type]) {
    return {
      ...state,
      ...action.payload,
    };
  }
  return state;
};
export default reducer;

const http = genericInterface(constants.API_ENDPOINT);

const boundLoading = loading(actionTypes.DATA_LOADING);
const boundError = createAction(actionTypes.DATA_LOAD_ERROR);
const boundDataLoadSuccess = createAction(actionTypes.DATA_LOAD_SUCCESS);
// const boundModalLoading = createAction(actionTypes.MODAL_LOADING);

const handleSearchStringChange = (searchString) => (dispatch) => {
  dispatch(boundLoading({ searchString }));
};

export const loader = (initializeProps, search = '') => async (dispatch, getState) => {
  const state = getState();
  if (initializeProps) {
    state.awsSupportedRegionsDropdown.pageNum = 1;
    state.awsSupportedRegionsDropdown.data = [];
    state.awsSupportedRegionsDropdown.hasMorePages = false;
    state.awsSupportedRegionsDropdown.loading = false;
    state.awsSupportedRegionsDropdown.searchString = search;
  }
  const { pageNum, pageSize, searchString } = state.awsSupportedRegionsDropdown;
  const param = searchString
    ? { page: pageNum, pageSize, search: searchString }
    : { page: pageNum, pageSize };
  dispatch(
    boundLoading({
      [constants.DATA]: [],
      ...constants.DEFAULT_STATE,
      searchString: search,
      loading: true,
    }),
  );
  try {
    const awsSupportedRegionsEntity = await http.read(null, {
      params: param,
    });
    const { data } = awsSupportedRegionsEntity || [];

    const dataWithId = data.map((x) => ({ id: x.name, name: i18n.t(x.name), ...x }));

    const combinedData = [
      ...state.awsSupportedRegionsDropdown.data,
      ...dataWithId];
    const updatedData = uniqBy(combinedData, 'id');

    dispatch(boundDataLoadSuccess({
      [constants.DATA]: updatedData,
      [constants.DATA_TABLE]: updatedData,
      [constants.DROPDOWN.PAGE_NUMBER]: pageNum + 1,
      [constants.DROPDOWN.HAS_MORE_PAGES]: data.length >= pageSize && pageSize > 0,
      searchString: search,
      loading: false,
    }));
  } catch (error) {
    const errorMsg = get(error, 'response.data.message');
    dispatch(boundError({ errorMsg, loading: false }));
    if (isEmpty(errorMsg)) dispatch(notifyError('ERROR_LOADING'));
    else dispatch(notifyError(errorMsg));
  }
};

const perfomSearch = () => (dispatch, getState) => {
  const state = getState();
  const { searchString } = state.awsSupportedRegionsDropdown;
  dispatch(loader(true, searchString));
};

const dropdownActions = genericDropdown.createActions({ actionTypes, selector, http });
export {
  selector,
  selectors,
  dropdownActions,
  handleSearchStringChange,
  perfomSearch,
};
