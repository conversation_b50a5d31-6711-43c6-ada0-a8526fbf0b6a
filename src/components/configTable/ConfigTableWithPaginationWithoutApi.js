/* eslint-disable jsx-a11y/no-noninteractive-tabindex */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable no-nested-ternary */
/* eslint-disable react/no-array-index-key */
/* eslint no-return-assign: 1 */
import React, {
  useCallback, useEffect, useMemo, useRef, useState,
} from 'react';
import PropTypes from 'prop-types';
import ReactTooltip from 'react-tooltip';
import { noop, isEmpty } from 'utils/lodash';
import { stringify } from 'utils/helpers';
import { withTranslation } from 'react-i18next';
import Loading from 'components/spinner/Loading';
import PaginationBar from 'components/PaginationBar';
  
import {
  useFlexLayout,
  useColumnOrder,
  useFilters,
  useResizeColumns,
  useSortBy,
  useTable,
  useGroupBy,
  useExpanded,
  usePagination,
} from 'react-table';
import { VariableSizeList } from 'react-window';
  
import ColumnContainer from './column-container';
import CellContainer from './cell-container';
  
import ColumnLayoutConfig from './components/column-layout-config';
import DragAndDropList from './components/drag-n-drop';
  
function ConfigTableWithPagination(props) {
  const {
    permission,
    columns,
    defaultRowHeight,
    initialState,
    onFiltersApply,
    onHandleCurrentPageChange,
    onHandleRowEdit,
    onHandleDuplicateRow,
    onHandleEnableDisableRow,
    onHandleRowDelete,
    onHandleParentRowDelete,
    onHandleRowView,
    onHandleRowViewDiff,
    onHandleRowSubEdit,
    onHandleRowDownload,
    onHandleSortBy,
    onHandleShowHideChilds,
    onHandleCheck,
    onHandleLink,
    onHandleLinkSelf,
    linkNameParameter,
    onHandleRowEllipsis,
    sortField,
    sortDirection,
    showColumnLayoutConfigurer,
    data,
    renderRowSubComponent,
    checkAll,
    tableHeight,
    maxTableHeight,
    disableTableHeaderDrag,
    hasNextPage,
    t,
    isApiPagination,
    numberOfLines,
    pageNumber,
    pageSize: pageSizeAPI,
    sizesPerPage,
    setPageSizeAPI,
    pageChangeHandler,
    loading,
  } = props;
  const mounted = useRef();
  const rowData = useMemo(() => data, [data]);
  const columnConfig = useMemo(() => columns, [columns]);
  const tooltipRef = useRef(null);
  const [tooltipContent, setTooltipContent] = useState('');
  const updatePosition = noop;
  
  const listRef = useRef({});

  useEffect(() => {
    if (!mounted.current) {
      // do componentDidMount logic
      mounted.current = true;
    } else {
      // do componentDidUpdate logic
      ReactTooltip.rebuild();
    }
  });
    
  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    rows,
    prepareRow,
    toggleHideAllColumns,
    setColumnOrder,
    setAllFilters,
    allColumns,
    totalColumnsWidth,
    resetResizing,
    // Instead of using 'rows', we'll use page,
    // which has only the rows for the active page
    page,
    // The rest of these things are super handy, too ;)
    canPreviousPage,
    canNextPage,
    pageOptions,
    pageCount,
    gotoPage,
    nextPage,
    previousPage,
    setPageSize: setPageSizeTable,
    state: {
      filters, selectedRowIds, expanded, pageIndex, pageSize,
    },
  } = useTable(
    {
      columns: columnConfig,
      data: rowData,
      initialState,
      autoResetPage: false,
      autoResetExpanded: false,
      autoResetGroupBy: false,
      autoResetSelectedRows: false,
      autoResetSortBy: false,
      autoResetFilters: false,
      autoResetRowState: false,
      ...isApiPagination && ({
        manualPagination: true,
        pageCount: Math.ceil(numberOfLines / pageSizeAPI),
      }),
    },
    useFilters,
    useColumnOrder,
    useResizeColumns,
    useFlexLayout,
    useGroupBy,
    useSortBy,
    useExpanded,
    usePagination,
  );
  
  useEffect(() => {
    onFiltersApply(filters, setAllFilters);
  }, [filters]);

  useEffect(() => {
    if (isApiPagination) pageChangeHandler(pageIndex + 1);
  }, [pageIndex]);

  useEffect(() => {
    gotoPage(pageNumber - 1);
  }, [pageNumber]);
  
  useEffect(() => {
    onHandleCurrentPageChange(page.map((x) => x && x.original));
  }, [stringify(page)]);

  const setPageSize = async (size) => {
    if (isApiPagination) {
      await setPageSizeAPI(size);
      await gotoPage(0);
    } else {
      await setPageSizeTable(size);
    }
  };
  
  // const itemCount = hasNextPage ? rows.length + 1 : rows.length;
  // If there are more rows to be loaded then add an extra row to hold a loading indicator.
  // const itemCount = hasNextPage ? rows.length + 1 : rows.length;
  const itemCount = page.length;
  
  // Every row is loaded except for our loading indicator row.
  const isItemLoaded = (index) => {
    return !hasNextPage || index < rows.length;
  };
  
  const [isResetLayout, setIsResetLayout] = useState(false);

  const getRowHeights = () => rowData.reduce((x, item, i) => {
    if (item.rowHeight) {
      // eslint-disable-next-line no-param-reassign
      x[i] = item.rowHeight;
    } else {
      // eslint-disable-next-line no-param-reassign
      x[i] = defaultRowHeight || 50; // height for each row
    }
    return x;
  }, {});
  
  const [rowSizes, setrowSizes] = useState(() => getRowHeights());

  useEffect(() => {
    if (isResetLayout) {
      setColumnOrder(columns.map((col) => col.id));
      toggleHideAllColumns(false);
      setIsResetLayout(false);
    }
  }, [isResetLayout]);

  useEffect(() => {
    if (rowData.length > 0) {
      return setrowSizes(() => getRowHeights());
    }
    setrowSizes(() => getRowHeights());
  }, [itemCount]);

  useEffect(() => {
    gotoPage(0);
  }, [rowData]);
  
  const getRowHeight = (index) => {
    return rowSizes[index] || 50;
  };
  
  const setRowHeight = (i, size) => {
    if (listRef.current) {
      listRef.current.resetAfterIndex(i);
    }
    setrowSizes((prevRowSizes) => ({
      ...prevRowSizes,
      [i]: prevRowSizes[i] === 50 ? size : 50,
    }));
  };
  
  const [isDragDisabled, setIsDragDisabled] = useState(false);
    
  const onDragEnter = (evt) => {
    const className = '' + evt.target.className || '';
    if (className.includes('resizer')) {
      setIsDragDisabled(true);
    }
  };
  
  const onDragLeave = () => {
    if (isDragDisabled) {
      setIsDragDisabled(false);
    }
  };
  
  const renderColumnSection = (column, idx, provided) => {
    const { disableReordering } = column;
  
    return (
      <div
        key={idx}
        className="column-cell-container"
        {...column.getHeaderProps()}>
        <div
          className={`cell ${disableReordering ? 'disable-ordering' : ''}`}
          // aria-label={t(column.Header) || 'Drag'}
          // role="button"
          // tabIndex="0"
          ref={provided.innerRef}
          key={column.id}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          onMouseDown={onDragEnter}
          onMouseUp={onDragLeave}>
          {column.render((columnProps) => (
            <ColumnContainer
              {...columnProps}
              checkAll={checkAll}
              setAllFilters={setAllFilters}
              sortField={sortField}
              sortDirection={sortDirection}
              onHandleSortBy={onHandleSortBy}
              onHandleCheck={onHandleCheck}
              filters={filters} />
          ))}
        </div>
      </div>
    );
  };
  
  const onNewListOrder = (result) => {
    setColumnOrder(result);
  };
  
  const RenderRow = useCallback(
    ({ index, style }) => {
      const row = page[index];
      if (!row) return null;
        
      if (row) prepareRow(row);
      if (!isItemLoaded(index) && hasNextPage) {
        return (
          <div style={style} className={`row ${index % 2 ? 'ListItemOdd' : 'ListItemEven'}`}>
            <Loading />
          </div>
        );
      }
      return (
        <>
          {!row.original.parentId && (
          // USING SPAN to be able to color the ParentRows different from  Child Rows
            <span
              data-tip={row.original && row.original.tooltip}
              className={`row ${index % 2 ? 'ListItemOdd' : 'ListItemEven'}`}
              {...row.getRowProps({ style })}>
              {row.cells.map((cell, idx) => {
                return (
                  <div key={idx} {...cell.getCellProps({ className: 'cell' })}>
                    {cell.render((cellProps) => (
                      <CellContainer
                        {...cellProps}
                        pageIndex={pageIndex}
                        pageSize={isApiPagination ? pageSizeAPI : pageSize}
                        permission={permission}
                        tooltipRef={tooltipRef}
                        tooltipContent={tooltipContent}
                        setTooltipContent={setTooltipContent}
                        updatePosition={updatePosition}
                        onHandleRowEdit={onHandleRowEdit}
                        onHandleDuplicateRow={onHandleDuplicateRow}
                        onHandleEnableDisableRow={onHandleEnableDisableRow}
                        onHandleRowView={onHandleRowView}
                        onHandleRowViewDiff={onHandleRowViewDiff}
                        onHandleRowSubEdit={onHandleRowSubEdit}
                        onHandleRowDelete={onHandleParentRowDelete || onHandleRowDelete}
                        onHandleRowDownload={onHandleRowDownload}
                        onHandleShowHideChilds={onHandleShowHideChilds}
                        onHandleCheck={onHandleCheck}
                        onHandleLink={onHandleLink}
                        onHandleLinkSelf={onHandleLinkSelf}
                        linkNameParameter={linkNameParameter}
                        onHandleRowEllipsis={onHandleRowEllipsis}
                        setSize={setRowHeight}
                        renderIndex={index} />
                    ))}
                  </div>
                );
              })}
  
              {/*
                If the row is in an expanded state, render a row with a
                column that fills the entire length of the table.
              */}
              {/* <button onClick={() => toggleSize(index)}>Toggle Size</button> */}
              {row.isExpanded ? (
                <div className="row expanded-row">
                  {/*
                    Inside it, call our renderRowSubComponent function. In reality,
                    you could pass whatever you want as props to
                    a component like this, including the entire
                    table instance. But for this example, we'll just
                    pass the row
                  */}
                  {renderRowSubComponent({ row })}
                </div>
              ) : null}
            </span>
          )}
  
          {Boolean(row.original.parentId) && (
            <div
              data-tip={row.original && row.original.tooltip}
              className="child-row"
              {...row.getRowProps({ style })}>
              {row.cells.map((cell, idx) => {
                return (
                  <div key={idx} {...cell.getCellProps({ className: 'cell' })}>
                    {cell.render((cellProps) => (
                      <CellContainer
                        {...cellProps}
                        pageIndex={pageIndex}
                        pageSize={isApiPagination ? pageSizeAPI : pageSize}
                        tooltipRef={tooltipRef}
                        tooltipContent={tooltipContent}
                        setTooltipContent={setTooltipContent}
                        updatePosition={updatePosition}
                        onHandleRowEdit={onHandleRowEdit}
                        onHandleDuplicateRow={onHandleDuplicateRow}
                        onHandleEnableDisableRow={onHandleEnableDisableRow}
                        onHandleRowView={onHandleRowView}
                        onHandleRowViewDiff={onHandleRowViewDiff}
                        onHandleRowSubEdit={onHandleRowSubEdit}
                        onHandleRowDelete={onHandleRowDelete}
                        onHandleRowDownload={onHandleRowDownload}
                        onHandleShowHideChilds={onHandleShowHideChilds}
                        onHandleCheck={onHandleCheck}
                        onHandleLink={onHandleLink}
                        onHandleLinkSelf={onHandleLinkSelf}
                        linkNameParameter={linkNameParameter}
                        onHandleRowEllipsis={onHandleRowEllipsis}
                        setSize={setRowHeight}
                        renderIndex={index} />
                    ))}
                  </div>
                );
              })}
  
              {/*
                If the row is in an expanded state, render a row with a
                column that fills the entire length of the table.
              */}
              {/* <button onClick={() => toggleSize(index)}>Toggle Size</button> */}
              {row.isExpanded ? (
                <div className="row expanded-row">
                  {/*
                    Inside it, call our renderRowSubComponent function. In reality,
                    you could pass whatever you want as props to
                    a component like this, including the entire
                    table instance. But for this example, we'll just
                    pass the row
                  */}
                  {renderRowSubComponent({ row })}
                </div>
              ) : null}
            </div>
          )}
        </>
      );
    },
    [prepareRow, page, selectedRowIds, expanded],
  );
  
  const virtualizedStyle = {
    overflow: 'overlay',
    width: '100%',
    maxHeight: maxTableHeight || '60vh',
    height: 'auto',
  };
  
  return (
    <div className="config-table-container">
      <article className="table-container" {...getTableProps()}>
        {showColumnLayoutConfigurer && (
          <ColumnLayoutConfig
            allColumns={allColumns}
            setColumnOrder={setColumnOrder}
            setIsResetLayout={setIsResetLayout}
            onHandleCheck={onHandleCheck}
            resetLayout={resetResizing} />
        )}
        <div className="head">
          {headerGroups.map((headerGroup, idx) => (
            <DragAndDropList
              key={idx}
              list={headerGroup.headers}
              listKeyName="id"
              droppableId="column-droppable"
              direction="horizontal"
              listContainerClass="row"
              listContainerProps={headerGroup.getHeaderGroupProps()}
              renderListItem={renderColumnSection}
              onNewListOrder={onNewListOrder}
              isDragDisabled={disableTableHeaderDrag || isDragDisabled} />
          ))}
        </div>
        { loading ? <Loading loading={loading} />
          : (
            <div className="content" {...getTableBodyProps()}>
              {
                (rows.length === 0)
                  ? (
                    <div role="row" className="row no-items-message">
                      <div role="cell">
                        {t('NO_MATCHING_ITEMS_FOUND')}
                      </div>
                    </div>
                  )
                  : (
                    <>
                      {!isEmpty(rowSizes) && (
                        <VariableSizeList
                          style={virtualizedStyle}
                          itemCount={itemCount}
                          itemSize={getRowHeight}
                          overscanCount={13}
                          height={tableHeight || 600}
                          ref={listRef}
                          rowSizes={rowSizes}
                          className="list-container"
                          width={totalColumnsWidth}>
                          {RenderRow}
                        </VariableSizeList>
                      )}
                    </>
                  )
              }
            </div>
          )}
      </article>
      <PaginationBar
        numberOfLines={isApiPagination ? numberOfLines : rowData && rowData.length}
        canPreviousPage={canPreviousPage}
        canNextPage={canNextPage}
        gotoPage={gotoPage}
        previousPage={previousPage}
        nextPage={nextPage}
        pageCount={pageCount}
        pageSize={isApiPagination ? pageSizeAPI : pageSize}
        setPageSize={setPageSize}
        pageOptions={pageOptions}
        sizesPerPage={sizesPerPage}
        pageIndex={pageIndex} />
      <ReactTooltip
        place="top"
        type="light"
        offset={{ top: -10 }}
        effect="solid"
        border
        multiline
        className="cc-group-tooltip-container"
        borderColor="#939393" />
      <ReactTooltip
        id="secondaryTooltip"
        place="right"
        type="light"
        effect="solid"
        border
        multiline
        isCapture
        className="cc-group-tooltip-container"
        borderColor="#939393" />
  
    </div>
  );
}
  
ConfigTableWithPagination.propTypes = {
  checkAll: PropTypes.bool,
  columns: PropTypes.arrayOf(Object),
  data: PropTypes.arrayOf(Object),
  defaultRowHeight: PropTypes.number,
  disableTableHeaderDrag: PropTypes.bool,
  handlePageIndex: PropTypes.func,
  handlePageSize: PropTypes.func,
  hasEditColumn: PropTypes.bool,
  hasIndexColumn: PropTypes.bool,
  hasNextPage: PropTypes.bool,
  initialState: PropTypes.objectOf(Object),
  isApiPagination: PropTypes.bool,
  loading: PropTypes.bool,
  loadMore: PropTypes.func,
  maxTableHeight: PropTypes.string,
  moreItemsLoading: PropTypes.bool,
  numberOfLines: PropTypes.number,
  onFiltersApply: PropTypes.func,
  onHandleCheck: PropTypes.func,
  onHandleCurrentPageChange: PropTypes.func,
  onHandleDuplicateRow: PropTypes.func,
  onHandleEnableDisableRow: PropTypes.func,
  onHandleLink: PropTypes.func,
  onHandleLinkSelf: PropTypes.func,
  linkNameParameter: PropTypes.string,
  onHandleRowDelete: PropTypes.func,
  onHandleParentRowDelete: PropTypes.func,
  onHandleRowDownload: PropTypes.func,
  onHandleRowEdit: PropTypes.func,
  onHandleRowEllipsis: PropTypes.func,
  onHandleRowSubEdit: PropTypes.func,
  onHandleRowView: PropTypes.func,
  onHandleRowViewDiff: PropTypes.func,
  onHandleShowHideChilds: PropTypes.func,
  onHandleSortBy: PropTypes.func,
  pageChangeHandler: PropTypes.func,
  pageIndex: PropTypes.number,
  pageNumber: PropTypes.number,
  pageSize: PropTypes.number,
  pageSizeAPI: PropTypes.number,
  pagination: PropTypes.bool,
  permission: PropTypes.string,
  renderRowSubComponent: PropTypes.func,
  setPageSizeAPI: PropTypes.func,
  showColumnLayoutConfigurer: PropTypes.bool,
  sizesPerPage: PropTypes.arrayOf(PropTypes.number),
  sortDirection: PropTypes.string,
  sortField: PropTypes.string,
  t: PropTypes.func,
  tableHeight: PropTypes.number,
};
  
ConfigTableWithPagination.defaultProps = {
  checkAll: false,
  columns: [{}],
  data: [{}],
  defaultRowHeight: 50,
  disableTableHeaderDrag: false,
  handlePageIndex: null,
  handlePageSize: null,
  hasEditColumn: false,
  hasIndexColumn: true,
  hasNextPage: false,
  initialState: {},
  isApiPagination: false,
  loading: false,
  loadMore: noop,
  maxTableHeight: '',
  moreItemsLoading: false,
  numberOfLines: 0,
  onFiltersApply: noop,
  onHandleCheck: noop,
  onHandleCurrentPageChange: noop,
  onHandleDuplicateRow: noop,
  onHandleEnableDisableRow: noop,
  onHandleLinkSelf: noop,
  linkNameParameter: '',
  onHandleRowDelete: noop,
  onHandleParentRowDelete: noop,
  onHandleRowDownload: noop,
  onHandleRowEdit: noop,
  onHandleRowEllipsis: noop,
  onHandleRowSubEdit: noop,
  onHandleRowView: noop,
  onHandleRowViewDiff: noop,
  onHandleShowHideChilds: noop,
  onHandleSortBy: null,
  pageChangeHandler: null,
  pageIndex: 0,
  pageNumber: 0,
  pageSize: 0,
  pageSizeAPI: 0,
  pagination: false,
  permission: 'NONE',
  renderRowSubComponent: noop,
  setPageSizeAPI: null,
  showColumnLayoutConfigurer: false,
  sizesPerPage: [10, 20, 30, 40, 50],
  sortDirection: '',
  sortField: '',
  t: (str) => str,
  tableHeight: 0,
};
  
export default withTranslation()(ConfigTableWithPagination);
