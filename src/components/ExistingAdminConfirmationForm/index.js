// @flow
import React from 'react';
import Loading from 'components/spinner/Loading';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';

import './index.scss';
 
export function ExistingAdminConfirmationForm({
  valid,
  t,
  modalLoading,
  handleCancel,
  handleSubmit,
  addAdmin,
}) {
  const onSubmit = (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    handleSubmit(addAdmin);
    handleCancel(false);
  };

  return (
    <form className="add-custom-app-form confirm-existing-admin-form">
      <Loading loading={modalLoading} />
      <div className="form-sections-container">
        <div className="form-section">
          <p className="modal-text">
            {t('ADMIN_LOGIN_NAME_ALREADY_EXISTS_MESSAGE')}
          </p>
        </div>
      </div>
      <div className="dialog-footer">
        <div className="dialog-footer-left">
          <button type="submit" onClick={(e) => onSubmit(e)} className={`submit ${!valid ? 'disabled' : ''}`}>{t('CONFIRM')}</button>
          <button type="button" onClick={() => handleCancel(false)} className="cancel">{t('CANCEL')}</button>
        </div>
      </div>
    </form>
  );
}

ExistingAdminConfirmationForm.propTypes = {
  handleCancel: PropTypes.func,
  handleSubmit: PropTypes.func,
  t: PropTypes.func,
  modalLoading: PropTypes.bool,
  valid: PropTypes.bool,
  addAdmin: PropTypes.bool,
};

ExistingAdminConfirmationForm.defaultProps = {
  handleCancel: noop,
  handleSubmit: noop,
  t: (str) => str,
  modalLoading: false,
  valid: true,
  addAdmin: true,
};

export default (withTranslation()(ExistingAdminConfirmationForm));
