// @flow

import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';

function OutOfUppercase(props) {
  const { number, t } = props;

  return (
    <span className="out-of uppercase">
      {t('OUT_OF')}
      {` ${number}`}
    </span>
  );
}

OutOfUppercase.propTypes = {
  number: PropTypes.number,
  t: PropTypes.func,
};

OutOfUppercase.defaultProps = {
  number: 100,
  t: (str) => str,
};

export default withTranslation()(OutOfUppercase);
export { OutOfUppercase };
