/* eslint-disable jsx-a11y/label-has-for */
import React, { useRef } from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import i18n from 'utils/i18n';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faClock, faCheckCircle, faTimesCircle } from '@fortawesome/pro-solid-svg-icons';
  
const defaultProps = {
  value: '',
  skipTranslation: false,
};
  
function UpgradeStatus({ value, skipTranslation }) {
  const {
    upgradeStatus, id, scheduled, errorMessage = 'One or more Cloud Connector Upgrades Failed', isZTG,
  } = value;
  const linkRef = useRef(null);
  const getErrorMessage = (errorCode) => {
    if (errorCode === -1) return 'ONE_OR_MORE_CC_FAILED';
    if (errorCode === 2) return 'EXCEEDS_UPGRADE_WINDOW';
    if (errorCode === 3) return 'DOWNLOAD_ERROR';
    return 'FAILED_OTHER';
  };
  
  const renderContent = () => (
    <label htmlFor={id} className="child-checkbox-label">
      { (upgradeStatus === 0) && (
        <>
          <FontAwesomeIcon
            icon={faClock}
            title="Scheduled"
            // onClick={() => onHandleShowHideChilds(id)}
            className="scheduled-icon" />
          {skipTranslation ? scheduled : i18n.t('SCHEDULED')}
        </>
      )}
      {(upgradeStatus === 1) && (
        <>
          <FontAwesomeIcon
            icon={faCheckCircle}
            title="Success"
            // onClick={() => onHandleShowHideChilds(id)}
            className="success-icon" />
          {skipTranslation ? scheduled : i18n.t('NO_PENDING_UPGRADES')}
        </>
      )}
      {(upgradeStatus > 1 || upgradeStatus === -1) && (
        <div className="failed-scheduled" data-tip={skipTranslation ? errorMessage : i18n.t(getErrorMessage(upgradeStatus))}>
          <FontAwesomeIcon
            icon={faTimesCircle}
            // title={`Failed ${id}`}
            // onClick={() => onHandleShowHideChilds(id)}
            className="failed-icon" />
          {skipTranslation ? scheduled : i18n.t('FAILED')}
        </div>
      )}
    </label>
  );
  
  return (
    <div
      ref={linkRef}
      // aria-label="Upgrade Status"
      onKeyPress={null}
      role="button"
      tabIndex="0">
      {isZTG ? <label htmlFor={id} className="child-checkbox-label">{i18n.t('NA')}</label> : renderContent()}
    </div>
  );
}
  
UpgradeStatus.defaultProps = defaultProps;
  
UpgradeStatus.propTypes = {
  value: PropTypes.shape({
    upgradeStatus: PropTypes.number,
    id: PropTypes.string,
    isZTG: PropTypes.bool,
    scheduled: PropTypes.string,
    errorMessage: PropTypes.string,
  }),
  skipTranslation: PropTypes.bool,
};
  
export default withTranslation()(UpgradeStatus);
