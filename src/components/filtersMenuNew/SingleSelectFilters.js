import PropTypes from 'prop-types';
import { noop, isEqual } from 'utils/lodash';

// import Localized from 'utils/i18n';
import { withTranslation } from 'react-i18next';

import React from 'react';

class SingleDropdown extends React.Component {
  constructor(props) {
    super(props);
    const { defaultValue } = props;
    this.state = {
      search: '',
      selectedValue: defaultValue,
      openList: false,
    };
    this.wrapperRef = null;
  }

  componentDidMount() {
    document.addEventListener('mousedown', this.handleMousedown);
  }

  componentDidUpdate(prevProps) {
    const { defaultValue } = this.props;
    if (prevProps.defaultValue !== defaultValue) {
      // eslint-disable-next-line
      this.setState({ selectedValue: defaultValue });
    }
  }

  componentWillUnmount() {
    document.removeEventListener('mousedown', this.handleMousedown);
  }

  setWrapperRef = (node) => {
    this.wrapperRef = node;
  };

  handleChangeValue = (item) => {
    if (item.isParent) {
      return;
    }
    const { setValue } = this.props;
    this.setState({
      selectedValue: item,
      openList: false,
    });
    setValue(item);
  };

  handleMousedown = (e) => {
    const containRef = this.wrapperRef.contains(e.target);
    if (!containRef) {
      this.setState({ openList: false });
    }
  };

  handleOpenList = () => {
    const { readOnlyDropdown } = this.props;
    const { openList } = this.state;
    if (!readOnlyDropdown) {
      this.setState({ openList: !openList });
    }
  };

  handleChangeInput = (event) => {
    this.setState({ search: event.target.value });
  };

  render() {
    // eslint-disable-next-line
    let { items, numericLabelSort, hasLocalSort } = this.props;
    if (hasLocalSort) {
      items.sort((a, b) => {
        if (numericLabelSort) {
          return a.label.localeCompare(b.label, undefined, { numeric: true });
        }
        return a.label.localeCompare(b.label);
      });
    }
    const {
      hasSearch, t, parentItems, showParent, readOnlyDropdown,
      showLabelInSelectedValue, labelSelectedValue, removeFilterCallback,
    } = this.props;
    const { search, selectedValue, openList } = this.state;
    if (hasSearch && search.trim().length) {
      items = items.filter(
        (item) => t(item.value).toLowerCase().indexOf(search.trim().toLowerCase()) !== -1,
      );
    }
    let dropdownOptions = (
      <div className="dropdown-list-content">
        {items.map((item) => (
          <div key={item.id} className={`dropdown-list-item ${isEqual(item.value, selectedValue.value) ? 'dropdown-selected-value' : ''}`}>
            <button onClick={() => { this.handleChangeValue(item); }} type="button"><span>{t(item.label)}</span></button>
          </div>
        ))}
      </div>
    );
    if (showParent) {
      const elementsList = [];
      parentItems.map((parent) => {
        elementsList.push(parent);
        items.map((item) => {
          if (item.parent === parent.value) {
            elementsList.push(item);
          }
          return item;
        });
        return parent;
      });
      dropdownOptions = (
        <div className="dropdown-list-content">
          {elementsList.map((item) => (
            <div key={item.id} className={`dropdown-list-item ${isEqual(item.isParent, true) ? 'dropdown-list-item-parent' : ''} ${isEqual(item.value, selectedValue.value) ? 'dropdown-selected-value' : ''}`}>
              <button onClick={() => { this.handleChangeValue(item); }} type="button"><span>{t(item.label)}</span></button>
            </div>
          ))}
        </div>
      );
    }

    return (
      <div>
        <div className="drop-down-container" ref={this.setWrapperRef}>
          <button
            className={`drop-down-selected-value ${readOnlyDropdown ? 'dropdown-read-only' : ''}`}
            onClick={this.handleOpenList}
            type="button">
            <div className="dropdown-selected-label">
              {showLabelInSelectedValue && (
                <span className="label-selected-value">
                  {t(labelSelectedValue)}
                  {' '}
&nbsp; = &nbsp;
                  {' '}
                </span>
              )}
              <span>{t(selectedValue.label)}</span>
            </div>
            {/*! readOnlyDropdown && (
              <span
                className={`dropdown-icon fa ${openList ? 'fa-caret-up' : 'fa-caret-down'}`}
              ></span>
            ) */}
          </button>
          <div
            className="far fa-times-circle remove-filter-icon"
            role="button"
            tabIndex={0}
            aria-label="remove-filter"
            onClick={removeFilterCallback}
            onKeyPress={removeFilterCallback}>
          </div>
          <div className="dropdown-box">
            <div className={`drop-down-list ${openList ? 'open' : ''}`}>
              {hasSearch && (
                <div className="dropdown-list-header">
                  <div className="dropdown-search">
                    <input
                      className="dropdown-input-search"
                      type="text"
                      value={search}
                      placeholder={t('DROPDOWN_SEARCH')}
                      onChange={this.handleChangeInput} />
                    <span className="search-icon fa fa-search"></span>
                  </div>
                </div>
              )}
              {dropdownOptions}
            </div>
          </div>
        </div>
      </div>
    );
  }
}

SingleDropdown.propTypes = {
  hasSearch: PropTypes.bool,
  hasLocalSort: PropTypes.bool,
  t: PropTypes.func,
  items: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.oneOfType([
        PropTypes.string,
        PropTypes.number,
        PropTypes.arrayOf(PropTypes.string),
        PropTypes.shape({}),
      ]),
      label: PropTypes.string,
    }),
  ),
  numericLabelSort: PropTypes.bool,
  parentItems: PropTypes.arrayOf(PropTypes.shape({})),
  readOnlyDropdown: PropTypes.bool,
  setValue: PropTypes.func,
  selectedValue: PropTypes.shape({
    value: PropTypes.oneOfType([
      PropTypes.number,
      PropTypes.string,
      PropTypes.arrayOf(PropTypes.string),
      PropTypes.shape({}),
    ]),
    label: PropTypes.string,
  }),
  showParent: PropTypes.bool,
  defaultValue: PropTypes.shape({
    value: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.number,
      PropTypes.arrayOf(PropTypes.string),
      PropTypes.shape({}),
    ]),
    label: PropTypes.string,
  }),
  labelSelectedValue: PropTypes.string,
  showLabelInSelectedValue: PropTypes.bool,
  removeFilterCallback: PropTypes.func,
};

SingleDropdown.defaultProps = {
  hasSearch: false,
  hasLocalSort: true,
  t: (str) => str,
  items: [],
  numericLabelSort: false,
  parentItems: [],
  readOnlyDropdown: false,
  selectedValue: {},
  setValue: noop,
  showLabelInSelectedValue: false,
  showParent: false,
  defaultValue: {},
  removeFilterCallback: noop,
};

export default withTranslation()(SingleDropdown);
