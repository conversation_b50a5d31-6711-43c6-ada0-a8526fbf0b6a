@import "scss/colors.scss";
@import 'scss/mixins.scss';

.ec-root-page {
.map-legend {
    height: 30px;
    bottom: 15px;
    padding: 5px 10px;
    border-radius: 5px;
    box-shadow: 0 1px 6px 0 $grey18;
    border: 1px solid $grey8;
    background-color: $grey17;
    position: absolute;
    font-size: 11px;
    color: $grey4;
    .container {
      line-height: 20px;
      height: 100%;
      @include DisplayFlex;
      align-items: center;
      background: inherit;
      .statuses {
        @include DisplayFlex;
        margin-left: 3px;
        div {
          margin-left: 5px;
          @include DisplayFlex;
          align-items: center;
          span {
            margin-left: 5px;
          }
          div.status-line {
            height: 2px;
            width: 12px;
            border-radius: 3px;
            &.good {
              background-color: $green2;
            }
            &.okay {
              background-color: $orange3;
            }
            &.poor {
              background-color: $red3;
            }
          }
        }
      }
      .separatore {
        height: 100%;
        border-right: 1px solid var(--semantic-color-border-base-primary);
        margin: 0 10px;
      }
    }
  }
}