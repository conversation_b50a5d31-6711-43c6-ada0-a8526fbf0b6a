import {
  createAction,
  loading,
  loadSuccess,
} from 'ducks/generics';
import { destroy } from 'redux-form';
import { omit, get } from 'utils/lodash';
import { genericInterface } from 'utils/http';
import { notify, notifyError } from 'ducks/notification';
import * as constants from './constants';
import actionTypes from './action-types';
import { checkActivation } from '../activation';

export { REDUCER_KEY } from './constants';

const actionTypeExists = Object.keys(actionTypes).reduce((hash, key) => ({
  ...hash,
  [actionTypes[key]]: true,
}), {});

export const reducer = (state = constants.DEFAULT_STATE, action = {}) => {
  if (actionTypeExists[action.type]) {
    return {
      ...state,
      ...action.payload,
    };
  }
  return state;
};
export default reducer;

const boundLoading = loading(actionTypes.DATA_LOADING);
const boundSuccess = loadSuccess(actionTypes.DATA_LOAD_SUCCESS);
const dataChanged = createAction(actionTypes.DATA_CHANGED);
const boundError = createAction(actionTypes.DATA_LOAD_ERROR);
const boundModalLoading = createAction(actionTypes.MODAL_LOADING);

export const loadAWSData = () => (dispatch) => {
  dispatch(boundLoading({ loading: true }));
  const cpAWSApi = genericInterface(constants.API_ENDPOINT);
  return cpAWSApi.read(null, {
    params: { cloudProvider: 'AWS' },
  })
    .then((response) => {
      dispatch(dataChanged({
        loading: false,
        [constants.DATA_CP_TABLE]: response.data,
      }));
    })
    .catch((error) => {
      const errorMsg = get(error, 'response.data.message', 'There is a problem with loading cloud providers, Please try again later.');
      dispatch(boundError(errorMsg));
    });
};

export const toggleDeleteForm = (toggle, data) => (dispatch) => {
  dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
  dispatch(dataChanged({
    [constants.SHOW_DELETE_FORM]: toggle,
    [constants.SELECTED_ROW_ID]: data ? data.id : null,
  }));
};

export const toggleViewModal = (toggle, appData) => (dispatch) => {
  if (typeof appData !== 'undefined') {
    dispatch(dataChanged({
      [constants.SHOW_VIEW_FORM]: toggle,
      [constants.APP_DATA]: appData,
    }));
  } else {
    dispatch(dataChanged({
      [constants.SHOW_VIEW_FORM]: toggle,
    }));
  }
};

export const deleteAWSData = (rowData) => (dispatch) => {
  dispatch(boundModalLoading({ [constants.MODAL_LOADING]: true }));
  const rowId = rowData;
  const deleteCpAwsAPI = genericInterface(constants.API_DELETE_ENDPOINT(rowId));

  return deleteCpAwsAPI.del()
    .then(() => {
      dispatch(toggleDeleteForm(false));
      dispatch(destroy('deleteConfirmationForm'));
      dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
      dispatch(notify('SUCCESSFULLY_DELETED'));
      dispatch(loadAWSData());
      dispatch(checkActivation());
    })
    .catch((error) => {
      const errorMsg = get(error, 'response.data.message', 'There is a problem with deleting cloud provider, Please try again later.');
      dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
      dispatch(boundError(errorMsg));
      dispatch(notifyError(errorMsg));
    });
};

export const getCloudProvidersData = () => (dispatch) => {
  dispatch(boundSuccess(constants.DATA));
};

export const toggleForm = (toggle, appData = constants.DEFAULT_FORM_DATA, title) => (dispatch) => {
  dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
  dispatch(dataChanged({
    [constants.SHOW_FORM]: toggle,
    [constants.APP_DATA]: appData,
    [constants.FORM_TITLE]: title,
  }));
};

const beforeSave = (values) => {
  let updatedValue = values;

  updatedValue = omit(updatedValue, 'type', 'name', 'active', 'lastModTime', 'lastModUid');

  const updatedEcValues = {
    ...updatedValue,
    cloudProvider: 'AWS',
  };

  return updatedEcValues;
};

export const saveForm = (values) => (dispatch) => {
  dispatch(boundModalLoading({ [constants.MODAL_LOADING]: true }));

  let saveCloudProviderApi = genericInterface(constants.API_ENDPOINT);
  let apiEndPoint = saveCloudProviderApi.create;

  if (values.id) {
    saveCloudProviderApi = genericInterface(constants.API_PUT_ENDPOINT);
    apiEndPoint = saveCloudProviderApi.update;
  }

  const updatedEcValues = beforeSave(values);

  return apiEndPoint(updatedEcValues, {})
    .then(() => {
      dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
      dispatch(toggleForm(false, '', {}));
      dispatch(destroy('addEditCustomAppForm'));
      dispatch(notify('SUCCESSFULLY_SAVED'));
      dispatch(loadAWSData());
      dispatch(checkActivation());
    })
    .catch((error) => {
      const errorMsg = get(error, 'response.data.message', 'There is a problem with saving cloud provider, Please try again later.');
      dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
      dispatch(boundError(errorMsg));
      dispatch(notifyError(errorMsg));
    });
};
