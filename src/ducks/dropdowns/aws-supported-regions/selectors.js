import {
  createStructuredSelector,
} from 'reselect';

import dropdown from 'ducks/generics/dropdown';
// import { get } from 'utils/lodash';

import { REDUCER_KEY } from './constants';

export const dropdownSelector = dropdown.createSelector({ REDUCER_KEY });

export const selector = createStructuredSelector({
  dropdown: dropdownSelector,
  // dropdown: dropdown.createSelector(
  //   dropdownSelector,
  //   (state) => {
  //     const values = get(state, 'values', {});
  //     console.log('..Selector values: ' + JSON.stringify(values));
  //     Object.values(values).map((e, idx) => {
  //       e.id = idx;
  //       return e;
  //     });
  //     // return values.map(val => val.name).join(', ');
  //   },
  // ),
});
export default selector;
