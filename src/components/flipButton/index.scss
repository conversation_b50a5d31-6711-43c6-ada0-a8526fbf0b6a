@import 'scss/colors.scss';

.ec-root-page {
.flip-button {
	border: none;
	background: none;
	outline: none;
	color: var(--semantic-color-content-interactive-primary-default);
	font-size: 13px;
	font-weight: 500;
	line-height: 19px;

	.fa-plus-circle {
		margin-right: 6px;
	}
	.fa-angle-down {
		margin-left: 6px;
	}
	.fa-angle-up {
		margin-left: 6px;
	}
}

.flip-menu {
	.ui-sortable {
		padding-top: 3em;
	}
	input[type="checkbox"] {
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	padding: 0; }
  
  .check-box {
	width: 100%;
	display: block;
	font-size: 13px;
	padding: 0 6px; }
	.check-box.disabled {
		background: var(—surface-fields-disabled);
		color: var(--semantic-color-content-interactive-primary-disabled) !important;		
		cursor: not-allowed;  		
	 }
	  .check-box.disabled .check-box-button {
		border: 1px solid var(--semantic-color-border-interactive-primary-disabled) !important;
		cursor: default; }
	  .check-box.disabled .check-box-label {
		background: var(—surface-fields-disabled);
		color: var(--semantic-color-content-interactive-primary-disabled) !important;	}
  
  .check-box-button {
	background: var(--semantic-color-surface-base-primary);
	border: 1px solid var(--semantic-color-content-base-secondary);
	cursor: pointer;
	display: inline-block;
	height: 18px;
	padding: 0px 2px;
	vertical-align: middle;
	width: 18px; }
	.check-box-button i {
	  display: none; }
	.check-box-button.on i {
	  display: inline-block; }
  
  .check-box-label {
	color: var(--semantic-color-content-base-primary);
	margin-left: 8px;
	display: inline-block;
	// vertical-align: middle;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap; }
	.check-box-label.help-tooltip {
	  cursor: help; }
  
  

	.showComponent{
		display: inline;
		z-index: 48;
	}
	.menu-item{
		height: 20px;
		width: 108px;
		color: var(--semantic-color-content-base-primary);
		font-size: 13px;
		letter-spacing: 0;
		line-height: 20px;
	}
	.menu-table-container{
		padding: 0.5em 0.5em 1em 0.5em;
	}
	.menu-cancel-div {
		height: 21px;
		width: 48%;
		float: left;
		padding: 0.25em 0em 0em 1em;
	}
	.menu-cancel {
		  height: 20px;
		  width: 42px;
		  color: var(--semantic-color-content-status-info-primary);
		  font-size: 13px;
		  font-weight: 500;
		  letter-spacing: 0;
		  line-height: 20px;
		  text-align: center;
	}
	.menu-apply-div {
		height: 26px;
		border-radius: 5px;
		background-color: var(--semantic-color-content-interactive-primary-default);
		width: 38%;
		float: right;
		margin: 0.25em 0.5em 0em 1em;
	}
	.menu-apply {
		color: var(--semantic-color-background-primary);
		letter-spacing: 0;
		line-height: 24px;
		text-align: center;
	}
	.flip-menu-editor{
		position: absolute;
		background: var(--semantic-color-background-primary);
		width: 11%;
		/* height: 100%; */
		top: 0;
		right: 0;
		margin-top: 50px;
		margin-right:  12.5px;
		padding-bottom: 5px;
		border: 1px solid var(--semantic-color-border-base-primary);
		border-radius: 5px 0 5px 5px;
		width: 180px;
		overflow-y: scroll;
		z-index: 1;
	}
	
	.rdg-menu-editor .react-grid-HeaderCell{
		font-weight: normal;
		font-size: 10px;
		text-transform: uppercase;
	}
	
	.rdg-menu-editor .header {
		font-size: 11px;
		padding: 8px;
		cursor: default;
		border-bottom: 1px solid var(--semantic-color-border-base-primary);
	}
	
	.rdg-menu-editor .table-form{
		padding: 8px 4px;
		left: 0;
		position: relative;
		cursor: move;
		&:hover {
			background-color: var(--semantic-color-surface-fields-hover);
		}
	}
	.table-form{
		white-space: nowrap; 
	}
	.rdg-menu-editor .react-grid-HeaderCell .react-grid-HeaderCell--frozen .rdg-row-actions-cell{
	width: none;
	}
	
	.rdg-menu-editor .rdg-row-index{
		color: var(--semantic-color-surface-base-primary);
	}
	.r-check-box{
		color: var(--semantic-color-content-interactive-primary-default);
		background-color: white;
		border-radius: 1px;
		border: 1px solid var(--semantic-color-content-interactive-primary-default);
		opacity: 1;
	}
	.rdg-menu-editor .form-input {
		color: var(--semantic-color-content-base-primary);
		max-height: 28px;
		text-align: left;
		overflow: visible;
	}
	
	.rdg-menu-editor i.fa.fa-bars{
		position: absolute;
		right: 8px;
		top: 12px;
		font-size: 10px;
		color: #747272;
	}

  .noLi{
	list-style-type: none;
	white-space: nowrap; 
	overflow: hidden;
	text-overflow: ellipsis;
	// width: 120px;

	height: 20px;
	width: 108px;
	color: var(--semantic-color-content-base-primary);
	font-size: 13px;
	letter-spacing: 0;
	line-height: 20px;
  }
  
  .hideMe{
	display: none;
  }
  
  .rdg-menu-editor .table-column-menu-header-text{
		text-transform: uppercase;
		color: var(--semantic-color-content-base-primary);
		width: calc(100% - 13px);
		display: inline-block;
		vertical-align: middle;
		text-align: left;
  }
  
  .rdg-menu-editor .table-column-menu-header i.fa {
	color: var(--semantic-color-content-interactive-primary-default);
	vertical-align: middle;
	cursor: pointer;
  }
  
  .noselect {
	-webkit-touch-callout: none; /* iOS Safari */
	  -webkit-user-select: none; /* Safari */
	   -khtml-user-select: none; /* Konqueror HTML */
		 -moz-user-select: none; /* Firefox */
		  -ms-user-select: none; /* Internet Explorer/Edge */
			  user-select: none; /* Non-prefixed version, currently
									supported by Chrome and Opera */
				  outline: none;
  }
  
  .preventEvent {
	pointer-events: none;
  }
}
}