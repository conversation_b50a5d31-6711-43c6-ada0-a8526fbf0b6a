import React from 'react';
import PropTypes from 'prop-types';
import { getNumInterfacesByModelType } from 'utils/helpers';
import { EntityDropdown } from 'components/entityDropdown';

function InterfaceNameDropDown(props) {
  const { modelType } = props;

  const maxNumInterfaces = getNumInterfacesByModelType(modelType);
  const LIST_ITEM = [];
  const managementInterface = (modelType === 'ZT800') ? 'GE3' : 'GE1';

  for (let idx = 1; idx <= maxNumInterfaces; idx += 1) {
    if (managementInterface !== `GE${idx}`) {
      LIST_ITEM.push({
        id: `GE${idx}`, name: `GE${idx}`,
      });
    }
  }

  return (
    <EntityDropdown
      {...props}
      data={LIST_ITEM}
      disableErrorMsg />
  );
}

InterfaceNameDropDown.propTypes = {
  modelType: PropTypes.string,
  netInterface: PropTypes.arrayOf(),
};
InterfaceNameDropDown.defaultProps = {
  modelType: '',
  netInterface: [],
};

export default InterfaceNameDropDown;
