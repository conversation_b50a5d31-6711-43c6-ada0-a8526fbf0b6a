import React from 'react';
import PropTypes from 'prop-types';
import Circle from '../Circle';

import './index.scss';

function MapBox({
  number,
  flex,
  circleColor,
  title,
  boxComponent,
}) {
  const minFlex = 0.25;
  return (
    <div className="map-box" style={{ flex: flex > minFlex ? minFlex : flex }}>
      <div className="content">
        <div className="title">
          <svg width="30" height="30">
            <Circle
              cx="15"
              cy="15"
              x="50%"
              y="50%"
              circleColor={circleColor}
              number={number} />
          </svg>
          <p>{title}</p>
        </div>
        {boxComponent}
      </div>

      <div className="separator" />
    </div>
  );
}

MapBox.propTypes = {
  number: PropTypes.number,
  circleColor: PropTypes.string,
  flex: PropTypes.number,
  title: PropTypes.string,
  boxComponent: PropTypes.element,
};

MapBox.defaultProps = {
  number: null,
  circleColor: null,
  flex: null,
  title: null,
  boxComponent: null,
};

export default MapBox;
