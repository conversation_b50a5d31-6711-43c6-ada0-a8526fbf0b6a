// this will be hooked onto the relative state tree.
import { BASE_API_PATH } from 'config';

export const REDUCER_KEY = 'dnsInsights';
export const INSIGHTS_API_ENDPOINT = `${BASE_API_PATH}/v1/ecReportData/dns`;

export const EXPORT_DNS_JOB_API_ENDPOINT = `${BASE_API_PATH}/v1/transactionData/exportDnsTransactionJob`;
export const EXPORT_DNS_DATA_API_ENDPOINT = `${BASE_API_PATH}/v1/transactionData/exportDnsTransactionData`;

export const DATA_ERROR = 'error';
export const DATA_LOADING = 'loading';

// Only for Insights
export const CHART_TYPE = 'chartType';
export const UNITS = 'units';
export const REPORT_DATA = 'reportData';
export const TREND_DATA = 'trendData';
export const TOTAL_TREND_DATA_KEYS = 'totalTrendDataKeys';
export const HISTORY = 'history';
export const CARD_ID = 'cardId';
export const DATA_TYPE_FILTERS = 'dataTypeFilters';
export const CURRENT_DATA_TYPE_FILTERS = 'currentDataTypeFilters';
export const DEFUALT_DATA_TYPE_VALUE = 'defaultDataTypeValue';
export const DATA_TYPE = 'dataType';

export const DATA_TABLE = 'sessionlogstabledata';
export const DATA = 'data';

// this will store data to create and edit form
export const APP_DATA = 'appData';
export const SHOW_ACCORDION = 'showAccordion';
export const TIMEFRAME = 'timeFrame';
export const LOCATIONS = 'locations';
export const FILTERS = 'filters';
export const CURRENT_FILTERS = 'currentFilters';
export const SHOW_FILTERS = 'showFilters';
export const STATE_FORM = 'form';
export const COLUMNS = 'columns';
export const DONWLOAD_CSV_DATA = 'downloadCsvData';
export const SHOW_PREVIEW = 'showPreview';

const sgAppData = [{
  id: '1',
  valid: true,
}];

const totalTrendDataKeys = [
  {
    key: 'Total',
    stroke: '#00B5BF',
  }];

export const columns = [{
  key: 'name',
  exportId: 'Name',
  name: 'Name',
  data: {},
  draggable: true,
  resizable: true,
  checkbox: true,
  sortable: true,
  visible: true,
}, {
  key: 'total',
  exportId: 'Total',
  name: 'Total',
  data: {},
  draggable: true,
  resizable: true,
  checkbox: true,
  sortable: true,
  visible: true,
}];

export const defaultDataTypeValue = {
  value: 'NO_GROUPING',
  label: 'OVER_ALL_TRAFFIC',
};

export const allDataTypeFilters = [{
  value: 'NO_GROUPING',
  label: 'OVER_ALL_TRAFFIC',
  data: {},
  order: 'none',
}, {
  value: 'DNS_REQ_RESP_ACTION', // API DATA_TYPE
  label: 'ACTION',
  data: {},
  order: 'none',
}, {
  value: 'DNS_RESOLVER',
  label: 'DNS_RESOLVER',
  data: {},
  order: 'none',
}, {
  value: 'DNS_RULE',
  label: 'DNS_RULE',
  data: {},
  order: 'none',
}, {
//   value: 'EC_DNS_GW_NAME',
//   label: 'EC_DNS_GW_NAME',
//   data: {},
//   order: 'none',
// }, {
//   value: 'EC_DNS_GW_FLAG',
//   label: 'EC_DNS_GW_FLAG',
//   data: {},
//   order: 'none',
// }, {
  value: 'EC_VM',
  label: 'CONNECTOR_VM',
  data: {},
  order: 'none',
}, {
  value: 'EC_INSTANCE',
  label: 'CONNECTOR_INSTANCE',
  data: {},
  order: 'none',
}, {
  value: 'EC_GROUP',
  label: 'CONNECTOR_GROUP',
  data: {},
  order: 'none',
}, {
  value: 'EC_PLATFORM',
  label: 'EC_PLATFORM',
  data: {},
  order: 'none',
}, {
  value: 'LOCATION',
  label: 'LOCATION', // 'Location',
  data: {},
  order: 'none',
}, {
  value: 'EC_SUBSCRIPTION_ID',
  label: 'SUBSCRIPTION_ID',
  data: {},
  order: 'none',
}, {
  value: 'EC_PROJECT_ID',
  label: 'EC_PROJECT_ID',
  data: {},
  order: 'none',
}, {
  value: 'EC_ACC_ID',
  label: 'ACCOUNT_ID',
  data: {},
  order: 'none',
}];

// DataType filter list
export const defaultDataTypeFilters = [{
  value: 'NO_GROUPING',
  label: 'OVER_ALL_TRAFFIC',
  data: {},
  order: 'none',
}, {
  value: 'DNS_REQ_RESP_ACTION',
  label: 'DNS_REQ_RESP_ACTION',
  data: {},
  order: 'none',
}, {
  value: 'DNS_RESOLVER',
  label: 'DNS_RESOLVER',
  data: {},
  order: 'none',
}, {
  value: 'DNS_RULE',
  label: 'DNS_RULE',
  data: {},
  order: 'none',
}, {
//   value: 'EC_DNS_GW_NAME',
//   label: 'EC_DNS_GW_NAME',
//   data: {},
//   order: 'none',
// }, {
//   value: 'EC_DNS_GW_FLAG',
//   label: 'EC_DNS_GW_FLAG',
//   data: {},
//   order: 'none',
// }, {
  value: 'EC_VM',
  label: 'CONNECTOR_VM',
  data: {},
  order: 'none',
}, {
  value: 'EC_INSTANCE',
  label: 'CONNECTOR_INSTANCE',
  data: {},
  order: 'none',
}, {
  value: 'EC_GROUP',
  label: 'CONNECTOR_GROUP',
  data: {},
  order: 'none',
}, {
  value: 'EC_PLATFORM',
  label: 'EC_PLATFORM',
  data: {},
  order: 'none',
}, {
  value: 'LOCATION',
  label: 'LOCATION',
  data: {},
  order: 'none',
}, {
  value: 'EC_SUBSCRIPTION_ID',
  label: 'SUBSCRIPTION_ID',
  data: {},
  order: 'none',
}, {
  value: 'EC_PROJECT_ID',
  label: 'EC_PROJECT_ID',
  data: {},
  order: 'none',
}, {
  value: 'EC_ACC_ID',
  label: 'ACCOUNT_ID',
  data: {},
  order: 'none',
}];

// "Add Filter" - list
export const dfilters = [{
  value: 'dnsRuleName',
  label: 'DNS_RULE_NAME',
  data: {},
  order: 'none',
}, {
//   value: 'dnsGwName',
//   label: 'EC_DNS_GW_NAME',
//   data: {},
//   order: 'none',
// }, {
//   value: 'dnsGwFlag',
//   label: 'EC_DNS_GW_FLAG',
//   data: {},
//   order: 'none',
// }, {
  value: 'requestAction',
  label: 'DNS_REQ_RESP_ACTION', // 'Request Action[Allow, Block, Resolve By ZPA ]',
  data: {},
  order: 'none',
}, {
  value: 'ecInstance',
  label: 'CONNECTOR_INSTANCE',
  data: {},
  order: 'none',
}, {
  value: 'ecVm',
  label: 'CONNECTOR_VM',
  data: {},
  order: 'none',
}, {
  value: 'ecGroup',
  label: 'CONNECTOR_GROUP',
  data: {},
  order: 'none',
}, {
  value: 'awsRegion',
  label: 'AWS_REGION', // 'awsRegion',
  data: {},
  order: 'none',
}, {
  value: 'availabilityZone',
  label: 'AWS_AVAILABILITY_ZONE',
  data: {},
  order: 'none',
}, {
  value: 'azureRegion',
  label: 'AZURE_REGION',
  data: {},
  order: 'none',
}, {
  value: 'azureAvailabilityZone',
  label: 'AZURE_AVAILABILITY_ZONE',
  data: {},
  order: 'none',
}, {
  value: 'platform',
  label: 'EC_PLATFORM',
  data: {},
  order: 'none',
}, {
  value: 'dnsResolver',
  label: 'DNS_RESOLVER',
  data: {},
  order: 'none',
}, {
  value: 'locationName',
  label: 'LOCATION',
  data: {},
  order: 'none',
}, {
  value: 'accountId',
  label: 'ACCOUNT_ID',
  data: {},
  order: 'none',
}, {
  value: 'gcpRegion',
  label: 'GCP_REGION',
  data: {},
  order: 'none',
}, {
  value: 'gcpAvailabilityZone',
  label: 'GCP_AVAILABILITY_ZONE',
  data: {},
  order: 'none',
}, {
  value: 'projectId',
  label: 'EC_PROJECT_ID',
  data: {},
  order: 'none',
}, {
  value: 'subscriptionId',
  label: 'SUBSCRIPTION_ID',
  data: {},
  order: 'none',
}];

export const dummyTestfilters = dfilters;
export const defaultFilters = {
  clientDestinationIp: false,
  clientDestinationPort: false,
  clientDestName: false,
  clientSourceIp: false,
  clientSourcePort: false,
  ecName: false,
  ecSourceIp: false,
  ecSourcePort: false,
  forwardingMethod: false,
  gatewayDestinationIp: false,
  gatewayDestinationPort: false,
  gatewayName: false,
  location: false,
  name: false,
  networkService: false,
  rdrRuleName: false,
  requestAction: false,
  serverCountryCode: false,
  serverDestinationIp: false,
  serverDestinationPort: false,
  serverIpCategory: false,
  serverSourceIp: false,
  serverSourcePort: false,
  total: false,
  zpaAppSegment: false,
};

export const RESET_FILTERS = {
  [APP_DATA]: sgAppData,
  [CARD_ID]: 0,
  [CHART_TYPE]: 'line',
  [COLUMNS]: columns,
  [CURRENT_DATA_TYPE_FILTERS]: defaultDataTypeFilters,
  [CURRENT_FILTERS]: dfilters,
  [DATA_ERROR]: null, // or error object so we can process and show to user
  [DATA_LOADING]: true,
  [DATA_TYPE_FILTERS]: defaultDataTypeFilters,
  [DATA_TYPE]: 'NO_GROUPING',
  [DEFUALT_DATA_TYPE_VALUE]: defaultDataTypeValue,
  [DONWLOAD_CSV_DATA]: [],
  [FILTERS]: dfilters,
  [HISTORY]: [],
  [REPORT_DATA]: [], // for Insights Data , dummy testLineChartData
  [SHOW_ACCORDION]: true,
  [SHOW_FILTERS]: true,
  [SHOW_PREVIEW]: false,
  [TIMEFRAME]: { id: 'current_day', value: 'current_day', label: 'CURRENT_DAY' },
  [TOTAL_TREND_DATA_KEYS]: totalTrendDataKeys,
  [TREND_DATA]: [],
  [UNITS]: 'TRANSACTIONS',
};

export const RESOURCE_DEFAULTS = {
  [APP_DATA]: sgAppData,
  [CARD_ID]: 0,
  [CHART_TYPE]: 'line',
  [COLUMNS]: columns,
  [CURRENT_DATA_TYPE_FILTERS]: defaultDataTypeFilters,
  [CURRENT_FILTERS]: dfilters,
  [DATA_ERROR]: null, // or error object so we can process and show to user
  [DATA_LOADING]: true,
  [DATA_TABLE]: [],
  [DATA_TYPE_FILTERS]: defaultDataTypeFilters,
  [DATA_TYPE]: 'NO_GROUPING',
  [DATA]: {},
  [DEFUALT_DATA_TYPE_VALUE]: defaultDataTypeValue,
  [DONWLOAD_CSV_DATA]: [],
  [FILTERS]: dfilters,
  [HISTORY]: [],
  [REPORT_DATA]: [], // for Insights Data
  [SHOW_ACCORDION]: true,
  [SHOW_FILTERS]: defaultFilters,
  [SHOW_PREVIEW]: false,
  [TIMEFRAME]: { id: 'current_day', value: 'current_day', label: 'CURRENT_DAY' },
  [TOTAL_TREND_DATA_KEYS]: totalTrendDataKeys,
  [TREND_DATA]: [],
  [UNITS]: 'TRANSACTIONS',
};

export const DEFAULTS = {
  ...RESOURCE_DEFAULTS,
};

export const DEFAULTS_BUT_ACCORDION = {
  ...RESOURCE_DEFAULTS,
  [SHOW_ACCORDION]: true,
};

// default state for this page/widget/component
export const DEFAULT_STATE = {
  ...DEFAULTS,
};

// For testing...to be used later
export const DUMMY_STORE_DATA = {
  [REDUCER_KEY]: {
    ...DEFAULT_STATE,
  },
};
