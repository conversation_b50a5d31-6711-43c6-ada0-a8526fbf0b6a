import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react';
import { useTooltip } from '@nivo/tooltip';
import BarText from './BarText';
import getTextWidth from '../../../utils/helpers/getTextWidth';

const noop = () => undefined;

function ToolTipMessage({ selectedDataKey, actionToolTip }) {
  return selectedDataKey && !actionToolTip
    ? (
      <div id="tooltip" className="tooltip">
        <div className="ec-tooltip-container">
          <div id="tooltip-top" className="tooltip-top tooltip-top-text">{selectedDataKey}</div>
          <div id="tooltip-bottom" className="tooltip-bottom tooltip-bottom-text">CLICK_FOR_MORE_INFO</div>
        </div>
      </div>
    )
    : <></>;
}

jest.mock('@nivo/tooltip', () => ({
  useTooltip: jest.fn(() => ({
    showTooltipFromEvent: jest.fn(),
    hideTooltip: jest.fn(),
  })),
}));

jest.mock('../../../utils/helpers/getTextWidth', () => jest.fn(() => 100));

describe('BarText component', () => {
  const props = {
    x: 10,
    y: 20,
    width: 30,
    color: 'ed',
    data: {
      data: {
        total: 'Total',
      },
      indexValue: 'Index Value',
    },
  };

  const sortedData = {
    'Index Value': {
      id: 'Index Value',
    },
  };

  const styles = [
    {
      id: 'Index Value',
      opacity: 1,
      strokeWidth: 1,
    },
  ];

  const setStyles = jest.fn();
  const setSelectedDataKey = jest.fn();
  const setActionToolTip = jest.fn();
  const actionToolTip = false;

  it('renders correctly', () => {
    const { getByText } = render(
      <BarText
        {...props}
        sortedData={sortedData}
        styles={styles}
        setStyles={setStyles}
        selectedDataKey=""
        setSelectedDataKey={setSelectedDataKey}
        actionToolTip={actionToolTip}
        setActionToolTip={setActionToolTip} />,
    );

    expect(getByText('Total')).toBeInTheDocument();
    expect(getByText('Index Value')).toBeInTheDocument();
  });
});

describe('ToolTipMessage component', () => {
  it('renders nothing when actionToolTip is true', () => {
    const { queryByText } = render(
      <ToolTipMessage
        selectedDataKey="Index Value"
        actionToolTip />,
    );

    expect(queryByText('Index Value')).not.toBeInTheDocument();
    expect(queryByText('CLICK_FOR_MORE_INFO')).not.toBeInTheDocument();
  });
});
