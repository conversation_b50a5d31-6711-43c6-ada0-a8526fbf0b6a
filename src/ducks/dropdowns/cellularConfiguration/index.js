import { genericInterface } from 'utils/http';
import { createAction, loading } from 'ducks/generics';
import genericDropdown from 'ducks/generics/dropdown';
import { change } from 'redux-form';
import { notifyError } from 'ducks/notification';
import {
  get, isEmpty, uniqBy,
} from 'utils/lodash';
import actionTypes from './action-types';
import selector, * as selectors from './selectors';
import * as constants from './constants';

export { REDUCER_KEY } from './constants';

const actionTypeExists = Object.keys(actionTypes).reduce((hash, key) => ({
  ...hash,
  [actionTypes[key]]: true,
}), {});

export const reducer = (state = constants.DEFAULT_STATE, action = {}) => {
  // Simplifyeverything: actions provide a payload, and we merge that payload.
  if (actionTypeExists[action.type]) {
    return {
      ...state,
      ...action.payload,
    };
  }
  return state;
};
export default reducer;

const http = genericInterface(constants.API_ENDPOINT);

const handleCellularConfigurationChange = (form, initialValue) => (dispatch) => {
  dispatch(change(form, 'cellularConfigurationDropdown', initialValue));
};

const boundLoading = loading(actionTypes.DATA_LOADING);
const boundError = createAction(actionTypes.DATA_LOAD_ERROR);
const boundDataLoadSuccess = createAction(actionTypes.DATA_LOAD_SUCCESS);

export const loader = (initializeProps) => async (dispatch, getState) => {
  const state = getState();
  if (initializeProps) {
    state.cellularConfigurationDropdown.pageNum = 1;
    state.cellularConfigurationDropdown.data = [];
    state.cellularConfigurationDropdown.hasMorePages = false;
    state.cellularConfigurationDropdown.loading = false;
  }
  const { pageNum, pageSize } = state.cellularConfigurationDropdown;
  const param = { page: pageNum, pageSize };
  dispatch(
    boundLoading({
      [constants.DATA]: [],
      ...constants.DEFAULT_STATE,
      loading: true,
    }),
  );
  try {
    const cellularConfigurationDropdown = await http.read(null, {
      params: param,
    });
    const { data } = cellularConfigurationDropdown || [];

    const dataWithId = data.map((x) => ({ id: x.name, ...x }));

    const combinedData = [
      ...state.cellularConfigurationDropdown.data,
      ...dataWithId];
    const updatedData = uniqBy(combinedData, 'id');

    dispatch(boundDataLoadSuccess({
      [constants.DATA]: updatedData,
      [constants.DROPDOWN.PAGE_NUMBER]: pageNum + 1,
      [constants.DROPDOWN.HAS_MORE_PAGES]: data.length >= pageSize && pageSize > 0,
      loading: false,
    }));
  } catch (error) {
    const errorMsg = get(error, 'response.data.message');
    dispatch(boundLoading({ loading: false }));
    dispatch(boundError(errorMsg));
    if (isEmpty(errorMsg)) dispatch(notifyError('ERROR_LOADING'));
    else dispatch(notifyError(errorMsg));
  }
};

const dropdownActions = genericDropdown.createActions({ actionTypes, selector, http });
export {
  selector,
  selectors,
  dropdownActions,
  handleCellularConfigurationChange,
};
