import React from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import * as loginSelectors from 'ducks/login/selectors';
import ServerError from 'components/errors/ServerError';
import { getPermission } from 'utils/helpers';

export function RBAC(props) {
  const {
    privilege,
    children,
    accessPrivileges,
  } = props;

  if (getPermission(privilege, accessPrivileges)) {
    return children;
  }

  return <ServerError error="You don't have necessary permissions to view this page" />;
}

RBAC.propTypes = {
  privilege: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.string), PropTypes.string]),
  accessPrivileges: PropTypes.shape({}),
  children: PropTypes.node.isRequired,
};

RBAC.defaultProps = {
  privilege: '',
  accessPrivileges: {},
};

export const mapStateToProps = (state) => ({
  accessPrivileges: loginSelectors.accessPermissionsSelector(state),
});
export default connect(
  mapStateToProps,
)(RBAC);
