import React from 'react';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faChartBar, faChartPie, faChartLine, faTable,
} from '@fortawesome/pro-solid-svg-icons';

const getChart = (label) => {
  switch (true) {
  case label === 'bar':
    return <FontAwesomeIcon icon={faChartBar} className="fa-2xl" />;
  case label === 'pie':
    return <FontAwesomeIcon icon={faChartPie} className="fa-2xl" />;
  case label === 'line':
    return <FontAwesomeIcon icon={faChartLine} className="fa-2xl" />;
  case label === 'table':
    return <FontAwesomeIcon icon={faTable} className="fa-2xl" />;
  default:
    return <FontAwesomeIcon icon={faChartLine} className="fa-2xl" />;
  }
};

function ChartButton(props = {}) {
  const {
    input,
    label,
    disabled,
    checked,
  } = props;

  input.checked = checked;

  return (
    <div className={`radio-button checked-${input.checked}`}>
      <label htmlFor={input.label}>
        <input
          type="radio"
          disabled={disabled}
          {...input} />
        <div className="chart-label">
          {input.checked}
        </div>
        <span>
          {getChart(label)}
        </span>
      </label>
    </div>
  );
}

ChartButton.defaultProps = {
  label: null,
  disabled: false,
};

ChartButton.propTypes = {
  label: PropTypes.string,
  disabled: PropTypes.bool,
  checked: PropTypes.bool,
  input: PropTypes.shape({
    name: PropTypes.string,
    value: PropTypes.any,
  }).isRequired,
};

export default ChartButton;
