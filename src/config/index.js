import {
  faQuestionCircle,
  faTachometerAlt,
  faUser,
  faBuilding,
  faCodeBranch,
} from '@fortawesome/pro-solid-svg-icons';
import { verifyConfigData } from 'utils/helpers';

import {
  faCloud, faSearch, faCogs, faChartLine,
} from '@fortawesome/pro-regular-svg-icons';
import colors from 'scss/colors.scss';
// import * as loginConstants from 'ducks/login/constants';

// export const BASE_API_PATH = process.env.REACT_APP_API_BASE_API_PATH || '/ec';
// export const BASE_API_PATH = process.env.REACT_APP_API_BASE_API_PATH;
// eslint-disable-next-line no-underscore-dangle, no-nested-ternary
export const BASE_API_PATH = ['development', 'test', 'oneui'].includes(process.env.NODE_ENV)
  ? ['oneui'].includes(process.env.NODE_ENV)
    ? `${process.env.REACT_APP_API_BASE_API_PATH || '/ec'}/api`
    : `${process.env.REACT_APP_API_BASE_API_PATH || '/ec'}/wapi`
  : `${window.externalPublicPath || ''}/wapi`;
  
export const BASE_ROUTE_PATH = ['development', 'test', 'oneui'].includes(process.env.NODE_ENV)
  ? process.env.REACT_APP_API_BASE_API_PATH || '/ec'
  : window.externalPublicPath || '';

export const ENVIRONMENT = process.env.NODE_ENV;

export const BUILD_TARGET = process.env.REACT_APP_BUILD_TARGET || 'external';

export const BUILD_TARGETS = {
  EXTERNAL: 'external',
  ONEUI: 'oneui',
};

export const ADMIN_LAYOUT = '/admin';
export const ONEUI_LAYOUT = '';

const layouts = {
  [BUILD_TARGETS.EXTERNAL]: ADMIN_LAYOUT,
  [BUILD_TARGETS.ONEUI]: ONEUI_LAYOUT,
};

// export const isOneUI = ENVIRONMENT === 'oneui';
export const isOneUI = BUILD_TARGET === BUILD_TARGETS.ONEUI;

export const BASE_LAYOUT = layouts[BUILD_TARGET] || '';

// eslint-disable-next-line max-len
export const REACT_APP_CSRF_ENABLED = (process && process.env && process.env.REACT_APP_CSRF_ENABLED) || false;

const HELP_ARTICLES = {
  ABOUT_ADMIN: 'about-administrator',
  ABOUT_API_KEY_MANAGEMENT: 'about-api-key-management',
  ABOUT_AWS_GROUPS_PARTNER_INTEGRATIONS: 'about-amazon-web-services-account-groups',
  ABOUT_PARTNER_INTEGRATIONS: 'about-partner-integrations',
  ABOUT_PHYSYCAL_BRANCH_DEVICES: 'about-physical-branch-devices',
  ABOUT_URL_CATEGORIES: '../zia/about-url-categories',
  ABOUT_VIRTUAL_BRANCH_DEVICES: 'about-virtual-branch-devices',
  ABOUT_ZERO_TRUST_GATEWAY: 'about-zero-trust-gateways',
  ABOUT_ZT_DEVICES: 'about-zt-devices',
  ADD_AWS_ACCOUNT: 'adding-aws-account',
  ADD_AWS_CLOUD_ACCOUNT: 'configuring-aws-cloud-account',
  ADD_AWS_GROUPS_PARTNER_INTEGRATIONS: 'adding-amazon-web-services-account-group',
  ADD_AZURE_ACCOUNT: 'adding-microsoft-azure-account',
  ADD_AZURE_CLOUD_ACCOUNT: 'configuring-azure-cloud-account',
  ADD_BRANCH_PROVISIONING_TEMPLATE: 'configuring-branch-provisioning-template',
  ADD_DEST_GROUP: 'configuring-destination-groups',
  ADD_DNS_CONTROL_POLICY: 'configuring-dns-filtering-rule',
  ADD_IP_POOL: 'configuring-ip-pool',
  ADD_LOG_AND_CONTROL_FWD: 'configuring-log-and-control-forwarding-rule',
  ADD_PROVISIONING_TEMPLATE: 'configuring-provisioning-templates',
  ADD_SRC_IP_GROUP: 'configuring-source-ip-groups',
  ADDING_ZERO_TRUST_GATEWAY: 'adding-zero-trust-gateways',
  ANALYZING_ZERO_TRUST_GATEWAY_DETAILS: 'analyzing-zero-trust-gateways-details',
  ANALYZING_ZERO_TRUST_GATEWAY: 'analyzing-zero-trust-gateways',
  // CONFIGURE_ZERO_TRUST_GATEWAY: 'configuring-zero-trust-gateway ',
  ADD_TRAFFIC_FWD: 'configuring-traffic-forwarding-rule',
  ADMIN_MANAGEMENT: 'about-administrator-management',
  ADVANCED_SETTINGS: 'about-advanced-settings',
  ANALYTICS_INSIGHTS: 'about-insights',
  ANALYTICS_LOGS: 'about-insights-logs',
  ANALYTICS_SESSION_LOGS: 'session-logs',
  AUDIT_LOGS: 'about-audit-logs',
  AZURE_AUTOMATION_SCRIPTS: 'deployment-templates-zscaler-cloud-connector',
  BRANCH_AND_CLOUD_GROUP: 'about-branch-and-cloud-connector-group',
  BRANCH_AND_CLOUD_IMAGES: 'about-branch-and-cloud-connector-images',
  BRANCH_AND_CLOUD_MONITORING: 'about-branch-cloud-connector-monitoring',
  BRANCH_CONNECTOR_GROUP: 'about-branch-connector-groups',
  BRANCH_PROVISIONING_TEMPLATES: 'about-branch-provisioning-template',
  CELLULAR_DEPLOYMENT_CONFIGURATION_ADD: 'adding-deployment-configuration',
  CELLULAR_DEPLOYMENT_CONFIGURATION: 'about-deployment-configuration',
  CELLULAR_USER_PLANE_FUNCTION_ADD: 'adding-user-plane-function',
  CELLULAR_USER_PLANE_FUNCTION: 'about-user-plane-function',
  CLOUD_AUTOMATION_SCRIPTS: 'deployment-templates-zscaler-cloud-connector',
  CLOUD_CONNECTOR_DETAILS: 'about-cloud-connector-details',
  CLOUD_CONNECTOR_GROUP: 'about-cloud-connector-group',
  DASHBOARD: 'about-cloud-connector-admin-portal',
  DEST_GROUP: 'about-destination-groups',
  DNS_CONTROL_POLICY: 'about-dns-control',
  EDITING_PHYSYCAL_BRANCH_DEVICES: 'editing-physical-branch-devices',
  EDITING_VIRTUAL_BRANCH_DEVICES: 'editing-virtual-branch-devices',
  IP_POOL: 'about-ip-pool',
  LOCATION_TEMPLATES: 'about-location-templates',
  LOCATIONS: 'about-locations',
  LOG_AND_CONTROL_FWD: 'about-log-and-control-forwarding',
  LOG_AND_CONTROL_GATEWAY: 'about-log-and-control-gateway',
  NETWORK_SERVICE_GROUPS: 'about-network-service-groups',
  NETWORK_SERVICE: 'about-network-services',
  NSS_CLOUD_FEEDS: 'about-cloud-nss-feeds',
  NSS_FEEDS: 'about-nss-feeds',
  NSS_SERVERS: 'about-nss-servers',
  ONBOARD_AWS_CLOUD_ACCOUNT: 'onboarding-amazon-web-services-cloud-account',
  ONBOARD_AZURE_CLOUD_ACCOUNT: 'onboarding-microsoft-azure-cloud-account',
  OTHER_AUTOMATION_SCRIPTS: 'deployment-templates-zscaler-cloud-connector',
  PROVISIONING_TEMPLATES: 'about-provisioning-templates',
  ROLE_MANAGEMENT: 'about-role-management',
  SRC_IP_GROUP: 'about-source-ip-groups',
  TOOLTIP_HELP_EXTERNAL_ADMIN_RANK: '../zia/about-admin-rank',
  TRAFFIC_FLOW: 'about-traffic-flow',
  TRAFFIC_FWD: 'about-traffic-forwarding',
  TRAFFIC_MONITORING: 'about-traffic-monitoring',
  ZIA_GATEWAY: 'about-zia-gateway',
  '/admin/dashboard': 'about-cloud-connector-admin-portal',
  '/admin/alerts': 'about-alerts',
};

const MONITOR_STATUS_DATA = [
  { value: 'active', label: 'Active Monitors' },
  { value: 'inactive', label: 'Inactive Monitors' },
];

const SHAREPOINT_MONITOR_DATA = [
  { value: 1, label: 'MTR SharePoint Monitor' },
  { value: 2, label: 'MTR SharePoint Monitor' },
  { value: 3, label: 'Default SharePoint Monitor' },
  { value: 4, label: 'Additional SharePoint' },
];

const SALESFORCE_MONITOR_DATA = [
  { value: 1, label: 'MTR SalesForce Monitor' },
  { value: 2, label: 'Default SalesForce Monitor' },
  { value: 3, label: 'Additional SalesForce' },
];

const OFFICE365_MONITOR_DATA = [
  { value: 1, label: 'MTR Office 365 Monitor' },
  { value: 2, label: 'Default Office 365 Monitor' },
  { value: 3, label: 'Additional Office 365' },
];

const ONEDRIVE_MONITOR_DATA = [
  { value: 1, label: 'MTR One drive Monitor' },
  { value: 2, label: 'Default One drive Monitor' },
  { value: 3, label: 'Additional One drive' },
];

const CIRCLE_COLOR = {
  30: 'red3', // bad
  60: 'orange2', // okay
  80: 'green2', // good
};

// HTTP status info source - https://developer.mozilla.org/en-US/docs/Web/HTTP/Status
const MONITOR_HTTP_SUCCESS_CODES = [
  { id: '100', name: '100 Continue' },
  { id: '101', name: '101 Switching Protocol' },
  { id: '102', name: '102 Processing' },
  { id: '103', name: '103 Early Hints' },
  { id: '200', name: '200 OK' },
  { id: '201', name: '201 Created' },
  { id: '202', name: '202 Accepted' },
  { id: '203', name: '203 Non-Authoritative Information' },
  { id: '204', name: '204 No Content' },
  { id: '205', name: '205 Reset Content' },
  { id: '206', name: '206 Partial Content' },
  { id: '207', name: '207 Multi-Status' },
  { id: '208', name: '208 Already Reported' },
  { id: '226', name: '226 IM Used' },
  { id: '300', name: '300 Multiple Choices' },
  { id: '301', name: '301 Moved Permanently' },
  { id: '302', name: '302 Found' },
  { id: '303', name: '303 See Other' },
  { id: '304', name: '304 Not Modified' },
  { id: '307', name: '307 Temporary Redirect' },
  { id: '308', name: '308 Permanent Redirect' },
];

const RADAR_CHART = [{
  to: 20,
  color: colors.red3,
}, {
  from: 20,
  to: 39,
  color: colors.orange3,
},
{
  from: 40,
  to: 59,
  color: colors.orange4,
},
{
  from: 60,
  to: 89,
  color: colors.blue2,
},
{
  from: 90,
  to: 100,
  color: colors.green8,
}];

const SCORE_STATUSES = {
  poor: 33.33,
  okay: 66.66,
  good: 100,
};

const MONITOR_DURATION = [
  { id: -1, name: 'Forever' },
  { id: '1 hour', name: '1 hour' },
  { id: '4 hours', name: '4 hours' },
  { id: '8 hours', name: '8 hours' },
  { id: '1 day', name: '1 day' },
  { id: '3 days', name: '3 days' },
  { id: '1 week', name: '1 week' },
  { id: '2 weeks', name: '2 weeks' },
  { id: '1 month', name: '1 month' },
];

const MONITOR_TYPES = [
  { id: 'WEB', name: 'Web' },
  { id: 'TRACERT', name: 'Traceroute' },
];

const MONITOR_TRACEROUTE_PROTOCOLS = [
  { id: 'ICMP', name: 'ICMP' },
  { id: 'TCP', name: 'TCP' },
  { id: 'UDP', name: 'UDP' },
];

const IDP_NAMES = [
  { id: 'domain1', name: 'Domain 1' },
  { id: 'domain2', name: 'Domain 2' },
  { id: 'domain3', name: 'Domain 3' },
];

const EDGECONNECTOR_WIZARD_INITIAL_VALUES = {
  active: true,
  mangementIp: true,
  staticIp: true,
  edgeconnectorGroups: true,
  provisioningKey: 'DUMMY-/68F0AOEgpcG8McLmwdborq2m6v2 A5oNEpSztoNEpSztJ/68F0AOEgpcG8McLmwdborq2m6v2A5oNEpS ztJ/68F0AOEgpcG8McLmwdborq2m6v2A5oNEpSztJ==',
  frequencyInSeconds: 5,
  maxRedirects: 10,
  pageFetch: true,
  protocol: 'ICMP',
  requestTimeoutSeconds: 10,
  requestMethods: ['GET'],
  requestHeaders: [{}],
  responseCodes: ['100', '101', '102', '103',
    '200', '201', '202', '203', '204', '205', '206', '207', '208', '226',
    '300', '301', '302', '303', '304', '307', '308'], // all 100's, 200's and 300's
  retries: 1,
  timeoutSeconds: 10,
  type: 'WEB',
};

const BUBBLES_POSITIONS = [50, 70, 54, 21, 62, 51, 25, 43, 65, 50];

const NAVBAR_ROUTES = [
  {
    path: '/dashboard',
    // redirectTo: '/admin/dashboard/dashboard',
    redirectTo: `${BASE_LAYOUT}/dashboard/connector-monitoring`,
    name: 'DASHBOARD',
    icon: faTachometerAlt,
    liclass: 'normal',
    iclass: 'fa-2x',
    perm: 'EDGE_CONNECTOR_DASHBOARD',
  },
  {
    path: '/analytics',
    redirectTo: `${BASE_LAYOUT}/analytics`,
    name: 'ANALYTICS',
    icon: faChartLine,
    liclass: 'normal',
    iclass: 'fa-2x',
    perm: 'EDGE_CONNECTOR_DASHBOARD',
  },
  // {
  //   path: '/logs',
  //   redirectTo: '/admin/logs',
  //   name: 'Logs',
  //   icon: faChartLine,
  //   liclass: 'normal',
  //   iclass: 'fa-2x',
  // },
  {
    path: '/administration',
    redirectTo: `${BASE_LAYOUT}/administration/edgeconnectors`,
    name: 'ADMINISTRATION',
    icon: faCogs,
    liclass: 'normal',
    iclass: 'fa-2x',
  },
  {
    path: '/policy',
    redirectTo: `${BASE_LAYOUT}/policy`,
    name: 'FORWARDING',
    icon: faCloud,
    liclass: 'normal',
    iclass: 'fa-2x',
    perm: 'EDGE_CONNECTOR_FORWARDING',
  },
  // {
  //   path: '/exec-insights',
  //   redirectTo: '/admin/exec-insights',
  //   name: 'Exec Insights',
  //   icon: faChartBar,
  //   // icon: faFileChartLine,
  //   liclass: 'normal',
  //   iclass: 'fa-2x',
  // },
  {
    path: '/search',
    redirectTo: `${BASE_LAYOUT}/search`,
    name: 'SEARCH',
    icon: faSearch,
    liclass: 'normal',
    iclass: 'fa-2x',
  },
  {
    name: 'ACTIVATION',
  },
  // {
  //   path: '/product-switch',
  //   redirectTo: '/admin/products',
  //   icon: faTh,
  //   liclass: 'small',
  //   iclass: 'fa',
  // },
  {
    hiddenName: 'PROFILE',
    path: '/profile',
    redirectTo: `${BASE_LAYOUT}/profile`,
    icon: faUser,
    liclass: 'small',
    iclass: 'fa',
  },
  {
    name: 'HELP',
    path: '/help',
    redirectTo: `${BASE_LAYOUT}/help`,
    icon: faQuestionCircle,
    liclass: 'small',
    iclass: 'fa',
  },
];

const LOGS_SUB_NAV_ROUTES = [
  {
    title: 'LOGS',
    routes: [
      {
        path: '/logs',
        redirectTo: `${BASE_LAYOUT}/logs/sessionlogs`,
        name: 'SESSION_LOGS',
        // perm: 'reportAccess',
        liclass: 'nav-menu-list-item',
      },
      // {
      //   path: '/logs',
      //   redirectTo: '/admin/devicelogs',
      //   name: 'Device Logs',
      //   liclass: 'nav-menu-list-item',
      // },
      // {
      //   path: '/logs',
      //   redirectTo: '/admin/firewalllogs',
      //   name: 'Firewall Logs',
      //   liclass: 'nav-menu-list-item',
      // },
      {
        path: '/logs',
        redirectTo: `${BASE_LAYOUT}/logs/dnslogs`,
        name: 'DNS Logs',
        // perm: 'reportAccess',
        liclass: 'nav-menu-list-item',
      },
      {
        path: '/logs',
        redirectTo: `${BASE_LAYOUT}/logs/tunnellogs`,
        name: 'TUNNEL_LOGS',
        // perm: 'reportAccess',
        liclass: 'nav-menu-list-item',
      },
    ],
  },
];

const ANALYTICS_SUB_NAV_ROUTES = [
  {
    title: 'ANALYTICS',
    perm: 'EDGE_CONNECTOR_DASHBOARD',
    routes: [
      {
        path: '/analytics',
        redirectTo: `${BASE_LAYOUT}/analytics/sessioninsights`,
        name: 'SESSION_INSIGHTS',
        perm: 'EDGE_CONNECTOR_DASHBOARD',
        liclass: 'nav-menu-list-item',
      },
      {
        path: '/analytics',
        redirectTo: `${BASE_LAYOUT}/analytics/dnsinsights`,
        name: 'DNS_INSIGHTS',
        perm: 'EDGE_CONNECTOR_DASHBOARD',
        liclass: 'nav-menu-list-item',
      },
      {
        path: '/analytics',
        redirectTo: `${BASE_LAYOUT}/analytics/tunnelinsights`,
        name: 'TUNNEL_INSIGHTS',
        perm: 'EDGE_CONNECTOR_DASHBOARD',
        liclass: 'nav-menu-list-item',
      },
    ],
  },
];
const ANALYTICS_SSO_ROUTES = [
  {
    // relayPath: '#insights/tunnel',
    // name: 'ZIA Tunnel Insights',
    // perm: 'reportAccess',
  },
];

const AUTHENTICATION_CONFIGURATION_SSO_ROUTES = [
//   {
//     relayPath: '#administration/api-key-management',
//     name: 'Cloud Service API Key Management',
//     perm: 'APIKEY_MANAGEMENT',
//   },
];

const ADMIN_CONTROL_SSO_ROUTES = [
  // {
  //   relayPath: '#administration/admin-management',
  //   name: 'Administrator Management',
  //   perm: 'EDGE_CONNECTOR_ADMIN_MANAGEMENT',
  // },
  // {
  //   relayPath: '#administration/role-management',
  //   name: 'Role Management',
  //   perm: 'EDGE_CONNECTOR_ADMIN_MANAGEMENT',
  // },
];

const NSS_SSO_ROUTES = [
  // {
  //   relayPath: '#administration/nss-settings',
  //   name: 'Nanolog Streaming Service',
  //   perm: 'EDGE_CONNECTOR_NSS_CONFIGURATION',
  // },
];

const FORWARDING_METHODS_SSO_ROUTES = [
  // {
  //   relayPath: '#administration/cloud-connector-zia-gateways',
  //   name: 'Gateways',
  //   perm: 'EDGE_CONNECTOR_FORWARDING',
  // },
];

const FILTERING_SSO_ROUTES = [
  // {
  //   relayPath: '#administration/ip-domain-groups',
  //   name: 'IP Groups',
  //   perm: 'EDGE_CONNECTOR_FORWARDING',
  // },
  // {
  //   relayPath: '#administration/network-services',
  //   name: 'Network Services',
  //   perm: 'EDGE_CONNECTOR_FORWARDING',
  // },
];

const FORWARDING_CONTROL_SSO_ROUTES = [
  // {
  //   relayPath: '#policy/firewall/cloud-connector-traffic-forwarding-control',
  //   name: 'Traffic Forwarding',
  //   perm: 'EDGE_CONNECTOR_FORWARDING',
  // },
  // {
  //   relayPath: '#policy/firewall/cloud-connector-log-and-control-forwarding-control',
  //   name: 'Log and Control Forwarding',
  //   perm: 'EDGE_CONNECTOR_FORWARDING',
  // },
  // {
  //   relayPath: '#policy/firewall/cloud-connector-dns-control',
  //   name: 'DNS Policies',
  //   perm: 'EDGE_CONNECTOR_FORWARDING',
  // },
];

const local = localStorage.getItem('configData');
const configData = (local && local.length) ? JSON.parse(local) : {};
const enablePhysicalConfig = verifyConfigData({ configData, key: 'enablePhysicalConfig' });
// const apiSubscriptions = localStorage.getItem('apiSubscriptions');
// const subscriptions = (apiSubscriptions && apiSubscriptions.length)
//   ? JSON.parse(apiSubscriptions)?.filter(x => x.subscribed)?.map(x => x.id)
//   : [];
// const has5G = has5Gsku(subscriptions);

const ADMIN_SUB_NAV_ROUTES = (configurationData) => {
  const enableCCA = verifyConfigData({ configData: configurationData, key: 'enableCCA' });
  const enableZTGateway = verifyConfigData({ configData: configurationData, key: 'enableZTGateway' });
  return [
    {
      title: 'AUTHENTICATION_CONFIGURATION',
      perm: 'APIKEY_MANAGEMENT',
      routes: [
        {
          path: '/administration',
          redirectTo: `${BASE_LAYOUT}/administration/api-key-management`,
          name: 'API_KEY_MANAGEMENT',
          liclass: 'nav-menu-list-item',
          perm: 'APIKEY_MANAGEMENT',
        },
      ],
    },
    {
      title: 'CONNECTOR_MANAGEMENT',
      perm: 'EDGE_CONNECTOR_CLOUD_PROVISIONING',
      routes: [
        {
          path: '/administration',
          redirectTo: `${BASE_LAYOUT}/administration/cloud-connector-groups`,
          name: 'CLOUD_CONNECTOR_GROUPS',
          liclass: 'nav-menu-list-item',
          perm: 'EDGE_CONNECTOR_CLOUD_PROVISIONING',
        },
      ],
    },
    {
      title: 'BRANCH_MANAGEMENT',
      perm: 'EDGE_CONNECTOR_CLOUD_PROVISIONING',
      routes: [
        {
          path: '/administration',
          redirectTo: `${BASE_LAYOUT}/administration/branch-devices/virtual`,
          name: 'BRANCH_DEVICES',
          liclass: 'nav-menu-list-item',
          perm: 'EDGE_CONNECTOR_CLOUD_PROVISIONING',
        },
      ],
    },
    {
      title: 'LOCATION_MANAGEMENT',
      perm: ['EDGE_CONNECTOR_LOCATION_MANAGEMENT', 'EDGE_CONNECTOR_TEMPLATE'],
      routes: [
        {
          path: '/administration',
          redirectTo: `${BASE_LAYOUT}/administration/locations`,
          name: 'LOCATIONS',
          perm: ['EDGE_CONNECTOR_LOCATION_MANAGEMENT'],
          liclass: 'nav-menu-list-item',
        },
        {
          path: '/administration',
          redirectTo: `${BASE_LAYOUT}/administration/location-templates`,
          name: 'LOCATION_TEMPLATES',
          liclass: 'nav-menu-list-item',
          perm: ['EDGE_CONNECTOR_TEMPLATE'],
        },
      ],
    },
    {
      title: 'PROVISIONING_CONTROL',
      perm: ['EDGE_CONNECTOR_TEMPLATE'],
      routes: [
        {
          path: '/administration',
          redirectTo: `${BASE_LAYOUT}/administration/provisioning-templates`,
          name: 'PROVISIONING_AND_CONFIGUATION',
          perm: ['EDGE_CONNECTOR_CLOUD_PROVISIONING', 'EDGE_CONNECTOR_TEMPLATE'],
          liclass: 'nav-menu-list-item',
        },
      ],
    },
    // ZS-14677
    // {
    //   title: 'THIRD PARTY CLOUD MANAGEMENT',
    //   perm: 'EDGE_CONNECTOR_CLOUD_PROVISIONING',
    //   routes: [
    //     {
    //       path: '/administration',
    //       redirectTo: '/admin/administration/cloudproviders-aws',
    //       name: 'AWS_CLOUD',
    //       liclass: 'nav-menu-list-item',
    //       perm: 'EDGE_CONNECTOR_CLOUD_PROVISIONING',
    //     },
    //     {
    //       path: '/administration',
    //       redirectTo: '/admin/administration/cloudproviders-azure',
    //       name: 'AZURE_CLOUD',
    //       liclass: 'nav-menu-list-item',
    //       perm: 'EDGE_CONNECTOR_CLOUD_PROVISIONING',
    //     },
    //   ],
    // },
    {
      title: 'GATEWAYS',
      perm: 'EDGE_CONNECTOR_FORWARDING',
      routes: [
        {
          path: '/administration',
          redirectTo: `${BASE_LAYOUT}/administration/gateways`,
          name: 'ZIA',
          liclass: 'nav-menu-list-item',
        },
        {
          path: '/administration',
          redirectTo: `${BASE_LAYOUT}/administration/log-and-control-gateways`,
          name: 'LOG_AND_CONTROL',
          liclass: 'nav-menu-list-item',
        },
        {
          path: '/administration',
          redirectTo: `${BASE_LAYOUT}/administration/dns-gateways`,
          name: 'DNS',
          liclass: 'nav-menu-list-item',
        },
      ],
    },
    {
      title: 'ADMINISTRATION_CONTROL',
      perm: 'EDGE_CONNECTOR_ADMIN_MANAGEMENT',
      routes: [
        {
          path: '/administration',
          redirectTo: `${BASE_LAYOUT}/administration/admin-management`,
          name: 'ADMIN_MANAGEMENT',
          liclass: 'nav-menu-list-item',
        },
        {
          path: '/administration',
          redirectTo: `${BASE_LAYOUT}/administration/role-management`,
          name: 'ROLE_MANAGEMENT',
          liclass: 'nav-menu-list-item',
        },
        {
          path: '/administration',
          redirectTo: `${BASE_LAYOUT}/administration/audit-logs`,
          name: 'AUDIT_LOGS',
          liclass: 'nav-menu-list-item',
          perm: '',
        },
      ],
    },
    // {
    //   title: 'CELLULAR',
    //   perm: 'EDGE_CONNECTOR_CELLULAR',
    //   routes: [
    //     {
    //       path: '/administration',
    //       redirectTo: '/admin/administration/cellular/deployment-configuration',
    //       isNew: true,
    //       name: 'CELLULAR_DEPLOYMENT_CONFIGURATION',
    //       liclass: 'nav-menu-list-item',
    //     },
    //     {
    //       path: '/administration',
    //       isNew: true,
    //       redirectTo: '/admin/administration/cellular/user-plane-function',
    //       name: 'USER_PLANE_FUNCTION',
    //       liclass: 'nav-menu-list-item',
    //     },
    //   ],
    // },
    {
      title: 'FILTERING',
      perm: 'EDGE_CONNECTOR_FORWARDING',
      routes: [
        {
          path: '/administration',
          redirectTo: `${BASE_LAYOUT}/administration/source-ip-groups`,
          name: 'SOURCE_IP_GROUPS',
          liclass: 'nav-menu-list-item',
        },
        {
          path: '/administration',
          redirectTo: `${BASE_LAYOUT}/administration/destination-ip-groups`,
          name: 'DESTINATION_IP_GROUPS',
          liclass: 'nav-menu-list-item',
        },
        {
          path: '/administration',
          redirectTo: `${BASE_LAYOUT}/administration/ip-pool`,
          name: 'IP_POOL',
          liclass: 'nav-menu-list-item',
        },
        {
          path: '/administration',
          redirectTo: `${BASE_LAYOUT}/administration/network-services`,
          name: 'NETWORK_SERVICES',
          liclass: 'nav-menu-list-item',
        },
      ],
    }, {
      title: 'CLOUD_AUTOMATION_SCRIPTS',
      perm: 'EDGE_CONNECTOR_CLOUD_PROVISIONING',
      routes: [
        {
          path: '/administration',
          redirectTo: `${BASE_LAYOUT}/administration/deployment-templates`,
          name: 'DEPLOYMENT_TEMPLATES',
          liclass: 'nav-menu-list-item',
          perm: 'EDGE_CONNECTOR_CLOUD_PROVISIONING',
        },
      // {
      //   path: '/administration',
      //   redirectTo: '/admin/administration/automation-scripts-azure',
      //   name: 'AZURE_CLOUD',
      //   liclass: 'nav-menu-list-item',
      //   perm: 'EDGE_CONNECTOR_CLOUD_PROVISIONING',
      // },
      // {
      //   path: '/administration',
      //   redirectTo: '/admin/administration/automation-scripts-other',
      //   name: 'OTHER_CLOUDS',
      //   liclass: 'nav-menu-list-item',
      //   perm: 'EDGE_CONNECTOR_CLOUD_PROVISIONING',
      // },
      ],
    },
    ...(enableCCA ? [{
      title: 'CLIENT_CONNECTOR_FOR_VDI',
      perm: 'EDGE_CONNECTOR_CLOUD_PROVISIONING',
      routes: [
        {
          path: '/administration',
          redirectTo: `${BASE_LAYOUT}/administration/vdi/agent-templates`,
          isNew: true,
          name: 'VDI_AGENT_TEMPLATES',
          liclass: 'nav-menu-list-item',
        },
        {
          path: '/administration',
          isNew: true,
          redirectTo: `${BASE_LAYOUT}/administration/vdi/agent-forwarding-profile`,
          name: 'VDI_AGENT_FORWARDING_PROFILE',
          liclass: 'nav-menu-list-item',
        },
        {
          path: '/administration',
          isNew: true,
          redirectTo: `${BASE_LAYOUT}/administration/vdi/device-management/devices`,
          name: 'VDI_DEVICE_MANAGEMENT',
          liclass: 'nav-menu-list-item',
        },
        {
          path: '/administration',
          isNew: true,
          redirectTo: `${BASE_LAYOUT}/administration/vdi/agent-app/ga`,
          name: 'VDI_AGENT_APP',
          liclass: 'nav-menu-list-item',
        },
      ],
    }] : []),
    {
      title: 'CLOUD_CONFIGURATION',
      perm: ['EDGE_CONNECTOR_NSS_CONFIGURATION', 'EDGE_CONNECTOR_ADMIN_MANAGEMENT'],
      routes: [
        {
          path: '/administration',
          redirectTo: `${BASE_LAYOUT}/administration/nss-settings-servers`,
          name: 'NANOLOG_STREAMING_SERVICES',
          liclass: 'nav-menu-list-item',
          perm: 'EDGE_CONNECTOR_NSS_CONFIGURATION',
        },
        {
          path: '/administration',
          redirectTo: `${BASE_LAYOUT}/administration/cloud-configuration-advanced-settings`,
          name: 'ADVANCED_SETTINGS',
          liclass: 'nav-menu-list-item',
          perm: 'EDGE_CONNECTOR_ADMIN_MANAGEMENT',
        },
      ],
    },
    {
      title: 'IMAGES',
      perm: 'EDGE_CONNECTOR_CLOUD_PROVISIONING',
      routes: [
        {
          path: '/administration',
          redirectTo: `${BASE_LAYOUT}/administration/branch-connector-images`,
          name: 'BRANCH_CONNECTOR_IMAGES',
          liclass: 'nav-menu-list-item',
          perm: 'EDGE_CONNECTOR_CLOUD_PROVISIONING',
        },
      ],
    },
    ...(enablePhysicalConfig ? [{
      title: 'HARDWARE_MANAGEMENT',
      perm: 'EDGE_CONNECTOR_CLOUD_PROVISIONING',
      routes: [
        {
          path: '/administration',
          redirectTo: `${BASE_LAYOUT}/administration/appliance-management/appliances`,
          isNew: true,
          name: 'ZT_DEVICES',
          liclass: 'nav-menu-list-item',
          perm: 'EDGE_CONNECTOR_CLOUD_PROVISIONING',
        },
      ],
    }] : []),
    {
      title: 'PUBLIC_CLOUD_COFIGURATION',
      perm: 'EDGE_CONNECTOR_CLOUD_PROVISIONING',
      routes: [
        {
          isNew: true,
          path: '/administration',
          redirectTo: `${BASE_LAYOUT}/administration/partner-integrations/aws`,
          name: 'PARTNER_INTEGRATIONS',
          liclass: 'nav-menu-list-item',
          perm: 'EDGE_CONNECTOR_CLOUD_PROVISIONING',
        },
        ...(enableZTGateway ? [{
          isNew: true,
          path: '/administration',
          redirectTo: `${BASE_LAYOUT}/administration/zero-trust-gateway`,
          name: 'ZERO_TRUST_GATEWAY',
          liclass: 'nav-menu-list-item',
          perm: 'EDGE_CONNECTOR_CLOUD_PROVISIONING',
        }] : []),
      ],
    },
  ];
};

const POLICY_SUB_NAV_ROUTES = [
  {
    title: 'POLICY_MANAGEMENT',
    perm: 'EDGE_CONNECTOR_FORWARDING',
    routes: [
      {
        path: '/policy',
        redirectTo: `${BASE_LAYOUT}/policy/cloud-connector-traffic-forwarding-control`,
        name: 'TRAFFIC_FORWARDING',
        perm: 'EDGE_CONNECTOR_FORWARDING',
        liclass: 'nav-menu-list-item',
      },
      {
        path: '/policy',
        redirectTo: `${BASE_LAYOUT}/policy/cloud-connector-log-and-control-forwarding-control`,
        name: 'LOG_AND_CONTROL_FORWARDING',
        perm: 'EDGE_CONNECTOR_FORWARDING',
        liclass: 'nav-menu-list-item',
      },
      {
        path: '/policy',
        redirectTo: `${BASE_LAYOUT}/policy/cloud-connector-dns-control`,
        name: 'DNS_POLICY',
        perm: 'EDGE_CONNECTOR_FORWARDING',
        liclass: 'nav-menu-list-item',
      },
      {
        path: '/policy',
        redirectTo: `${BASE_LAYOUT}/policy/edge-connector-traffic-forwarding-control`,
        name: 'TRAFFIC_FORWARDING',
        perm: 'EDGE_CONNECTOR_FORWARDING',
        liclass: 'nav-menu-list-item hidden',
      },
      {
        path: '/policy',
        redirectTo: `${BASE_LAYOUT}/policy/edge-connector-log-and-control-forwarding-control`,
        name: 'LOG_AND_CONTROL_FORWARDING',
        perm: 'EDGE_CONNECTOR_FORWARDING',
        liclass: 'nav-menu-list-item hidden',
      },
      {
        path: '/policy',
        redirectTo: `${BASE_LAYOUT}/policy/edge-connector-dns-control`,
        name: 'DNS_POLICY',
        perm: 'EDGE_CONNECTOR_FORWARDING',
        liclass: 'nav-menu-list-item hidden',
      },
    ],
  },
];
const HELP_SUB_NAV_ROUTES = [
  {
    title: 'SUPPORT',
    routes: [
      {
        path: '/help',
        redirectTo: 'https://help.zscaler.com/cloud-branch-connector',
        name: 'ZSCALER_HELP_PORTAL',
        externalLink: true,
        liclass: 'nav-menu-list-item',
      },
      {
        path: '/help',
        redirectTo: 'remote-assistance',
        name: 'REMOTE_ASSISTANCE',
        externalLink: false,
        perm: 'REMOTE_ASSISTANCE_MANAGEMENT',
        liclass: 'nav-menu-list-item',
      },
      {
        path: '/help',
        redirectTo: 'submit-ticket',
        name: 'SUBMIT_TICKET',
        externalLink: false,
        liclass: 'nav-menu-list-item',
      },
      {
        path: '/help',
        redirectTo: 'https://config.zscaler.com/zscaler.net/fcr',
        name: 'CLOUD_CONFIG_REQUIREMENTS',
        externalLink: true,
        liclass: 'nav-menu-list-item',
      },
    ],
  },
  // {
  //   title: 'SECURITY',
  //   perm: 'EDGE_CONNECTOR_FORWARDING',
  //   routes: [
  //     {
  //       path: '/help',
  //       redirectTo: 'https://threatlibrary.zscaler.com/',
  //       name: 'THREAT_LIBRARY',
  //       externalLink: true,
  //       liclass: 'nav-menu-list-item',
  //     },
  //     {
  //       path: '/help',
  //       redirectTo: 'https://zulu.zscaler.com/',
  //       name: 'ZULU',
  //       externalLink: true,
  //       liclass: 'nav-menu-list-item',
  //     },
  //     {
  //       path: '/help',
  //       redirectTo: 'https://www.zscaler.com/blogs/security-research',
  //       name: 'RESEARCH_BLOG',
  //       externalLink: true,
  //       liclass: 'nav-menu-list-item',
  //     },
  //   ],
  // },
  // {
  //   title: 'TOOLS',
  //   perm: 'EDGE_CONNECTOR_FORWARDING',
  //   routes: [
  //     {
  //       path: '/help',
  //       redirectTo: 'http://ip.zscaler.com/',
  //       name: 'PROXY_TEST',
  //       externalLink: true,
  //       liclass: 'nav-menu-list-item',
  //     },
  //     {
  //       path: '/help',
  //       redirectTo: 'url-lookup',
  //       name: 'URL_LOOKUP',
  //       externalLink: false,
  //       liclass: 'nav-menu-list-item',
  //     },
  //     {
  //       path: '/help',
  //       redirectTo: 'blacklisted-ip-check',
  //       name: 'BLACKLISTED_IP_CHECK',
  //       externalLink: false,
  //       liclass: 'nav-menu-list-item',
  //     },
  //     {
  //       path: '/help',
  //       redirectTo: 'https://zmtr.zscaler.com/',
  //       name: 'ZSCALER_ANALYZER',
  //       externalLink: true,
  //       liclass: 'nav-menu-list-item',
  //     },
  // ],
  // },
];

// 'ZSCALER_HELP_PORTAL': 'Zscaler Help Portal',
// 'REMOTE_ASSISTANCE': 'Remote Assistance',
// 'SUBMIT_TICKET': 'Submit a Ticket',
// 'CLOUD_CONFIG_REQUIREMENTS': 'Cloud Configuration Requirements',

// 'THREAT_LIBRARY': 'Threat Library',
// 'ZULU': 'Zulu',
// 'RESEARCH_BLOG': 'ThreatLabz | Security Research',

// 'PROXY_TEST': 'Proxy Test',
// 'URL_LOOKUP': 'URL Lookup',
// 'BLACKLISTED_IP_CHECK': 'Blacklisted IP Check',
// 'ZSCALER_ANALYZER': 'Zscaler Analyzer',

const APP_SLIDES_TO_SHOW = 3;

/* for EdgeConnector */
const RULE_ORDER = [
  { id: '1', name: '1' },
  { id: '2', name: '2' },
  { id: '3', name: '3' },
  { id: 'Default', name: 'Default' },
];

const FWD_METHOD = [
  { id: 'ECZPA', name: 'ECZPA' },
  { id: 'ZIA', name: 'ZIA' },
  { id: 'Direct', name: 'Direct' },
];

const RULE_STATUS = [
  { id: 'Enabled', name: 'Enabled' },
  { id: 'Disabled', name: 'Disabled' },
];

const FWD_POLICY_CRITERIA_ALL = [{
  id: 'general',
  title: 'General',
  to: '/',
},
{
  id: 'categories-services',
  title: 'Categories & Services',
  to: '/',
},
{
  id: 'source',
  title: 'Source',
  to: '/',
},
{
  id: 'destination',
  title: 'Destination',
  to: '/',
}];

const FWD_POLICY_CRITERIA_MIN = [{
  id: 'general',
  title: 'General',
  to: '/',
},
{
  id: 'source',
  title: 'Source',
  to: '/',
},
{
  id: 'destination',
  title: 'Destination',
  to: '/',
}];

const EC_GROUPS = [
  { id: 'EC Group 1', name: 'EC Group 1' },
  { id: 'EC Group 2', name: 'EC Group 2' },
];

const EDGECONNECTORS = [
  { id: 'Edgeconnector 1', name: 'Edgeconnector 1' },
  { id: 'Edgeconnector 2', name: 'Edgeconnector 2' },
];

const MANUAL_GATEWAY = [
  { id: 'Gateway 1', name: 'Gateway 1' },
  { id: 'Gateway 2', name: 'Gateway 2' },
];

const IP_CATEGORY = [
  { id: 'IP 1', name: 'IP 1' },
  { id: 'IP 2', name: 'IP 2' },
];

const NETWORK_SERVICE = [
  { id: 'NW Service 1', name: 'NW Service 1' },
  { id: 'NW Service 2', name: 'NW Service 2' },
];

const NETWORK_SERVICE_GROUP = [
  { id: 'NW Service Group 1', name: 'NW Service Group 1' },
  { id: 'NW Service Group 2', name: 'NW Service Group 2' },
];

const SOURCE_IP_GROUP = [
  { id: 'Source IP Group 1', name: 'Source IP Group 1' },
  { id: 'Source IP Group 2', name: 'Source IP Group 2' },
];

const DESTINATION_IP_FQDN_GROUP = [
  { id: 'Destination IP Group 1', name: 'Destination IP Group 1' },
  { id: 'Destination IP Group 2', name: 'Destination IP Group 2' },
];

const DESTINATION_COUNTRY = [
  { id: 'USA', name: 'USA' },
  { id: 'Canada', name: 'Canada' },
  { id: 'India', name: 'India' },
];

const EXISTING_LOC = [
  { id: 65761, name: 'BNG' },
  { id: 65760, name: 'test' },
];

const TOTAL_EC_DATA = {
  pieData: [
    {
      id: 'Active',
      label: 'Active',
      value: 44,
    },
    {
      id: 'Registered',
      label: 'Registered',
      value: 12,
    },
    {
      id: 'Provisioned',
      label: 'Provisioned',
      value: 10,
    },
  ],
  colors: ['#6AA920', '#FF8620', '#18829D'],
  innerText: 'Deployed',
};

const ACTIVE_HEALTH_DATA = {
  pieData: [
    {
      id: 'Healthy',
      label: 'Healthy',
      value: 40,
    },
    {
      id: 'Unhealthy',
      label: 'Unhealthy',
      value: 4,
    },
  ],
  colors: ['#73A13C', '#EC564F'],
  innerText: 'Active',
};

const STATIC_THROUGHPUT_PIE = {
  pieData: [
    {
      id: 'ZIA',
      label: 'ZIA',
      value: 219.82,
    },
    {
      id: 'ECZPA',
      label: 'ECZPA',
      value: 0,
    },
    {
      id: 'Direct',
      label: 'Direct',
      value: 0,
    },
  ],
  colors: [
    '#77B347',
    '#5DB5CB',
    '#FFB700',
  ],
  innerText: 'MBps',
};

const STATIC_SESSION_PIE = {
  pieData: [
    {
      id: 'ZIA',
      label: 'ZIA',
      value: 5086,
    },
    {
      id: 'ECZPA',
      label: 'ECZPA',
      value: 0,
    },
    {
      id: 'Direct',
      label: 'Direct',
      value: 0,
    },
  ],
  colors: [
    '#77B347',
    '#5DB5CB',
    '#FFB700',
  ],
  innerText: 'MBps',
};
const GEO_DATA = [{
  id: 12,
  pinSize: 15,
  pinColor: '#0076BE', // dot $blue2: #0076BE;
  geoLocation: {
    longitude: -70.891000,
    latitude: 50.538745,
  },
  content: [{
    name: 'GROUPS',
    value: '3',
  }, {
    name: 'EDGECONNECTORS',
    value: '15',
  }],
  header: 'OT1',
  pinIcon: faBuilding,
  icon: faBuilding,
  deploymentType: 'AWS',
  group: 'CC Group 15',
  availabilityZone: 'N. Califorina',
  manIpAddr: '*********',
  serviceIpAddr1: '***********',
  vmSize: 'Medium',
  ziaTraffic: '10/10,000',
  zpaTraffic: '20/20,2000',
  directTraffic: '3/300',
}, {
  id: 122,
  pinSize: 15,
  pinColor: '#0076BE', // dot $blue2: #0076BE;
  geoLocation: {
    longitude: -115.845312,
    latitude: 33.686068,
  },
  content: [{
    name: 'GROUPS',
    value: '4',
  }, {
    name: 'EDGECONNECTORS',
    value: '14',
  }],
  header: 'LA1',
  pinIcon: faBuilding,
  icon: faBuilding,
  deploymentType: 'AWS',
  group: 'CC Group 15',
  availabilityZone: 'N. Califorina',
  manIpAddr: '*********',
  serviceIpAddr1: '***********',
  vmSize: 'Medium',
  ziaTraffic: '10/10,000',
  zpaTraffic: '20/20,2000',
  directTraffic: '3/300',
}, {
  id: 123,
  pinSize: 15,
  pinColor: '#0076BE', // dot $blue2: #0076BE;
  geoLocation: {
    longitude: -45.802727,
    latitude: 63.792191,
  },
  content: [{
    name: 'GROUPS',
    value: '1',
  }, {
    name: 'EDGECONNECTORS',
    value: '3',
  }],
  header: 'KN1',
  pinIcon: faBuilding,
  icon: faBuilding,
  deploymentType: 'AWS',
  group: 'CC Group 15',
  availabilityZone: 'N. Califorina',
  manIpAddr: '*********',
  serviceIpAddr1: '***********',
  vmSize: 'Medium',
  ziaTraffic: '10/10,000',
  zpaTraffic: '20/20,2000',
  directTraffic: '3/300',
}, {
  id: 120000,
  pinSize: 15,
  pinColor: '#0076BE', // dot $blue2: #0076BE;
  geoLocation: {
    longitude: -117.511207,
    latitude: 48,
  },
  content: [{
    name: 'GROUPS',
    value: '4',
  }, {
    name: 'EDGECONNECTORS',
    value: '14',
  }],
  color: colors.green10,
  header: 'SJ1',
  pinIcon: faBuilding,
  icon: faBuilding,
  deploymentType: 'AWS',
  group: 'CC Group 15',
  availabilityZone: 'N. Califorina',
  manIpAddr: '*********',
  serviceIpAddr1: '***********',
  vmSize: 'Medium',
  ziaTraffic: '10/10,000',
  zpaTraffic: '20/20,2000',
  directTraffic: '3/300',
}, {
  id: 120002,
  pinSize: 15,
  pinColor: '#0076BE', // dot $blue2: #0076BE;
  geoLocation: {
    longitude: -121.988571,
    latitude: 37.548271,
  },
  content: [{
    name: 'GROUPS',
    value: '4',
  }, {
    name: 'EDGECONNECTORS',
    value: '334',
  }],
  color: colors.green10,
  header: 'FM1',
  pinIcon: faBuilding,
  icon: faBuilding,
  deploymentType: 'AWS',
  group: 'CC Group 15',
  availabilityZone: 'N. Califorina',
  manIpAddr: '*********',
  serviceIpAddr1: '***********',
  vmSize: 'Medium',
  ziaTraffic: '10/10,000',
  zpaTraffic: '20/20,2000',
  directTraffic: '3/300',
}, {
  id: 120003,
  pinSize: 15,
  pinColor: '#0076BE', // dot $blue2: #0076BE;
  geoLocation: {
    longitude: -122.036346,
    latitude: 37.368832,
  },
  content: [{
    name: 'GROUPS',
    value: '4',
  }, {
    name: 'EDGECONNECTORS',
    value: '334',
  }],
  color: colors.green10,
  header: 'SYL',
  pinIcon: faBuilding,
  icon: faBuilding,
  deploymentType: 'AWS',
  group: 'CC Group 15',
  availabilityZone: 'N. Califorina',
  manIpAddr: '*********',
  serviceIpAddr1: '***********',
  vmSize: 'Medium',
  ziaTraffic: '10/10,000',
  zpaTraffic: '20/20,2000',
  directTraffic: '3/300',
}, {
  id: 120001,
  pinSize: 15,
  pinColor: '#0076BE', // dot $blue2: #0076BE;
  geoLocation: {
    longitude: -105.604208,
    latitude: 38.774295,
  },
  content: [{
    name: 'IP ADDRESS',
    value: '************/4',
  }, {
    name: 'GROUP',
    value: 'EC Group1',
  }],
  header: 'EC1',
  pinIcon: faCodeBranch,
  icon: faCodeBranch,
  deploymentType: 'AWS',
  group: 'CC Group 15',
  availabilityZone: 'N. Califorina',
  manIpAddr: '*********',
  serviceIpAddr1: '***********',
  vmSize: 'Medium',
  ziaTraffic: '10/10,000',
  zpaTraffic: '20/20,2000',
  directTraffic: '3/300',
},
{
  id: 120004,
  pinSize: 15,
  pinColor: '#0076BE', // dot $blue2: #0076BE;
  geoLocation: {
    longitude: -85.131043,
    latitude: 32.723754,
  },
  content: [{
    name: 'GROUPS',
    value: '1',
  }, {
    name: 'EDGECONNECTORS',
    value: '3',
  }],
  header: 'EV1',
  pinIcon: faCodeBranch,
  icon: faCodeBranch,
  deploymentType: 'AWS',
  group: 'CC Group 15',
  availabilityZone: 'N. Califorina',
  manIpAddr: '*********',
  serviceIpAddr1: '***********',
  vmSize: 'Medium',
  ziaTraffic: '10/10,000',
  zpaTraffic: '20/20,2000',
  directTraffic: '3/300',
}];

const EDGECONNECTORS_NW_DATA = [{
  id: 12,
  pinSize: 15,
  pinColor: '#0076BE', // dot $blue2: #0076BE;
  geolocation: {
    longitude: -70.891000,
    latitude: 50.538745,
  },
  content: [{
    name: 'GROUPS',
    value: '3',
  }, {
    name: 'EDGECONNECTORS',
    value: '15',
  }],
  header: 'OT1',
  // pinIcon: faBuilding,
  icon: faBuilding,
}, {
  id: 122,
  pinSize: 15,
  pinColor: '#0076BE', // dot $blue2: #0076BE;
  geolocation: {
    longitude: -115.845312,
    latitude: 33.686068,
  },
  content: [{
    name: 'GROUPS',
    value: '4',
  }, {
    name: 'EDGECONNECTORS',
    value: '14',
  }],
  header: 'LA1',
  // pinIcon: faBuilding,
  icon: faBuilding,
}, {
  id: 123,
  pinSize: 15,
  pinColor: '#0076BE', // dot $blue2: #0076BE;
  geolocation: {
    longitude: -45.802727,
    latitude: 63.792191,
  },
  content: [{
    name: 'GROUPS',
    value: '1',
  }, {
    name: 'EDGECONNECTORS',
    value: '3',
  }],
  header: 'KN1',
  icon: faBuilding,
}, {
  id: 120000,
  pinSize: 15,
  pinColor: '#0076BE', // dot $blue2: #0076BE;
  geolocation: {
    longitude: -117.511207,
    latitude: 48,
  },
  content: [{
    name: 'GROUPS',
    value: '4',
  }, {
    name: 'EDGECONNECTORS',
    value: '14',
  }],
  color: colors.green10,
  header: 'SJ1',
  pinIcon: faBuilding,
  icon: faBuilding,
}, {
  id: 120001,
  pinSize: 15,
  pinColor: '#0076BE', // dot $blue2: #0076BE;
  geolocation: {
    longitude: -105.604208,
    latitude: 38.774295,
  },
  content: [{
    name: 'IP ADDRESS',
    value: '************/4',
  }, {
    name: 'GROUP',
    value: 'EC Group1',
  }],
  header: 'EC1',
  pinIcon: faCodeBranch,
  icon: faCodeBranch,
},
{
  id: 120004,
  pinSize: 15,
  pinColor: '#0076BE', // dot $blue2: #0076BE;
  geolocation: {
    longitude: -85.131043,
    latitude: 32.723754,
  },
  content: [{
    name: 'GROUPS',
    value: '1',
  }, {
    name: 'EDGECONNECTORS',
    value: '3',
  }],
  header: 'EV1',
  // pinIcon: faCodeBranch,
  icon: faCodeBranch,
}];

const LOCATION_MAP_DATA = [{
  id: 12,
  pinSize: 15,
  pinColor: '#0076BE', // dot $blue2: #0076BE;
  geolocation: {
    longitude: -70.891000,
    latitude: 50.538745,
  },
  header: 'OT1',
  icon: faBuilding,
}];

const MANAGEMENT_IP_GROUP = [
  { id: 'managementIPDynamic', name: 'DHCP Management IP' },
  { id: 'managementIPManual', name: 'Manually set Management IP' },
];

const LOGS_EVENT_TIME = [
  { label: '11:30:45 AM, September 28, 2019', value: '11:30:45 AM, September 28, 2019' },
  { label: '12:30:45 AM, September 28, 2019', value: '12:30:45 AM, September 28, 2019' },
  { label: '1:30:45 AM, September 28, 2019', value: '1:30:45 AM, September 28, 2019' },
  { label: '2:30:45 AM, September 28, 2019', value: '2:30:45 AM, September 28, 2019' },
  { label: '3:30:45 AM, September 28, 2019', value: '3:30:45 AM, September 28, 2019' },
];

const LOGS_EC_LOCATION = [
  { label: 'San Jose', value: 'San Jose' },
  { label: 'Santa Clara', value: 'Santa Clara' },
  { label: 'Los Angeles', value: 'Los Angeles' },
  { label: 'Union City', value: 'Union City' },
];

const LOGS_EC_VERSION = [
  { label: '1.1', value: '1.1' },
  { label: '1.2', value: '1.2' },
  { label: '1.3', value: '1.3' },
  { label: '1.4', value: '1.4' },
];

const TREND_TIME_FRAME_DATA = [
  {
    id: 'last_4_hrs', name: '4_HOURS', label: '4_HOURS', value: '4_HOURS',
  },
  {
    id: 'last_24_hrs', name: '24_HOURS', label: '24_HOURS', value: '24_HOURS',
  },
  {
    id: 'last_1_hour', name: '1_HOUR', label: '1_HOUR', value: '1_HOUR',
  },
  {
    id: 'last_1_week', name: '1_WEEK', label: '1_WEEK', value: '1_WEEK',
  },
  {
    id: 'last_1_month', name: '1_MONTH', label: '1_MONTH', value: '1_MONTH',
  },
];

const TREND_DETAIL_DATA = [
  {
    id: 'SHOW_DETAILS', name: 'SHOW_DETAILS', label: 'SHOW_DETAILS', value: 'SHOW_DETAILS',
  },
  {
    id: 'TOTAL', name: 'TOTAL', label: 'TOTAL', value: 'TOTAL',
  },
];

const PROTOCOL_TYPES = [
  { id: 'ALL', name: 'ALL' },
  { id: 'TCP', name: 'TCP' },
  { id: 'UDP', name: 'UDP' },
];

const REQ_ACTION = [
  { id: 'BOTH_REQ_RESP_ALLOW', name: 'Allow' },
  { id: 'EITHER_REQ_RESP_BLOCK', name: 'Block' },
  { id: 'EITHER_REQ_RESP_REDIRECT_NO_BLOCK', name: 'Redirect' },
  { id: 'RESOLVED_BY_ZPA', name: 'Resolved' },
];

const TRAFFIC_FWD_METHOD = [
  { id: 'DIRECT', name: 'DIRECT' },
  { id: 'LOCAL_SWITCH', name: 'LOCAL_SWITCH' },
  { id: 'ECDIRECTSCTPXFORM', name: 'ECDIRECTSCTPXFORM' },
  { id: 'ZIA', name: 'ZIA' },
  { id: 'ECZPA', name: 'ZPA' },
  // { id: 'ECZPASCTPXFORM', name: 'ECZPASCTPXFORM' },
  { id: 'DROP', name: 'DROP' },
];

const EC_DIRECTION = [
  { id: 'RX', name: 'RX' },
  { id: 'TX', name: 'TX' },
];

const EC_TRAFFIC_TYPE = [
  { id: 'ANY', name: 'ANY' },
  { id: 'DEFAULT', name: 'DEFAULT' },
  { id: 'INTERNAL', name: 'INTERNAL' },
  { id: 'EXTERNAL', name: 'EXTERNAL' },
];

const TRAFFIC_DIRECTION = [
  { id: 'INBOUND', name: 'INBOUND' },
  { id: 'OUTBOUND', name: 'OUTBOUND' },
];

const RESOLVER = [
  { id: 'ZIA', name: 'ZIA' },
  { id: 'DIRECT', name: 'DIRECT' },
  { id: 'ECSELF', name: 'CLOUD_CONNECTOR' },
  // "DIRECT": {"name": "DIRECT", "val" : "0X1"},
  // "DROP"  : {"name": "DROP"  , "val" : "0X2"},
  // "ZIA"   : {"name": "ZIA"   , "val" : "0X3"},
  // "ECZPA" : {"name": "ECZPA" , "val" : "0X6"},
  // "ECSELF": {"name": "ECSELF", "val" : "0X7"},
];

const STRING_CONDITIONS = [
  { id: 'CONTAINS', name: 'CONTAINS' },
  { id: 'STARTS_WITH', name: 'STARTS_WITH' },
  { id: 'ENDS_WITH', name: 'ENDS_WITH' },
  { id: 'EXACT_MATCH', name: 'EXACT_MATCH' },
  { id: 'DOES_NOT_CONTAINS', name: 'DOES_NOT_CONTAINS' },
  { id: 'DOES_NOT_ENDS_WITH', name: 'DOES_NOT_ENDS_WITH' },
  { id: 'DOES_NOT_STARTS_WITH', name: 'DOES_NOT_STARTS_WITH' },
  { id: 'NOT_NULL', name: 'NOT_NULL' },
  { id: 'IS_NULL', name: 'IS_NULL' },
];

const DNS_POLICY_ACTION = [
  {
    id: 'BOTH_REQ_RESP_ALLOW',
    name: 'BOTH_REQ_RESP_ALLOW',
    value: 'BOTH_REQ_RESP_ALLOW',
    backendName: 'Allow',
    originalName: 'Allow',
  },
  {
    id: 'EITHER_REQ_RESP_BLOCK',
    name: 'EITHER_REQ_RESP_BLOCK',
    value: 'EITHER_REQ_RESP_BLOCK',
    backendName: 'Block',
    originalName: 'Block',
  },
  {
    id: 'REDIR_ZPA',
    name: 'REDIR_ZPA',
    value: 'REDIR_ZPA',
    backendName: 'Redirect to ZPA',
    originalName: 'Redirect to ZPA',
  },
];

const TUNNEL_INSIGHTS_METRICS = [
  { id: 'DPDRCV', name: 'DPDRCV' },
  { id: 'RX_BYTES', name: 'RX_BYTES' },
  { id: 'RX_PACKETS', name: 'RX_PACKETS' },
  { id: 'TX_BYTES', name: 'TX_BYTES' },
  { id: 'TX_PACKETS', name: 'TX_PACKETS' },
];

const TUNNEL_TYPES = [
  { id: 'SVPN', name: 'SVPN' },
  { id: 'IPSEC', name: 'IPSEC' },
  { id: 'GRE', name: 'GRE' },
];

const ADMINISTRATOR_STATUS = [
  { id: 'enabled', name: 'ENABLED' },
  { id: 'disabled', name: 'DISABLED' },
];

const ADMINISTRATOR_SCOPE = [
  { id: 'ORGANIZATION', name: 'ORGANIZATION' },
  // { id: 'DEPARTMENT', name: 'DEPARTMENT' },
  { id: 'LOCATION', name: 'LOCATION' },
  // { id: 'LOCATION_GROUP', name: 'LOCATION_GROUP' },
];

const INSIGHTS_TIME_FRAME_DATA = [
  { id: 'current_day', name: 'CURRENT_DAY' },
  { id: 'current_week', name: 'CURRENT_WEEK' },
  { id: 'current_month', name: 'CURRENT_MONTH' },
  { id: 'previous_day', name: 'PREV_DAY' },
  { id: 'previous_week', name: 'PREV_WEEK' },
  { id: 'previous_month', name: 'PREV_MONTH' },
  { id: 'custom', name: 'CUSTOM' },
];

const LOGS_TIME_FRAME_DATA = [
  { id: 'last_5_mins', name: 'LAST_5_MINS' },
  { id: 'last_15_mins', name: 'LAST_15_MINS' },
  { id: 'last_30_mins', name: 'LAST_30_MINS' },
  { id: 'last_1_hour', name: 'LAST_1_HOUR' },
  { id: 'last_2_hours', name: 'LAST_2_HOURS' },
  { id: 'last_5_hours', name: 'LAST_5_HOURS' },
  { id: 'last_10_hours', name: 'LAST_10_HOURS' },
  { id: 'last_24_hrs', name: 'LAST_24_HOURS' },
  { id: 'current_day', name: 'CURRENT_DAY' },
  { id: 'current_week', name: 'CURRENT_WEEK' },
  { id: 'current_month', name: 'CURRENT_MONTH' },
  { id: 'previous_day', name: 'PREV_DAY' },
  { id: 'previous_week', name: 'PREV_WEEK' },
  { id: 'previous_month', name: 'PREV_MONTH' },
  { id: 'custom', name: 'CUSTOM' },
];

const AUDIT_LOGS_TIME_FRAME_DATA = [
  { id: 'current_day', name: 'CURRENT_DAY' },
  { id: 'current_week', name: 'CURRENT_WEEK' },
  { id: 'current_month', name: 'CURRENT_MONTH' },
  { id: 'previous_day', name: 'PREV_DAY' },
  { id: 'previous_week', name: 'PREV_WEEK' },
  { id: 'previous_month', name: 'PREV_MONTH' },
];

const AUDIT_LOGS_SEARCH_DATA = [
  { id: 'objectName', name: 'RESOURCE' },
  { id: 'adminName', name: 'ADMIN_ID' },
  { id: 'clientIP', name: 'CLIENT_IP' },
];

const DNS_POLICY_SEARCH_DATA = [
  { id: 'ruleName', name: 'RULE_NAME' },
  { id: 'ruleOrder', name: 'RULE_ORDER' },
  { id: 'ruleDescription', name: 'DESCRIPTION' },
  { id: 'ruleAction', name: 'ACTION' },
  { id: 'location', name: 'LOCATION' },
];

const POLICY_SEARCH_DATA = [
  { id: 'ruleName', name: 'RULE_NAME' },
  { id: 'ruleOrder', name: 'RULE_ORDER' },
  { id: 'ruleDescription', name: 'DESCRIPTION' },
  { id: 'ruleForwardMethod', name: 'TRAFFIC_FORWARDING_METHOD' },
  { id: 'location', name: 'LOCATION' },
];

const TIME_FRAME_DATA = [
  { id: 'last_1_min', name: 'LAST_1_MIN' },
  { id: 'last_2_mins', name: 'LAST_2_MINS' },
  { id: 'last_5_mins', name: 'LAST_5_MINS' },
  { id: 'last_15_mins', name: 'LAST_15_MINS' },
  { id: 'last_30_mins', name: 'LAST_30_MINS' },
  { id: 'last_1_hour', name: 'LAST_1_HOUR' },
  { id: 'last_2_hours', name: 'LAST_2_HOURS' },
  { id: 'last_5_hours', name: 'LAST_5_HOURS' },
  { id: 'last_10_hours', name: 'LAST_10_HOURS' },
  { id: 'last_24_hrs', name: 'LAST_24_HOURS' },
  { id: 'last_1_week', name: 'LAST_1_WEEK' },
  { id: 'last_1_month', name: 'LAST_1_MONTH' },
];

const TIME_FRAME = [
  { label: 'Last 24 Hours', value: 'Last_24_hours' },
  { label: '1 Hour', value: '1_hour' },
  { label: '1 Week', value: '1_week' },
  { label: '1 Month', value: '1_month' },
];

const LOGS_HYPERVISOR_VERSION = [
  { label: 'ESXi 2.0.0', value: 'ESXi 2.0.0' },
  { label: 'ESXi 2.0.1', value: 'ESXi 2.0.1' },
  { label: 'ESXi 2.0.2', value: 'ESXi 2.0.2' },
  { label: 'ESXi 2.0.3', value: 'ESXi 2.0.3' },
];

const LOGS_FWD_METHOD = [
  { label: 'ZPA', value: 'ZPA' },
  { label: 'ZIA', value: 'ZIA' },
  { label: 'Direct', value: 'Direct' },
];

const LOGS_ZIP = [
  { label: '*********', value: '*********' },
  { label: '*********', value: '*********' },
  { label: '*********', value: '*********' },
  { label: '*********', value: '*********' },
  { label: '*********', value: '*********' },
  { label: '*********', value: '*********' },
];

const LOGS_DEST_IP = [
  { label: '111.543.4.22', value: '111.543.4.22' },
  { label: '112.444.3.22', value: '112.444.3.22' },
  { label: '110.444.3.22', value: '110.444.3.22' },
  { label: '111.543.4.10', value: '111.543.4.10' },
  { label: '112.444.3.09', value: '112.444.3.09' },
  { label: '110.444.3.08', value: '110.444.3.08' },
];

const LOGS_EC_GROUP = [
  { label: 'Group 1', value: 'Group 1' },
  { label: 'Group 2', value: 'Group 2' },
  { label: 'Group 4', value: 'Group 4' },
  { label: 'Group 16', value: 'Group 16' },
  { label: 'Group 23', value: 'Group 23' },
];

const LOACTION_TEMPLATE = [
  { id: 'ZS_DEFAULT', name: 'ZS-default-location-template' },
  { id: 'ZS_PRIMARY', name: 'ZS-primary-location-template' },
];

const TIME_ZONE = [{
  name: 'NOT_SPECIFIED',
  id: '0',
},
{
  name: 'GMT_12_00_DATELINE',
  id: '1',
},
];

const STATIC_IP_ADRESSES = [
  { id: '*************', name: '*************' },
  { id: '************', name: '************' },
];
const PROXY_PORTS = [
  { id: '8233', name: '8233' },
  { id: '9233', name: '9233' },
];
const VIRTUAL_ZENS = [
  { id: 'Virtual Zen1', name: 'Virtual Zen1' },
  { id: 'Virtual Zen2', name: 'Virtual Zen2' },
];

const VIRTUAL_ZEN_CLUSTERS = [
  { id: 'Virtual Cluster1', name: 'Virtual Cluster1' },
  { id: 'Virtual Cluster2', name: 'Virtual Zen Cluster2' },
];

const IP_ADDRESS = [
  { id: '************', name: '************' },
  { id: '************', name: '************' },
];

const INTERNET_INSIGHTS_DATA = {
  pieData: [
    {
      id: 'Accessing internet through ZIA',
      label: 'Accessing internet through ZIA',
      value: 50,
    },
    {
      id: 'Accessing internet directly',
      label: 'Accessing internet directly',
      value: 24,
    },
  ],
  colors: ['#77B347', '#FFB700'],
  innerText: 'Server Devices',
};

const PRIVATE_INSIGHTS_DATA = {
  pieData: [
    {
      id: 'ZPA',
      label: 'Accessing private applications through ZPA',
      value: 12,
    },
    {
      id: 'Direct',
      label: 'Accessing private applications directly',
      value: 30,
    },
  ],
  colors: ['#77B347', '#FFB700'],
  innerText: 'Server Devices',
};

const STATIC_REGIONS = [{
  id: 0,
  name: 'All',
}, {
  id: 1,
  name: 'Asia Pacific',
}, {
  id: 2,
  name: 'US East',
}, {
  id: 3,
  name: 'US West',
}, {
  id: 4,
  name: 'Europe',
}];

const FEATURE_PERMISSIONS = {
  PARTNER_INTEGRATION: 'NONE', // NOT Supported
  POLICY_RESOURCE_MANAGEMENT: 'NONE', // NOT Supported
  APIKEY_MANAGEMENT: 'NONE',
  EDGE_CONNECTOR_ADMIN_MANAGEMENT: 'NONE',
  EDGE_CONNECTOR_CLOUD_PROVISIONING: 'NONE',
  EDGE_CONNECTOR_DASHBOARD: 'NONE',
  EDGE_CONNECTOR_FORWARDING: 'NONE',
  EDGE_CONNECTOR_LOCATION_MANAGEMENT: 'NONE',
  EDGE_CONNECTOR_NSS_CONFIGURATION: 'NONE',
  EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT: 'NONE',
  EDGE_CONNECTOR_TEMPLATE: 'NONE',
  PROXY_GATEWAY: 'NONE',
  REMOTE_ASSISTANCE_MANAGEMENT: 'NONE',
};

const GET_TIMESTAMP_BLACKLIST = [
  'api/v1/adminUsers',
  'api/v1/ccaDevice/summary?',
  'api/v1/discoveryService/',
  'api/v1/ecAdminSamlSettings/downloadXmlMetaData?',
  'api/v1/ecbuild/image/',
  'api/v1/ecbuild/images',
  'api/v1/ecgroup/lite?deployType=CLOUD',
  'api/v1/gateways?type',
  'api/v1/ipDestinationGroups/lite?type',
  'api/v1/location',
  'api/v1/orgProvisioning/domains',
  'api/v1/provUrl?provUrlType=ONPREM&provUrlType=ONPREM_PHYSICAL',
  'api/v1/publicCloudInfo/count',
  'api/v1/publicCloudInfo/supportedRegions?cloudType',
  'api/v1/publicCloudTenant/ccGroups',
  'api/v1/slots?type=EC_DNSRULESLOT',
  'api/v1/slots?type=EC_RDRRULESLOT&&type=EC_SELFRULESLOT',
  'api/v1/slots?type=ECZPA_GATEWAY&&type=ZIA_GATEWAY&&type=ECSELF_GATEWAY',
  'api/v1/slots?type=NW_SERVICE',
  'api/v1/ztGateway/status',
];

export default NAVBAR_ROUTES;

export {
  ACTIVE_HEALTH_DATA,
  ADMIN_CONTROL_SSO_ROUTES,
  ADMIN_SUB_NAV_ROUTES,
  ADMINISTRATOR_SCOPE,
  ADMINISTRATOR_STATUS,
  ANALYTICS_SSO_ROUTES,
  ANALYTICS_SUB_NAV_ROUTES,
  APP_SLIDES_TO_SHOW,
  AUDIT_LOGS_SEARCH_DATA,
  AUDIT_LOGS_TIME_FRAME_DATA,
  AUTHENTICATION_CONFIGURATION_SSO_ROUTES,
  BUBBLES_POSITIONS,
  CIRCLE_COLOR,
  DESTINATION_COUNTRY,
  DESTINATION_IP_FQDN_GROUP,
  DNS_POLICY_ACTION,
  DNS_POLICY_SEARCH_DATA,
  EC_DIRECTION,
  EC_GROUPS,
  EC_TRAFFIC_TYPE,
  EDGECONNECTOR_WIZARD_INITIAL_VALUES,
  EDGECONNECTORS_NW_DATA,
  EDGECONNECTORS,
  EXISTING_LOC,
  FEATURE_PERMISSIONS,
  FILTERING_SSO_ROUTES,
  FORWARDING_CONTROL_SSO_ROUTES,
  FORWARDING_METHODS_SSO_ROUTES,
  FWD_METHOD,
  FWD_POLICY_CRITERIA_ALL,
  FWD_POLICY_CRITERIA_MIN,
  GEO_DATA,
  GET_TIMESTAMP_BLACKLIST,
  HELP_ARTICLES,
  HELP_SUB_NAV_ROUTES,
  IDP_NAMES,
  INSIGHTS_TIME_FRAME_DATA,
  INTERNET_INSIGHTS_DATA,
  IP_ADDRESS,
  IP_CATEGORY,
  LOACTION_TEMPLATE,
  LOCATION_MAP_DATA,
  LOGS_DEST_IP,
  LOGS_EC_GROUP,
  LOGS_EC_LOCATION,
  LOGS_EC_VERSION,
  LOGS_EVENT_TIME,
  LOGS_FWD_METHOD,
  LOGS_HYPERVISOR_VERSION,
  LOGS_SUB_NAV_ROUTES,
  LOGS_TIME_FRAME_DATA,
  LOGS_ZIP,
  MANAGEMENT_IP_GROUP,
  MANUAL_GATEWAY,
  MONITOR_DURATION,
  MONITOR_HTTP_SUCCESS_CODES,
  MONITOR_STATUS_DATA,
  MONITOR_TRACEROUTE_PROTOCOLS,
  MONITOR_TYPES,
  NETWORK_SERVICE_GROUP,
  NETWORK_SERVICE,
  NSS_SSO_ROUTES,
  OFFICE365_MONITOR_DATA,
  ONEDRIVE_MONITOR_DATA,
  POLICY_SEARCH_DATA,
  POLICY_SUB_NAV_ROUTES,
  PRIVATE_INSIGHTS_DATA,
  PROTOCOL_TYPES,
  PROXY_PORTS,
  RADAR_CHART,
  REQ_ACTION,
  RESOLVER,
  RULE_ORDER,
  RULE_STATUS,
  SALESFORCE_MONITOR_DATA,
  SCORE_STATUSES,
  SHAREPOINT_MONITOR_DATA,
  SOURCE_IP_GROUP,
  STATIC_IP_ADRESSES,
  STATIC_REGIONS,
  STATIC_SESSION_PIE,
  STATIC_THROUGHPUT_PIE,
  STRING_CONDITIONS,
  TIME_FRAME_DATA,
  TIME_FRAME,
  TIME_ZONE,
  TOTAL_EC_DATA,
  TRAFFIC_DIRECTION,
  TRAFFIC_FWD_METHOD,
  TREND_DETAIL_DATA,
  TREND_TIME_FRAME_DATA,
  TUNNEL_INSIGHTS_METRICS,
  TUNNEL_TYPES,
  VIRTUAL_ZEN_CLUSTERS,
  VIRTUAL_ZENS,
};
