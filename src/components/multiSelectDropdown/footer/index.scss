@import 'scss/colors.scss';
@import 'scss/mixins.scss';

.ec-root-page {
.multi-select-footer {
  background: var(--semantic-color-content-inverted-base-secondary);
  height: 40px;
  line-height: 40px;
  font-size: 12px;
  padding: 0 10px;
  position: absolute;
  width: 100%;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;

  button {
    padding: 6px 7px;
    line-height: 12px;
  }

  .left-side {
    float: left;

    .done-button {
      font-size: inherit;
    }
  }

  .right-side {
    float: right;

    button {
      border: none;
      color: var(--semantic-color-content-interactive-primary-default);
      background: inherit;
      padding: 0 7px 0;

      &:nth-child(1) {
        border-radius: 1px solid var(--semantic-color-border-base-primary);
        padding-left: 0;
      }
      &:nth-child(2) {
        padding-right: 0;
      }
    }
  }
}
}