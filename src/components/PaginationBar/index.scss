@import "scss/colors.scss";
@import "scss/mixins.scss";


.ec-root-page {
.pagination-bar{
  display: flex;
  margin-top: 5px;
  .pagination-bar-left{
    flex: 1 1;
    text-align: center;
  }
  .pagination-bar-center{
    align-items: center;
    display: flex;
    flex: 1.5 1;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-around;
    margin-bottom: 0;
    text-align: center;
  }
  .pagination-bar-right{
    flex: 1 1;
    text-align: center;
  }
  .pagination-bar-left button, .pagination-bar-right button {
    background: var(--semantic-color-surface-base-primary);
    border: .0625rem solid var(--semantic-color-content-interactive-primary-default);
    color: var(--semantic-color-content-interactive-primary-default);
    -webkit-appearance: none;
    appearance: none;
    border-radius: 3px;
    cursor: pointer;
    display: block;
    font-size: 1em;
    height: 100%;
    outline-width: 0;
    padding: 6px;
    transition: all .1s ease;
    width: 100%;
    &:disabled {    
      background: var(--semantic-color-surface-base-primary);
      border: .0625rem solid var(--semantic-color-border-interactive-primary-disabled);
      color: var(--semantic-color-content-interactive-primary-disabled);
      opacity: 1;
    }
}
  .rows-per-page {
    margin-right: 160px;
    margin-right: 40px;
    color: #979899;
    align-self: flex-start;    
    .rows-per-page-select {
      color: var(--semantic-color-content-interactive-primary-default);
      background: inherit;
      border: none;
    }
  }
  button {
    border:none;
    &:disabled {
      border:none;
      .fontStyle {
        border-color: #979899;
        color: #979899;
      }  
    }
    .fontStyle {
      height: 14px;
      width: 13px;
      color: var(--semantic-color-content-interactive-primary-default);
      font-size: 14px;
      line-height: 14px;
      background-color: #F7F9FA;
      &:disabled {
        border-color: #979899;
        color: #979899;
      }
    }
  }
  .page-of {
    color: #979899;
    input::-webkit-outer-spin-button, 
    input::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }
    /* Firefox */
    input[type=number] {
      -moz-appearance: textfield;
    }
  }
  .page-number-input {
    max-width: 40px;
    color: var(--semantic-color-content-interactive-primary-default);
    background: inherit;
    border: none;
    text-align: center;
  }
  .page-number-total {
    min-width: 40px;
    color: #979899;
    display: inline-block;
    background: inherit;
    border: none;
    text-align: center;
  }
  .rows-per-page-select {
    background-color: green;
  }
}
}