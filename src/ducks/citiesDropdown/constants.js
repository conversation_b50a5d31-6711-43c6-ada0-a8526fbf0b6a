import { BASE_API_PATH } from 'config';

export const REDUCER_KEY = 'citiesDropdown';

export const API_ENDPOINT = `${BASE_API_PATH}/api/v1/reports`;

export const DATA_ERROR = 'error';
export const DATA_LOADING = 'loading';
export const DATA = 'data';

export const RESOURCE_DEFAULTS = {
  [DATA_ERROR]: {},
  [DATA_LOADING]: true,
  [DATA]: [],
};

export const DEFAULTS = {
  ...RESOURCE_DEFAULTS,
};

export const DEFAULT_STATE = {
  ...DEFAULTS,
};

export const DUMMY_STORE_DATA = {
  [REDUCER_KEY]: {
    ...DEFAULT_STATE,
  },
};
