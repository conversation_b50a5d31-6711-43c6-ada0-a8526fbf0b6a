import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowRight, faCheck, faTimes } from '@fortawesome/pro-regular-svg-icons';
import { withTranslation } from 'react-i18next';
import { updatePolicyTips } from 'ducks/profile';

function PolicyTipDescription({ title, tip, t }) {
// DNS_CONTROL_TIPS_DESC
// style="height: 71px;"
  const { policyInformation } = useSelector((state) => state.profile) || {};
  const [isMouseOver, setMouseOver] = useState(false);
  const dispatch = useDispatch();

  const handleMouseOver = () => setMouseOver(true);
  const handleMouseLeave = () => setMouseOver(false);
  const handleEnableTip = () => dispatch(updatePolicyTips(true));
  const handleDisableTip = () => dispatch(updatePolicyTips(false));

  if (policyInformation === 'DISABLE') return <></>;

  return (
    <div
      className="policy-tips-text-container"
      onMouseOver={handleMouseOver}
      onMouseLeave={handleMouseLeave}
      onFocus={(e) => e}>
      <div className="policy-tips-text-title">{title}</div>
      {!isMouseOver && <div className="policy-tips-text">{tip}</div>}
      {isMouseOver && (policyInformation === 'ENABLE') && (
        <div className="enable-disable-tips-container">
          <div className="disable-tips-message-text">
            {t('NAVIGATE_TO_ADMINISTRATION')}
            {' '}
            <FontAwesomeIcon icon={faArrowRight} />
            {' '}
            {t('DISABLE_TIPS_MESSAGE')}
          </div>
        </div>
      )}
      {isMouseOver && (policyInformation !== 'ENABLE') && (
        <div className="enable-disable-tips">
          <span role="button" tabIndex={0} className="tips-button enable-tips-button" onClick={handleEnableTip} onKeyPress={handleEnableTip}>
            <FontAwesomeIcon icon={faCheck} />
            {' '}
            {t('ENABLE_POLICY_INFORMATION')}
          </span>
          <span role="button" tabIndex={0} className="tips-button disable-tips-button" onClick={handleDisableTip} onKeyPress={handleDisableTip}>
            <FontAwesomeIcon icon={faTimes} />
            {' '}
            {t('DISABLE_POLICY_INFORMATION')}
          </span>
        </div>
      )}
    </div>
  );
}

PolicyTipDescription.propTypes = {
  title: PropTypes.string,
  tip: PropTypes.string,
  t: PropTypes.func,
};
  
PolicyTipDescription.defaultProps = {
  title: '',
  tip: '',
  t: (str) => str,
};

export default withTranslation()(PolicyTipDescription);
