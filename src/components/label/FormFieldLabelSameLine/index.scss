@import 'scss/colors.scss';

.ec-root-page {
.form-field-label-same-line-wrapper {
	display: flex;
    align-items: center;
	.form-field-label {
		color: $grey1;
		font-weight: 500;
		line-height: 20px;
		display: inline-block;
		min-width: max-content;
	}
	.form-field-label:hover + .rTooltip{
		display: block;
	}
	.top-right.rTooltip {
		top: auto;
		left: auto;
		max-width:  550px;
		min-width:  450px;
	}
	.form-field-tooltip-container {
		background: var(--semantic-color-background-primary);
		color:  var(--semantic-color-content-base-primary);
		border: 1px solid var(--semantic-color-border-base-primary);
		max-width: 550px;
		min-width: 260px;
		overflow: hidden;
		padding: 12px 16px;
		word-wrap: break-word;
		white-space: break-spaces;
		text-transform: none;
	}
}
}