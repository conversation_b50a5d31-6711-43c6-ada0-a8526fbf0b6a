/* eslint-disable jsx-a11y/label-has-for */
/* eslint-disable jsx-a11y/label-has-associated-control */
/* eslint-disable react/jsx-handler-names */
import React, { useState, useRef, useEffect } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import PropTypes from 'prop-types';
import ReactTooltip from 'react-tooltip';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';

import { FormFieldLabel } from 'components/label';
import {
  activate, checkActivation, toggleActivationPanel, updateForceActivate,
} from 'ducks/activation';
import Loading from 'components/spinner/Loading';
import ServerError from 'components/errors/ServerError';
import * as loginSelectors from 'ducks/login/selectors';

import './index.scss';

export function Activation(props) {
  const {
    t,
    actions: {
      toggleActivationPanel: toggleActivPanel,
    },
    toggleActivationPanel: toggleState,
    myActivateStatus,
    currentlyEditing,
    currentlyEditingLength,
    usersQueued,
    usersQueuedCount,
    adminRank,
  } = props;
  const valueRef = useRef(null);
  const [isValueEllipsisActive, setIsValueEllipsisActive] = useState(false);

  useEffect(() => {
    const { actions } = props;
    const { load } = actions;
    load();
  }, []);

  useEffect(() => {
    if (!valueRef.current) return;

    if (valueRef.current.offsetWidth < valueRef.current.scrollWidth) {
      setIsValueEllipsisActive(true);
    }
  }, [toggleState, currentlyEditing, valueRef]);

  const hanldeOnClickForceActivate = (e) => {
    e.preventDefault();
    e.stopPropagation();
    const { actions, forceActivation } = props;
    const { updateForceActivateState } = actions;
    e.currentTarget.lastElementChild.checked = !forceActivation;
    updateForceActivateState(!forceActivation);
  };

  const hanldeActivate = (e) => {
    e.stopPropagation();
    const { actions, forceActivation } = props;
    // eslint-disable-next-line no-shadow
    const { activate } = actions;
    activate(forceActivation);
  };

  if (toggleState) {
    return (
      <div
        className="activation-panel"
        onMouseOver={() => toggleActivPanel(true)}
        onFocus={() => toggleActivPanel(true)}
        onMouseLeave={() => toggleActivPanel(false)}>
        <Loading {...props}>
          <ServerError {...props}>
            <div className="activation-status-container">
              <FormFieldLabel text={t('MY_ACTIVATION_STATUS')} styleClass="no-margin-top" />
              <p className={`activation-status ${(myActivateStatus === '' || myActivateStatus === 'NO_ACTIVATION_PENDING') ? '' : 'edit'}`}>{t(myActivateStatus)}</p>
            </div>
            <div className="activation-status-container">
              <FormFieldLabel text={`${t('CURRENTLY_EDITING')} ( ${(currentlyEditingLength)} )`} styleClass="no-margin-top" />
              <p
                ref={valueRef}
                data-for="ActivationTooltip"
                data-tip={isValueEllipsisActive ? currentlyEditing : ''}
                className={`activation-status ${isValueEllipsisActive ? 'has-elipsis' : ''} ${currentlyEditingLength ? 'edit' : ''}`}>
                {currentlyEditing}
              </p>
            </div>
            <div className="activation-status-container">
              <FormFieldLabel text={`${t('QUEUED_ACTIVATIONS')} ( ${(usersQueuedCount)} )`} styleClass="no-margin-top" />
              <p className={`activation-status ${usersQueuedCount ? 'edit' : ''}`}>{usersQueued}</p>
            </div>
            <div className={`force-activate-parent ${adminRank !== 0 ? 'hidden' : ''}`}>
              <label
                aria-label={t('FORCE_ACTIVATE')}
                // eslint-disable-next-line jsx-a11y/no-noninteractive-element-to-interactive-role
                role="link"
                tabIndex={0}
                onKeyPress={hanldeOnClickForceActivate}
                onClick={hanldeOnClickForceActivate}>
                <span>
                  {t('FORCE_ACTIVATE')}
                </span>
                <input
                  type="checkbox"
                  id="forceActivate"
                  disabled={myActivateStatus === '' || myActivateStatus === 'NO_ACTIVATION_PENDING'} />
              </label>
            </div>
            <button
              type="button"
              className="activate-button blue-button"
              disabled={myActivateStatus === '' || myActivateStatus === 'NO_ACTIVATION_PENDING'}
              onClick={hanldeActivate}>
              {t('ACTIVATE')}
            </button>
          </ServerError>
        </Loading>
        <ReactTooltip
          id="ActivationTooltip"
          place="right"
          type="light"
          effect="solid"
          border
          multiline
          isCapture
          borderColor="#939393" />
      </div>
    );
  }
  return null;
}

const mapStateToProps = (state) => ({
  ...state.activation,
  adminRank: loginSelectors.adminRankSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    activate,
    load: checkActivation,
    toggleActivationPanel,
    updateForceActivateState: updateForceActivate,
  }, dispatch);

  return {
    actions,
  };
};

Activation.propTypes = {
  actions: PropTypes.shape({
    activate: PropTypes.func,
    load: PropTypes.func,
    toggleActivationPanel: PropTypes.func,
    updateForceActivateState: PropTypes.func,
  }),
  classes: PropTypes.shape({}),
  data: PropTypes.shape({}),
  title: PropTypes.string,
  t: PropTypes.func,
  toggleActivationPanel: PropTypes.bool,
  myActivateStatus: PropTypes.string,
  currentlyEditing: PropTypes.string,
  currentlyEditingLength: PropTypes.number,
  usersQueued: PropTypes.string,
  usersQueuedCount: PropTypes.number,
  forceActivation: PropTypes.bool,
  adminRank: PropTypes.number,
};

Activation.defaultProps = {
  actions: {
    load: noop,
  },
  classes: {},
  data: {},
  title: 'Activation',
  t: (str) => str,
  toggleActivationPanel: false,
  myActivateStatus: '',
  forceActivation: false,
  adminRank: 0,
};
export default withTranslation()(connect(
  mapStateToProps,
  mapDispatchToProps,
)(Activation));
