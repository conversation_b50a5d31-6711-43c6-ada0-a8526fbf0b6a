import { faAngleUp, faAngleDown } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import PropTypes from 'prop-types';
import { noop } from 'utils/lodash';
import React from 'react';
import { withTranslation } from 'react-i18next';
import DropdownSearch from './DropdownSearch';

import './index.scss';

class AdvDropdownWithSearch extends React.Component {
  static propTypes = {
    items: PropTypes.arrayOf(PropTypes.shape({
      value: PropTypes.string,
      label: PropTypes.string,
    })),
    setValue: PropTypes.func,
    selectedValue: PropTypes.shape({
      value: PropTypes.string,
      label: PropTypes.string,
    }),
    defaultValue: PropTypes.shape({
      value: PropTypes.string,
      label: PropTypes.string,
    }),
    onClickCb: PropTypes.func,
    t: PropTypes.func,
  };

  static defaultProps = {
    items: [],
    selectedValue: {},
    setValue: noop,
    defaultValue: {},
    onClickCb: null,
    t: (str) => str,
  };

  constructor(props) {
    super(props);
    const { defaultValue } = props;
    this.state = {
      selectedValue: defaultValue,
      openList: false,
    };
    this.wrapperRef = null;
  }

  componentDidMount() {
    document.addEventListener('mousedown', this.handleMousedown);
  }

  componentWillUnmount() {
    document.removeEventListener('mousedown', this.handleMousedown);
  }

  setWrapperRef = (node) => {
    this.wrapperRef = node;
  };

  handleChangeValue = (item) => {
    const { setValue, onClickCb, defaultValue } = this.props;
    setValue(item);
    this.setState({
      selectedValue: defaultValue,
      openList: false,
    });
    onClickCb(item);
  };

  handleMousedown = (e) => {
    const { defaultValue } = this.props;
    const containRef = this.wrapperRef && this.wrapperRef.contains(e.target);
    if (!containRef) {
      this.setState({ openList: false, selectedValue: defaultValue });
    }
  };

  handleOpenList = () => {
    const { openList } = this.state;
    const { defaultValue } = this.props;
    this.setState({ openList: !openList, selectedValue: defaultValue });
  };

  render() {
    const { items, t } = this.props;
    const { selectedValue, openList } = this.state;
    return (
      <div className="drop-down-container" ref={this.setWrapperRef}>
        <button
          className="drop-down-selected-value"
          onClick={this.handleOpenList}
          type="button">
          <span>{selectedValue.label}</span>
          <FontAwesomeIcon icon={openList ? faAngleUp : faAngleDown} pull="right" />
        </button>
        <ul className={`drop-down-list ${openList ? 'open' : ''}`}>
          <li>
            <DropdownSearch {...this.props} />
          </li>
          <div className="items-container">
            {items.filter((item) => item.value !== selectedValue.value).map((item) => (
              <li key={item.value}>
                <button onClick={() => { this.handleChangeValue(item); }} type="button"><span>{t(item.label)}</span></button>
              </li>
            ))}
          </div>
        </ul>
      </div>
    );
  }
}

export default withTranslation()(AdvDropdownWithSearch);
