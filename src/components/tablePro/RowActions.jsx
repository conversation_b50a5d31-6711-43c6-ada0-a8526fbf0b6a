import React from 'react';
import PropTypes from 'prop-types';

import <PERSON><PERSON>ie<PERSON> from './RowView';
import <PERSON>Dele<PERSON> from './RowDelete';
import RowEdit from './RowEdit';
import RowCopy from './RowCopy';
import RowDownload from './RowDownload';
import RowDiffView from './RowDiffView';
import RowRefresh from './RowRefresh';
import RowRun from './RowRun';
import RowDisable from './RowDisable';
import RowEllipsis from './RowEllipsis';
import RowSubEdit from './RowSubEdit';
import RowTestConnectivity from './RowTestConnectivity';

const noop = () => undefined;
// RowActions is responsible to support Actions of Each Row
function RowActions(props) {
  const {
    onViewClick,
    onViewDiffClick,
    onEditClick,
    onDuplicateClick,
    onRefreshClick,
    onRunClick,
    onEllipsisClick,
    onDisableClick,
    onSubEditClick,
    onDeleteClick,
    onDownloadClick,
    onTestConnectivityClick,
    isReadOnly,
    showSubEdit,
    isDeletable,
    isEditable,
    isCopyable,
    isDownloadable,
    isRefreshable,
    isRunable,
    isEllipsisable,
    isDisable,
    isViewDiff,
    disableDownload,
    showTestConnectivityIcon,
    isTestConnectivityInProgress,
    isTestConnectivityFeedDisabled,
  } = props;

  return (
    <div className="table-row-menu-container table-cell cell-actions">
      <div className="table-row-menu">
        <RowView isReadOnly={isReadOnly} onClickCallBack={onViewClick} />
        <RowEdit isEditable={isEditable} onClickCallBack={onEditClick} />
        <RowRefresh isRefreshable={isRefreshable} onClickCallBack={onRefreshClick} />
        <RowCopy isEditable={isCopyable} onClickCallBack={onDuplicateClick} />
        <RowDelete isDeletable={isDeletable} onClickCallBack={onDeleteClick} />
        <RowEllipsis isEllipsisable={isEllipsisable} onClickCallBack={onEllipsisClick} />
        <RowDisable isDisable={isDisable} onClickCallBack={onDisableClick} />
        <RowSubEdit showSubEdit={showSubEdit} onClickCallBack={onSubEditClick} />
        <RowDiffView isViewDiff={isViewDiff} onClickCallBack={onViewDiffClick} />
        <RowDownload
          isDownloadable={isDownloadable}
          onClickCallBack={onDownloadClick}
          disableDownload={disableDownload} />
        <RowTestConnectivity
          isTestConnectivityInProgress={isTestConnectivityInProgress}
          isTestConnectivityFeedDisabled={isTestConnectivityFeedDisabled}
          onClickCallBack={onTestConnectivityClick}
          showTestConnectivityIcon={showTestConnectivityIcon} />
        <RowRun isRunable={isRunable} onClickCallBack={onRunClick} />
      </div>
    </div>
  );
}

RowActions.propTypes = {
  onEditClick: PropTypes.func,
  onDuplicateClick: PropTypes.func,
  onRefreshClick: PropTypes.func,
  onRunClick: PropTypes.func,
  onEllipsisClick: PropTypes.func,
  onDisableClick: PropTypes.func,
  onSubEditClick: PropTypes.func,
  onDeleteClick: PropTypes.func,
  onViewClick: PropTypes.func,
  onViewDiffClick: PropTypes.func,
  onDownloadClick: PropTypes.func,
  onTestConnectivityClick: PropTypes.func,
  showSubEdit: PropTypes.bool,
  isReadOnly: PropTypes.bool,
  isDeletable: PropTypes.bool,
  isEditable: PropTypes.bool,
  isCopyable: PropTypes.bool,
  isDownloadable: PropTypes.bool,
  isRefreshable: PropTypes.bool,
  isRunable: PropTypes.bool,
  isEllipsisable: PropTypes.bool,
  isDisable: PropTypes.bool,
  isViewDiff: PropTypes.bool,
  disableDownload: PropTypes.bool,
  showTestConnectivityIcon: PropTypes.bool,
  isTestConnectivityInProgress: PropTypes.bool,
  isTestConnectivityFeedDisabled: PropTypes.bool,
};

RowActions.defaultProps = {
  onEditClick: noop,
  onDuplicateClick: noop,
  onRefreshClick: noop,
  onRunClick: noop,
  onEllipsisClick: noop,
  onDisableClick: noop,
  onSubEditClick: noop,
  onDeleteClick: noop,
  onViewClick: noop,
  onViewDiffClick: noop,
  onDownloadClick: noop,
  onTestConnectivityClick: noop,
  showSubEdit: false,
  isReadOnly: false,
  isDeletable: false,
  isEditable: false,
  isCopyable: false,
  isDownloadable: false,
  isRefreshable: false,
  isRunable: false,
  isEllipsisable: false,
  isDisable: false,
  isViewDiff: false,
  disableDownload: false,
  showTestConnectivityIcon: false,
  isTestConnectivityInProgress: false,
  isTestConnectivityFeedDisabled: false,
};

export default RowActions;
