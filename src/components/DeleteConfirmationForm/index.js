// @flow
import React, { useState } from 'react';
import { reduxForm } from 'redux-form';
import Loading from 'components/spinner/Loading';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';

import './index.scss';
 
export function BasicCustomAppForm({
  valid,
  t,
  message,
  modalLoading,
  selectedRowID,
  handleCancel,
  handleDelete,
  subItems,
}) {
  const [submitting, setSubmitting] = useState(false);

  const onSubmitHandler = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setSubmitting(true);
    handleDelete(selectedRowID, subItems);
  };

  return (
    <form onSubmit={onSubmitHandler} className="add-custom-app-form delete-confirm-form">
      <Loading loading={modalLoading}>
        <div className="form-sections-container">
          <div className="form-section">
            {message || (
              <>
                <p className="modal-text">
                  {subItems && subItems.length > 1 ? t('DELETE_CONFIRMATION_MESSAGE2') : t('DELETE_CONFIRMATION_MESSAGE1')}
                </p>
                <p className="modal-text">
                  {t('ACTION_CAN_NOT_BE_UNDONE')}
                </p>
                {subItems.map((item, index) => <p key={item.id} className={`disabled-input ${index === 0 ? 'full-width' : ''} no-margin-bottom`}>{item.name}</p>)}
              </>
            )}
          </div>
        </div>
        <div className="dialog-footer">
          <div className="dialog-footer-left">
            <button type="submit" disabled={submitting} className={`submit ${!valid ? 'disabled' : ''}`}>{t('DELETE')}</button>
            <button type="button" className="cancel" onClick={() => handleCancel(false)}>{t('CANCEL')}</button>
          </div>
        </div>
      </Loading>
    </form>
  );
}

BasicCustomAppForm.propTypes = {
  handleCancel: PropTypes.func,
  handleDelete: PropTypes.func,
  t: PropTypes.func,
  message: PropTypes.string,
  modalLoading: PropTypes.bool,
  selectedRowID: PropTypes.string,
  valid: PropTypes.bool,
  subItems: PropTypes.arrayOf(),
};

BasicCustomAppForm.defaultProps = {
  handleCancel: noop,
  handleDelete: noop,
  t: (str) => str,
  message: null,
  modalLoading: false,
  selectedRowID: null,
  valid: true,
  subItems: [],
};

const DeleteConfirmationForm = reduxForm({
  form: 'deleteConfirmationForm',
})(BasicCustomAppForm);

export default (withTranslation()(DeleteConfirmationForm));
