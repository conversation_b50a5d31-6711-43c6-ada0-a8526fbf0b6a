/* eslint-disable react/jsx-handler-names */
import React from 'react';
import PropTypes from 'prop-types';
import { noop, debounce } from 'utils/lodash';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSearch, faTimes } from '@fortawesome/pro-solid-svg-icons';
import { withTranslation } from 'react-i18next';

export class MultiDropdownAsyncSearch extends React.Component {
  constructor(props) {
    super(props);
    const { searchString } = this.props;
    this.state = {
      search: searchString || '',
    };
    this.debounceInput = debounce(this.performSearch, 900);
  }

  componentWillUnmount() {
    const {
      actions: {
        setSearchString,
        onSearchStringChange,
        searchParamName,
      },
    } = this.props;
    setSearchString('', searchParamName);
    onSearchStringChange('');
  }

  clearSearch = () => {
    const { actions: { onSearchStringChange } } = this.props;
    this.setState({ search: '' }, this.performSearch);
    onSearchStringChange('');
  };

  onChange = (event) => {
    const { actions: { onSearchStringChange } } = this.props;
    const search = event.target.value;
    this.setState({ search }, this.debounceInput);
    onSearchStringChange(search);
  };

  performSearch = () => {
    const { actions: { onPerformSearch } } = this.props;
    onPerformSearch();
  };

  renderClearButton = () => {
    const { search } = this.state;
    if (search.length === 0) return null;
    return (
      <button
        onClick={this.clearSearch}
        type="button"
        className="clear-button">
        <FontAwesomeIcon icon={faTimes} />
      </button>
    );
  };

  render() {
    const { search } = this.state;
    const {
      tabIndex,
      placeholder,
      t,
    } = this.props;

    return (
      <div className="dropdown-search">
        <input
          className="dropdown-search-text"
          type="text"
          value={search}
          tabIndex={tabIndex}
          placeholder={t(placeholder)}
          onChange={this.onChange} />
        <span className="action-buttons">
          {this.renderClearButton()}
          <button
            onClick={this.performSearch}
            type="button"
            className="remove-button">
            <FontAwesomeIcon icon={faSearch} />
          </button>
        </span>
      </div>
    );
  }
}

MultiDropdownAsyncSearch.propTypes = {
  searchString: PropTypes.string,
  actions: PropTypes.shape({
    setSearchString: PropTypes.func,
    searchData: PropTypes.func,
  }),
  tabIndex: PropTypes.number,
  placeholder: PropTypes.string,
  t: PropTypes.func,
};

MultiDropdownAsyncSearch.defaultProps = {
  searchString: '',
  actions: {
    setSearchString: noop,
    searchData: noop,
  },
  tabIndex: -1,
  placeholder: 'SEARCH',
  t: (str) => str,
};

export default withTranslation()(MultiDropdownAsyncSearch);
