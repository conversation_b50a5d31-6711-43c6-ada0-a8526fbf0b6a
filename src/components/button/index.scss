@import 'scss/colors.scss';

.ec-root-page {
.download-button {
	border: none;
	background: none;
	outline: none;
	color: var(--semantic-color-content-interactive-primary-default);
	font-size: 13px;
	font-weight: 500;
	line-height: 19px;
	width: fit-content;
	cursor: pointer;
	position: relative;
	z-index: 1;
	.icon {
		margin-right: 6px;
	}
}

.static-radio {
	cursor: pointer;
    font-size: 13px;
    position: relative;
    text-align: center;
    white-space: nowrap;
	height: 49px;
    width: 100%;
    border-radius: 5px;
    margin-top: 2em;
	.label {
		padding-left: 0.4em;	
	}
	.button-radio-button {
		background: $white;
		border-bottom: 1px solid var(--semantic-color-content-interactive-primary-default);
		border-left: 1px solid var(--semantic-color-content-interactive-primary-default);
		border-top: 1px solid var(--semantic-color-content-interactive-primary-default);
		color: var(--semantic-color-content-interactive-primary-default);
		display: inline-block;
		padding: 8px 12px;
		text-align: center;
		vertical-align: top;
		width: 100%;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.button-radio-button:only-child {
		border-radius: 3px;
	}

	.button-radio-button::-moz-selection {
		background: transparent;
	}

	.button-radio-button::selection {
		background: transparent;
	}

	.button-radio-button:hover {
		background: $blue3;
		color: var(--semantic-color-content-interactive-primary-default);
	}

	.button-radio-button.button-radio-active {
		background: var(--semantic-color-content-interactive-primary-default);
		color: $white;
	}

	.button-radio-active:hover {
		background: var(--semantic-color-content-interactive-primary-default);
		color: $white;
	}

}

.external-id-buttons {
    display: flex;
    min-height: 32px;
    .primary-button {
      align-items: center;
      display: flex;
      justify-content: center;
      width: 117px;
      background: var(--semantic-color-content-interactive-primary-default);
	  border:none;
      border-radius: 4px;
      font-style: normal;
      font-weight: 500;
      font-size: 13px;
      line-height: 20px;
      text-align: center;
      color: var(--semantic-color-content-immutable-white);
      cursor: pointer;
    }
    .secondary-button {
      align-items: center;
	  padding: 0 16px;
      display: flex;
      justify-content: center;
      width: fit-content;
      background: var(--semantic-color-background-primary);
	  border:none;
      border-radius: 4px;
      font-style: normal;
      font-weight: 500;
      font-size: 13px;
      line-height: 20px;
      text-align: center;
      color: var(--semantic-color-content-status-info-primary);
      cursor: pointer;
    }
  }
}