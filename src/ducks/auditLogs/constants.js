// this will be hooked onto the relative state tree.
import { BASE_API_PATH } from 'config';

export const REDUCER_KEY = 'auditLogs';

export const API_ENDPOINT = `${BASE_API_PATH}/v1/auditLogEntries`;

export const TXN_DATA_API_ENDPOINT = `${BASE_API_PATH}/v1/transactionData/dnsRequest`;
export const TXN_DATA_WEB_API_ENDPOINT = `${BASE_API_PATH}/v1/transactionData/web`;
export const TXN_DATA_DNS_LOGS_API_ENDPOINT = `${BASE_API_PATH}/v1/transactionData/dns`;

export const EXPORT_AUDIT_JOB_API = `${BASE_API_PATH}/v1/auditlogEntryReport`;
export const EXPORT_AUDIT_DATA_API = `${BASE_API_PATH}/v1/auditlogEntryReport`;
export const EXPORT_AUDIT_DATA_DOWNLOAD_API = `${BASE_API_PATH}/v1/auditlogEntryReport/download`;

export const CATEGORY_DROPDOWN_API = `${BASE_API_PATH}/v1/auditCatalog/categories`;
export const INTERFACE_DROPDOWN_API = `${BASE_API_PATH}/v1/auditCatalog/interfaces`;
export const RESULT_DROPDOWN_API = `${BASE_API_PATH}/v1/auditCatalog/results`;

export const DATA_ERROR = 'error';
export const DATA_LOADING = 'loading';
export const DATA_TABLE = 'auditLogstabledata';
export const DATA = 'data';
export const CATEGORY_DROPDOWN_DATA = 'categoryDropdownData';
export const INTERFACE_DROPDOWN_DATA = 'interfaceDropdownData';
export const RESULT_DROPDOWN_DATA = 'resultDropdownData';
// this will store data to create and edit form
export const APP_DATA = 'appData';
export const SHOW_ACCORDION = 'showAccordion';
export const NO_OF_RECORDS = 'noOfRecords';
export const FILTERS = 'filters';
export const STATE_FORM = 'form';
export const COLUMNS = 'auditLogsColumns';
export const DONWLOAD_CSV_DATA = 'downloadCsvData';
export const FORM_DATA = 'formData';
export const TIME_FRAME = 'timeFrame';
export const SELECTED_CATEGORY = 'selectedCategory';
export const SHOW_VIEW_FORM = 'showViewForm';
export const FORM_DIFF_DATA = 'formDiffData';
export const FORM_TITLE = 'formTitle';
export const CATEGORY_UPDATED = 'categoryUpdate';

const sgAppData = [{
  id: '1',
  valid: true,
}];

export const auditLogsColumns = [
  {
    key: 'timestamp',
    exportId: 'TIMESTAMP',
    name: 'Timestamp',
    data: {},
    draggable: true,
    resizable: true,
    checkbox: true,
    sortable: true,
    visible: true,
  },
  {
    key: 'actionType',
    exportId: 'ACTION_TYPE',
    name: 'Action',
    data: {},
    draggable: true,
    resizable: true,
    checkbox: true,
    sortable: true,
    visible: true,
  },
  {
    key: 'category',
    exportId: 'CATEGORY',
    name: 'CATEGORY',
    data: {},
    draggable: true,
    resizable: true,
    checkbox: true,
    sortable: true,
    visible: true,
  },
  {
    key: 'subCategory',
    exportId: 'SUB_CATEGORY',
    name: 'SUB_CATEGORY',
    data: {},
    draggable: true,
    resizable: true,
    checkbox: true,
    sortable: true,
    visible: true,
  },
  {
    key: 'objectName',
    exportId: 'RESOURCE_NAME',
    name: 'Resource',
    data: {},
    draggable: true,
    resizable: true,
    checkbox: true,
    sortable: true,
    visible: true,
  },
  {
    key: 'userName',
    exportId: 'USER',
    name: 'Admin ID',
    data: {},
    draggable: true,
    resizable: true,
    checkbox: true,
    sortable: true,
    visible: true,
  },
  {
    key: 'clientIP',
    exportId: 'CLIENT_IP',
    name: 'CLIENT_IP',
    data: {},
    draggable: true,
    resizable: true,
    checkbox: true,
    sortable: true,
    visible: true,
  },
  {
    key: 'actionInterface',
    exportId: 'ACTION_INTERFACE',
    name: 'ACTION_INTERFACE',
    data: {},
    draggable: true,
    resizable: true,
    checkbox: true,
    sortable: true,
    visible: true,
  },
  {
    key: 'actionResult',
    exportId: 'ACTION_RESULT',
    name: 'ACTION_RESULT',
    data: {},
    draggable: true,
    resizable: true,
    checkbox: true,
    sortable: true,
    visible: true,
  },
  {
    key: 'actionResult',
    exportId: 'ACTION_RESULT',
    name: 'ACTION_RESULT',
    data: {},
    draggable: true,
    resizable: true,
    checkbox: true,
    sortable: true,
    visible: true,
  },
];

export const auditLogsSampleData = [];

export const auditLogsColumn = [
  {
    id: 'dataIdx',
    accessor: (row, i) => i + 1,
    Header: 'NUMBER_ABBR',
    width: 60,
    disableReordering: true,
    disableResizing: true,
    disableSortBy: true,
    meta: {
      skipTranslation: false,
      customCellType: 'RENDER_INDEX',
    },
  },
  {
    id: 'timestamp',
    accessor: (row) => row.timestamp,
    Header: 'TIMESTAMP',
    width: 120,
    defaultCanSort: true,
    disableSortBy: false,
    meta: {
      skipTranslation: false,
      customCellType: 'DATE_TIME',
    },
  },
  {
    id: 'actionType',
    accessor: (row) => row.actionType || 'NONE',
    Header: 'ACTION_TYPE',
    width: 120,
    defaultCanSort: false,
    disableSortBy: true,
    meta: {
      skipTranslation: false,
    },
  },
  {
    id: 'category',
    accessor: (row) => row.category || 'NONE',
    Header: 'CATEGORY',
    width: 120,
    defaultCanSort: false,
    disableSortBy: true,
    meta: {
      skipTranslation: false,
    },
  },
  {
    id: 'subCategory',
    accessor: (row) => row.subCategory || 'NONE',
    Header: 'SUB_CATEGORY',
    width: 120,
    defaultCanSort: false,
    disableSortBy: true,
    meta: {
      skipTranslation: false,
    },
  },
  {
    id: 'objectName',
    accessor: (row) => row.objectName || 'NONE',
    Header: 'RESOURCE_NAME',
    width: 120,
    defaultCanSort: false,
    disableSortBy: true,
    meta: {
      skipTranslation: false,
    },
  },
  {
    id: 'userName',
    accessor: (row) => row.userName || 'NONE',
    Header: 'ADMIN_ID',
    width: 120,
    defaultCanSort: false,
    disableSortBy: true,
    meta: {
      skipTranslation: false,
    },
  },
  {
    id: 'clientIP',
    accessor: (row) => row.clientIP || 'NONE',
    Header: 'CLIENT_IP',
    width: 120,
    defaultCanSort: false,
    disableSortBy: true,
    meta: {
      skipTranslation: false,
    },
  },
  {
    id: 'actionInterface',
    accessor: (row) => row.actionInterface || 'NONE',
    Header: 'ACTION_INTERFACE',
    width: 120,
    defaultCanSort: false,
    disableSortBy: true,
    meta: {
      skipTranslation: false,
    },
  },
  {
    id: 'actionResult',
    accessor: (row) => row.actionResult || 'NONE',
    Header: 'ACTION_RESULT',
    width: 120,
    defaultCanSort: false,
    disableSortBy: true,
    meta: {
      skipTranslation: false,
    },
  },
];

const editColumn = {
  id: 'editor',
  Header: '',
  width: 60,
  disableReordering: true,
  disableResizing: true,
  disableSortBy: true,
  meta: {
    customCellType: 'ROW_ACTIONS',
    pageName: 'AUDIT_LOGS',
  },
};

export const AUDIT_LOGS_TABLE_CONFIGS = {
  columns: [...auditLogsColumn, editColumn],
  initialState: {
    sortBy: [{ id: 'name' }],
  },
  disableTableHeaderDrag: true, // disable table column drag and drop
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: true,
  maxTableHeight: 'calc(100vh - 30rem)',
};

export const dlocs = [];

export const RESET_FILTERS = {
  [DATA_ERROR]: null, // or error object so we can process and show to user
  [DATA_LOADING]: true,
  [APP_DATA]: sgAppData,
  [NO_OF_RECORDS]: '1000',
  [COLUMNS]: auditLogsColumns,
  [DONWLOAD_CSV_DATA]: [],
  [FORM_DIFF_DATA]: {},
};

export const RESOURCE_DEFAULTS = {
  [DATA_ERROR]: null, // or error object so we can process and show to user
  [DATA_LOADING]: true,
  [APP_DATA]: sgAppData,
  [DATA]: {},
  [DATA_TABLE]: auditLogsSampleData,
  [NO_OF_RECORDS]: '1000',
  [COLUMNS]: auditLogsColumns,
  [DONWLOAD_CSV_DATA]: [],
  [CATEGORY_DROPDOWN_DATA]: [],
  [INTERFACE_DROPDOWN_DATA]: [],
  [RESULT_DROPDOWN_DATA]: [],
  [FORM_DATA]: {
    timeFrame: 'current_day',
    pageSize: 100,
    page: 1,
  },
  [TIME_FRAME]: 'current_day',
  [SELECTED_CATEGORY]: null,
  [SHOW_VIEW_FORM]: false,
  [FORM_TITLE]: '',
  [CATEGORY_UPDATED]: false,
};

export const RESET_CSV_DOWNLOAD = {
  [DONWLOAD_CSV_DATA]: [],
};

export const DEFAULTS = {
  ...RESOURCE_DEFAULTS,
};

export const DEFAULTS_BUT_ACCORDION = {
  ...RESOURCE_DEFAULTS,
  [SHOW_ACCORDION]: true,
};

// default state for this page/widget/component
export const DEFAULT_STATE = {
  ...DEFAULTS,
};

// For testing...to be used later
export const DUMMY_STORE_DATA = {
  [REDUCER_KEY]: {
    ...DEFAULT_STATE,
  },
};
