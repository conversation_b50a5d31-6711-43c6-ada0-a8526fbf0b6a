import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import i18n from 'utils/i18n';
import PolicyItemView from '../PolicyItemView';
  
function Default({ value, valueObj, skipTranslation }) {
  // const { description } = value || {};
  
  const renderContent = () => (
    <PolicyItemView
      label={i18n.t('')}
      valueObj={valueObj}
      value={skipTranslation ? value : i18n.t(value)} />
  );
    
  return (
    <div
      onKeyPress={null}
      // aria-label="description item"
      role="button"
      tabIndex="0">
      {renderContent()}
    </div>
  );
}
  
Default.defaultProps = {
  value: '',
  valueObj: null,
  skipTranslation: false,
};
  
Default.propTypes = {
  value: PropTypes.shape({}),
  valueObj: PropTypes.objectOf(Object),
  skipTranslation: PropTypes.bool,
};
  
export default withTranslation()(Default);
