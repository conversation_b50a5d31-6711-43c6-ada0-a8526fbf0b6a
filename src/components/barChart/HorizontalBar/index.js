// @flow
import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { ResponsiveBar } from '@nivo/bar';
import GenericErrorMessage from 'components/errors/ServerError/GenericErrorMessage';
import BarText from './BarText';

const getColor = () => '#90D9F4';

function ToolTipMessageAction({ selectedDataKey, actionToolTip, DrillDownAction }) {
  return ((selectedDataKey && actionToolTip)
    ? <DrillDownAction filter={selectedDataKey} />
    : <></>);
}
ToolTipMessageAction.propTypes = {
  selectedDataKey: PropTypes.string,
  actionToolTip: PropTypes.bool,
  DrillDownAction: PropTypes.shape({}),
};

export function HorizontalBar(props) {
  const { dataSequence, DrillDownAction, height } = props;
  const [styles, setStyles] = useState([]);
  const [selectedDataKey, setSelectedDataKey] = useState('');
  const [actionToolTip, setActionToolTip] = useState(false);

  if (Array.isArray(dataSequence) && !dataSequence.length) {
    return <GenericErrorMessage />;
  }
  const sortedData = dataSequence.sort((a, b) => a.raw - b.raw);

  return (
    <div role="button" style={{ height: '100%' }} onClick={() => setActionToolTip(!actionToolTip)} tabIndex={0} onKeyPress={(e) => e}>
      <div style={{ height }}>
        <div className="line-chart-field-tooltip-container">
          <ToolTipMessageAction
            DrillDownAction={DrillDownAction}
            selectedDataKey={selectedDataKey}
            actionToolTip={actionToolTip} />
        </div>
        <ResponsiveBar
          data={sortedData}
          indexBy="name"
          groupMode="grouped"
          layout="horizontal"
          // keys={['raw']}
          // height={24}
          margin={{
            top: 8, right: 50, bottom: 0, left: 130,
          }}
          padding={0.3}
          colors={getColor}
          defs={[
            {
              id: 'dots',
              type: 'patternDots',
              background: 'inherit',
              color: '#38bcb2',
              size: 4,
              padding: 1,
              stagger: true,
            },
            {
              id: 'lines',
              type: 'patternLines',
              background: 'inherit',
              color: '#eed312',
              rotation: -45,
              lineWidth: 6,
              spacing: 10,
            },
          ]}
          fill={[
            {
              match: {
                id: 'fries',
              },
              id: 'dots',
            },
            {
              match: {
                id: 'sandwich',
              },
              id: 'lines',
            },
          ]}
          barComponent={(e) => BarText(
            e,
            sortedData,
            styles,
            setStyles,
            selectedDataKey,
            setSelectedDataKey,
            actionToolTip,
            setActionToolTip,
          )}
          axisTop={null}
          axisRight={null}
          axisBottom={null}
          axisLeft={null}
          enableGridX={false}
          enableGridY={false}
          labelSkipWidth={0}
          labelSkipHeight={12}
          labelTextColor={{ from: 'color', modifiers: [['darker', 1.6]] }}
          legends={[]}
          animate
          motionStiffness={90}
          motionDamping={15} />
      </div>
    </div>
  );
}

HorizontalBar.propTypes = {
  dataSequence: PropTypes.arrayOf(PropTypes.shape({})),
  DrillDownAction: PropTypes.shape({}),
  height: PropTypes.string,
};

HorizontalBar.defaultProps = {
  dataSequence: [],
  DrillDownAction: null,
  height: '11em',
};

export default HorizontalBar;
