// Generated by <PERSON>, assisted by <PERSON><PERSON><PERSON><PERSON> on 22-November-2024
  
describe('getVmGroupIds function', () => {
  function getVmGroupIds(pages) {
    return pages.filter((x, idx, array) => !x?.parentId
      // Must have one child
      && ((array.some((item) => x?.id === item?.parentId)
      // All the childs must be marked
      && (array.filter((item) => x?.id === item?.parentId).every((item) => item?.scheduled)))
      // or Must have no child
      || (!array.some((item) => x?.id === item?.parentId)
      // Must be marked
      && x.scheduled)))
      .map(({ id }) => id);
  }
  it('should return an array of ids of pages that have no parent and are scheduled', () => {
    const pages = [
      { id: 1, parentId: null, scheduled: true },
      { id: 2, parentId: 1, scheduled: true },
      { id: 3, parentId: null, scheduled: false },
    ];
  
    const result = getVmGroupIds(pages);
  
    expect(result).toEqual([1]);
  });
  
  it('should return an array of ids of pages that have children and all children are scheduled', () => {
    const pages = [
      { id: 1, parentId: null, scheduled: true },
      { id: 2, parentId: 1, scheduled: true },
      { id: 3, parentId: 1, scheduled: true },
    ];
  
    const result = getVmGroupIds(pages);
  
    expect(result).toEqual([1]);
  });
  
  it('should return an empty array if no pages match the conditions', () => {
    const pages = [
      { id: 1, parentId: null, scheduled: false },
      { id: 2, parentId: 1, scheduled: false },
      { id: 3, parentId: 1, scheduled: false },
    ];
  
    const result = getVmGroupIds(pages);
  
    expect(result).toEqual([]);
  });
  
  it('should return an array of ids of pages that have multiple parents and all children are scheduled', () => {
    const pages = [
      { id: 1, parentId: null, scheduled: true },
      { id: 2, parentId: 1, scheduled: true },
      { id: 3, parentId: 1, scheduled: true },
      { id: 4, parentId: null, scheduled: true },
      { id: 5, parentId: 4, scheduled: true },
      { id: 6, parentId: 4, scheduled: true },
    ];
  
    const result = getVmGroupIds(pages);
  
    expect(result).toEqual([1, 4]);
  });
  
  it('should return an array of ids of pages that have a parent without child', () => {
    const pages = [
      { id: 1, parentId: null, scheduled: true },
      { id: 2, parentId: null, scheduled: true },
    ];
  
    const result = getVmGroupIds(pages);
  
    expect(result).toEqual([1, 2]);
  });
  
  it('should return an array of ids of pages that have a parent with unscheduled child', () => {
    const pages = [
      { id: 1, parentId: null, scheduled: true },
      { id: 2, parentId: 1, scheduled: false },
      { id: 3, parentId: 1, scheduled: true },
    ];
  
    const result = getVmGroupIds(pages);
  
    expect(result).toEqual([]);
  });
});
