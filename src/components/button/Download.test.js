import React from 'react';
import { render, fireEvent } from '@testing-library/react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faDownload } from '@fortawesome/pro-solid-svg-icons';
import Download from './Download';

jest.mock('@fortawesome/react-fontawesome', () => ({
  FontAwesomeIcon: jest.fn(() => <div>FontAwesomeIcon</div>),
}));

describe('Download component', () => {
  const props = {
    label: 'Download',
    onActionCb: jest.fn(),
    tabIndex: 1,
    tooltip: 'Download tooltip',
  };

  it('renders correctly', () => {
    const { getByText, getByRole } = render(<Download {...props} />);
    expect(getByText('Download')).toBeInTheDocument();
    expect(getByRole('button')).toBeInTheDocument();
    expect(getByRole('button')).toHaveAttribute('tabindex', '1');
  });

  it('calls onActionCb when clicked', () => {
    const { getByRole } = render(<Download {...props} />);
    const button = getByRole('button');
    fireEvent.click(button);
    expect(props.onActionCb).toHaveBeenCalledTimes(1);
  });

  it('calls onActionCb when key pressed', () => {
    const { getByRole } = render(<Download {...props} />);
    const button = getByRole('button');
    fireEvent.keyPress(button, { key: 'Enter', code: 'Enter' });
    expect(props.onActionCb).toHaveBeenCalledTimes(1);
  });

  it('renders FontAwesomeIcon', () => {
    const { getByText } = render(<Download {...props} />);
    expect(getByText('FontAwesomeIcon')).toBeInTheDocument();
  });
});
