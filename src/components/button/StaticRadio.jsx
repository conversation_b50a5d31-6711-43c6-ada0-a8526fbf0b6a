import React from 'react';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCheckCircle } from '@fortawesome/pro-solid-svg-icons';

function StaticRadio(props) {
  const {
    label,
  } = props;
  return (
    <div className="static-radio">
      <div className="button-radio-button button-radio-active">
        <FontAwesomeIcon icon={faCheckCircle} />
        <span className="label">
          {label}
        </span>
      </div>
    </div>
  );
}

StaticRadio.propTypes = {
  label: PropTypes.string,
};

StaticRadio.defaultProps = {
  label: '',
};

export default StaticRadio;
