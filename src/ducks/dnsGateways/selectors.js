import { createSelector } from 'reselect';
import { getFormValues, getFormSyncErrors, getFormMeta } from 'redux-form';

import * as constants from './constants';

export const baseSelector = (state) => state[constants.REDUCER_KEY];

export const loadingSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA_LOADING],
);

export const errorSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA_ERROR],
);

export const dataSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA] || {},
);

export const appDataSelector = createSelector(
  baseSelector,
  (state) => state[constants.APP_DATA],
);

export const modalLoadingSelector = createSelector(
  baseSelector,
  (state) => state[constants.MODAL_LOADING],
);

export const selectedRowIDSelector = createSelector(
  baseSelector,
  (state) => state[constants.SELECTED_ROW_ID],
);

export const formTitleSelector = createSelector(
  baseSelector,
  (state) => state[constants.FORM_TITLE],
);

export const formValuesSelector = (state) => getFormValues('addEditDnsGatewayForm')(state);
export const formMetaSelector = (state) => getFormMeta('addEditDnsGatewayForm')(state);
export const formSyncErrorsSelector = (state) => getFormSyncErrors('addEditDnsGatewayForm')(state);
// alway return top level selector
export default baseSelector;
