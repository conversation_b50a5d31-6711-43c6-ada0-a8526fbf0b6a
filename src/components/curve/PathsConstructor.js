// @flow

import React from 'react';
import PropTypes from 'prop-types';
// import colors from 'scss/colors.scss';
import DrawPath from './DrawPath';

// fill="#B6B7BA" colors.grey28

function PathsConstructor(props) {
  const { paths } = props;
  return (
    <div style={{ width: '145em', position: 'absolute' }}>
      <svg viewBox="0 0 1500 1500" preserveAspectRatio="none">
        <defs>
          <marker
            id="arrowhead"
            viewBox="0 0 10 10"
            refX="5"
            refY="5"
            markerWidth="8"
            markerHeight="8">
            <circle cx="5" cy="5" r="5" fill="#B6B7BA" />
          </marker>
        </defs>
        <g className="traffic-path-root">
          {paths.src.map((p) => (
            <DrawPath p={p} markerStart="url(#arrowhead)" key={p.index} />
          ))}
          {paths.subnet.map((p) => (
            <DrawPath p={p} markerStart="url(#arrowhead)" key={p.index} />
          ))}
          {paths.service.map((p) => (
            <DrawPath p={p} markerEnd="url(#arrowhead)" key={p.index} />
          ))}
          {paths.dest.map((p) => (
            <DrawPath p={p} markerStart="url(#arrowhead)" key={p.index} />
          ))}
        </g>
      </svg>
 
    </div>
  );
}

PathsConstructor.propTypes = {
  paths: PropTypes.shape(),
};

PathsConstructor.defaultProps = {
  paths: {},
};

export default PathsConstructor;
