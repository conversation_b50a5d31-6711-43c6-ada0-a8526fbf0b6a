import React from 'react';
import PropTypes from 'prop-types';
import { isEmpty, noop } from 'utils/lodash';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';
import { withTranslation } from 'react-i18next';
import DropdownHeader from './DropdownHeader';

export class MultiSelected extends React.Component {
  itemRenderer = (item) => {
    const {
      actions: { remove },
      isViewOnly,
      t,
    } = this.props;
    return (
      <div
        key={`selected-${item.id}`}
        id={item.id}
        label={item.label}
        className="selected">
        <span>{t(item.name)}</span>
        <span>
          {!isViewOnly && (
            <button
              onClick={() => remove(item)}
              type="button"
              className="remove-button">
              <FontAwesomeIcon icon={faTimes} />
            </button>
          )}
        </span>
      </div>
    );
  };

  render() {
    const {
      selectedHeader,
      selectedValues,
    } = this.props;

    return (
      <div className="dropdown-selected">
        <DropdownHeader text={selectedHeader} />
        <div className="selected-items">
          {selectedValues.filter((x) => !isEmpty(x.parent)).map(this.itemRenderer)}
        </div>
      </div>
    );
  }
}

MultiSelected.propTypes = {
  isViewOnly: PropTypes.bool,
  actions: PropTypes.shape({
    remove: PropTypes.func,
  }),
  selectedHeader: PropTypes.string,
  selectedValues: PropTypes.arrayOf(PropTypes.shape({})),
  t: PropTypes.func,
};

MultiSelected.defaultProps = {
  isViewOnly: false,
  actions: {
    remove: noop,
  },
  selectedHeader: '',
  selectedValues: [],
  t: (str) => str,
};

export default withTranslation()(MultiSelected);
