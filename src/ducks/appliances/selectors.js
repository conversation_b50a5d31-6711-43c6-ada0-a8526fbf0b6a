import { createSelector } from 'reselect';
import { getFormValues, getFormMeta, getFormSyncErrors } from 'redux-form';

import * as constants from './constants';

export const baseSelector = (state) => state[constants.REDUCER_KEY];

export const loadingSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA_LOADING],
);

export const errorSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA_ERROR],
);

export const dataSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA] || {},
);

export const appDataSelector = createSelector(
  baseSelector,
  (state) => state[constants.APP_DATA] || {},
);

export const dataTableSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA_TABLE] || {},
);

export const modalLoadingSelector = createSelector(
  baseSelector,
  (state) => state[constants.MODAL_LOADING],
);

export const selectedRowIDSelector = createSelector(
  baseSelector,
  (state) => state[constants.SELECTED_ROW_ID],
);

export const formValuesSelector = (state) => getFormValues('appliancesForm')(state);
export const formMetaSelector = (state) => getFormMeta('appliancesForm')(state);
export const formSyncErrorsSelector = (state) => getFormSyncErrors('appliancesForm')(state);

export const formDataSelector = createSelector(
  baseSelector,
  formValuesSelector,
  (data, formValues) => formValues || data,
);

// alway return top level selector
export default baseSelector;
