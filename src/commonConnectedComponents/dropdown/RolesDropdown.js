import React, { useEffect } from 'react';
import { EntityDropdown } from 'components/entityDropdown';
import { useDispatch, useSelector } from 'react-redux';
import { dropdownActions } from 'ducks/dropdowns/roles';
import { isEmpty } from 'utils/lodash';

export function RolesDropdown(props) {
  const dispatch = useDispatch();
  const { data } = useSelector((state) => state.rolesDropdown);
  
  useEffect(
    () => {
      dispatch(dropdownActions.load());
    },
    [],
  );

  if (isEmpty(data)) return <></>;

  return <EntityDropdown data={data} {...props} />;
}

export default RolesDropdown;
