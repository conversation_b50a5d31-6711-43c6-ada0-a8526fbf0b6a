/* eslint-disable jsx-a11y/label-has-for */
import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import i18n from 'utils/i18n';
import { isEmpty } from 'utils/lodash';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faInfoCircle,
  faClock,
} from '@fortawesome/pro-solid-svg-icons';
  
const svgEnabled = (
  <svg width="38" height="20" viewBox="0 0 38 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0 10C0 4.47715 4.47715 0 10 0H28C33.5228 0 38 4.47715 38 10C38 15.5228 33.5228 20 28 20H10C4.47715 20 0 15.5228 0 10Z" fill="#2160E1" />
    <g filter="url(#filter0_dd_13221_3078)">
      <circle cx="28" cy="10" r="6" fill="white" />
    </g>
    <path d="M16.0156 6.98438C16.3203 7.26562 16.3203 7.75781 16.0156 8.03906L10.0156 14.0391C9.73438 14.3438 9.24219 14.3438 8.96094 14.0391L5.96094 11.0391C5.65625 10.7578 5.65625 10.2656 5.96094 9.98438C6.24219 9.67969 6.73438 9.67969 7.01562 9.98438L9.47656 12.4453L14.9609 6.98438C15.2422 6.67969 15.7344 6.67969 16.0156 6.98438Z" fill="#F7F8FA" />
    <defs>
      <filter id="filter0_dd_13221_3078" x="20" y="2" width="16" height="17" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
        <feMorphology radius="1" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_13221_3078" />
        <feOffset />
        <feGaussianBlur stdDeviation="0.5" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_13221_3078" />
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
        <feOffset dy="1" />
        <feGaussianBlur stdDeviation="1" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.14 0" />
        <feBlend mode="normal" in2="effect1_dropShadow_13221_3078" result="effect2_dropShadow_13221_3078" />
        <feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_13221_3078" result="shape" />
      </filter>
    </defs>
  </svg>
);

const svgDisabled = (
  <svg width="38" height="20" viewBox="0 0 38 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.5 10C0.5 4.7533 4.7533 0.5 10 0.5H28C33.2467 0.5 37.5 4.7533 37.5 10C37.5 15.2467 33.2467 19.5 28 19.5H10C4.7533 19.5 0.5 15.2467 0.5 10Z" fill="#F7F8FA" />
    <path d="M0.5 10C0.5 4.7533 4.7533 0.5 10 0.5H28C33.2467 0.5 37.5 4.7533 37.5 10C37.5 15.2467 33.2467 19.5 28 19.5H10C4.7533 19.5 0.5 15.2467 0.5 10Z" stroke="#BAC2CF" />
    <g filter="url(#filter0_dd_13221_3124)">
      <circle cx="10" cy="10" r="6" fill="#2160E1" />
    </g>
    <path d="M28.5156 12.9844C28.8203 13.2656 28.8203 13.7578 28.5156 14.0391C28.375 14.1797 28.1875 14.25 28 14.25C27.7891 14.25 27.6016 14.1797 27.4609 14.0391L25 11.5781L22.5156 14.0391C22.375 14.1797 22.1875 14.25 22 14.25C21.7891 14.25 21.6016 14.1797 21.4609 14.0391C21.1562 13.7578 21.1562 13.2656 21.4609 12.9844L23.9219 10.5L21.4609 8.03906C21.1562 7.75781 21.1562 7.26562 21.4609 6.98438C21.7422 6.67969 22.2344 6.67969 22.5156 6.98438L25 9.44531L27.4609 6.98438C27.7422 6.67969 28.2344 6.67969 28.5156 6.98438C28.8203 7.26562 28.8203 7.75781 28.5156 8.03906L26.0547 10.5234L28.5156 12.9844Z" fill="#2160E1" />
    <defs>
      <filter id="filter0_dd_13221_3124" x="2" y="2" width="16" height="17" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
        <feMorphology radius="1" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_13221_3124" />
        <feOffset />
        <feGaussianBlur stdDeviation="0.5" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_13221_3124" />
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
        <feOffset dy="1" />
        <feGaussianBlur stdDeviation="1" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.14 0" />
        <feBlend mode="normal" in2="effect1_dropShadow_13221_3124" result="effect2_dropShadow_13221_3124" />
        <feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_13221_3124" result="shape" />
      </filter>
    </defs>
  </svg>

);

function PartnerPermission(props) {
  const { value, cellValue, onEnableDisableRow } = props || {};
  const { permissionStatus } = value || {};
  const { status } = permissionStatus || {};
  const { reason = '' } = status || {};
  let { assumeRole = '' } = status || {};

  const renderStatus = () => {
    if (isEmpty(assumeRole)) {
      assumeRole = cellValue || '';
    }
    if (assumeRole.toUpperCase() === 'ENABLED') {
      return (
        <div className="partner-project-status-container">
          {svgEnabled}
          {i18n.t(assumeRole.toUpperCase())}
        </div>
      );
    }
    if (assumeRole.toUpperCase() === 'DISABLED') {
      return (
        <div className="partner-project-status-container">
          {svgDisabled}
          {i18n.t(assumeRole.toUpperCase())}
        </div>
      );
    }
    return (
      <label htmlFor="status" className="child-checkbox-label">
        {i18n.t(assumeRole)}
      </label>
      
    );
  };
  
  return (
    <div
      onKeyDown={null}
      onClick={onEnableDisableRow}
      // aria-label="Operational Status"
      role="button"
      tabIndex="0">
      {renderStatus()}
    </div>
  );
}

PartnerPermission.propTypes = {
  value: PropTypes.shape({}),
  // eslint-disable-next-line react/no-unused-prop-types
  cellValue: PropTypes.string,
};

PartnerPermission.defaultProps = {
  value: {},
  cellValue: '',
};
  
export default withTranslation()(PartnerPermission);
