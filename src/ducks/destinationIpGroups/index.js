import {
  createAction,
  loading,
  loadSuccess,
  loadError,
} from 'ducks/generics';
import { destroy } from 'redux-form';
import {
  omit, union, get, isNull, isUndefined, isEmpty, map,
} from 'utils/lodash';
import { genericInterface } from 'utils/http';
import { notify, notifyError } from 'ducks/notification';
import { ipAddressesOrRanges, pureFqdn, looseurlAddressSchemeless } from 'utils/validations';
import { checkActivation } from '../activation';
import * as constants from './constants';
import * as selectors from './selectors';

import actionTypes from './action-types';

// we want this to exported for to be used in store and other places
// from index so...elsewhere we can say import {REDUCER_KEY as widget} from 'ducks/widget'
// for exmaple
export { REDUCER_KEY } from './constants';

const actionTypeExists = Object.keys(actionTypes).reduce((hash, key) => ({
  ...hash,
  [actionTypes[key]]: true,
}), {});

// unlike other reducer, we will use
export const reducer = (state = constants.DEFAULT_STATE, action = {}) => {
  // We will merge payload from actions to the state for this widget/page/component
  // that only matches to our actions in actiontypes
  // for example once data is loaded our action below will dispatch
  // action and data
  if (actionTypeExists[action.type]) {
    return {
      ...state,
      ...action.payload,
    };
  }
  return state;
};
export default reducer;

const boundError = loadError(actionTypes.DATA_LOAD_ERROR);
const boundLoading = loading(actionTypes.DATA_LOADING);
const boundSuccess = loadSuccess(actionTypes.DATA_LOAD_SUCCESS);
const dataChanged = createAction(actionTypes.DATA_CHANGED);
const boundModalLoading = createAction(actionTypes.MODAL_LOADING);

export const getDestinationIPGroupData = () => (dispatch) => {
  dispatch(boundSuccess(constants.DATA));
};

export const toggleForm = (appData = constants.DEFAULT_FORM_DATA, toggle, formTitle) => (dispatch) => {
  if (toggle) {
    if (appData && appData.id) {
      dispatch(dataChanged({
        [constants.FORM_TITLE]: 'EDIT_DESTINATION_IP_GROUP',
        [constants.SHOW_FORM]: toggle,
        [constants.APP_DATA]: appData,
        [constants.FORM_MODE]: formTitle === 'ADD_DESTINATION_IP' ? 'NEW' : 'EDIT',
      }));
    } else {
      dispatch(dataChanged({
        [constants.FORM_TITLE]: 'ADD_DESTINATION_IP_GROUP',
        [constants.SHOW_FORM]: toggle,
        [constants.APP_DATA]: appData,
        [constants.FORM_MODE]: formTitle === 'ADD_DESTINATION_IP' ? 'NEW' : 'EDIT',
        [constants.TYPE]: 'DSTN_IP',
      }));
    }
  } else {
    dispatch(dataChanged({
      [constants.IP_ADDRESSES]: [],
      [constants.WILD_FQDN]: [],
      [constants.FQDN]: [],
      [constants.SHOW_FORM]: toggle,
      [constants.APP_DATA]: appData,
    }));
  }
};

export const loadDestinationIPData = (hardLoad) => (dispatch, getState) => {
  const currentState = getState()[constants.REDUCER_KEY];
  if (!hardLoad && currentState.destinationIPTableData.length > 0) {
    return;
  }
  dispatch(boundLoading({ loading: true }));
  const destinationIPApi = genericInterface(constants.API_ENDPOINT);
  return destinationIPApi.read()
    .then((response) => {
      dispatch(dataChanged({
        loading: false,
        [constants.DATA_SG_TABLE]: response.data,
      }));
    })
    .catch((error) => {
      const errorMsg = get(error, 'response.data.message', 'There is a problem with loading location templates, Please try again later.');
      dispatch(boundError(errorMsg));
    });
};

export const localData = () => (dispatch) => {
  dispatch(boundSuccess(constants.DATA));
};

export const toggleDeleteForm = (data, toggle) => (dispatch) => {
  dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
  dispatch(dataChanged({
    [constants.SHOW_DELETE_FORM]: toggle,
    [constants.SELECTED_ROW_ID]: data ? data.id : null,
  }));
};

export const deleteDestinationIP = (rowData) => (dispatch) => {
  dispatch(boundModalLoading({ [constants.MODAL_LOADING]: true }));
  const rowId = rowData;
  const deleteDestinationIpApi = genericInterface(constants.API_UPDATE_DELETE_ENDPOINT(rowId));

  return deleteDestinationIpApi.del()
    .then(() => {
      dispatch(toggleDeleteForm(false));
      dispatch(destroy('deleteConfirmationForm'));
      dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
      dispatch(notify('SUCCESSFULLY_DELETED'));
      dispatch(loadDestinationIPData(true));
      dispatch(checkActivation());
    })
    .catch((error) => {
      const errorMsg = get(error, 'response.data.message', 'There is a problem with deleting Destination IP, Please try again later.');
      dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
      dispatch(notifyError(errorMsg));
    });
};

const getCountryValues = (countryArray) => {
  const updateValues = map(countryArray, (el) => el.name);
  return updateValues.filter((x) => !(x === 'ANY' || x === 'NONE'));
};

const beforeSave = (dispatch, values, address, ipAddressValid, fqdnValid, wildFqdnValid) => {
  let updatedValue = {};
  let omitKeys = [
    'isDeletable',
    'isEditable',
    'isReadOnly',
  ];
  let addresses = [];
  // throw error if IP Address field has any non-ip values
  // if (values.id) {
  // updatedValue = values;
  // } else {
  if (!isNull(ipAddressValid) && address.type === 'DSTN_IP') {
    dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
    if (isEmpty(address.ipAddressesData)) {
      dispatch(notifyError('IP Address field cannot be empty!'));
    } else {
      dispatch(notifyError(ipAddressValid));
    }
    return null;
  }
  if (!isNull(fqdnValid) && address.type === 'DSTN_FQDN') {
    dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
    if (isEmpty(address.fqdnData)) {
      dispatch(notifyError('FQDN field cannot be empty!'));
    } else {
      dispatch(notifyError(fqdnValid));
    }
    return null;
  }
  if (!isNull(wildFqdnValid) && address.type === 'DSTN_DOMAIN') {
    dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
    if (isEmpty(address.wildFqdnData)) {
      dispatch(notifyError('Domains field cannot be empty!'));
    } else {
      dispatch(notifyError(wildFqdnValid));
    }
    return null;
  }
  if (!values.countries && address.type === 'DSTN_OTHER') {
    dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
    if (isUndefined(values.countries)) {
      dispatch(notifyError('Country field cannot be empty!'));
    } else {
      dispatch(notifyError('There is validation error, Please check.'));
    }
    return null;
  }
    
  if (values.description !== '' && !isUndefined(values.description)) {
    updatedValue.description = values.description;
  }

  if (address.type === 'DSTN_IP') {
    addresses = address.ipAddressesData;
  } else if (address.type === 'DSTN_FQDN') {
    addresses = address.fqdnData;
  } else if (address.type === 'DSTN_DOMAIN') {
    addresses = address.wildFqdnData;
  }

  updatedValue.name = values.name;
  updatedValue.addresses = addresses;
  updatedValue.type = address.type;

  if (address.type === 'DSTN_OTHER') {
    omitKeys = union(omitKeys, 'addresses');
    updatedValue.countries = getCountryValues(values.countries);
    // updatedValue.ipCategories = getCategoryValues(values.ipCategories);
  }
    
  // }

  updatedValue = omit(updatedValue, ...omitKeys);

  const updatedFormValues = {
    ...updatedValue,
  };

  return updatedFormValues;
};

export const saveForm = (values) => (dispatch, getState) => {
  dispatch(boundModalLoading({ [constants.MODAL_LOADING]: true }));

  const currentState = getState();
  const type = values.type ? values.type : selectors.typeSelector(currentState);
  const ipAddressesData = selectors.ipAddressesSelector(currentState);
  const fqdnData = selectors.fqdnSelector(currentState);
  const wildFqdnData = selectors.wildFqdnSelector(currentState);

  const address = {
    ipAddressesData,
    fqdnData,
    wildFqdnData,
    type,
  };
  
  const ipAddressValid = ipAddressesOrRanges(ipAddressesData.join(','));
  const fqdnValid = pureFqdn(fqdnData.join(','));
  const wildFqdnValid = looseurlAddressSchemeless(wildFqdnData.join(','));
  const savedestinationIPApi = genericInterface(constants.API_ENDPOINT);
  const updateDestinationIpApi = genericInterface(constants.API_UPDATE_DELETE_ENDPOINT(values.id));
  const apiEndPoint = values.id ? updateDestinationIpApi.update : savedestinationIPApi.create;
  const updatedFormValues = beforeSave(
    dispatch,
    values,
    address,
    ipAddressValid,
    fqdnValid,
    wildFqdnValid,
  );

  if (isNull(updatedFormValues)) {
    return null;
  }
  return apiEndPoint(updatedFormValues, {})
    .then(() => {
      dispatch(toggleForm({}, false, ''));
      dispatch(destroy('destinationIPGroupForm'));
      dispatch(notify('SUCCESSFULLY_SAVED'));
      dispatch(loadDestinationIPData(true));
      dispatch(checkActivation());
      dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
      dispatch(dataChanged({
        [constants.WILD_FQDN]: [],
        [constants.IP_ADDRESSES]: [],
        [constants.FQDN]: [],
        [constants.TYPE]: 'DSTN_IP',
      }));
    })
    .catch((error) => {
      const errorMsg = get(error, 'response.data.message', 'There is a problem with saving Destination IP Group, Please try again later.');
      dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
      dispatch(notifyError(errorMsg));
    });
};

export const handleIPAddresses = (ipAddressList) => (dispatch) => {
  dispatch(dataChanged({
    [constants.IP_ADDRESSES]: ipAddressList,
  }));
};

export const handleFQDNAddresses = (ipAddressList) => (dispatch) => {
  dispatch(dataChanged({
    [constants.FQDN]: ipAddressList,
  }));
};

export const handleWILDFQDNAddresses = (ipAddressList) => (dispatch) => {
  dispatch(dataChanged({
    [constants.WILD_FQDN]: ipAddressList,
  }));
};

export const handleTypeChange = (event) => (dispatch) => {
  dispatch(dataChanged({
    [constants.TYPE]: event.currentTarget.value,
  }));
};

export const updatedCountry = (data) => (dispatch) => {
  dispatch(dataChanged({
    [constants.SELECTED_COUNTRY]: data,
  }));
};

export const toggleViewModal = (appData, toggle) => (dispatch) => {
  if (typeof appData !== 'undefined' && !isNull(appData)) {
    dispatch(dataChanged({
      [constants.SHOW_VIEW_FORM]: toggle,
      [constants.APP_DATA]: appData,
    }));
  } else {
    dispatch(dataChanged({
      [constants.SHOW_VIEW_FORM]: toggle,
      [constants.APP_DATA]: {},
    }));
  }
};
