@import "scss/colors.scss";
@import "scss/mixins.scss";

.ec-root-page {
.modal-overlay {
  z-index: 9;
  background-color: $grey19;
  bottom: 0;
  left: 0;
  opacity: 0;
  position: fixed;
  right: 0;
  top: 0;
  transition: opacity 400ms ease-in-out;

  &.ReactModal__Overlay--after-open{
    opacity: 1;
  }

  &.ReactModal__Overlay--before-close{
    opacity: 0;
  }
  .modal-plain-body {
    bottom: auto;
    left: calc(50% - 335px);
    right: auto;
    top: 50px;
  }
  .modal-body {
    bottom: auto;
    left: calc(50% - 343px);
    position: absolute;
    right: auto;
    top: 50px;
    width: 686px;

    .modal-header {
      @include DisplayFlex;
      align-items: center;
      background-color: var(--semantic-color-background-primary);
      color: var(--semantic-color-content-base-primary);
      border-radius: 5px 5px 0 0;
      font-size: 16px;
      font-weight: 500;
      justify-content: space-between;
      min-height: 50px;
      padding: 0 16px;
      
      .close-button {
        background-color: $transparent;
        border: none;

        .fa-times,.fa-xmark {
          color: var(--semantic-color-content-interactive-primary-default);
          font-size: 20px;
        }
      }
    }

    .modal-content {
      background: var(--semantic-color-background-primary);
      border-radius: 0 0 5px 5px;

      .input-center-vertical {
        color: var(--semantic-color-content-base-primary);
      }

      .modal-footer {
        @include DisplayFlex;
        align-items: center;
        background: var(--semantic-color-background-primary);
        border-radius: 0 0 5px 5px;
        height: 60px;
        margin-top:  29px;
        padding: 0 16px;

        button {
          background-color: var(--semantic-color-surface-base-primary);      
          border: none;
          color: var(--semantic-color-content-interactive-primary-default);
          font-size: 16px;
          line-height: 19px;

          &.primary-button {
            border-radius: 5px;
            background-color: var(--semantic-color-content-interactive-primary-default);
            color: var(--semantic-color-surface-base-primary);      
            font-size: 16px;
            font-weight: 500;
            height: 32px;
            padding: 0 17px;

            &.disabled{
              background-color: var(--semantic-color-surface-base-primary);      
              color: var(--semantic-color-content-interactive-primary-default);
              box-shadow: none;
            }
          }

          &:not(:first-of-type) {
            margin-left:  24px;
          }

          &.disabled, &:disabled {
            box-sizing: border-box;
            border: 1px solid #CACBCC;
            border-radius: 5px;
            background-color: #F7F9FA;
            color: #CACBCC;
            cursor: not-allowed;
          }
        }
      }
      .modal-footer.white-background {
        background-color: var(--semantic-color-surface-base-primary);      
        height: auto;
        margin-top: 0; 
        padding: 16px;
      }
    }
  }
}
}