/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import SearchedValues from './component/SearchValues';
import ErrorMessage from './component/ErrorMessage';
import ListAll from './component/ListAll';
import Footer from './component/Footer';

import './index.scss';

function ListBuilder(props) {
  const { value: initialValue } = props;
  const [autoFocus, setAutoFocus] = useState(false);
  const [newIssuers, setNewIssuers] = useState('');
  const [value, setValue] = useState(initialValue || []);
  const [search, setSearch] = useState();
  useEffect(() => {
    setValue(initialValue || []);
  }, [initialValue]);

  const handleRemoveAll = async () => {
    const { action } = props;

    await action([]);
  };

  const handleAdd = async () => {
    const { action } = props;
    newIssuers.split(/[\n ,]+/).forEach((item) => {
      const input = item.trim();

      if (input !== '' && value.indexOf(input) === -1) {
        value.push(input);
      }
    });
    setNewIssuers('');
    await handleRemoveAll();
    await action(value);
  };

  const handleNewIssuers = (e) => {
    e.stopPropagation();
    setNewIssuers(e.target.value);
  };

  const handleBlur = () => {
    if (newIssuers.length > 0) handleAdd();
    setAutoFocus(false);
  };

  const handleFocus = () => {
    setAutoFocus(true);
  };

  const handleRemove = async (removeItem) => {
    const { action } = props;
    await action(value.filter((item) => {
      return item !== removeItem;
    }));
  };

  // eslint-disable-next-line no-shadow
  const handleSearchFilter = (event, value) => setSearch(value);
   
  const valueLength = value.length;
  const hasValue = valueLength > 0;

  const {
    addButtonText, attribute, error, id, placeholder,
    removeItemsText, t, hasSearch, validation, disabled, meta,
  } = props;
  const isInvalid = meta.touched && (value.some((x) => validation(x) !== '') || !!meta.error);

  let errorMessage = error;

  if (Array.isArray(error)) {
    errorMessage = error[0].message;
  }

  return (
    <div className="list-builder">
      <div
        className={`list-builder-header ${error ? 'has-error' : ''}`}>
        <textarea
          // eslint-disable-next-line jsx-a11y/no-autofocus
          autoFocus={autoFocus}
          className={`list-builder-input-textarea  ${isInvalid ? 'invalid' : ''} ${hasValue ? 'non-empty' : ''}`}
          id={id}
          disabled={disabled}
          value={newIssuers}
          name={attribute}
          onChange={handleNewIssuers}
          onBlur={handleBlur}
          onFocus={handleFocus}
          placeholder={t(placeholder)} />
        {!disabled && (
          <span
            className="button primary list-builder-add"
            onClick={handleAdd}>
            {t(addButtonText || 'ADD_ITEMS')}
          </span>
        )}
      </div>
      <div
        className={`list-builder-body ${error ? 'has-error' : ''}`}>
        {error && <ErrorMessage message={errorMessage} /> }
        {hasValue
                    && (
                      <>
                        {hasSearch && <SearchedValues onSearchFilter={handleSearchFilter} />}
                        <ListAll
                          list={value}
                          search={search}
                          validation={validation}
                          disabled={disabled}
                          onRemove={handleRemove} />
                        <Footer
                          valueLength={valueLength}
                          disabled={disabled}
                          removeItemsText={t(removeItemsText || 'REMOVE_ALL_ITEMS')}
                          totalItemText={t('ITEMS_TOTAL')}
                          onRemoveAll={handleRemoveAll} />
                      </>
                    )}
      </div>

    </div>
  );
}

ListBuilder.propTypes = {
  t: PropTypes.func,
  hasSearch: PropTypes.bool,
  action: PropTypes.func.isRequired,
  addButtonText: PropTypes.string,
  attribute: PropTypes.string, // .isRequired,
  error: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.array,
  ]),
  id: PropTypes.string,
  disabled: PropTypes.bool,
  placeholder: PropTypes.string,
  removeItemsText: PropTypes.string,
  value: PropTypes.arrayOf(PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.string,
  ])),
  validation: PropTypes.func,
  meta: PropTypes.shape({}),
};

export default (ListBuilder);
// connect(mapStateToProps, mapDispatchToProps)(withTranslation()
