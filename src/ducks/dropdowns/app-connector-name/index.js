import { genericInterface } from 'utils/http';
import { createAction, loading } from 'ducks/generics';
import genericDropdown from 'ducks/generics/dropdown';
import { change } from 'redux-form';
import { notifyError } from 'ducks/notification';
import {
  get, isEmpty, uniqBy,
} from 'utils/lodash';
import actionTypes from './action-types';
import selector, * as selectors from './selectors';
import * as constants from './constants';

export { REDUCER_KEY } from './constants';

const actionTypeExists = Object.keys(actionTypes).reduce((hash, key) => ({
  ...hash,
  [actionTypes[key]]: true,
}), {});

export const reducer = (state = constants.DEFAULT_STATE, action = {}) => {
  // Simplifyeverything: actions provide a payload, and we merge that payload.
  if (actionTypeExists[action.type]) {
    return {
      ...state,
      ...action.payload,
    };
  }
  return state;
};
export default reducer;

let http;

const handleAppConnectorNameChange = (form, initialValue) => (dispatch) => {
  dispatch(change(form, 'appConnectorNameDropdown', initialValue));
};

const boundLoading = loading(actionTypes.DATA_LOADING);
const boundError = createAction(actionTypes.DATA_LOAD_ERROR);
const boundDataLoadSuccess = createAction(actionTypes.DATA_LOAD_SUCCESS);

export const loader = (initializeProps, groupName, search = '') => async (dispatch, getState) => {
  http = genericInterface(constants.API_ENDPOINT(groupName && groupName.externalId));
  const state = getState();
  if (initializeProps) {
    state.appConnectorNameDropdown.pageNum = 1;
    state.appConnectorNameDropdown.data = [];
    state.appConnectorNameDropdown.hasMorePages = false;
    state.appConnectorNameDropdown.loading = false;
    // state.appConnectorNameDropdown.searchString = search;
  }
  const { pageNum, pageSize, searchString } = state.appConnectorNameDropdown;
  const param = searchString
    ? { page: pageNum, pageSize, search: searchString }
    : { page: pageNum, pageSize };
  dispatch(
    boundLoading({
      [constants.DATA]: [],
      ...constants.DEFAULT_STATE,
      searchString: search,
      loading: true,
    }),
  );
  try {
    const appConnectorNameDropdown = await http.read(null, {
      params: param,
    });
    const { data = [] } = appConnectorNameDropdown || {};

    const dataWithId = data.map((x) => ({ id: x.name, ...x }));

    const combinedData = [
      ...state.appConnectorNameDropdown.data,
      ...dataWithId];
    const updatedData = uniqBy(combinedData, 'id');

    dispatch(boundDataLoadSuccess({
      [constants.DATA]: updatedData,
      [constants.DROPDOWN.PAGE_NUMBER]: pageNum + 1,
      [constants.DROPDOWN.HAS_MORE_PAGES]: data.length >= pageSize && pageSize > 0,
      searchString: search,
      loading: false,
    }));
  } catch (error) {
    const errorMsg = get(error, 'response.data.message');
    dispatch(boundLoading({ loading: false }));
    dispatch(boundError(errorMsg));
    if (isEmpty(errorMsg)) dispatch(notifyError('ERROR_LOADING'));
    else dispatch(notifyError(errorMsg));
  }
};

const handleSearchStringChange = (searchString) => (dispatch) => {
  dispatch(boundLoading({ searchString }));
};

const perfomSearch = (groupName) => (dispatch, getState) => {
  const state = getState();
  const { searchString } = state.appConnectorNameDropdown;
  dispatch(loader(true, groupName, searchString));
};

const dropdownActions = genericDropdown.createActions({ actionTypes, selector, http });
export {
  selector,
  selectors,
  dropdownActions,
  handleAppConnectorNameChange,
  handleSearchStringChange,
  perfomSearch,
};
