import { genericInterface } from 'utils/http';
import { createAction, loading } from 'ducks/generics';
import genericDropdown from 'ducks/generics/dropdown';
import { notifyError } from 'ducks/notification';
import {
  get, isEmpty, uniqBy,
} from 'utils/lodash';
import actionTypes from './action-types';
import selector, * as selectors from './selectors';
import * as constants from './constants';

export { REDUCER_KEY } from './constants';

const actionTypeExists = Object.keys(actionTypes).reduce((hash, key) => ({
  ...hash,
  [actionTypes[key]]: true,
}), {});

export const reducer = (state = constants.DEFAULT_STATE, action = {}) => {
  // Simplifyeverything: actions provide a payload, and we merge that payload.
  if (actionTypeExists[action.type]) {
    return {
      ...state,
      ...action.payload,
    };
  }
  return state;
};
export default reducer;

const http = genericInterface(constants.API_ENDPOINT);

const boundLoading = loading(actionTypes.DATA_LOADING);
const boundError = createAction(actionTypes.DATA_LOAD_ERROR);
const boundDataLoadSuccess = createAction(actionTypes.DATA_LOAD_SUCCESS);
// const boundModalLoading = createAction(actionTypes.MODAL_LOADING);

const handleSearchStringChange = (searchString) => (dispatch) => {
  dispatch(boundLoading({ searchString }));
};

export const loader = (initializeProps, search = '', modelType, defaultData = {}) => async (dispatch, getState) => {
  const state = getState();
  if (initializeProps) {
    state.appliancesEntityDropdown.pageNum = 1;
    state.appliancesEntityDropdown.data = [];
    state.appliancesEntityDropdown.hasMorePages = false;
    state.appliancesEntityDropdown.loading = false;
    state.appliancesEntityDropdown.searchString = search;
  }
  const { pageNum, pageSize, searchString } = state.appliancesEntityDropdown;
  const param = searchString
    ? {
      page: pageNum, pageSize, modelType, search: searchString, getUnused: true,
    }
    : {
      page: pageNum, pageSize, modelType, getUnused: true,
    };
  dispatch(
    boundLoading({
      [constants.DATA]: [],
      ...constants.DEFAULT_STATE,
      searchString: search,
      loading: true,
    }),
  );
  try {
    const appliancesEntity = await http.read(null, {
      params: param,
    });
    const appliancesEntityFull = await http.read(null, {
      params: {
        page: pageNum, pageSize, modelType,
      },
    });
    const fullData = appliancesEntityFull.data || [];

    const { data } = appliancesEntity || [];
    // get current selected appliance
    let updateData = fullData.filter((item) => item.serialNumber === defaultData.serialNumber);
    updateData = [...data, ...updateData];
    const dataWithId = updateData.map((x) => ({
      ...x,
      name: x.serialNumber,
      realName: x.name || x.serialNumber,
    }));

    const combinedData = [
      ...state.appliancesEntityDropdown.data,
      ...dataWithId];
    const updatedData = uniqBy(combinedData, 'id');
   
    dispatch(boundDataLoadSuccess({
      [constants.DATA]: updatedData,
      [constants.DROPDOWN.PAGE_NUMBER]: pageNum + 1,
      [constants.DROPDOWN.HAS_MORE_PAGES]: data.length >= pageSize && pageSize > 0,
      searchString: search,
      loading: false,
    }));
  } catch (error) {
    const errorMsg = get(error, 'response.data.message');
    dispatch(boundError({ errorMsg, loading: false }));
    if (isEmpty(errorMsg)) dispatch(notifyError('ERROR_LOADING'));
    else dispatch(notifyError(errorMsg));
  }
};

const perfomSearch = (modelType) => (dispatch, getState) => {
  const state = getState();
  const { searchString } = state.appliancesEntityDropdown;
  dispatch(loader(true, searchString, modelType));
};

const dropdownActions = genericDropdown.createActions({ actionTypes, selector, http });
export {
  selector,
  selectors,
  dropdownActions,
  handleSearchStringChange,
  perfomSearch,
};
