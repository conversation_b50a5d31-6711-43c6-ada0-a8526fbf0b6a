.ec-root-page {
.modal-overlay .modal-body {
  &.disable-cc-group {
    top: calc(50% - 20rem);
  }
  &.enable-cc-group {
    top: calc(50% - 20rem);
  }
}

.delete-confirm-form {
  overflow: hidden;
  border: 1px solid var(--semantic-color-border-base-primary);
  &.add-custom-app-form {
    .form-sections-container {
      .form-section {
        margin-top: 20px;
        color: var(--semantic-color-content-base-primary);
      }
    }
  }
  .modal-text {
    width: 100%;
    padding-bottom: 10px;
  }
}

.delete-confirm-form.cc-group {
  .form-sections-container {
    max-height: fit-content;
    background: var(--semantic-color-surface-base-primary);
    .form-section{
      background: var(--semantic-color-background-primary);
    }
  }
  .are-you-sure-you-want {
    color:  var(--semantic-color-content-base-primary);
    font-size: 13px;
    letter-spacing: 0;
    line-height: 20px;
    margin-bottom: 16px;
  }
  .cc-group-items{
    margin-bottom: 16px;
    display: flex;
    flex-direction: column;
  }
  .cc-group-delete-alert{
    color:  var(--semantic-color-content-base-primary);
    font-size: 11px;
    letter-spacing: 0;
    line-height: 15px;
  }
  .cc-group-title {
    font-weight: 500;
    font-size: 13px;
    line-height: 20px;
    margin: 0;
  }
  .cc-group-text {
    font-size: 13px;
    line-height: 20px;
    margin: 0;
    color:  var(--semantic-color-content-base-primary);
  }
}

.modal-overlay {
  .modal-body.delete-cc-group {
    max-width: 400px;
    left: calc(50% - 200px);
  }
  .modal-content.delete-cc-group {
    min-height: fit-content;
  }
}

.modal-overlay{
  .modal-body.disable-data-collection, .modal-body.delete-accounts {
    max-width: 480px;
    left: calc(50% - 240px);
    top: calc(50% - 168px - 10px);

    .form-section {
      background-color: var(--semantic-color-surface-base-primary);      
    }
    .delete-confirm-form {
      background-color: var(--semantic-color-background-primary);
      color: var(--semantic-color-content-base-primary);
    }

    .modal-text {
      color: var(--semantic-color-content-base-primary);
      font-size: 13px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
    }
    .are-you-sure-text {
      color: var(--semantic-color-content-base-primary);
      font-size: 13px;
      font-style: normal;
      font-weight: 500;
      line-height: 24px;
    }
    .are-you-sure {
      display: flex;
      justify-content: center;
      #areYouSure {
        margin-right: 8px;
      }
    }
    .i-uderstand-the-consequence {
      color: var(--semantic-color-content-base-primary);
      /* Paragraph/S | Regular */
      font-size: 13px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
    }
    .form-footer {
      // display: flex;
      // padding: 12px 20px;
      // flex-direction: column;
      // justify-content: center;
      // align-items: flex-start;
      // gap: 10px;
      // align-self: stretch;
      border-radius: 0px 0px 8px 8px;
      background: var(--semantic-color-background-primary);
    }
  }
}
}