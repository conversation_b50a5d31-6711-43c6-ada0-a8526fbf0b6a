import React, {
  useState, useEffect, useRef, useMemo,
} from 'react';
import PropTypes from 'prop-types';
import Loading from '../spinner/Loading';
import MultiDropdownPanel from './MultiDropdownPanel';
import SingleDropdownPanel from './SingleDropdownPanel';
import DropdownSearch from './DropdownSearch';
import DropdownFooter from './DropdownFooter';
import DefaultDropdown from './DefaultDropdown';

function DropdownPanel(props) {
  const {
    value,
    isMulti,
    isRadio,
    showSearch,
    options,
    loading,
    handleChange,
    hideDropdown,
    width,
    maxWidth,
    defaultSelected,
    triggerChangeOnSelect,
    triggerChange,
    designType,
  } = props;
  const dropdownRef = useRef();

  const [selectedValues, setSelectedValues] = useState(value);
  const [initialValues, setInitialValues] = useState(selectedValues);
  const [searchText, setSearchText] = useState('');

  useEffect(() => {
    setInitialValues(selectedValues);
  }, []);

  const getOptions = options.map((obj) => {
    return {
      label: obj.label,
      value: obj.value,
      disabled: obj.disabled ? obj.disabled : false,
    };
  });

  useEffect(() => {
    handleChange(selectedValues);
  }, [selectedValues]);

  const handleFooterChange = (val) => {
    setSelectedValues(val);
    setTimeout(() => {
      hideDropdown(val);
    }, 0);
  };

  const renderSingleDropdownPanel = () => (
    <SingleDropdownPanel
      options={getOptions}
      value={value}
      selectedValues={selectedValues}
      dropdownRef={dropdownRef}
      setSelectedValues={(val) => setSelectedValues(val)}
      searchText={searchText}
      setSearchText={setSearchText}
      hideDropdown={hideDropdown}
      isRadio={isRadio}
      showSearch={showSearch}
      designType={designType} />
  );

  const renderMultiDropdownPanel = () => (
    <MultiDropdownPanel
      options={getOptions}
      value={value}
      selectedValues={selectedValues}
      dropdownRef={dropdownRef}
      setSelectedValues={(val) => setSelectedValues(val)}
      triggerChangeOnSelect={triggerChangeOnSelect}
      triggerChange={triggerChange}
      searchText={searchText}
      setSearchText={setSearchText}
      hideDropdown={hideDropdown}
      defaultSelected={defaultSelected}
      showSearch={showSearch} />
  );

  const renderDefaultDropdown = () => (
    <DefaultDropdown
      options={getOptions}
      onChange={(localSelected, _options) => {
        const newList = _options
          .filter(({ value: v }) => localSelected.includes(v))
          .map(({ label, value: _v }) => ({ label, value: _v }));
        setSelectedValues(newList);
        // hideDropdown(newList);
      }}
      selected={selectedValues.map(({ value: v }) => v)}
      initialValues={initialValues}
      onCancel={(val) => handleFooterChange(val)}
      onOk={(val) => handleFooterChange(val)}
      loading={loading}
      showSearch={showSearch} />
  );

  const renderDropdownContent = useMemo(() => {
    if (isMulti && designType === 'normal') {
      return renderMultiDropdownPanel();
    }
    if (isMulti && getOptions.length > 0 && designType !== 'normal') {
      return renderDefaultDropdown();
    }
    if (isMulti && getOptions.length < 0 && designType !== 'normal') {
      return renderMultiDropdownPanel();
    }
    return renderSingleDropdownPanel();
  }, [selectedValues, searchText]);

  return (
    <div
      ref={dropdownRef}
      className="dropdown-multiselect-wrapper"
      style={{ width, maxWidth }}>
      {loading ? (
        <Loading isOverlay={false} />
      ) : (
        <>
          {designType === 'newFilter' ? null : (
            <DropdownSearch
              value={searchText}
              onChange={setSearchText}
              showSearch={showSearch} />
          )}
          {renderDropdownContent}
          {designType === 'newFilter' ? null : (
            <DropdownFooter
              defaultSelected={defaultSelected}
              initialValues={initialValues}
              handleFooterClick={(val) => handleFooterChange(val)}
              isMulti={isMulti} />
          )}
        </>
      )}
    </div>
  );
}

DropdownPanel.propTypes = {
  options: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
      disabled: PropTypes.bool,
    }),
  ).isRequired,
  value: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
    }),
  ).isRequired,
  triggerChangeOnSelect: PropTypes.func,
  triggerChange: PropTypes.func,
  designType: PropTypes.string,
  isMulti: PropTypes.bool,
  isRadio: PropTypes.bool,
  showSearch: PropTypes.bool,
  loading: PropTypes.bool,
  width: PropTypes.string,
  maxWidth: PropTypes.string,
  hideDropdown: PropTypes.func.isRequired,
  handleChange: PropTypes.func.isRequired,
  defaultSelected: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
    }),
  ),
};

DropdownPanel.defaultProps = {
  isMulti: true,
  showSearch: true,
  loading: false,
  width: '224px',
  maxWidth: '400px',
};

export default DropdownPanel;
