import { get } from 'utils/lodash';
import { createSelector } from 'reselect';
import { getLocation } from 'utils/helpers';
import * as constants from './constants';

// get login state
export const baseSelector = (state) => state[constants.REDUCER_KEY];

// get data object from this state
export const dataSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA] || [], // return the data arr
);

export const loadingSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA_LOADING],
);

export const errorSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA_ERROR],
);

export const cities = createSelector(
  errorSelector,
  loadingSelector,
  dataSelector,
  (error, loading, citiesResponse) => {
    const payload = get(citiesResponse, 'top[0].payload', []);
    const data = payload.map((item) => {
      const dimensions = get(item, 'dimensions', {});
      const { city, state, country } = dimensions;
      const location = getLocation(city, state, country);
      return {
        id: location.id,
        name: location.name,
        filter: {
          city: { in: [city] },
          state: { in: [state] },
          country: { in: [country] },
        },
      };
    });
    return {
      error,
      loading,
      data,
    };
  },
);

// alway return top level selector
export default baseSelector;
