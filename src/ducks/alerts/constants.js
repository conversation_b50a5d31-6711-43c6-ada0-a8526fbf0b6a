import { BASE_API_PATH } from 'config';

export const REDUCER_KEY = 'alerts';
export const API_ENDPOINT = `${BASE_API_PATH}/api/v1/reports`;

export const DATA_ERROR = 'error';
export const DATA_LOADING = 'loading';
export const DATA = 'data';

export const ALERT_RULES_FILTER = {
  id: 'alertRulesFilter',
  dataSrc: '/ec/api/v1/departments',
  name: 'rile_id',
};

export const IMPACTED_DEVICES_FILTER = {
  id: 'impactedDevicesFilter',
  dataSrc: '/ec/api/v1/devices',
  name: 'device_id',
};

export const IMPACTED_GEO_FILTER = {
  id: 'impactedGeoFilter',
  dataSrc: '/ec/api/v1/departments',
  name: 'geo_id',
};

export const IMPACTED_APPS_FILTER = {
  id: 'impactedAppsFilter',
  dataSrc: '/ec/api/v1/applications',
  name: 'app_id',
};

export const FILTERS = [
  ALERT_RULES_FILTER,
  IMPACTED_DEVICES_FILTER,
  IMPACTED_GEO_FILTER,
  IMPACTED_APPS_FILTER,
];

export const RESOURCE_DEFAULTS = {
  [DATA_ERROR]: null,
  [DATA_LOADING]: true,
  [DATA]: [],
};

export const DEFAULTS = {
  ...RESOURCE_DEFAULTS,
};

export const DEFAULT_STATE = {
  ...DEFAULTS,
};

export const DUMMY_STORE_DATA = {
  [REDUCER_KEY]: {
    ...DEFAULT_STATE,
  },
};

export const MOCK_DATA = {
  data: [
    {
      id: 12,
      severity: 12,
      ruleName: 'test name',
      impactedApp: 'ServiceNow',
      type: 'Network',
      impactedGeo: [{}, {}],
      impactedDevices: [],
      startedOn: 1576846838187,
      endedOn: null,
      enabled: false,
    },
    {
      id: 45,
      severity: 45,
      ruleName: 'test name 2',
      impactedApp: 'SharePoint',
      type: 'Network',
      impactedGeo: [{}],
      impactedDevices: [{}, {}],
      startedOn: 1576846830187,
      endedOn: 1576846838187,
      enabled: true,
    },
    {
      id: 75,
      severity: 75,
      ruleName: 'test name 3',
      impactedApp: 'Box.net',
      type: 'Network',
      impactedGeo: [{}, {}, {}],
      impactedDevices: [{}],
      startedOn: 1576846830187,
      endedOn: 1576846838187,
      enabled: false,
    },
  ],
};
