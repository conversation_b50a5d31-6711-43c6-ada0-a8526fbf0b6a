// @flow

import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';

function OutOf(props) {
  const { number, t } = props;

  return (
    <span className="out-of">
      {t('OUT_OF')}
      {` ${number}`}
    </span>
  );
}

OutOf.propTypes = {
  number: PropTypes.number,
  t: PropTypes.func,
};

OutOf.defaultProps = {
  number: 100,
  t: (str) => str,
};

export default withTranslation()(OutOf);
export { OutOf };
