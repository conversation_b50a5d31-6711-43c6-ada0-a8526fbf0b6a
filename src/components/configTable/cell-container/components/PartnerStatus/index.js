/* eslint-disable jsx-a11y/label-has-for */
import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import i18n from 'utils/i18n';
import { isEmpty } from 'utils/lodash';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faInfoCircle,
  faClock,
} from '@fortawesome/pro-solid-svg-icons';
  
function PartnerPermission(props) {
  const { value, cellValue, onEnableDisableRow } = props || {};
  const { permissionStatus } = value || {};
  const { status } = permissionStatus || {};
  const { reason = '' } = status || {};
  let { assumeRole = '' } = status || {};

  const renderStatus = () => {
    if (isEmpty(assumeRole)) {
      assumeRole = cellValue || '';
    }
    if (assumeRole.toUpperCase() === 'ALLOWED' || assumeRole.toUpperCase() === 'SUCCESS' || assumeRole.toUpperCase() === 'ACTIVATED'
    || assumeRole.toUpperCase() === 'ENABLED' || assumeRole.toUpperCase() === 'HEALTHY') {
      return (
        <label htmlFor="status" className="permission-status-cell child-checkbox-label">
          <FontAwesomeIcon
            icon={faInfoCircle}
            className="success-icon" />
          {' '}
          {i18n.t(assumeRole.toUpperCase())}
        </label>
      );
    }
    if (assumeRole.toUpperCase() === 'DISABLED') {
      return (
        <label htmlFor="status" className="permission-status-cell disabled child-checkbox-label ">
          <FontAwesomeIcon
            icon={faClock}
            className="fa-circle-x-mark-icon" />
          {' '}
          {i18n.t(assumeRole.toUpperCase())}
        </label>
      );
    }
    return (
      <label htmlFor="status" className="child-checkbox-label">
        {i18n.t(assumeRole)}
      </label>
      
    );
  };
  
  return (
    <div
      onKeyDown={null}
      onClick={onEnableDisableRow}
      // aria-label="Operational Status"
      role="button"
      tabIndex="0">
      {renderStatus()}
    </div>
  );
}

PartnerPermission.propTypes = {
  value: PropTypes.shape({}),
  // eslint-disable-next-line react/no-unused-prop-types
  cellValue: PropTypes.string,
};

PartnerPermission.defaultProps = {
  value: {},
  cellValue: '',
};
  
export default withTranslation()(PartnerPermission);
