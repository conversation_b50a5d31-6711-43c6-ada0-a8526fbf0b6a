/* eslint-disable jsx-a11y/no-noninteractive-element-to-interactive-role */

// @flow

import React from 'react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';

import './index.scss';

function RadioTab(props) {
  const { optionsList, toggler } = props;
  const { t } = useTranslation();

  return (
    <div className="radio-tab-container">
      <div className="radio-buttons">
        {
          optionsList.map((option) => {
            return (
              <div className={`radio-button ${option.value ? 'checked-true' : 'checked-false'}`} key={option.label}>
                <label
                  role="button"
                  tabIndex={0}
                  htmlFor={t(option.label)}
                  onKeyUp={(str) => str}
                  onClick={(e) => toggler(e.target.textContent)}>
                  <input
                    type="radio"
                    value={t(option.label)}
                    name={option.name}
                    checked={option.value}
                    onChange={(e) => toggler(e.target.textContent)} />
                  <div className="check-circle"></div>
                  <span>{t(option.label)}</span>
                </label>
              </div>
            );
          })
        }
      </div>
    </div>
  );
}

RadioTab.propTypes = {
  optionsList: PropTypes.arrayOf(PropTypes.shape()),
  toggler: PropTypes.func,
};

RadioTab.defaultProps = {
  optionsList: [],
  toggler: (str) => str,
};

export default RadioTab;
