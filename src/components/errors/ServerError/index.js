import React from 'react';
import PropTypes from 'prop-types';
import { isEmpty } from 'utils/lodash';
import { Navigate, BrowserRouter } from 'react-router-dom';

import { BASE_ROUTE_PATH } from 'config';
import i18n from 'utils/i18n';

import GenericErrorMessage from './GenericErrorMessage';

function ServerError(props) {
  const { error, children } = props;
  let message = 'NO_WIDGET_DATA';
  // we will make this more robust as we go along.
  // we need to show 401, 406, 415, 404, 500, 503, etc.
  if (error) {
    const { response } = error;
    if (response && (response.status === 403) && response.data) {
      message = i18n.t(response.data.code) + '. ' + response.data.message;
    }

    if (response && (response.status === 401)) {
      return (
        <Navigate to="/login" />
      );
    }
    
    if (typeof error === 'string') {
      return error;
    }
  }
  
  return !error || isEmpty(error) ? children : <GenericErrorMessage message={message} {...props} />;
}

ServerError.propTypes = {
  error: PropTypes.oneOfType([
    PropTypes.shape({}),
    PropTypes.string,
  ]),
  children: PropTypes.oneOfType([
    PropTypes.shape({}),
    PropTypes.arrayOf(PropTypes.shape({})),
  ]),
};

ServerError.defaultProps = {
  error: null,
  children: null,
};

export default ServerError;
