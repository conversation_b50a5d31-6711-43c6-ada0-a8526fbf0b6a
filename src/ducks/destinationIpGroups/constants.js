// this will be hooked onto the relative state tree.
import { BASE_API_PATH } from 'config';
import i18n from 'utils/i18n';

export const REDUCER_KEY = 'destinationipgroups';

export const API_ENDPOINT = `${BASE_API_PATH}/v1/ipDestinationGroups`;

export const API_UPDATE_DELETE_ENDPOINT = (id) => `${BASE_API_PATH}/v1/ipDestinationGroups/${id}`;

export const IP_DESTINATION_GROUPS_API_ENDPOINT = `${BASE_API_PATH}/v1/ipDestinationGroups`;
export const IP_DESTINATION_GROUPS_CATEGORY_API_ENDPOOINT = `${BASE_API_PATH}/v1/urlCategories`;
export const IP_DSTN_CATEORY_GET_API = `${BASE_API_PATH}/v1/urlCategories/lite?customOnly=true`;
export const IP_GROUPS_API_ENDPOINT = `${BASE_API_PATH}/v1/ipGroups`;

export const DATA_ERROR = 'error';
export const DATA_LOADING = 'loading';
export const MODAL_LOADING = 'modalLoading';
export const DATA_SG_TABLE = 'destinationIPTableData';
export const DATA = 'data';

// this will store data to create and edit form
export const APP_DATA = 'appData';
export const SHOW_FORM = 'showForm';
export const CUSTOM_APPTYPE = 'CUSTOM';
export const IP_ADDRESSES_DATA = 'ipAddressesData';
export const EXPANDED_APPS = 'expandedApps';

export const SHOW_DELETE_FORM = 'showDeleteForm';
export const SELECTED_ROW_ID = 'selectedRowID';
export const FORM_MODE = 'formMode';
export const IP_ADDRESSES = 'ipAddresses';
export const FQDN = 'fqdnList';
export const WILD_FQDN = 'wildFqdnList';
export const FORM_TITLE = 'formTitle';
export const TYPE = 'type';
export const SELECTED_COUNTRY = 'selectedCounty';
export const SELECTED_CATEGORY = 'selectedCategory';
export const SHOW_VIEW_FORM = 'showViewForm';

export const DEFAULT_FORM_DATA = {
  name: '',
  active: true,
  type: CUSTOM_APPTYPE,
};

const sgAppData = [{
  id: '1',
  valid: true,
  value: 'Source IP Group1',
}];

export const ipAddressList = [];

export const destinationIPColumns = [
  {
    id: 'dataIdx',
    accessor: (row, i) => i + 1,
    Header: 'NUMBER_ABBR',
    width: 40,
    disableReordering: true,
    disableResizing: true,
    disableSortBy: true,
    meta: {
      skipTranslation: false,
      customCellType: 'RENDER_INDEX',
    },
  },
  {
    id: 'name',
    accessor: (row) => row.name,
    Header: 'NAME',
    width: 100,
    defaultCanSort: false,
    disableSortBy: true,
    meta: {
      skipTranslation: false,
      customCellType: 'DEFAULT',
    },
  },
  {
    id: 'type',
    accessor: (row) => row.type,
    Header: 'TYPE',
    width: 60,
    defaultCanSort: false,
    disableSortBy: true,
    meta: {
      skipTranslation: false,
    },
  },
  {
    id: 'addresses',
    accessor: (row) => (row.addresses && row.addresses.join(', ')) || '---',

    Header: 'IP_ADDRESS_OR_FQDN_OR_WILDCARD_FQDN',
    width: 100,
    defaultCanSort: false,
    disableSortBy: true,
    meta: {
      skipTranslation: false,
      customCellType: 'DEFAULT',
    },
  },
  {
    id: 'countries',
    accessor: (row) => (row.countries && row.countries.map((x) => i18n.t(x)).join(', ')) || '---',
    Header: 'COUNTRIES',
    width: 100,
    defaultCanSort: false,
    disableSortBy: true,
    meta: {
      skipTranslation: false,
    },
  },
  // {
  //   id: 'ipCategories',
  //   accessor: (row) => row.ipCategories || '---',
  //   Header: 'CATEGORIES',
  //   width: 200,
  //   defaultCanSort: false,
  //   disableSortBy: true,
  //   meta: {
  //     skipTranslation: false,
  //   },
  // },
  {
    id: 'description',
    accessor: (row) => row.description || '---',
    Header: 'DESCRIPTION',
    width: 120,
    defaultCanSort: false,
    disableSortBy: true,
    meta: {
      skipTranslation: false,
      customCellType: 'DEFAULT',
    },
  },
];

const editColumn = {
  id: 'editor',
  Header: '',
  width: 90,
  disableReordering: true,
  disableResizing: true,
  disableSortBy: true,
  meta: {
    customCellType: 'ROW_ACTIONS',
    pageName: 'DESTINATION_IP',
  },
};

export const DESTINATION_TABLE_CONFIGS = {
  columns: [...destinationIPColumns, editColumn],
  initialState: {
    sortBy: [{ id: 'name' }],
  },
  disableTableHeaderDrag: true, // disable table column drag and drop
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: true,
  maxTableHeight: 'calc(100vh - 19rem)',
};

export const RESOURCE_DEFAULTS = {
  [DATA_ERROR]: null, // or error object so we can process and show to user
  [DATA_LOADING]: true,
  [APP_DATA]: sgAppData,
  [SHOW_FORM]: false,
  [DATA]: [],
  [DATA_SG_TABLE]: [],
  [IP_ADDRESSES_DATA]: {},
  [EXPANDED_APPS]: [],
  [MODAL_LOADING]: false,
  [SHOW_DELETE_FORM]: false,
  [SELECTED_ROW_ID]: null,
  [FORM_MODE]: 'NEW',
  [IP_ADDRESSES]: [],
  [FORM_TITLE]: 'ADD_DESTINATION_IP_GROUP',
  [TYPE]: 'DSTN_IP',
  [FQDN]: [],
  [WILD_FQDN]: [],
  [SELECTED_COUNTRY]: [],
  [SHOW_VIEW_FORM]: false,
};

export const DEFAULTS = {
  ...RESOURCE_DEFAULTS,
};

// default state for this page/widget/component
export const DEFAULT_STATE = {
  ...DEFAULTS,
};

// For testing...to be used later
export const DUMMY_STORE_DATA = {
  [REDUCER_KEY]: {
    ...DEFAULT_STATE,
  },
};
