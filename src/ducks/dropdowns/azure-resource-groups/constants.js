import dropdown from 'ducks/generics/dropdown';
import { BASE_API_PATH } from 'config';

export const REDUCER_KEY = 'azureResourceGroupsDropdown';
export const API_ENDPOINT = (region) => `${BASE_API_PATH}/v1/discoveryService/azure/${region || 'WESTUS'}/resourceGroups`;

export const DROPDOWN = dropdown.createStateConstants();
export const DROPDOWN_DEFAULT_STATE = dropdown.createDefaultState();

export const DEFAULT_STATE = {
  ...DROPDOWN_DEFAULT_STATE,
  skipLoad: true,
};

export const DUMMY_STORE_DATA = {
  [REDUCER_KEY]: {
    ...DEFAULT_STATE,
  },
};
