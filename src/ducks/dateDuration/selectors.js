import { createSelector } from 'reselect';
import * as constants from './constants';

export const dateDurationSelector = (state) => state[constants.REDUCER_KEY];

export const defaultDurationSelector = createSelector(
  dateDurationSelector,
  (state) => state.defaultDuration,
);

export const dateRange = createSelector(
  dateDurationSelector,
  (state) => ({
    [constants.START_DATE]: state[constants.START_DATE],
    [constants.END_DATE]: state[constants.END_DATE],
    [constants.INTERVAL]: state[constants.INTERVAL],
  }),
);

export default dateDurationSelector;
