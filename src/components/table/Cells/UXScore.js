// @flow

import React from 'react';
import PropTypes from 'prop-types';
import { ScoreBarSmall } from 'components/scoreBar';
import { OutOf } from 'components/outOf';
import ScoreDifference from 'components/scoreDifference';

import './index.scss';

function UXScore({ value }) {
  const { uxScore, pctChangeSinceLastCheck } = value;

  return (
    <div className="cell-table-ux-score">
      <span className="score-number">{uxScore}</span>
      <OutOf />
      <ScoreBarSmall percentage={uxScore} />
      <ScoreDifference number={pctChangeSinceLastCheck} />
    </div>
  );
}

UXScore.propTypes = {
  value: PropTypes.shape(),
};

UXScore.defaultProps = {
  value: {},
};

export default UXScore;
