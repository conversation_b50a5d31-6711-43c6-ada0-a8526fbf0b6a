import {
  createAction,
} from 'ducks/generics';
import * as constants from './constants';
import actionTypes from './action-types';

// we want this to exported for to be used in store and other places
// from index so...elsewhere we can say import {REDUCER_KEY as widget} from 'ducks/widget'
// for exmaple
export { REDUCER_KEY } from './constants';

const actionTypeExists = Object.keys(actionTypes).reduce((hash, key) => ({
  ...hash,
  [actionTypes[key]]: true,
}), {});

// unlike other reducer, we will use
export const reducer = (state = constants.DEFAULT_STATE, action = {}) => {
  // We will merge payload from actions to the state for this widget/page/component
  // that only matches to our actions in actiontypes
  // for example once data is loaded our action below will dispatch
  // action and data
  if (actionTypeExists[action.type]) {
    return {
      ...state,
      ...action.payload,
    };
  }
  return state;
};
export default reducer;

const boundToggleAdminPanel = createAction(actionTypes.TOGGLE_ADMIN_PANEL);

export const toggleAdminPanel = (isOpen) => (dispatch) => {
  dispatch(boundToggleAdminPanel({ [constants.TOGGLE_ADMIN_PANEL]: isOpen }));
};
