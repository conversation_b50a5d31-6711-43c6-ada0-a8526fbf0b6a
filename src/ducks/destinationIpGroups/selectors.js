import { createSelector } from 'reselect';
import { getFormValues, getFormMeta, getFormSyncErrors } from 'redux-form';

import * as constants from './constants';

export const baseSelector = (state) => state[constants.REDUCER_KEY];

export const loadingSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA_LOADING],
);

export const errorSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA_ERROR],
);

export const dataSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA] || {},
);

export const appDataSelector = createSelector(
  baseSelector,
  (state) => state[constants.APP_DATA] || {},
);

export const ipAddressListSelector = createSelector(
  baseSelector,
  (state) => state[constants.IP_ADDRESSES_DATA] || [],
);

export const ipAddressesSelector = createSelector(
  baseSelector,
  (state) => state[constants.IP_ADDRESSES] || [],
);

export const fqdnSelector = createSelector(
  baseSelector,
  (state) => state[constants.FQDN] || [],
);

export const wildFqdnSelector = createSelector(
  baseSelector,
  (state) => state[constants.WILD_FQDN] || [],
);

export const typeSelector = createSelector(
  baseSelector,
  (state) => state[constants.TYPE],
);

export const modalLoadingSelector = createSelector(
  baseSelector,
  (state) => state[constants.MODAL_LOADING],
);

export const selectedRowIDSelector = createSelector(
  baseSelector,
  (state) => state[constants.SELECTED_ROW_ID],
);

export const selectedCountrySelector = createSelector(
  baseSelector,
  (state) => state[constants.SELECTED_COUNTRY],
);

export const showFormSelector = createSelector(
  baseSelector,
  (state) => state[constants.SHOW_FORM],
);

export const formTitleSelector = createSelector(
  baseSelector,
  (state) => state[constants.FORM_TITLE],
);

export const formModeSelector = createSelector(
  baseSelector,
  (state) => state[constants.FORM_MODE],
);

export const sourceIPFormValuesSelector = (state) => getFormValues('sourceIPGroupForm')(state);
export const sourceIPFormMetaSelector = (state) => getFormMeta('sourceIPGroupForm')(state);
export const sourceIPFormSyncErrorsSelector = (state) => getFormSyncErrors('sourceIPGroupForm')(state);

// alway return top level selector
export default baseSelector;
