# Edge UX Dual-Purpose Library

A React library that can function both as a standalone application and as reusable components for Zscaler Edge Connector UI.

## Installation

```bash
npm install @zscaler/edge-ux
```

## Usage as a Library

### Basic Setup

```javascript
import { AppProvider, DashboardPage, AdministrationPage } from '@zscaler/edge-ux';
import '@zscaler/edge-ux/dist/index.css';

// Initialize HTTP client for API calls
import { initializeHttpClient } from '@zscaler/edge-ux';

initializeHttpClient({
  baseURL: 'https://api.zscaler.com',
  getToken: () => localStorage.getItem('authToken'),
  timeout: 30000,
  headers: {
    'X-Custom-Header': 'value'
  }
});

function MyApp() {
  return (
    <AppProvider config={{
      theme: {
        'primary-color': '#007bff',
        'secondary-color': '#6c757d'
      },
      i18n: {
        language: 'en-US'
      }
    }}>
      <DashboardPage />
      <AdministrationPage />
    </AppProvider>
  );
}
```

### NextJS Integration

```javascript
// app/layout.tsx
import { AppProvider } from '@zscaler/edge-ux';
import '@zscaler/edge-ux/dist/index.css';

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body>
        <AppProvider>
          {children}
        </AppProvider>
      </body>
    </html>
  );
}

// app/dashboard/page.tsx
import { DashboardPage } from '@zscaler/edge-ux';

export default function Dashboard() {
  return <DashboardPage />;
}
```

### Available Components

#### Pages
- **DashboardPage**: Main dashboard with connector monitoring
- **AdministrationPage**: Administration interface
- **AnalyticsPage**: Analytics and reporting
- **PolicyPage**: Policy management
- **ProfilePage**: User profile management
- **HelpPage**: Help and documentation
- **LoginPage**: Authentication interface
- **OneIdentityLoginPage**: One Identity SSO login

#### Components
- **Header**: Application header with navigation
- **MainNav**: Main navigation component
- **Footer**: Application footer
- **Modal**: Modal dialog component
- **Button, ButtonNew**: Button components
- **Input, InputPassword**: Input field components
- **Table, TablePro, ConfigTable**: Table components
- **Dropdown, CommonDropdown, MultiSelectDropdown**: Dropdown components
- **Spinner, Loading, PageLoader**: Loading indicators
- **Notification**: Notification system
- **PieChart, BarChart, LineChart**: Chart components
- **WorldMap**: Geographic visualization
- **NavTabs**: Tab navigation
- **Toggle, RadioGroup, CheckboxInput**: Form controls
- **DateTimePicker**: Date and time selection
- **SearchBox**: Search input component
- **PaginationBar**: Pagination controls
- **Tooltip**: Tooltip component
- **Drawer**: Slide-out drawer component

#### Providers
- **AppProvider**: Main application provider

#### Utilities
- **http, genericInterface**: HTTP client utilities
- **initializeHttpClient**: HTTP client initialization
- **helpers**: Utility functions
- **validations**: Validation utilities
- **persistentStorage**: Storage utilities

### Configuration Options

```javascript
const config = {
  httpClient: {
    baseURL: 'https://api.zscaler.com',
    getToken: () => localStorage.getItem('token'),
    timeout: 30000,
    headers: {
      'X-API-Key': 'your-api-key'
    }
  },
  theme: {
    'primary-color': '#007bff',
    'secondary-color': '#6c757d',
    'success-color': '#28a745',
    'danger-color': '#dc3545'
  },
  i18n: {
    language: 'en-US',
    resources: {
      // Custom translations
    }
  },
  initialState: {
    // Initial Redux state
  }
};
```

## Development

### Build Commands

```bash
# Build standalone application
npm run build

# Build library
npm run build-lib

# Test library build
npm run test-lib

# Analyze bundle size
npm run analyze
```

### Peer Dependencies

This library requires the following peer dependencies:

- react >=18.0.0
- react-dom >=18.0.0
- react-router-dom >=6.0.0 (optional)
- redux >=4.0.0
- react-redux >=7.0.0
- i18next >=15.0.0 (optional)
- react-i18next >=10.0.0 (optional)
- @material-ui/core >=4.0.0 (optional)
- @material-ui/icons >=4.0.0 (optional)

## License

ISC
