import React from 'react';
import PropTypes from 'prop-types';
import SimpleSearchInput from 'components/Input/SimpleSearchInput';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSearch } from '@fortawesome/pro-solid-svg-icons';

function HeaderRowSearchTool(props) {
  const {
    onStartSearch, searchField, fieldName, onSearchFilter, onClearFilter, showSearchBox, searchData,
  } = props;

  const clickCallback = (e) => {
    e.stopPropagation();
    if (!showSearchBox || searchField !== fieldName) {
      onStartSearch(fieldName);
      onClearFilter();
    }
  };
  const isSelected = !showSearchBox && searchData && searchField === fieldName;

  return (
    <>
      <span
        className={`table-header-row-search-button${isSelected ? '-selected' : ''}`}
        onKeyPress={null}
        role="button"
        aria-label="Search"
        tabIndex="0"
        onClick={clickCallback}>
        <FontAwesomeIcon icon={faSearch} data-tip data-for={fieldName} />
      </span>
      {showSearchBox && searchField === fieldName && (
        <div className="table-header-row-box-container">
          <div className="search-container">
            <SimpleSearchInput
              withButton
              onKeyPressCb={onSearchFilter} />
          </div>
        </div>
      )}
    </>
  );
}

HeaderRowSearchTool.propTypes = {
  searchField: PropTypes.string,
  fieldName: PropTypes.string,
  onSearchFilter: PropTypes.func,
  onClearFilter: PropTypes.func,
  onStartSearch: PropTypes.func,
  showSearchBox: PropTypes.bool,
  searchData: PropTypes.string,
};

HeaderRowSearchTool.defaultProps = {
  searchField: '',
  fieldName: '',
  onSearchFilter: null,
  onClearFilter: null,
  onStartSearch: null,
  showSearchBox: false,
  searchData: '',
};

export default HeaderRowSearchTool;
