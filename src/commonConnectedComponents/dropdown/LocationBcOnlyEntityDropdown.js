/* eslint-disable react/jsx-handler-names */
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { PropTypes } from 'prop-types';
import { change, updateSyncErrors } from 'redux-form';
import { isEmpty } from 'utils/lodash';
import { loader, handleSearchStringChange, perfomSearch } from 'ducks/dropdowns/locationsBcOnlyEntity';
import selectors from 'ducks/dropdowns/locationsBcOnlyEntity/selectors';
import AdvDropdownWithAsyncSearch from 'components/dropDown/AdvDropdownWithAsyncSearch';
import {
  setLocationsState,
} from 'ducks/provisioningTemplatesBranch';

export function LocationBcOnlyEntityDropdown(props) {
  const dispatch = useDispatch();
  const selector = useSelector((state) => selectors(state).dropdown);
  const {
    data, hasMorePages, loading, searchString = '',
  } = selector;
  const { input, meta } = props;
  const { name, value } = input || {};
  const { form } = meta || {};

  useEffect(() => {
    dispatch(loader(true, ''));
  }, []);

  useEffect(() => {
    if (!loading && isEmpty(searchString)) dispatch(setLocationsState(data.length));
  }, [data]);

  return (
    <AdvDropdownWithAsyncSearch
      {...props}
      items={data}
      currentItems={[]}
      hasNextPage={hasMorePages}
      defaultValue={value}
      onPerformSearch={() => dispatch(perfomSearch())}
      onSearchStringChange={(search) => dispatch(handleSearchStringChange(search))}
      onClickCb={(item) => {
        dispatch(change(form, name, item));
        dispatch(updateSyncErrors(form));
      }}
      onChange={input.onChange}
      onBlur={input.onBlur}
      loading={loading}
      styleClass="min-width-350px"
      searchString={searchString} />
  );
}

LocationBcOnlyEntityDropdown.propTypes = {
  input: PropTypes.shape({
    onChange: PropTypes.func,
    onBlur: PropTypes.func,
  }),
  meta: PropTypes.shape({}),
};
  
LocationBcOnlyEntityDropdown.defaultProps = {
  input: {},
  meta: {},
};

export default LocationBcOnlyEntityDropdown;
