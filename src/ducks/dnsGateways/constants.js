// this will be hooked onto the relative state tree.
import { BASE_API_PATH } from 'config';

export const REDUCER_KEY = 'dnsGateways';

export const API_ENDPOINT = `${BASE_API_PATH}/v1/dnsGateways`;
// export const DC_API_ENDPOINT = `${BASE_API_PATH}/v1/gateways/dc/ECSELF`;
// export const PZEN_API_ENDPOINT = `${BASE_API_PATH}/v1/gateways/pzen/ECSELF`;
// export const SUBCLOUDS_API_ENDPOINT = `${BASE_API_PATH}/v1/gateways/subclouds`;

export const API_DELETE_ENDPOINT = (id) => `${BASE_API_PATH}/v1/dnsGateways/${id}`;

export const DATA_ERROR = 'error';
export const DATA_LOADING = 'loading';
export const MODAL_LOADING = 'modalLoading';

export const DATA_TABLE = 'gatewaystabledata';
export const DATA = 'data';

// this will store data to create and edit form
export const APP_DATA = 'appData';
export const SHOW_FORM = 'showForm';
export const CUSTOM_APPTYPE = 'CUSTOM';
export const EXPANDED_APPS = 'expandedApps';
export const FAIL_CLOSE = 'failClosed';
export const SHOW_DELETE_FORM = 'showDeleteForm';
export const SEARCH_DATA = 'searchData';
export const SELECTED_ROW_ID = 'selectedRowID';
export const SHOW_PRIMARY_PROXY_AUTO = 'showPrimaryProxyAuto';
export const SHOW_PRIMARY_PROXY_MANUAL = 'showPrimaryProxyManual';
export const SHOW_PRIMARY_PROXY_MANUAL_OVERRIDE = 'showPrimaryProxyManualOverride';
export const PRIMARY_TYPE = 'primaryType';
export const SECONDARY_TYPE = 'secondaryType';
export const DC_DATA = 'dcData';
export const DC_API_CALLED = 'dcApiCalled';
export const SHOW_VIEW_FORM = 'showViewForm';
export const FORM_TITLE = 'formTitle';

export const DEFAULT_FORM_DATA = {
  name: '',
  active: true,
  type: CUSTOM_APPTYPE,
};

const sgAppData = [{
  id: '1',
  valid: true,
  value: 'Gateway 1',
}];

export const gatewayColumns = [
  {
    id: 'dataIdx',
    accessor: (row, i) => i + 1,
    Header: 'NUMBER_ABBR',
    width: 60,
    disableReordering: true,
    disableResizing: true,
    disableSortBy: true,
    meta: {
      skipTranslation: false,
      customCellType: 'RENDER_INDEX',
    },
  },
  {
    id: 'name',
    accessor: (row) => row.name,
    Header: 'GATEWAY_NAME',
    width: 20,
    defaultCanSort: true,
    meta: {
      skipTranslation: false,
    },
  },
  {
    id: 'primaryIp',
    accessor: (row) => row.primaryIp || row.ecDnsGatewayOptionsPrimary || '---',
    Header: 'PRIMARY_PROXY',
    width: 40,
    disableSortBy: true,
    meta: {
      skipTranslation: false,
    },
  },
  {
    id: 'secondaryIp',
    accessor: (row) => row.secondaryIp || row.ecDnsGatewayOptionsSecondary || '---',
    Header: 'SECONDARY_PROXY',
    width: 40,
    disableSortBy: true,
    meta: {
      skipTranslation: false,
    },
  },
  {
    id: 'failureBehavior',
    accessor: (row) => row.failureBehavior,
    Header: 'FAILURE_BEHAVIOR',
    width: 40,
    defaultCanSort: true,
    meta: {
      skipTranslation: false,
    },
  },
];

const editColumn = {
  id: 'editor',
  Header: '',
  width: 90,
  disableReordering: true,
  disableResizing: true,
  disableSortBy: true,
  meta: {
    customCellType: 'ROW_ACTIONS',
    pageName: 'LOG_AND_CONTROL_GATEWAYS',
  },
};

export const DNS_GATEWAY_TABLE_CONFIGS = {
  columns: [...gatewayColumns, editColumn],
  initialState: {
    sortBy: [{ id: 'name' }],
  },
  disableTableHeaderDrag: true, // disable table column drag and drop
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: true,
};

const gatewayData = [];

export const RESOURCE_DEFAULTS = {
  [DATA_ERROR]: null, // or error object so we can process and show to user
  [DATA_LOADING]: true,
  [MODAL_LOADING]: false,
  [APP_DATA]: sgAppData,
  [SHOW_FORM]: false,
  [DATA]: {},
  [DATA_TABLE]: gatewayData,
  [EXPANDED_APPS]: [],
  [FAIL_CLOSE]: false,
  [SHOW_DELETE_FORM]: false,
  [SELECTED_ROW_ID]: null,
  [SHOW_VIEW_FORM]: false,
  [FORM_TITLE]: '',
};

export const DEFAULTS = {
  ...RESOURCE_DEFAULTS,
};

// default state for this page/widget/component
export const DEFAULT_STATE = {
  ...DEFAULTS,
};

// For testing...to be used later
export const DUMMY_STORE_DATA = {
  [REDUCER_KEY]: {
    ...DEFAULT_STATE,
  },
};

export const dnsOptions = [
  { id: 'CUSTOM_DNS_SERVER', label: 'CUSTOM_DNS_SERVER' },
  { id: 'LAN_PRI_DNS', label: 'LAN_PRI_DNS' },
  { id: 'LAN_SEC_DNS', label: 'LAN_SEC_DNS' },
  // { id: 'WAN_PRI_DNS', label: 'WAN_PRI_DNS' },
  // { id: 'WAN_SEC_DNS', label: 'WAN_SEC_DNS' },
];
