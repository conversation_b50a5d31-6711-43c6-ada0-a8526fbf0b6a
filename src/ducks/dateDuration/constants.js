// *** NOTE *** //
// Keep this file unchanged until APIs are stable.

import moment from 'moment-timezone';

export const REDUCER_KEY = 'dateDuration';

export const START_DATE = 'startDate';
export const END_DATE = 'endDate';
export const INTERVAL = 'interval';
export const DEFAULT_DURATION = 'defaultDuration';
export const API_TIME_FORMAT = 'x'; // moment config - unix epoch with milliseconds

export const DURATION_DATA = [
  {
    id: 1, value: 'hrs_24', label: '24 Hours', day: 1, interval: '1h',
  },
  {
    id: 2, value: 'hrs_48', label: '48 Hours', day: 2, interval: '2h',
  },
  {
    id: 3, value: 'days_3', label: '3 Days', day: 3, interval: '4h',
  },
  {
    id: 4, value: 'days_7', label: '7 Days', day: 7, interval: '1d',
  },
  {
    id: 5, value: 'days_30', label: '30 Days', day: 30, interval: '1d',
  },
];

const duration = DURATION_DATA[0];
const { interval } = duration;

// following line is to avoid localstorage issue during unit test
const startDay = (duration && duration.day) || 1;
const startDate = moment().subtract(startDay, 'days').format(API_TIME_FORMAT);
const endDate = moment().format(API_TIME_FORMAT);

export const RESOURCE_DEFAULTS = {
  [START_DATE]: Number(Math.floor(startDate / 1000)),
  [END_DATE]: Number(Math.floor(endDate / 1000)),
  [INTERVAL]: interval,
  [DEFAULT_DURATION]: duration,
};

export const DEFAULTS = {
  ...RESOURCE_DEFAULTS,
};

export const DEFAULT_STATE = {
  ...DEFAULTS,
};

export const DUMMY_STORE_DATA = {
  [REDUCER_KEY]: {
    ...DEFAULT_STATE,
  },
};
