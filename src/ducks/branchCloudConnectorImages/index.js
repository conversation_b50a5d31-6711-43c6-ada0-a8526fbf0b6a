/* eslint-disable consistent-return */
import {
  createAction,
  loading,
} from 'ducks/generics';
import { get, isEmpty } from 'utils/lodash';
import { genericInterface } from 'utils/http';
import { notifyError } from 'ducks/notification';
import * as constants from './constants';
import actionTypes from './action-types';

export { REDUCER_KEY } from './constants';

const actionTypeExists = Object.keys(actionTypes).reduce((hash, key) => ({
  ...hash,
  [actionTypes[key]]: true,
}), {});

export const reducer = (state = constants.DEFAULT_STATE, action = {}) => {
  if (actionTypeExists[action.type]) {
    return {
      ...state,
      ...action.payload,
    };
  }
  return state;
};
export default reducer;

const boundLoading = loading(actionTypes.DATA_LOADING);
const dataChanged = createAction(actionTypes.DATA_CHANGED);
const boundError = createAction(actionTypes.DATA_LOAD_ERROR);

export const typedArrayToURL = (typedArray, mimeType) => {
  const URL = window.URL || window.webkitURL;
  return URL.createObjectURL(new Blob([typedArray.buffer], { type: mimeType }));
};

export const loader = () => async (dispatch) => {
  try {
    const downloadLinktApi = await genericInterface(
      constants.API_DOWNLOAD_LINK,
    ).read(null, { });
    const { data } = downloadLinktApi || [];
    const esxi = data.find((x) => x.type === 'ESXI') || {};
    const kvm = data.find((x) => x.type === 'KVM') || {};
    const hyperV = data.find((x) => x.type === 'HYPER_V') || {};
    const imagesTableData = [{
      hypervisor: 'Linux KVM',
      name: 'KVM',
      version: kvm.version,
    }, {
      hypervisor: 'VMware ESXi',
      name: 'ESXI',
      version: esxi.version,
    }, {
      hypervisor: 'Microsoft Hyper V',
      name: 'HYPER_V',
      version: hyperV.version,
    }];
    const esxiLink = esxi.downloadUrl;
    const kvmLink = kvm.downloadUrl;
    const hyperVLink = hyperV.downloadUrl;
  
    await dispatch(dataChanged({
      loading: false,
      [constants.IMAGES_DATA_TABLE]: imagesTableData,
      [constants.ESXI_LINK]: esxiLink,
      [constants.KVM_LINK]: kvmLink,
      [constants.HYPER_V_LINK]: hyperVLink,
    }));
  } catch (error) {
    const errorMsg = get(error, 'response.data.message');
    dispatch(dataChanged({ loading: false }));
    if (isEmpty(errorMsg)) dispatch(notifyError('ERROR_LOADING_ADMIN_MANAGEMENT'));
    else dispatch(notifyError(errorMsg));
  }
};

export const downloadImage = (appData) => (dispatch) => {
  if (appData.disableDownload) return;
  const { name, isHybrid } = appData;
  dispatch(boundLoading({
    loading: true,
    [constants.APP_DATA]: appData,
    [constants.SHOW_MODAL]: true,
  }));
  // wapi/v1/downloadScript/branch/KVM
  // wapi/v1/downloadScript/branch/ESXI

  const imageApi = isHybrid
    ? genericInterface(constants.API_DOWNLOAD_APP_CONNECTOR_ENDPOINT(name))
    : genericInterface(constants.API_DOWNLOAD_ENDPOINT(name));
  return imageApi.read(null, {
    responseType: 'arraybuffer',
    headers: {
      'Content-type': 'application/x-www-form-urlencoded',
    },
    onDownloadProgress: (progressEvent) => {
      const { loaded, total, currentTarget } = progressEvent;
      const { response } = currentTarget || {};
      const { byteLength } = response || {};
      dispatch(dataChanged({
        [constants.LOADED]: loaded,
        [constants.TOTAL_DOWNLOAD]: response ? byteLength : total,
      }));
    },
    
  })
    .then((response) => {
      dispatch(boundLoading({
        loading: false,
        [constants.SHOW_MODAL]: false,
      }));
      let filename = '';
      const disposition = response.headers['content-disposition'];
      const type = response.headers['content-type'];

      const downloadUrl = typedArrayToURL(new Uint8Array(response.data), type);

      if (disposition && disposition.indexOf('attachment') !== -1) {
        const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
        const matches = filenameRegex.exec(disposition);
        if (matches != null && matches[1]) filename = matches[1].replace(/['"]/g, '');
      }

      let blob;
      if (typeof window.navigator.msSaveBlob !== 'undefined') {
        window.navigator.msSaveBlob(blob, filename);
      } else {
        if (filename) {
          const a = document.createElement('a');
          if (typeof a.download === 'undefined') {
            window.location = downloadUrl;
          } else {
            a.href = downloadUrl;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
          }
        } else {
          window.location = downloadUrl;
        }
        setTimeout(() => { URL.revokeObjectURL(downloadUrl); }, 100); // cleanup
      }
    })
    .catch((error) => {
      dispatch(boundLoading({
        loading: false,
        [constants.SHOW_MODAL]: false,
      }));
      const enc = new TextDecoder('utf-8');
      const response = JSON.parse(enc.decode(error.response && error.response.data));
      const errorMsg = get(response, 'message', 'There is a problem with loading cloud providers, Please try again later.');
      dispatch(boundError(errorMsg));
      dispatch(notifyError(errorMsg));
    });
};

export const loadLocalData = () => (dispatch) => {
  dispatch(dataChanged({
    loading: false,
    [constants.IMAGES_DATA_TABLE]: constants.imagesTableData,
  }));
};
