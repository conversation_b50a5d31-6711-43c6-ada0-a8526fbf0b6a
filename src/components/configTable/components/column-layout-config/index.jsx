import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';

import { cloneDeep, noop } from 'utils/lodash';

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSlidersUp } from '@fortawesome/pro-regular-svg-icons';
import { faBars } from '@fortawesome/free-solid-svg-icons';

import './index.scss';

import DragAndDropList from '../drag-n-drop';

function IconColumn({ value, handleIconClick }) {
  return (
    <section className="icon">
      <FontAwesomeIcon icon={faSlidersUp} onClick={handleIconClick} />
    </section>
  );
}

const defaultProps = {
  allColumns: [],
  setColumnOrder: noop,
  setIsResetLayout: noop,
  resetLayout: noop,
  t: (str) => str,
};

function ColumnLayoutConfig(props) {
  const {
    allColumns, t, setColumnOrder, setIsResetLayout, resetLayout,
  } = props;
  
  const [columns, setColumns] = useState(allColumns);
  const [showColumns, setShowColumns] = useState(false);
  
  const handleCancelClick = () => {
    return setShowColumns(() => !showColumns);
  };

  const resetDefaulColumns = () => {
    setIsResetLayout(true);
    resetLayout();
    handleCancelClick();
  };
  useEffect(() => {
    let draggableCol = cloneDeep(allColumns);
    draggableCol = draggableCol.filter((col) => !col.disableReordering);
    setColumns(draggableCol);
  }, [allColumns]);

  const renderColumnSection = (column, idx, provided) => {
    const { Header, meta = {} } = column;
    const { skipTranslation = false } = meta;
    const checkboxCount = allColumns.filter(
      (col) => !col.disableReordering && col.totalVisibleHeaderCount,
    );

    return (
      <div
        ref={provided.innerRef}
        key={column.id}
        className="column-config"
        {...provided.draggableProps}
        {...provided.dragHandleProps}>
        {checkboxCount.length === 1 && checkboxCount[0].id === column.id ? (
          <input type="checkbox" {...column.getToggleHiddenProps()} className="checkbox" disabled />
        ) : (
          <input type="checkbox" {...column.getToggleHiddenProps()} className="checkbox" />
        )}
        <span className="name">{skipTranslation ? Header : t(Header)}</span>
        <span className="icon">
          <FontAwesomeIcon icon={faBars} />
        </span>
      </div>
    );
  };

  const onNewListOrder = (result) => {
    const diff = allColumns.filter((col) => result.indexOf(col.id) === -1);
    const newOrder = cloneDeep(result);

    // check if table column menu is present
    if (diff.length === 2) {
      newOrder.push(diff[1]?.id);
    }
    newOrder.unshift(diff[0]?.id);

    setColumnOrder(newOrder);
  };

  const handleIconClick = () => {
    return setShowColumns(() => !showColumns);
  };

  return (
    <article className="column-layout-config-container">
      <IconColumn
        value={showColumns}
        handleIconClick={handleIconClick} />
      <section className={`config-container ${showColumns ? 'display-block' : ''}`}>
        <section className="actions">
          <section className="table-column-menu-header">
            <span className="table-column-menu-header-text">{t('CUSTOMIZE_COLUMNS')}</span>
            <i
              role="button"
              tabIndex="0"
              aria-label="Reset columns"
              className="fas fa-undo"
              onClick={() => {
                setIsResetLayout(true);
                resetLayout();
              }}
              onKeyPress={noop} />
          </section>
        </section>

        <DragAndDropList
          list={columns}
          listKeyName="id"
          direction="vertical"
          droppableId="config-droppable"
          listContainerClass="config"
          listContainerProps={{}}
          renderListItem={renderColumnSection}
          onNewListOrder={onNewListOrder} />

        <section className="actions table-column-menu-footer">
          <div className="table-column-menu-footer-buttons">
            <button
              onClick={handleCancelClick}
              type="button"
              className="cancel">
              {t('CANCEL')}
            </button>
            <button
              onClick={resetDefaulColumns}
              type="button"
              className="cancel">
              {t('RESET')}
            </button>
          </div>
        </section>
      </section>
    </article>
  );
}

ColumnLayoutConfig.defaultProps = defaultProps;

ColumnLayoutConfig.propTypes = {
  allColumns: PropTypes.arrayOf(Object),
  setColumnOrder: PropTypes.func,
  setIsResetLayout: PropTypes.func,
  resetLayout: PropTypes.func,
  t: PropTypes.func,
};

export default withTranslation()(ColumnLayoutConfig);
