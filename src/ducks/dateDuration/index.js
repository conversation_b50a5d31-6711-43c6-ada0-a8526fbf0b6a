import moment from 'moment-timezone';

import * as constants from './constants';
import actionTypes from './action-types';

export { REDUCER_KEY } from './constants';

const actionTypeExists = Object.keys(actionTypes).reduce((hash, key) => ({
  ...hash,
  [actionTypes[key]]: true,
}), {});

export const reducer = (state = constants.DEFAULT_STATE, action = {}) => {
  if (actionTypeExists[action.type]) {
    return {
      ...state,
      ...action.payload,
    };
  }
  return state;
};
export default reducer;

export const resetDate = () => ({
  type: actionTypes.DROPDOWN_RESET,
  payload: {
    ...constants.DEFAULTS,
  },
});

export const setDateDuration = (duration) => (dispatch) => {
  const { day, interval } = duration;
  const startDate = moment().subtract(day, 'days').format(constants.API_TIME_FORMAT);
  const endDate = moment().format(constants.API_TIME_FORMAT);

  return dispatch({
    type: actionTypes.DROPDOWN_SET,
    payload: {
      [constants.START_DATE]: Number(Math.floor(startDate / 1000)),
      [constants.START_DATE]: Number(Math.floor(endDate / 1000)),
      [constants.INTERVAL]: interval,
      [constants.DEFAULT_DURATION]: duration,
    },
  });
};
