/* eslint-disable react/no-danger */
import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';

import './index.scss';

function ToolTipPane(props) {
  const {
    toolTip,
    toolTipJSX,
    infoText,
    addInfo,
    t,
  } = props;

  return (
    <div className="rTooltip top-right">
      <div className="rTooltip-container">
        <div className="rTooltip-top rTooltip-top-text">
          {infoText != null && infoText.length > 1
            ? (
              <div className={`help-error-text has-info-text ${!addInfo ? 'hidden' : ''}`}>
                {t(infoText)}
              </div>
            )
            : ''}
          {toolTip && <div className="help-text" dangerouslySetInnerHTML={{ __html: t(toolTip) }} />}
          {toolTipJSX && (
            <div className="help-text">
              {toolTipJSX}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

ToolTipPane.propTypes = {
  toolTip: PropTypes.string,
  toolTipJSX: PropTypes.element,
  infoText: PropTypes.string,
  addInfo: PropTypes.bool,
  t: PropTypes.func,
};

ToolTipPane.defaultProps = {
  toolTip: '',
  toolTipJSX: <></>,
  infoText: '',
  addInfo: false,
  t: (str) => str,
};

export default withTranslation()(ToolTipPane);
