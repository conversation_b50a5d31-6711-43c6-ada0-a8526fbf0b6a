import { genericInterface } from 'utils/http';
import { createAction, loading } from 'ducks/generics';
import genericDropdown from 'ducks/generics/dropdown';
import { change } from 'redux-form';
import { notifyError } from 'ducks/notification';
import {
  get, isEmpty, uniqBy,
} from 'utils/lodash';
import actionTypes from './action-types';
import selector, * as selectors from './selectors';
import * as constants from './constants';

export { REDUCER_KEY } from './constants';

const actionTypeExists = Object.keys(actionTypes).reduce((hash, key) => ({
  ...hash,
  [actionTypes[key]]: true,
}), {});

export const reducer = (state = constants.DEFAULT_STATE, action = {}) => {
  // Simplifyeverything: actions provide a payload, and we merge that payload.
  if (actionTypeExists[action.type]) {
    return {
      ...state,
      ...action.payload,
    };
  }
  return state;
};
export default reducer;

const http = genericInterface(constants.API_ENDPOINT);

const handleAppConnectorGroupChange = (form, initialValue) => (dispatch) => {
  dispatch(change(form, 'appConnectorGroupDropdown', initialValue));
};

const boundLoading = loading(actionTypes.DATA_LOADING);
const boundError = createAction(actionTypes.DATA_LOAD_ERROR);
const boundDataLoadSuccess = createAction(actionTypes.DATA_LOAD_SUCCESS);

export const loader = (initializeProps, search = '') => async (dispatch, getState) => {
  const state = getState();
  if (initializeProps) {
    state.appConnectorGroupDropdown.pageNum = 1;
    state.appConnectorGroupDropdown.data = [];
    state.appConnectorGroupDropdown.hasMorePages = false;
    state.appConnectorGroupDropdown.loading = false;
    // state.appConnectorGroupDropdown.searchString = search;
  }
  const { pageNum, pageSize, searchString } = state.appConnectorGroupDropdown;
  const param = searchString
    ? { page: pageNum, pageSize, search: searchString }
    : { page: pageNum, pageSize };
  dispatch(
    boundLoading({
      [constants.DATA]: [],
      ...constants.DEFAULT_STATE,
      searchString: search,
      loading: true,
    }),
  );
  try {
    const appConnectorGroupDropdown = await http.read(null, {
      params: param,
    });
    const { data = [] } = appConnectorGroupDropdown || {};

    const dataWithId = data.map((x) => ({ id: x.externalId, ...x }));

    const combinedData = [
      ...state.appConnectorGroupDropdown.data,
      ...dataWithId];
    const updatedData = uniqBy(combinedData, 'id');

    dispatch(boundDataLoadSuccess({
      [constants.DATA]: updatedData,
      [constants.DROPDOWN.PAGE_NUMBER]: pageNum + 1,
      [constants.DROPDOWN.HAS_MORE_PAGES]: updatedData.length >= pageSize && pageSize > 0,
      searchString: search,
      loading: false,
    }));
  } catch (error) {
    const errorMsg = get(error, 'response.data.message');
    dispatch(boundLoading({ loading: false }));
    dispatch(boundError(errorMsg));
    if (isEmpty(errorMsg)) dispatch(notifyError('ERROR_LOADING'));
    else dispatch(notifyError(errorMsg));
  }
};

const handleSearchStringChange = (searchString) => (dispatch) => {
  dispatch(boundLoading({ searchString }));
};

const perfomSearch = () => (dispatch, getState) => {
  const state = getState();
  const { searchString } = state.appConnectorGroupDropdown;
  dispatch(loader(true, searchString));
};

const dropdownActions = genericDropdown.createActions({ actionTypes, selector, http });
export {
  selector,
  selectors,
  dropdownActions,
  handleAppConnectorGroupChange,
  handleSearchStringChange,
  perfomSearch,
};
