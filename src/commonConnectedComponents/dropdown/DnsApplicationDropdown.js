import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import { get } from 'utils/lodash';

import DropdownWithSearch from 'components/dropDown/DropdownWithSearch';
import { dropdownActions, selector } from 'ducks/dropdowns/dns-application';
import labelHelper from './labelHelper';

export const getUnselectedValues = (state) => {
  const { selectedValueMap, data } = selector(state).dropdown;

  // this is your unselected values
  const unselectedValues = data.map((item, index) => {
    const checked = !!selectedValueMap[get(item, 'id')];
    const disabled = false;
    return {
      original: item,
      id: get(item, 'id', index),
      name: get(item, 'name', ''),
      checked,
      disabled,
    };
  });
  return unselectedValues;
};

export const mapStateToProps = (state, ownProps) => {
  const { t } = ownProps;

  const dropdownState = selector(state).dropdown;
  const { selectedValues, values } = dropdownState;
  const dropdownLabels = labelHelper(t, selectedValues, values, 'TOOLTIP_USED_IN_EXCLUDED_CRITERIA', 'ANY');

  return {
    ...dropdownState,
    ...dropdownLabels,
    unselectedValues: getUnselectedValues(state),
  };
};

export const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    ...dropdownActions,
  }, dispatch);

  return {
    actions,
  };
};

export default withTranslation()(connect(
  mapStateToProps,
  mapDispatchToProps,
)(DropdownWithSearch));
