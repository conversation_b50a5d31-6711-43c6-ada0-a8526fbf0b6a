import React, { useEffect } from 'react';
import { EntityDropdown } from 'components/entityDropdown';
import { useDispatch, useSelector } from 'react-redux';
import { loader } from 'ducks/dropdowns/cellularConfiguration';
import selectors from 'ducks/dropdowns/cellularConfiguration/selectors';
import { isEmpty } from 'utils/lodash';

export function CellularConfigurationDropDown(props) {
  const dispatch = useDispatch();
  const { data, hasMorePages } = useSelector((state) => selectors(state).dropdown);

  useEffect(() => {
    dispatch(loader(true));
  }, []);

  return (isEmpty(data)) ? <></> : (
    <div className="entity-dropdown-container">
      <EntityDropdown
        {...props}
        data={data}
        hasNextPage={hasMorePages}
        loadNextPage={() => dispatch(loader(false))} />
    </div>
  );
}

export default CellularConfigurationDropDown;
