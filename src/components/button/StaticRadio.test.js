import React from 'react';
import { getAllByLabelText, getAllByText, render } from '@testing-library/react';
import StaticRadio from './StaticRadio';

describe('StaticRadio component', () => {
  it('renders correctly', () => {
    const { getByText } = render(
      <StaticRadio label="Test Label" />,
    );

    expect(getByText('Test Label')).toBeInTheDocument();
  });

  it('renders with correct class names', () => {
    const { getByText } = render(
      <StaticRadio label="Test Label" />,
    );
    
    expect(getByText('Test Label')).toHaveClass('label');
  });
});

// describe('StaticRadio prop types', () => {
//   it('label is a string', () => {
//     expect(StaticRadio.propTypes.label).toBe(PropTypes.string);
//   });

//   it('label has a default value of an empty string', () => {
//     expect(StaticRadio.defaultProps.label).toBe('');
//   });
// });
