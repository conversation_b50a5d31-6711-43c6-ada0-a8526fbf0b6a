import React from 'react';
import { render, fireEvent } from '@testing-library/react';
import PrimaryButton from './Button';

describe('PrimaryButton component', () => {
  const defaultProps = {
    label: 'Primary Button',
    cclass: 'custom-class',
    onActionCb: jest.fn(),
    enable: true,
  };

  it('renders correctly', () => {
    const { getByText } = render(<PrimaryButton {...defaultProps} />);
    expect(getByText('Primary Button')).toBeInTheDocument();
  });

  it('calls onActionCb when clicked', () => {
    const { getByText } = render(<PrimaryButton {...defaultProps} />);
    const button = getByText('Primary Button');
    fireEvent.click(button);
    expect(defaultProps.onActionCb).toHaveBeenCalledTimes(1);
  });

  it('is disabled when enable is false', () => {
    const { getByText } = render(<PrimaryButton {...defaultProps} enable={false} />);
    const button = getByText('Primary Button');
    expect(button).toBeDisabled();
  });

  it('has correct class names', () => {
    const { getByText } = render(<PrimaryButton {...defaultProps} />);
    const button = getByText('Primary Button');
    expect(button).toHaveClass('button primary -js-password custom-class');
  });

  it('renders with custom label', () => {
    const { getByText } = render(<PrimaryButton label="Custom Label" />);
    expect(getByText('Custom Label')).toBeInTheDocument();
  });
});
