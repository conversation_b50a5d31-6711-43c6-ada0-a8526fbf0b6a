import React from 'react';
import PropTypes from 'prop-types';
import i18n from 'utils/i18n';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faMapMarker, faLevelUp } from '@fortawesome/pro-solid-svg-icons';

const noop = () => undefined;
// RowSubEdit is responsible to support EDIT feature of the respective row
function RowSubEdit(props) {
  const {
    onClickCallBack,
    showSubEdit,
  } = props;

  if (showSubEdit) {
    return (
      <span
        className="sub-edit"
        onKeyPress={noop}
        role="button"
        aria-label={i18n.t('EDIT')}
        tabIndex="0"
        onClick={onClickCallBack}>
        <FontAwesomeIcon
          icon={faLevelUp}
          title={i18n.t('EDIT')}
          className="level-up-icon fa-rotate-90" />
        <FontAwesomeIcon
          icon={faMapMarker}
          title={i18n.t('SUBEDIT')}
          className="map-marker-icon" />
      </span>
    );
  }
  return '';
}

RowSubEdit.propTypes = {
  onClickCallBack: PropTypes.func,
  showSubEdit: PropTypes.bool,
};

RowSubEdit.defaultProps = {
  onClickCallBack: noop,
  showSubEdit: false,
};

export default RowSubEdit;
