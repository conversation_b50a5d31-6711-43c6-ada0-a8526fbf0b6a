/* eslint-disable */
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { without, noop } from 'utils/lodash';
import { withTranslation } from 'react-i18next';
import i18n from 'utils/i18n';
import DropdownWithSearch from '../dropDown/DropdownWithSearch';
import MultiSelectFilters from './MultiSelectFilters';
import MultiSelectApiFilters from './MultiSelectApiFilters';
import SingleSelectFilters from './SingleSelectFilters';
import SingleSelectApiFilters from './SingleSelectApiFilters';
import AdvDropdownWithSearch from './../dropDown/AdvDropdownWithSearch';
import AdvDropdownWithAsyncSearch from './../dropDown/AdvDropdownWithAsyncSearch';
import MultiDropdownWithSearch from './../dropDown/MultiDropdownWithSearch';

class FiltersMenu extends Component {
  constructor(props) {
    super(props);
    const { dropdownOptions, initialValues } = this.props;

    if (dropdownOptions.firstDropValues && dropdownOptions.secDropValues) {
      let remaningAvailableOptionsLevel1 = dropdownOptions.firstDropValues;
      let selectedFiltersLevel1 = [];
      let remaningAvailableOptionsLevel2 = dropdownOptions.secDropValues;
      let selectedFiltersLevel2 = [];

      if (initialValues && Object.keys(initialValues).length) {
        const initialKeys = Object.keys(initialValues);

        dropdownOptions.firstDropValues.forEach(option => {
          if (initialKeys.includes(option.value[0])) {
            selectedFiltersLevel1.push({
              operation: option,
              value: initialValues[option.value[0]],
            });
            remaningAvailableOptionsLevel1 = without(remaningAvailableOptionsLevel1, option);
          }
        });

        dropdownOptions.secDropValues.forEach(option => {
          if (initialKeys.includes(option.value[0])) {
            selectedFiltersLevel2.push({
              operation: option,
              value: initialValues[option.value[0]],
            });
            remaningAvailableOptionsLevel2 = without(remaningAvailableOptionsLevel2, option);
          }
        });
      }

      if (selectedFiltersLevel1.length === 0) {
        remaningAvailableOptionsLevel1 = dropdownOptions.firstDropValues.slice(
          1,
          dropdownOptions.firstDropValues.length,
        );
        selectedFiltersLevel1 = [
          {
            operation: dropdownOptions.firstDropValues[0],
            value: '',
          },
        ];
      }

      if (selectedFiltersLevel2.length === 0) {
        remaningAvailableOptionsLevel2 = dropdownOptions.secDropValues.slice(
          1,
          dropdownOptions.secDropValues.length,
        );
        selectedFiltersLevel2 = [
          {
            operation: dropdownOptions.secDropValues[0],
            value: '',
          },
        ];
      }

      this.state = {
        remaningAvailableOptionsLevel1,
        selectedFiltersLevel1,
        remaningAvailableOptionsLevel2,
        selectedFiltersLevel2,
      };
    } else {
      this.state = {};
    }
  }
  addOrComponent = (remainingOptionsState, selectedFiltersState) => {
    const remainingAvailableOptions = this.state[remainingOptionsState];
    const selectedFilters = this.state[selectedFiltersState];
    if (remainingAvailableOptions.length === 0) return;
    const selectedOperation = remainingAvailableOptions[0];
    const indexItem = remainingAvailableOptions.indexOf(selectedOperation);
    if (indexItem !== -1) {
      remainingAvailableOptions.splice(indexItem, 1);
    }
    const oldValue = { ...selectedFilters[0] };
    oldValue.operation = selectedOperation;
    oldValue.value = '';
    selectedFilters.push(oldValue);
    this.setState({
      [remainingOptionsState]: remainingAvailableOptions,
      [selectedFiltersState]: selectedFilters,
    });
  }

  setValue = () => {
    const { setItemCallback, typeSelected: {type} } = this.props;
    const { selectedFiltersLevel1, selectedFiltersLevel2 } = this.state;
    const resultObj = {};
    let allFiltersSelected = selectedFiltersLevel1.concat(selectedFiltersLevel2);
    switch(type) {
      case 'STRING_MULTISELECT':
        allFiltersSelected = allFiltersSelected.filter(item => !!item.value.trim());
        allFiltersSelected.forEach((item) => {
          resultObj[item.operation.value[0]] = [item.value];
        });
        break;
      case 'SINGLE_SELECT_NUMERIC':
        allFiltersSelected = allFiltersSelected.filter(item => !!item.value.trim());
        allFiltersSelected.forEach((item) => {
          resultObj[item.operation.value[0]] = Number(item.value);
        });
        break;
      case 'ENUM_MULTISELECT':
      case 'API_MULTISELECT':
        allFiltersSelected = allFiltersSelected.filter(item => !!item.value.length);
        allFiltersSelected.forEach((item) => {
          resultObj[item.operation.value[0]] = item.value;
        });
        break;
    }
    setItemCallback(resultObj);
  }

  operationChange = (index, value, remainingOptionsState, selectedFiltersState) => {
    const selectedFilters = [...this.state[selectedFiltersState]];
    const remainingAvailableOptions = [...this.state[remainingOptionsState]];
    const prevValue = { ...selectedFilters[index] };
    const indexItem = remainingAvailableOptions.indexOf(value);
    if (indexItem !== -1) {
      remainingAvailableOptions.splice(indexItem, 1);
    }
    remainingAvailableOptions.push(prevValue.operation);
    prevValue.operation = value;
    selectedFilters[index] = prevValue;
    this.setState({
      [remainingOptionsState]: remainingAvailableOptions,
      [selectedFiltersState]: selectedFilters,
    }, () => {
      this.setValue();
    });
  }

  inputChange = (index, value, selectedFiltersState) => {
    const selectedFilters = this.state[selectedFiltersState];
    const prevValue = { ...selectedFilters[index] };
    prevValue.value = value;
    selectedFilters[index] = prevValue;
    this.setState({ [selectedFiltersState]: selectedFilters }, () => {
      this.setValue();
    });
  }

  handleDropdownFilterChange = (index, selValue, selectedFiltersState) => {
    let selectedFilterNames;
    const { typeSelected: {type} } = this.props;
    const selectedFilters = this.state[selectedFiltersState];
    const prevValue = { ...selectedFilters[index] };
    if (type === "API_MULTISELECT") {
      selectedFilterNames = selValue.map(item => {
        let obj = {};
        obj["id"] = item.id;
        obj["name"] = item.name;
        return obj;
      });
    } else {
      selectedFilterNames = selValue.map(item => item.name);
    }
    prevValue.value = selectedFilterNames;
    selectedFilters[index] = prevValue;
    this.setState({ [selectedFiltersState]: selectedFilters }, () => {
      this.setValue();
    });
  }

  removeDropdown = (index, operation, remainingOptionsState, selectedFiltersState) => {
    const remainingAvailableOptions = this.state[remainingOptionsState];
    let selectedFilters = this.state[selectedFiltersState];
    selectedFilters = selectedFilters.filter(e => e.operation.id !== operation.id);
    remainingAvailableOptions.push(operation);
    this.setState({
      [remainingOptionsState]: remainingAvailableOptions,
      [selectedFiltersState]: selectedFilters,
    }, () => { this.setValue(); });
  }
  
  render() {
    const {
      t, reset, removeFilterCallback, fetchFilterDropdownOptions, setItemCallback,
      typeSelected: {label, type, dropdownOptions, dropdownApi, hasToggle, toggleOptions, isLoading,  defaultValue, hasSearch},
      validateFilter,
    } = this.props;
    const {
      selectedFiltersLevel1, selectedFiltersLevel2, remaningAvailableOptionsLevel1, remaningAvailableOptionsLevel2,
    } = this.state;
    let inputValueType;
    let dropdownType;
    if (type === "SINGLE_SELECT_NUMERIC" || type === "STRING_MULTISELECT") {
      inputValueType = "text";
      dropdownType= "twoLevel";
    } else if (type === "ENUM_MULTISELECT") {
      inputValueType = "multiSelectDropdown";
      dropdownType= "twoLevel";
    } else if (type === "API_MULTISELECT") {
      inputValueType = "multiSelectApiDropdown";
      dropdownType= "twoLevel";
    } 
    const firstDropdownComponents = dropdownType === "twoLevel"? selectedFiltersLevel1.map((item, index) => (
      <li className="dropdown-input" key={index}>
        <DropdownWithSearch
          index={index}
          t={t}
          isLoading={isLoading}
          inputDropdownOptions={dropdownOptions}
          inputValueType={inputValueType}
          remainingAvailableOptions={remaningAvailableOptionsLevel1}
          selectionOperation={item.operation}
          enteredValue={item.value}
          validateFilter={validateFilter}
          handleDropdownFilterChange={(selValue) =>
            this.handleDropdownFilterChange(index, selValue, 'selectedFiltersLevel1')
          }
          hasRemove={selectedFiltersLevel1.length > 1}
          fetchOptionsCallback={() => fetchFilterDropdownOptions()}
          inputChange={(index, value) => this.inputChange(index, value, 'selectedFiltersLevel1')}
          operationChange={(index, item) => this.operationChange(index, item, 'remaningAvailableOptionsLevel1', 'selectedFiltersLevel1')}
          removeFilter={(index, operation) => this.removeDropdown(index, operation, 'remaningAvailableOptionsLevel1', 'selectedFiltersLevel1')}
          typeFilter={type} />
      </li>
    )) : [];
    const secondDropdownComponents = dropdownType === "twoLevel"? selectedFiltersLevel2.map((item, index) => (
      <li className="dropdown-input" key={index}>
        <DropdownWithSearch
          index={index}
          t={t}
          isLoading={isLoading}
          inputDropdownOptions={dropdownOptions}
          inputValueType={inputValueType}
          remainingAvailableOptions={remaningAvailableOptionsLevel2}
          selectionOperation={item.operation}
          enteredValue={item.value}
          handleDropdownFilterChange={(selValue) => this.handleDropdownFilterChange(index, selValue, 'selectedFiltersLevel2')}
          validateFilter={validateFilter}
          hasRemove={selectedFiltersLevel2.length > 1}
          fetchOptionsCallback={() => fetchFilterDropdownOptions()}
          inputChange={(index, value) => this.inputChange(index, value, 'selectedFiltersLevel2')}
          operationChange={(index, item) => this.operationChange(index, item, 'remaningAvailableOptionsLevel2', 'selectedFiltersLevel2')}
          removeFilter={(index, operation) => this.removeDropdown(index, operation, 'remaningAvailableOptionsLevel2', 'selectedFiltersLevel2')}
          typeFilter={type} />
      </li>
    )): [];
    
    return (
      <div className={`advance-filter-type new-filter-component ${label.toLowerCase()}`}>
        {type === 'MULTISELECT_DROPDOWN' && (
          <div>
            {!dropdownApi && <MultiSelectFilters
              hasSearch={hasSearch}
              items={dropdownOptions}
              hasToggle={hasToggle}
              toggleList={toggleOptions}
              toggleAction={(selValue) => setItemCallback(selValue)}
              setValue={(selValue) => setItemCallback(selValue)}
              labelSelectedValue={t(label)}
              showLabelInSelectedValue
              defaultValue={defaultValue}
              removeFilterCallback={removeFilterCallback}
              reset={reset}
              searchProperty="label"
              hasLocalSort />}
            {dropdownApi && (
              <MultiSelectApiFilters
              fetchOptionsCallback={() => fetchFilterDropdownOptions()}
              hasSearch={hasSearch}
              isLoading={isLoading}
              items={dropdownOptions}
              searchProperty="label"
              setValue={(selValue) => setItemCallback(selValue)}
              labelSelectedValue={i18n.localizeString(label)}
              showLabelInSelectedValue
              defaultValue={defaultValue}
              removeFilterCallback={removeFilterCallback}
              reset={reset}
              hasLocalSort />)}
          </div>)}
          {type === 'SINGLESELECT_DROPDOWN' && (
          <div>
            {!dropdownApi && <SingleSelectFilters
              hasSearch={hasSearch}
              items={dropdownOptions}
              hasToggle={hasToggle}
              toggleList={toggleOptions}
              toggleAction={(selValue) => setItemCallback(selValue)}
              setValue={(selValue) => setItemCallback(selValue)}
              labelSelectedValue={t(label)}
              showLabelInSelectedValue
              defaultValue={defaultValue}
              removeFilterCallback={removeFilterCallback}
              reset={reset}
              hasLocalSort />}
            {dropdownApi && (
              <SingleSelectApiFilters
              fetchOptionsCallback={() => fetchFilterDropdownOptions()}
              hasSearch={hasSearch}
              isLoading={isLoading}
              items={dropdownOptions}
              searchProperty="label"
              setValue={(selValue) => setItemCallback(selValue)}
              labelSelectedValue={t(label)}
              showLabelInSelectedValue
              defaultValue={defaultValue}
              removeFilterCallback={removeFilterCallback}
              reset={reset}
              hasLocalSort/>)}
          </div>)}
        {dropdownType === "twoLevel" && (
          <div className="filter-operators">
            <ul className="op">
              <li className="op-body">
                <div className="op-type">{t('OPERATOR_AND')}</div>
                <ul className="op op-deep level-1">
                  <li>
                    <div className="op-type">{t('OPERATOR_OR')}</div>
                    <div
                      className="add-or-icon far fa-plus-circle"
                      role="button"
                      onClick={() => {
                        this.addOrComponent(
                          'remaningAvailableOptionsLevel1',
                          'selectedFiltersLevel1',
                        );
                      }}
                      tabIndex={0}
                      onKeyPress={() => {
                        this.addOrComponent(
                          'remaningAvailableOptionsLevel1',
                          'selectedFiltersLevel1',
                        );
                      }}
                    ></div>
                    <ul className="op op-deep level-2">{firstDropdownComponents}</ul>
                  </li>
                  <li>
                    <div className="op-type op-and">{t('OPERATOR_OR')}</div>
                    <div
                      className="add-or-icon far fa-plus-circle"
                      role="button"
                      onClick={() => {
                        this.addOrComponent(
                          'remaningAvailableOptionsLevel2',
                          'selectedFiltersLevel2',
                        );
                      }}
                      tabIndex={0}
                      onKeyPress={() => {
                        this.addOrComponent(
                          'remaningAvailableOptionsLevel2',
                          'selectedFiltersLevel2',
                        );
                      }}
                    ></div>
                    <ul className="op op-deep level-2">{secondDropdownComponents}</ul>
                  </li>
                </ul>
              </li>
            </ul>
          </div>
        )}
      </div>
    );
  }
}

FiltersMenu.propTypes = {
  t: PropTypes.func,
  dropdownOptions: PropTypes.shape({}),
  fetchFilterDropdownOptions: PropTypes.func,
  removeFilterCallback: PropTypes.func,
  typeSelected: PropTypes.shape({}),
  reset: PropTypes.bool,
  initialValues: PropTypes.shape({}),
  validateFilter: PropTypes.func,
};

FiltersMenu.defaultProps = {
  t: str => str,
  dropdownOptions: {},
  fetchFilterDropdownOptions: noop,
  removeFilterCallback: noop,
  typeSelected: {},
  initialValues: {},
  validateFilter: noop,
};

export default withTranslation()(FiltersMenu);
