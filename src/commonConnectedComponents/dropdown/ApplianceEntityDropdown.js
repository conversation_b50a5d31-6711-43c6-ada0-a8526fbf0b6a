/* eslint-disable react/jsx-handler-names */
import React, { useEffect } from 'react';
import Loading from 'components/spinner/Loading';
import { useDispatch, useSelector } from 'react-redux';
import { PropTypes } from 'prop-types';
import { change, updateSyncErrors } from 'redux-form';
import { loader, handleSearchStringChange, perfomSearch } from 'ducks/dropdowns/appliancesEntity';
import selectors from 'ducks/dropdowns/appliancesEntity/selectors';
import AdvDropdownWithAsyncSearch from 'components/dropDown/AdvDropdownWithAsyncSearch';

export function ApplianceEntityDropdown(props) {
  const dispatch = useDispatch();
  const selector = useSelector((state) => selectors(state).dropdown);
  const {
    data, hasMorePages, loading, searchString = '',
  } = selector;
  const {
    input, meta, disabled, modelType, defaultData,
  } = props;
  const { name, value } = input || {};
  const { form } = meta || {};

  useEffect(() => {
    dispatch(loader(true, '', modelType, defaultData));
  }, []);

  return (
    <>
      <Loading loading={loading} />
      <AdvDropdownWithAsyncSearch
        {...props}
        items={data}
        disabled={disabled}
        currentItems={[]}
        hasNextPage={hasMorePages}
        defaultValue={value}
        onPerformSearch={() => dispatch(perfomSearch(modelType))}
        onSearchStringChange={(search) => dispatch(handleSearchStringChange(search))}
        onClickCb={(item) => {
          dispatch(change(form, name, item));
          dispatch(updateSyncErrors(form));
        }}
        onChange={input.onChange}
        onBlur={input.onBlur}
        loading={false}
        searchString={searchString} />
    </>
  );
}

ApplianceEntityDropdown.propTypes = {
  disabled: PropTypes.bool,
  input: PropTypes.shape({}),
  meta: PropTypes.shape({}),
  modelType: PropTypes.string,
  defaultData: PropTypes.shape(),
};
  
ApplianceEntityDropdown.defaultProps = {
  disabled: false,
  input: {},
  meta: {},
  modelType: '',
  defaultData: {},
};

export default ApplianceEntityDropdown;
