import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import { get } from 'utils/lodash';

import DropdownWithSearch from 'components/dropDown/DropdownWithSearch';
import { dropdownActions, selector } from 'ducks/dropdowns/audit-logs-subcategories';
import * as AuditLogsSelectors from 'ducks/auditLogs/selectors';
import labelHelper from './labelHelper';

export const getUnselectedValues = (state) => {
  const { selectedValueMap, data } = selector(state).dropdown;

  // map data to see if item is in selecteValues from Excluded Criteria dropdown
  // this is your unselected values
  const unselectedValues = data.map((item, index) => {
    const checked = !!selectedValueMap[get(item, 'id')];
    const disabled = false;
    return {
      original: item,
      id: get(item, 'id', index) || item,
      name: get(item, 'name', '') || item,
      checked,
      disabled,
    };
  });
  return unselectedValues;
};

export const mapStateToProps = (state, ownProps) => {
  const { t } = ownProps;

  const dropdownState = selector(state).dropdown;
  const { selectedValues, data } = dropdownState;
  const categoryUpdate = AuditLogsSelectors.categoryUpdatedSelector(state);
  const setSelectedValue = (categoryUpdate && data.length < 1) ? [] : selectedValues;
  const dropdownLabels = labelHelper(t, setSelectedValue, setSelectedValue, '', 'ANY');

  if (setSelectedValue.length < 1) {
    dropdownLabels.displayLabel = 'Any';
  }
  
  return {
    ...dropdownState,
    ...dropdownLabels,
    selectedValues,
    unselectedValues: getUnselectedValues(state),
    categoryUpdated: categoryUpdate,
  };
};

export const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    ...dropdownActions,
  }, dispatch);

  return {
    actions,
  };
};

export default withTranslation()(connect(
  mapStateToProps,
  mapDispatchToProps,
)(DropdownWithSearch));
