import React from 'react';
import { useSelector } from 'react-redux';
import * as trafficFwdPolicies from 'ducks/trafficFwdPolicies/selectors';

import { EntityDropdown } from 'components/entityDropdown';

function Types(props) {
  const baseSelector = useSelector((state) => trafficFwdPolicies.baseSelector(state)) || {};
  const { addPolicy, numberOfOrderedLines } = baseSelector;
  const LIST_ITEM = [];

  for (let idx = 1; idx <= numberOfOrderedLines; idx += 1) {
    LIST_ITEM.push({ id: idx, name: idx });
  }
  if (addPolicy) LIST_ITEM.push({ id: numberOfOrderedLines + 1, name: numberOfOrderedLines + 1 });

  return (
    <EntityDropdown
      data={LIST_ITEM}
      {...props} />
  );
}

export default Types;
