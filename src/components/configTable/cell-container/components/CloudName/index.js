// @flow

import React from 'react';
import { withTranslation } from 'react-i18next';
import PropTypes from 'prop-types';

import awsIcon from 'images/cloudProviders/aws.png';
import azureIcon from 'images/cloudProviders/azure.png';
import gcpIcon from 'images/cloudProviders/gcp.png';

const IMG_CONFIG = {
  AWS: awsIcon,
  AZURE: azureIcon,
  GCP: gcpIcon,
};

function CloudName(props) {
  const { value, t, skipTranslation } = props;
  const { provUrlData, platform = '' } = value || {};

  const noneStr = t('NONE');
  const accountType = (provUrlData && provUrlData.cloudProviderType)
    ? provUrlData.cloudProviderType
    : platform;

  if (accountType) {
    return (
      <div className="account-column cloud-name">
        <img src={IMG_CONFIG[accountType]} alt={`${IMG_CONFIG[accountType]} logo`} />
        <span className={`${accountType.toLowerCase()}-text`}>{skipTranslation ? accountType : t(accountType)}</span>
      </div>
    );
  }
  return noneStr;
}

CloudName.propTypes = {
  t: PropTypes.func,
  value: PropTypes.shape(),
  skipTranslation: PropTypes.bool,
};

CloudName.defaultProps = {
  t: null,
  value: {},
  skipTranslation: false,
};

export default withTranslation()(CloudName);
