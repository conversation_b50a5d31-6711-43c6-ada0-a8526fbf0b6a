import React from 'react';
import ReactTooltip from 'react-tooltip';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faMapMarkerAlt } from '@fortawesome/pro-solid-svg-icons';
import { withTranslation } from 'react-i18next';
import getScoreBarColor from 'utils/helpers/getScoreBarColor';
import './index.scss';

const DATA_TIP = 'map-tooltip';
function MapTooltip({ ...props }) {
  const { t, location, dataConfig } = props;

  return (
    <ReactTooltip place="right" type="light" className="world-map-tooltip">
      <div className="location-name">
        <FontAwesomeIcon icon={faMapMarkerAlt} />
        <span>{location.toUpperCase()}</span>
      </div>
      {dataConfig.map((item) => (
        <div key={item.keyName}>
          <span>
            {`${t(item.label)} : `}
          </span>
          <span style={item.color ? { color: getScoreBarColor(props[item.keyName]) } : null}>
            {props[item.keyName]}
          </span>
        </div>
      ))}
    </ReactTooltip>
  );
}

MapTooltip.propTypes = {
  location: PropTypes.string,
  t: PropTypes.func,
  dataConfig: PropTypes.arrayOf(PropTypes.shape({
    keyName: PropTypes.string,
    label: PropTypes.string,
    color: PropTypes.bool,
  })).isRequired,
};

MapTooltip.defaultProps = {
  location: '',
  t: (str) => str,
};

const WrappedMapTooltip = withTranslation()(MapTooltip);

export { WrappedMapTooltip, DATA_TIP };
export default MapTooltip;
