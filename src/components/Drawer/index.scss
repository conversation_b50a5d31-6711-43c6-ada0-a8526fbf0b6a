
.ec-root-page {
.drawer-container {    
  .drawer {
    background: var(--semantic-color-background-primary);
    position: fixed;
    overflow: auto;
    box-shadow: -2px 0px 8px rgba(0, 0, 0, 0.1);
    transition: width 0.4s ease-in;
		z-index: 99998;
    &.left, &.right {
      top: 0;
      max-width: 45vw;
      // min-width: 300px;
      height: calc(100vh - 37px);
    }
		&.left{
      left: 0;
      // transform: translateX(-100%);
    }
    &.right {
      right: 0;
      // transform: translateX(100%);
    }
    &.top, &.bottom {
			left: 0;
      right: 0;
      width: 100%;
      height: 30vh;
      min-height: 300px;
    }
		&.top {
      top: 0;
      // transform: translateY(-100%);
    }
    &.bottom {
      bottom: 37px;
      // transform: translateY(120%);
    }
    &:not(.active) {
      // transform: translate(0);
      width: 0 !important;
    }
		.drawer-body {
      max-height: calc(100vh - 80px);
      padding: 10px;
      overflow-y: auto;
		}
    .drawer-header,
    .drawer-footer {
      background-color: var(--semantic-color-surface-base-primary);
      padding: 10px;
      left: 0;
      right: 0;
    }
    .drawer-header {
      height: 77.5px;
			position: sticky;
      top: 0;
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid var(--semantic-color-border-base-primary);
      .drawer-actions {
        svg {margin-left: 24px;}
      }
      .drawer-close-icon {
        color: #00578c;
        cursor: pointer;
        font-size: 16px;
      }
    }
    .drawer-footer {
			position: fixed;
      bottom: 0;
    }

  }

  .dialog-mask {
    opacity: 0;
    transition: opacity 0.4s ease-in;
    pointer-events: none;
		z-index: 9997;
  }
  .active ~.dialog-mask {
    opacity: 0.25;
    pointer-events: auto;
  }
}
}