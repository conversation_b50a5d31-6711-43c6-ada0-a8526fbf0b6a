/* eslint-disable react/jsx-handler-names */
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { PropTypes } from 'prop-types';
import { change, updateSyncErrors } from 'redux-form';
import {
  selector, handleSearchStringChange, perfomSearch,
} from 'ducks/dropdowns/azure-subscriptions';
import AdvDropdownWithAsyncSearch from 'components/dropDown/AdvDropdownWithAsyncSearch';

export function AzureSubscriptionsEntity(props) {
  const dispatch = useDispatch();
  const selectedData = useSelector((state) => selector(state).dropdown);
  const {
    data, hasMorePages, searchString = '',
  } = selectedData;
  const { input, meta } = props;
  const { name, value } = input || {};
  const { form } = meta || {};

  return (
    <AdvDropdownWithAsyncSearch
      {...props}
      styleClass="full-width margin-right-8px min-width-287px"
      items={data}
      currentItems={[]}
      hasNextPage={hasMorePages}
      defaultValue={value}
      onPerformSearch={() => dispatch(perfomSearch())}
      onSearchStringChange={(search) => dispatch(handleSearchStringChange(search))}
      onClickCb={(item) => {
        dispatch(change(form, name, item));
        dispatch(updateSyncErrors(form));
      }}
      onChange={input.onChange}
      onBlur={input.onBlur}
      loading={false}
      searchString={searchString} />
  );
}

AzureSubscriptionsEntity.propTypes = {
  input: PropTypes.shape({
    onChange: PropTypes.func,
    onBlur: PropTypes.func,
  }),
  meta: PropTypes.shape({}),
};
  
AzureSubscriptionsEntity.defaultProps = {
  input: {},
  meta: {},
};

export default AzureSubscriptionsEntity;
