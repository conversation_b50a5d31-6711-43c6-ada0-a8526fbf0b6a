/* eslint-disable jsx-a11y/label-has-for */
import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import i18n from 'utils/i18n';
import { isEmpty } from 'utils/lodash';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import ReactTooltip from 'react-tooltip';
import {
  faCheckCircle,
  faClock,
  faCircleXmark,
  faHexagonExclamation,
} from '@fortawesome/pro-solid-svg-icons';
import { faInfoCircle } from '@fortawesome/pro-light-svg-icons';
  
function PartnerPermission(props) {
  const { value, cellValue } = props || {};
  const { permissionStatus } = value || {};
  const { status, errorStatus } = permissionStatus || {};
  const { reason = '' } = status || {};
  let { assumeRole = '' } = status || {};

  const renderStatus = () => {
    if (isEmpty(assumeRole)) {
      assumeRole = cellValue || '';
    }
    if (assumeRole.toUpperCase() === 'ALLOWED' || assumeRole.toUpperCase() === 'SUCCESS' || assumeRole.toUpperCase() === 'ACTIVATED'
    || assumeRole.toUpperCase() === 'ENABLED' || assumeRole.toUpperCase() === 'HEALTHY' || assumeRole.toUpperCase() === 'ACTIVE') {
      return (
        <label htmlFor="status" className="partner-permission-cell enabled child-checkbox-label">
          <FontAwesomeIcon
            icon={faCheckCircle}
            className="success-icon" />
          {i18n.t(assumeRole.toUpperCase())}
        </label>
      );
    }
    if (assumeRole.toUpperCase() === 'DENIED') {
      return (
        <label htmlFor="status" className="partner-permission-cell denied child-checkbox-label">
          <FontAwesomeIcon
            icon={faHexagonExclamation}
            className="fa-circle-x-mark-icon" />
          {i18n.t(assumeRole.toUpperCase())}
          <div className="error-tooltip" data-tip={i18n.t(reason)}>
            <FontAwesomeIcon icon={faInfoCircle} className="fa-info-circle-icon" data-for="permissionDenied" data-tip={errorStatus} />
          </div>
        </label>
      );
    }
    if (assumeRole.toUpperCase() === 'ERROR') {
      return (
        <label htmlFor="status" className="child-checkbox-label">
          <FontAwesomeIcon
            icon={faCircleXmark}
            className="fa-circle-x-mark-icon color-error-content-default" />
          {i18n.t(assumeRole.toUpperCase())}
          <div className="error-tooltip" data-tip={i18n.t(reason)}>
            <FontAwesomeIcon icon={faInfoCircle} className="fa-info-circle-icon" />
          </div>
        </label>
      );
    }
    if (assumeRole.toUpperCase() === 'UNAVAILABLE'
        || assumeRole.toUpperCase() === 'NOT_AVAILABLE'
        || assumeRole.toUpperCase() === 'NA'
        || assumeRole.toUpperCase() === 'INACTIVE'
        || assumeRole.toUpperCase() === 'FAILED'
        || assumeRole.toUpperCase() === 'UNHEALTHY') {
      return (
        <label htmlFor="status" className="partner-permission-cell na child-checkbox-label">
          <>
            <FontAwesomeIcon
              icon={faHexagonExclamation}
              className="fa-circle-x-mark-icon" />
            {i18n.t(assumeRole.toUpperCase())}

          </>
        </label>
      );
    }
    if (assumeRole.toUpperCase() === 'CREATED') {
      return (
        <label htmlFor="status" className="child-checkbox-label">
          <FontAwesomeIcon
            icon={faCheckCircle}
            className="fa-clock fa-clock-icon" />
          {i18n.t(assumeRole.toUpperCase())}
        </label>
      );
    }
    if (assumeRole.toUpperCase() === 'PENDING' || assumeRole.toUpperCase() === 'INIT') {
      return (
        <label htmlFor="status" className="partner-permission-cell pending child-checkbox-label">
          <FontAwesomeIcon
            icon={faClock}
            className="fa-clock-icon" />
          {i18n.t(assumeRole.toUpperCase())}
        </label>
      );
    }

    return (
      <label htmlFor="status" className="child-checkbox-label">
        {i18n.t(assumeRole)}
      </label>
      
    );
  };
  
  return (
    <div
      onKeyDown={null}
      // aria-label="Operational Status"
      role="button"
      tabIndex="0">
      {renderStatus()}
      <ReactTooltip
        id="permissionDenied"
        border
        multiline
        place="right"
        type="light"
        className="react-tooltip"
        effect="solid" />
    </div>
  );
}

PartnerPermission.propTypes = {
  value: PropTypes.shape({}),
  // eslint-disable-next-line react/no-unused-prop-types
  cellValue: PropTypes.string,
};

PartnerPermission.defaultProps = {
  value: {},
  cellValue: '',
};
  
export default withTranslation()(PartnerPermission);
