// @flow
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
} from 'react-simple-maps';
import { getScoreBarColor } from 'utils/helpers';
import { withTranslation } from 'react-i18next';
import Map from '../Map';
import MapLegend from '../MapLegend';
import MapBox from './MapBox';
import Circle from './Circle';

import './index.scss';

class MapWithBox extends Component {
  static propTypes = {
    data: PropTypes.arrayOf(PropTypes.shape({
      id: PropTypes.number,
      score: PropTypes.number,
      geolocation: PropTypes.shape({
        longitude: PropTypes.number,
        latitude: PropTypes.number,
      }),
      title: PropTypes.string,
      boxComponent: PropTypes.element,
    })),
    legendTitle: PropTypes.string,
    title: PropTypes.string,
    description: PropTypes.string,
    t: PropTypes.func,
  };

  static defaultProps = {
    data: [],
    legendTitle: null,
    title: null,
    description: null,
    t: (str) => str,
  };

  initBoxes = (data) => {
    const halfLength = Math.round(data.length / 2);
    const BoxesArray = data.map((item, index) => (
      <MapBox
        flex={1 / halfLength}
        number={index + 1}
        title={item.title}
        circleColor={getScoreBarColor(item.score)}
        boxComponent={item.boxComponent}
        key={`left-boxes-${item.id}`} />
    ));

    return {
      leftSideBoxes: BoxesArray.slice(0, halfLength),
      rightSideBoxes: BoxesArray.slice(halfLength, BoxesArray.length),
    };
  };

  render() {
    const {
      data, legendTitle, title, description, t,
    } = this.props;
    const boxes = this.initBoxes(data);

    return (
      <div className="box-map-container">
        <div className="left-boxes">
          {boxes.leftSideBoxes}
        </div>
        <div className="container">
          <div className="title">
            <p>{t(title)}</p>
            <p>{t(description)}</p>
          </div>
          <Map zooming={false}>
            <Markers tabable={false}>
              {data.map((coordinate, index) => (
                <Marker
                  key={`${coordinate.id}`}
                  marker={{
                    coordinates: [
                      coordinate.geolocation.longitude,
                      coordinate.geolocation.latitude],
                  }}
                  onMouseMove={this.handleMouseMove}>
                  <Circle
                    circleColor={getScoreBarColor(coordinate.score)}
                    number={index + 1} />
                </Marker>
              ))}
            </Markers>
          </Map>
          <MapLegend title={legendTitle} />
        </div>
        <div className="right-boxes">
          {boxes.rightSideBoxes}
        </div>
      </div>
    );
  }
}

export default withTranslation()(MapWithBox);
export { MapWithBox };
