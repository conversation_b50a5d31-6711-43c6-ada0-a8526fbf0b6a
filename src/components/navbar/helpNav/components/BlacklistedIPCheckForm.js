import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { reduxForm } from 'redux-form';
import { withTranslation } from 'react-i18next';
import Loading from 'components/spinner/Loading';
import * as selectors from 'ducks/helpNav/selectors';

import {
  toggleShowForm, blacklistIps,
} from 'ducks/helpNav';

import {
  BlackedListIP,
} from './GenericFields';

export function BasicCustomAppForm(props) {
  const {
    t, actions, handleSubmit, loading,
  } = props;

  const onSubmit = () => {
    actions.blacklistIps();
  };

  const cancelHandle = () => {
    actions.toggleShowForm(false);
  };

  return (
    <Loading loading={loading}>
      <form onSubmit={handleSubmit(onSubmit)} className="adminManagement-form">
        <div className="form-sections-container">
          <BlackedListIP {...props} />
        </div>
        <div className="dialog-footer">
          <div className="dialog-footer-left">
            <button type="button" className="submit" onClick={cancelHandle}>{t('CLOSE')}</button>
          </div>
        </div>
      </form>
    </Loading>
  );
}

BasicCustomAppForm.propTypes = {
  t: PropTypes.func,
  loading: PropTypes.bool,
  handleSubmit: PropTypes.func,
  actions: PropTypes.shape({
    blacklistIps: PropTypes.func,
    toggleShowForm: PropTypes.func,
  }),
};
  
BasicCustomAppForm.defaultProps = {
  t: (str) => str,
  loading: false,
  handleSubmit: (str) => str,
  actions: {
    blacklistIps: null,
    toggleShowForm: null,
  },
};

const BasicCustomForm = reduxForm({
  form: 'blacklistIps',
})(BasicCustomAppForm);

const BlacklistedIPCheckForm = connect(
  () => ({
    initialValues: {
      ips: '',
    },
  }),
)(BasicCustomForm);

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    blacklistIps,
    toggleShowForm,
  }, dispatch);

  return {
    actions,
  };
};

const mapStateToProps = (state) => ({
  ...selectors.baseSelector(state),
});

// eslint-disable-next-line max-len
export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(BlacklistedIPCheckForm));
