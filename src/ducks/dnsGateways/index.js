/* eslint-disable consistent-return */
/* eslint-disable no-param-reassign */
import {
  createAction,
  loading,
  loadSuccess,
} from 'ducks/generics';

import { destroy } from 'redux-form';
import {
  get, isUndefined, omit, isNull, isEmpty,
} from 'utils/lodash';
import { genericInterface } from 'utils/http';
import { notify, notifyError } from 'ducks/notification';
import * as constants from './constants';
import actionTypes from './action-types';

import { checkActivation } from '../activation';

import { gatewayObject } from './template';

export { REDUCER_KEY } from './constants';

const actionTypeExists = Object.keys(actionTypes).reduce((hash, key) => ({
  ...hash,
  [actionTypes[key]]: true,
}), {});

export const reducer = (state = constants.DEFAULT_STATE, action = {}) => {
  if (actionTypeExists[action.type]) {
    return {
      ...state,
      ...action.payload,
    };
  }
  return state;
};
export default reducer;

const boundLoading = loading(actionTypes.DATA_LOADING);
const boundSuccess = loadSuccess(actionTypes.DATA_LOAD_SUCCESS);
const dataChanged = createAction(actionTypes.DATA_CHANGED);
const boundModalLoading = createAction(actionTypes.MODAL_LOADING);
const boundError = createAction(actionTypes.DATA_LOAD_ERROR);

export const loadGatewaysData = () => (dispatch) => {
  dispatch(boundLoading({ loading: true }));
  const gatewaysApi = genericInterface(constants.API_ENDPOINT);
  return gatewaysApi.read(null, {
    params: {
      // type: 'ECSELF',
    },
  })
    .then((response) => {
      dispatch(dataChanged({
        loading: false,
        [constants.DATA_TABLE]: response.data,
      }));
    })
    .catch((error) => {
      const errorMsg = get(error, 'response.data.message', 'There is a problem with loading location templates, Please try again later.');
      dispatch(boundError(errorMsg));
      dispatch(dataChanged({
        loading: false,
      }));
    });
};

export const localData = () => (dispatch) => {
  dispatch(boundSuccess(constants.DATA));
};

export const toggleDeleteForm = (data, toggle) => (dispatch) => {
  dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
  dispatch(dataChanged({
    [constants.SHOW_DELETE_FORM]: toggle,
    [constants.SELECTED_ROW_ID]: data ? data.id : null,
  }));
};

export const deleteGateways = (rowData) => (dispatch) => {
  const rowId = rowData;
  const deleteGatewaysApi = genericInterface(constants.API_DELETE_ENDPOINT(rowId));
  dispatch(boundModalLoading({ [constants.MODAL_LOADING]: true }));

  return deleteGatewaysApi.del()
    .then(() => {
      dispatch(toggleDeleteForm(false));
      dispatch(destroy('deleteConfirmationForm'));
      dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
      dispatch(notify('SUCCESSFULLY_DELETED'));
      dispatch(loadGatewaysData());
      dispatch(checkActivation());
    })
    .catch((error) => {
      const errorMsg = get(error, 'response.data.message', 'There is a problem with deleting gateways, Please try again later.');
      dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
      dispatch(boundError(errorMsg));
      dispatch(notifyError(errorMsg));
    });
};

export const handleChange = (type) => (appData) => (dispatch) => {
  dispatch(dataChanged({
    [type]: appData,
  }));
};

export const handleFailCloseChange = handleChange(constants.FAIL_CLOSE);

const getTemplateData = () => {
  return gatewayObject;
};

export const handlePrimaryProxyChange = (selectedValue) => (dispatch) => {
  dispatch(dataChanged({
    [constants.PRIMARY_TYPE]: selectedValue,
  }));
};

export const handleSecondaryProxyChange = (selectedValue) => (dispatch) => {
  dispatch(dataChanged({
    [constants.SECONDARY_TYPE]: selectedValue,
  }));
};

export const toggleForm = (appData, toggle, title) => (dispatch) => {
  if (isUndefined(title)) title = 'EDIT_DNS_GATEWAY';
  if (appData) {
    dispatch(dataChanged({
      [constants.SHOW_FORM]: toggle,
      [constants.APP_DATA]: appData,
      [constants.FORM_TITLE]: title,
    }));
  } else {
    dispatch(dataChanged({
      [constants.SHOW_FORM]: toggle,
      [constants.APP_DATA]: getTemplateData(),
      [constants.PRIMARY_TYPE]: '',
      [constants.SECONDARY_TYPE]: '',
    }));
  }
};

const beforeSave = (values) => {
  let updatedValue = { ...values };
  let omitKeys = [];

  omitKeys = [
    'primaryProxy',
    'secondaryProxy',
    'manualOverridePrimary',
    'manualOverrideSecondary',
    'isDeletable',
    'isEditable',
    'isReadOnly',
  ];

  const ecDnsGatewayOptionsPrimary = updatedValue?.ecDnsGatewayOptionsPrimary?.id;
  const ecDnsGatewayOptionsSecondary = updatedValue?.ecDnsGatewayOptionsSecondary?.id;
  updatedValue = omit(updatedValue, ...['ecDnsGatewayOptionsPrimary', 'ecDnsGatewayOptionsSecondary']);

  if (ecDnsGatewayOptionsPrimary !== 'CUSTOM_DNS_SERVER') {
    updatedValue.ecDnsGatewayOptionsPrimary = `${ecDnsGatewayOptionsPrimary}_AS_PRI`;
    omitKeys.push('primaryIp');
  }
  if (ecDnsGatewayOptionsSecondary !== 'CUSTOM_DNS_SERVER') {
    updatedValue.ecDnsGatewayOptionsSecondary = `${ecDnsGatewayOptionsSecondary}_AS_SEC`;
    omitKeys.push('secondaryIp');
  }
  if (isEmpty(updatedValue.secondaryIp)) {
    omitKeys.push('secondaryIp');
  }

  updatedValue.failureBehavior = (
    updatedValue.failureBehavior && updatedValue.failureBehavior.id
  ) || updatedValue.failureBehavior;
  updatedValue = omit(updatedValue, ...omitKeys);

  const updatedFormValues = {
    ...updatedValue,
  };

  return updatedFormValues;
};

export const saveForm = (values) => (dispatch) => {
  const saveGatewaysApi = genericInterface(constants.API_ENDPOINT);
  const apiEndPoint = values.id ? saveGatewaysApi.update : saveGatewaysApi.create;
  const updatedFormValues = beforeSave(values);
  dispatch(boundModalLoading({ [constants.MODAL_LOADING]: true }));

  if (updatedFormValues.error) {
    dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
    dispatch(notifyError(updatedFormValues.errorMsg));
    return;
  }

  return apiEndPoint(updatedFormValues, {})
    .then(() => {
      dispatch(boundModalLoading(
        {
          [constants.MODAL_LOADING]: false,
          [constants.PRIMARY_TYPE]: '',
          [constants.SECONDARY_TYPE]: '',
        },
      ));
      dispatch(toggleForm(false, '', {}));
      dispatch(destroy('addEditCustomAppForm'));
      dispatch(notify('SUCCESSFULLY_SAVED'));
      dispatch(loadGatewaysData());
      dispatch(checkActivation());
    })
    .catch((error) => {
      const errorMsg = get(error, 'response.data.message', 'There is a problem with saving gateway, Please try again later.');
      dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
      dispatch(boundError(errorMsg));
      dispatch(notifyError(errorMsg));
    });
};

export const toggleViewModal = (appData, toggle) => (dispatch) => {
  if (!isUndefined(appData) && !isNull(appData)) {
    dispatch(dataChanged({
      [constants.SHOW_VIEW_FORM]: toggle,
      [constants.APP_DATA]: appData,
    }));
  } else {
    dispatch(dataChanged({
      [constants.SHOW_VIEW_FORM]: toggle,
      [constants.APP_DATA]: getTemplateData(),
    }));
  }
};

export const handleOnSearchFilter = (event, searchText) => (dispatch) => {
  if (event.keyCode && event.keyCode === 13 && searchText.length > 0) {
    return dispatch(dataChanged({
      [constants.SEARCH_DATA]: searchText,
    }));
  } if (searchText.length === 0) {
    return dispatch(dataChanged({
      [constants.SEARCH_DATA]: '',
    }));
  }

  return null;
};
