import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react';
import { withTranslation, I18nextProvider } from 'react-i18next';
import i18n from 'utils/i18n';
import SectionLable from './index';

jest.mock('react-i18next', () => ({
  ...jest.requireActual('react-i18next'),
  withTranslation: () => (Component) => Component,
}));

describe('SectionLable component', () => {
  const props = {
    label: 'LABEL',
    tooltip: 'TOOLTIP',
    width: '100%',
    infoTextParam: 1,
    isInvalid: false,
    infoText: 'INFO_TEXT',
    addInfo: true,
    extraInfoTooltip: 'EXTRA_INFO_TOOLTIP',
    showExtraInfo: true,
    customLabelStyle: {},
    t: (str) => str,
  };

  it('renders correctly', () => {
    const { getByText } = render(
      <I18nextProvider i18n={i18n}>
        <SectionLable {...props} />
      </I18nextProvider>,
    );

    expect(getByText('LABEL')).toBeInTheDocument();
    expect(getByText('INFO_TEXT')).toBeInTheDocument();
  });

  it('renders without extra info', () => {
    const { getByText } = render(
      <I18nextProvider i18n={i18n}>
        <SectionLable {...props} showExtraInfo={false} />
      </I18nextProvider>,
    );

    expect(getByText('LABEL')).toBeInTheDocument();
    expect(getByText('INFO_TEXT')).toBeInTheDocument();
    expect(() => getByText('TOOLTIP')).toThrow();
  });

  it('renders with extra info', () => {
    const { getByText } = render(
      <I18nextProvider i18n={i18n}>
        <SectionLable {...props} showExtraInfo />
      </I18nextProvider>,
    );

    expect(getByText('LABEL')).toBeInTheDocument();
    expect(getByText('LABEL')).toHaveClass('cursorHelp');
    expect(() => getByText('TOOLTIP')).toThrow();
  });

  it('handles mouse over and out correctly', () => {
    const { getByText } = render(
      <I18nextProvider i18n={i18n}>
        <SectionLable {...props} />
      </I18nextProvider>,
    );

    const label = getByText('LABEL');
    const icon = getByText('INFO_TEXT');

    fireEvent.mouseOver(label);
    expect(getByText('INFO_TEXT')).toBeInTheDocument();
  });
});
