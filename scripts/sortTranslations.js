const path = require('path');
const fs = require('fs');

function getTransSortedObject(translation) {
  const translationOrdered = {};
  const TRANS_DATA = /{([^}]+)}/g;
  const translationInner = TRANS_DATA.exec(translation)[0]
    .replace(/(^[ \t]*\n)/gm, '')
    .replace(/,\n {2}}/g, '}')
    .replace(/'/g, '"');
  const translationObject = JSON.parse(`${translationInner}`);

  Object.keys(translationObject)
    .sort((item1, item2) => (item1.toLowerCase() > item2.toLowerCase() ? 1 : -1))
    .forEach((key) => {
      translationOrdered[key] = translationObject[key];
    });

  return translationOrdered;
}

function getTransString(translationObject) {
  const FOUR_SPACE = '    ';
  const TWO_SPACE = '  ';

  return JSON.stringify(translationObject)
    .replace(/:/g, ': ')
    .replace(/"/g, "'")
    .replace(/,/g, `,\n${FOUR_SPACE}`)
    .replace(/{/g, `{\n${FOUR_SPACE}`)
    .replace(/}/g, `,\n${TWO_SPACE}}`);
}

function readFiles(dirname) {
  const rxp = /translation:([^}]+)}/g;
  fs.readdir(dirname, (error, filenames) => {
    if (error) {
      // console.log(error);
      return;
    }
    filenames.forEach((filename) => {
      if (!filename.includes('-')) {
        return;
      }
      fs.readFile(`${dirname}/${filename}`, 'utf-8', (err, content) => {
        if (err) {
          // console.log(err);
          return;
        }

        const translation = rxp.exec(content)[0];
        const translationObject = getTransSortedObject(translation);
        const translationString = getTransString(translationObject);

        fs.writeFile(`${dirname}/${filename}`, content.replace(rxp, `translation: ${translationString}`), (writeFileErr) => {
          if (writeFileErr) {
            // console.log(writeFileErr);
          }
        });
      });
    });
  });
}

readFiles(`${path.dirname('src/utils/i18n')}/i18n`);
