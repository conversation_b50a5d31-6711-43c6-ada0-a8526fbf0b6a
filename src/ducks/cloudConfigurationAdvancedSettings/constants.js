// this will be hooked onto the relative state tree.
import { BASE_API_PATH } from 'config';

export const REDUCER_KEY = 'cloudConfigurationAdvancedSettings';

export const CC_ADVANCED_SETTINGS_API_QUERY = `${BASE_API_PATH}/v1/publicCloudAccountIdStatus`;

export const DATA_ERROR = 'error';
export const DATA_LOADING = 'loading';
export const MODAL_LOADING = 'modalLoading';
export const DATA_TABLE = 'dataTable';
export const DATA = 'data';

// this will store data to create and edit form
export const APP_DATA = 'appData';
export const VIEW_ONLY = 'viewOnly';
export const ACCOUNT_ID = 'accountIdEnabled';
export const SUBSCRIPTION_ID = 'subIdEnabled';
export const PROJECT_ID = 'projectIdEnabled';

export const RESOURCE_DEFAULTS = {
  [DATA_ERROR]: null, // or error object so we can process and show to user
  [DATA_LOADING]: true,
  [ACCOUNT_ID]: null,
  [SUBSCRIPTION_ID]: null,
  [PROJECT_ID]: null,
};

export const DEFAULTS = {
  ...RESOURCE_DEFAULTS,
};

// default state for this page/widget/component
export const DEFAULT_STATE = {
  ...DEFAULTS,
};

// For testing...to be used later
export const DUMMY_STORE_DATA = {
  [REDUCER_KEY]: {
    ...DEFAULT_STATE,
  },
};
