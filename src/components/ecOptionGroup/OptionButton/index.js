import React from 'react';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faDotCircle, faCircle } from '@fortawesome/pro-regular-svg-icons';
import { Trans } from 'react-i18next';

function optionButton(props = {}) {
  const {
    input,
    label,
    disabled,
    checked,
  } = props;

  input.checked = checked;

  return (
    <div className={`option-button ${disabled ? 'disabled' : ''} checked-${input.checked}`}>
      <label htmlFor={input.label}>
        <input
          type="radio"
          disabled={disabled}
          {...input} />
        <div className="check-circle">
          {!input.checked && <FontAwesomeIcon icon={faCircle} />}
          {input.checked && <FontAwesomeIcon icon={faDotCircle} />}
        </div>
        <span><Trans>{label}</Trans></span>
      </label>
    </div>
  );
}

optionButton.defaultProps = {
  label: null,
  disabled: false,
};

optionButton.propTypes = {
  label: PropTypes.string,
  disabled: PropTypes.bool,
  checked: PropTypes.bool,
  input: PropTypes.shape({
    name: PropTypes.string,
    value: PropTypes.any,
  }).isRequired,
};

export default optionButton;
