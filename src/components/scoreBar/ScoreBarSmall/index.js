// @flow

import React from 'react';
import PropTypes from 'prop-types';
import getScoreBarColor from 'utils/helpers/getScoreBarColor';

import './index.scss';

function ScoreBarSmall(props) {
  const { percentage } = props;
  const color = getScoreBarColor(percentage);

  return (
    <div className="score-bar-small" style={{ background: color }}>
      <div
        className="score"
        style={{
          width: `${100 - percentage}%`,
        }} />
    </div>
  );
}

ScoreBarSmall.propTypes = {
  percentage: PropTypes.number,
};

ScoreBarSmall.defaultProps = {
  percentage: null,
};

export default ScoreBarSmall;
