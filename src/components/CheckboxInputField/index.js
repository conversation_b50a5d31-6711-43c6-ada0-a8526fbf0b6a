import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import './index.scss';

export function CheckboxInputField(props = {}) {
  const {
    label,
    id,
    input,
    hidden,
    styleClass,
    t,
  } = props;
  const { disabled } = input;
  const classNames = disabled ? 'container disabled' : 'container';
  return (
    <div className={`checkbox-container ${styleClass} ${hidden ? 'hidden' : ''}`}>
      <label
        className={classNames}
        htmlFor={id}>
        <input
          id={id}
          type="checkbox"
          {...input} />
        <span className="checkmark">
          <span />
        </span>
        <span className="label-text">{t(label)}</span>
      </label>
    </div>
  );
}

CheckboxInputField.propTypes = {
  id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  label: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  styleClass: PropTypes.string,
  input: PropTypes.shape({
    onChange: PropTypes.func,
    value: PropTypes.string,
    name: PropTypes.string,
    checked: PropTypes.bool,
    disabled: PropTypes.bool,
  }),
  hidden: PropTypes.bool,
  t: PropTypes.func,
};

CheckboxInputField.defaultProps = {
  id: null,
  label: null,
  input: {},
  styleClass: '',
  hidden: false,
  t: (str) => str,
};

export default withTranslation()(CheckboxInputField);
