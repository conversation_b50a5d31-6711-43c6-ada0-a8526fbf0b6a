// @flow

import React from 'react';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faAngleUp, faAngleDown } from '@fortawesome/pro-solid-svg-icons';
import './index.scss';

function SimpleHeader({ title, sortable, onSortClick }) {
  return (
    <button type="button" className="header-simple" onClick={onSortClick}>
      {title}
      {sortable && (
        <div className="sorting-icon-cont">
          <div className="sorting-arrow-up"><FontAwesomeIcon icon={faAngleUp} /></div>
          <div className="sorting-arrow-down"><FontAwesomeIcon icon={faAngleDown} /></div>
        </div>
      )}
    </button>
  );
}

SimpleHeader.propTypes = {
  title: PropTypes.string,
  sortable: PropTypes.bool,
  onSortClick: PropTypes.func,
};

SimpleHeader.defaultProps = {
  title: null,
  sortable: true,
  onSortClick: (str) => str,
};

export default SimpleHeader;
