import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import Modal from 'components/modal';
import { get, isEmpty, noop } from 'utils/lodash';
import { Field, reduxForm, reset } from 'redux-form';
import InputPassword from 'components/InputPassword';
import ServerError from 'components/errors/ServerError';
import * as passwordExpirySelector from 'ducks/passwordExpiry/selectors';
import {
  togglePasswordExpiryModal,
  getCurrentPasswordVerified,
  createNewPassword,
} from 'ducks/passwordExpiry';
import { logout } from 'ducks/login';
import PersistentStorage, {
  LS_IS_PASSWORD_EXPIRY_NOTIFICATION_CLOSED,
} from 'utils/persistentStorage';
import './index.scss';
import {
  hasGoodPasswordStrength, required, isSameAsCurrentPassword, isSameAsNewPassword,
} from 'utils/validations';
import Banner from './components/Banner';

export function PasswordExpiryContainer(props) {
  PasswordExpiryContainer.propTypes = {
    callTogglePasswordExpiryChangeListener: PropTypes.func,
    modalTitle: PropTypes.string,
  };

  PasswordExpiryContainer.defaultProps = {
    callTogglePasswordExpiryChangeListener: noop,
    modalTitle: 'CHANGE_PASSWORD',
  };
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const {
    pristine,
    submitting,
    invalid,
    handleSubmit,
    callTogglePasswordExpiryChangeListener,
    modalTitle,
  } = props;
  const [fieldAttributes, setFieldAttributes] = useState({});
  const {
    error,
    passwordExpirySettings,
    isPasswordExpiryModalNeedToBeOpened,
    isValidCurrentPassword,
  } = useSelector(passwordExpirySelector.infoSelector);

  let timeout = null;

  const {
    passwordExpirationEnabled, expiresInDays,
    passwordExpiryDays,
  } = passwordExpirySettings;

  useEffect(() => {
    if (isEmpty(passwordExpirySettings)
        && !PersistentStorage.getItem(LS_IS_PASSWORD_EXPIRY_NOTIFICATION_CLOSED)) {
      // dispatch(getPasswordExpirySettings());
    } else if (!passwordExpirationEnabled || (passwordExpirationEnabled
          && expiresInDays >= 15
          && callTogglePasswordExpiryChangeListener)
          || (PersistentStorage.getItem(LS_IS_PASSWORD_EXPIRY_NOTIFICATION_CLOSED)
              && callTogglePasswordExpiryChangeListener)
    ) {
      callTogglePasswordExpiryChangeListener();
    }
  }, [
    passwordExpirySettings,
    passwordExpirationEnabled,
    expiresInDays,
    callTogglePasswordExpiryChangeListener,
    dispatch]);

  const dismissModal = () => {
    dispatch(reset('passwordExpiryForm'));
    setFieldAttributes({
      isShowValidCurrentPassword: false,
    });
    dispatch(togglePasswordExpiryModal(false));
    if (callTogglePasswordExpiryChangeListener) {
      callTogglePasswordExpiryChangeListener();
    }
  };

  const onPasswordEntered = (value) => {
    dispatch(getCurrentPasswordVerified(value));
    setFieldAttributes({
      isShowValidCurrentPassword: true,
    });
  };

  const onPasswordChange = (e, currVal, prevVal, name) => {
    if (currVal !== prevVal && name === 'currentPassword') {
      const isShowCurrentPasswordIcon = get(fieldAttributes, 'isShowCurrentPasswordIcon', false);
      if (!isShowCurrentPasswordIcon) {
        setFieldAttributes({
          isShowCurrentPasswordIcon: true,
          isShowValidCurrentPassword: false,
        });
        if (prevVal === undefined && currVal.length > 4) {
          clearTimeout(timeout);
          timeout = setTimeout(() => {
            onPasswordEntered(currVal);
          }, 500);
        }
      } else if (isShowCurrentPasswordIcon && currVal.length === 0) {
        clearTimeout(timeout);
        setFieldAttributes({
          isShowCurrentPasswordIcon: false,
        });
      } else if (currVal.length > 4) {
        clearTimeout(timeout);
        timeout = setTimeout(() => {
          onPasswordEntered(currVal);
        }, 1000);
      }
    }
    if (currVal !== prevVal && name === 'newPassword') {
      const isShowNewPasswordIcon = get(fieldAttributes, 'isShowNewPasswordIcon', false);
      if (!isShowNewPasswordIcon) {
        setFieldAttributes({
          isShowNewPasswordIcon: true,
        });
      } else if (isShowNewPasswordIcon && currVal !== prevVal && currVal.length === 0) {
        setFieldAttributes({
          isShowNewPasswordIcon: false,
        });
      }
    }
    if (currVal !== prevVal && name === 'confirmPassword') {
      const isShowConfirmPasswordIcon = get(fieldAttributes, 'isShowConfirmPasswordIcon', false);
      if (!isShowConfirmPasswordIcon) {
        setFieldAttributes({
          isShowConfirmPasswordIcon: true,
        });
      } else if (isShowConfirmPasswordIcon && currVal !== prevVal && currVal.length === 0) {
        setFieldAttributes({
          isShowConfirmPasswordIcon: false,
        });
      }
    }
  };
  const oNFocusOnCurrentPassword = (e) => {
    if (e.target.value.length !== 0) {
      setFieldAttributes({
        isShowCurrentPasswordIcon: true,
        isShowValidCurrentPassword: false,
      });
    }
  };
  const oNFocusOnNewPassword = (e) => {
    if (e.target.value.length !== 0) {
      setFieldAttributes({
        isShowNewPasswordIcon: true,
      });
    }
  };
  const oNFocusOnConfirmPassword = (e) => {
    if (e.target.value.length !== 0) {
      setFieldAttributes({
        isShowConfirmPasswordIcon: true,
      });
    }
  };

  const isLogOut = !isEmpty(passwordExpirySettings)
      && passwordExpirationEnabled
      && expiresInDays < 0;

  const handlePasswordSubmission = async (values) => {
    const currentPassword = get(values, 'currentPassword', '');
    const newPassword = get(values, 'newPassword', '');
    await dispatch(createNewPassword(currentPassword, newPassword.trim()));
    await dispatch(reset('passwordExpiryForm'));
    if (isLogOut) dispatch(logout());
  };

  const {
    isShowCurrentPasswordIcon,
    isShowNewPasswordIcon,
    isShowConfirmPasswordIcon,
    isShowValidCurrentPassword,
  } = fieldAttributes;

  let expiryDays = '';
  if (!isEmpty(passwordExpirySettings)
      && passwordExpirationEnabled
      && expiresInDays <= 0) {
    expiryDays = `(${t('PASSWORD_EXPIRED_ALREADY')})`;
  } else if (!isEmpty(passwordExpirySettings)
      && passwordExpirationEnabled
      && expiresInDays === 1) {
    expiryDays = `(${t('EXPIRES_IN')} ${expiresInDays} ${t('DAY')})`;
  } else if (!isEmpty(passwordExpirySettings)
      && passwordExpirationEnabled
      && expiresInDays <= 15) {
    expiryDays = `(${t('EXPIRES_IN')} ${expiresInDays} ${t('DAYS')})`;
  }

  let passwordExpiryDaysWindow = '';
  if (!isEmpty(passwordExpirySettings)
      && passwordExpirationEnabled) {
    passwordExpiryDaysWindow = `${t('CHANGE_PASSWORD_WINDOW')} ${passwordExpiryDays} ${t('DAYS')}`;
  }
  return (
    <Modal
      title={`${t(modalTitle)} ${expiryDays}`}
      isOpen={isPasswordExpiryModalNeedToBeOpened}
      closeModal={() => dismissModal()}
      customClass="password-modal-content">
      <div>
        <ServerError error={error}>
          <form onSubmit={handleSubmit(handlePasswordSubmission)} className="modal-form">
            <div className="container">
              {
                passwordExpiryDaysWindow !== '' && !isShowValidCurrentPassword
                  && (
                    <Banner
                      notificationType="NOTIFICATION"
                      message={passwordExpiryDaysWindow} />
                  )
              }
              {
                isShowValidCurrentPassword && !isValidCurrentPassword
                  && <Banner notificationType="CRITICAL" message="CURRENT_PASSWORD_NOT_VALID" />
              }
              <Field
                name="currentPassword"
                id="currentPassword"
                component={InputPassword}
                isDisabled={isValidCurrentPassword}
                label={t('VERIFY_CURRENT_PASSWORD')}
                validate={[
                  required,
                ]}
                required
                showPasswordIcon={isShowCurrentPasswordIcon}
                outlineUnSet
                onChange={
                  (
                    e,
                    currVal,
                    prevVal,
                    name,
                  ) => onPasswordChange(e, currVal, prevVal, name)
                }
                onFocus={(e) => oNFocusOnCurrentPassword(e)}
                onBlur={
                  (
                    e,
                    currVal,
                    prevVal,
                    name,
                  ) => onPasswordChange(e, currVal, prevVal, name)
                } />
              {
                isValidCurrentPassword
                  && (
                    <>
                      <Field
                        name="newPassword"
                        id="newPassword"
                        component={InputPassword}
                        isDisabled={false}
                        label={t('NEW_PASSWORD')}
                        validate={[
                          required,
                          hasGoodPasswordStrength,
                          isSameAsCurrentPassword,
                        ]}
                        required
                        showPasswordIcon={isShowNewPasswordIcon}
                        outlineUnSet
                        placeholder={t('NEW_PASSWORD_PLACEHOLDER')}
                        onChange={
                          (
                            e,
                            currVal,
                            prevVal,
                            name,
                          ) => onPasswordChange(e, currVal, prevVal, name)
                        }
                        onFocus={(e) => oNFocusOnNewPassword(e)} />
                      <Field
                        name="confirmPassword"
                        id="confirmPassword"
                        component={InputPassword}
                        isDisabled={false}
                        label={t('CONFIRM_PASSWORD')}
                        validate={[
                          required,
                          hasGoodPasswordStrength,
                          isSameAsNewPassword,
                        ]}
                        required
                        showPasswordIcon={isShowConfirmPasswordIcon}
                        outlineUnSet
                        placeholder={t('CONFIRM_PASSWORD_PLACEHOLDER')}
                        onChange={
                          (
                            e,
                            currVal,
                            prevVal,
                            name,
                          ) => onPasswordChange(e, currVal, prevVal, name)
                        }
                        onFocus={(e) => oNFocusOnConfirmPassword(e)} />
                      <p className="change-password-warning">{t('PASSWORD_MESSAGE_WARNING_MESSAGE')}</p>
                    </>
                  )
              }
            </div>
            <div className="modal-footer change-password-modal-footer">
              <button
                type="submit"
                className="primary-button"
                disabled={pristine || submitting || invalid || !isValidCurrentPassword}>
                {t('SUBMIT')}
              </button>
              <button
                type="button"
                onClick={
                  () => (isLogOut
                    ? dispatch(logout())
                    : dismissModal())
                }>
                {
                  isLogOut ? t('LOGOUT') : t('DISMISS')
                }
              </button>
            </div>
          </form>
        </ServerError>
      </div>
    </Modal>
  );
}

PasswordExpiryContainer.propTypes = {
  pristine: PropTypes.bool,
  submitting: PropTypes.bool,
  invalid: PropTypes.bool,
  handleSubmit: PropTypes.func,
};

PasswordExpiryContainer.defaultProps = {
  pristine: false,
  submitting: false,
  invalid: false,
  handleSubmit: noop,
};

const PasswordExpiryModal = reduxForm({
  form: 'passwordExpiryForm',
})(PasswordExpiryContainer);

export default PasswordExpiryModal;
