import React from 'react';
import { EntityDropdown } from 'components/entityDropdown';

const HA_DEVICE_STATUS = [
  { id: 'ACTIVE_STANDBY', name: 'ACTIVE_STANDBY' },
  { id: 'ACTIVE_ACTIVE', name: 'ACTIVE_ACTIVE' },
];

export function StatusDropdown(props) {
  return (
    <EntityDropdown
      {...props}
      disable
      isSearchable={false}
      data={HA_DEVICE_STATUS} />
  );
}

export default StatusDropdown;
