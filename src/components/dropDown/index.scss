@import 'scss/mixins.scss';
@import "scss/colors.scss";

.ec-root-page {
div.drop-down-container {
  position: relative;
  width: 100%;
  display: inline-block;
  color: var(--semantic-color-content-interactive-primary-default);
  .search-to-see-more {
    min-height: 30px;		
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--semantic-color-content-status-info-primary);
  }
  button {
    color: inherit;
    background: inherit;
    border: none;
    border-radius: inherit;
  }
  button.drop-down-selected-value {
    @include DisplayFlex;
    width: 100%;
    padding: 7px;
    // border: 1px solid var(--semantic-color-border-base-primary);
    border-radius: 0.5rem;
    background: var(--semantic-color-background-primary);
    align-items: center;
    span {
      flex: 0.9;
    }
    img {
      flex: 0.1;
      width: 20px;
    }
  }
  ul.drop-down-list {
    position: absolute;
    margin-top: -1px;
    background-color: var(--semantic-color-surface-base-primary);
    width: 100%;
    list-style-type: none;
    border: 1px solid var(--semantic-color-border-base-primary);
    border-radius: 10px;
    visibility: hidden;
    opacity: 0;
    transition: visibility 0s, opacity 0.1s linear;
    z-index: 9;
    li {
      border-radius: inherit;
      width: calc(100% - 1px);
      margin: 0 auto;
      button {
        padding: 8px;
        width: 100%;
      }
    }
    li.selected {
       background-color: var(--semantic-color-background-pale);
    }
    li:hover {
       background-color: var(--semantic-color-background-pale);
      color: var(--semantic-color-content-base-primary);
    }
  }
  ul.drop-down-list.open {
    visibility: visible;
    opacity: 1;
  }
}

.dropdown-unselected {
  .dropdown-search-add-button  {
    display: flex;
    &.dropdown-search-text input {
      background-color: #F3F3F3;
      border: none;
      height: 26px;
      width:  196px;
      font-size: 13px;
    }  
  }
  .multiselect-selected-list {
    overflow: auto;
    max-height: 310px;
  }
}

div.select-container {
  position: relative;
  width: 100%;
  display: inline-block;
  color: var(--semantic-color-content-interactive-primary-default);
  button {
    color: inherit;
    background: inherit;
    border: none;
    border-radius: inherit;
  }
  button.select-selected-value {
    @include DisplayFlex;
    width: 100%;
    padding: 8px;
    border: 1px solid var(--semantic-color-border-base-primary);;
    border-radius: 12px;
     background-color: var(--semantic-color-background-pale);
    align-items: center;
    span {
      flex: 0.9;
    }
    img {
      flex: 0.1;
      width: 20px;
    }
  }
  ul.select-list {
    position: absolute;
    margin-top: -1px;
    background-color: $white;
    width: 100%;
    list-style-type: none;
    border: 1px solid var(--semantic-color-border-base-primary);;
    border-radius: 10px;
    transition: visibility 0s, opacity 0.1s linear;
    z-index: 9;
    li {
      border-radius: inherit;
      width: calc(100% - 1px);
      margin: 0 auto;
      button {
        // padding: 8px;
        width: 100%;
      }
    }
    li.selected {
       background-color: var(--semantic-color-background-pale);
       color: var(--semantic-color-content-base-primary);
    }
    li:hover {
      background-color: var(--semantic-color-background-pale);
      color: var(--semantic-color-content-base-primary);
    }
  }
  ul.select-list.open {
    visibility: visible;
    opacity: 1;
  }
}

/*
 *  Drill Down
 */
 .drill-down-container {
  position: absolute;
  z-index: 100;
  background-color: var(--semantic-color-surface-base-primary);
  border: 1px solid var(--semantic-color-border-base-primary);
  // box-shadow: 0 0 8px 0 rgba(42, 44, 48, 0.25);
  border-radius: 3px;
  line-height: normal; }

.drill-down-header {
  text-align: left;
  padding: 8px;
  position: relative;
  width: 100%; }

.drill-down-search {
  .search-input-text {
    display: inline-block;
    font-size: 13px;
    height: 32px;
    padding: 6px 26px 6px 0;
    vertical-align: middle;
    max-width: 230px;
    border: 0px;
    background: var(--semantic-color-background-pale);
  }
  .search-input {
    border: none;
    padding: 0px 10px 0px 8px;
    border-radius: 8px;
    vertical-align: middle;
    .search-icon {
      width: 10%;
      height: 20px;
      .clear-button {
        display: none;
      }
      .remove-button {
        color: var(--semantic-color-content-status-info-primary);
        margin-right: 12px;
        border: none;
      }
    }
  }
}

.drill-down-body {
  position: relative;
  width: 100%;
  max-height: 155px;
  overflow-x: hidden;
  overflow-y: auto; }

.drill-down-body-content {
  position: relative;
  width: 100%;
  height: 100%;
  word-wrap: break-word; }

.drill-down-item {
  color: var(--semantic-color-content-status-info-primary);
  cursor: pointer;
  display: block;
  font-size: 13px;
  max-width: 100%;
  min-width: 100%;
  padding: 8px 12px;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; }
  .drill-down-item:hover, .drill-down-item.hover {
    background:  var(--semantic-color-background-primary);
    color: var(--semantic-color-content-status-info-primary); }
  .drill-down-item.drill-down-item-selected {
     background-color: var(--semantic-color-background-pale);
    color: var(--semantic-color-content-base-primary);
  }
  .drill-down-item.drill-down-item-selected:hover, .drill-down-item.drill-down-item-selected.hover {
     background-color: var(--semantic-color-background-pale);
    color: var(--semantic-color-content-base-primary);
    }
  .drill-down-item.empty {
    font-style: italic;
    text-align: center; }
  .drill-down-item.disabled {
    color: var(--semantic-color-content-interactive-primary-disabled);
    cursor: default;
    opacity: .5; }
    .drill-down-item.disabled:hover {
      background: none; }

.drill-down-empty-content {
  width: 100%;
  word-wrap: break-word;
  padding: 5px 6px 5px 16px; }

.drill-down-header-text {
  font-weight: bold;
  font-size: 13px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; }

.adv-dropdown-with-async-search {

  div.drop-down-container.error {
    .selected-value {
      color: var(--semantic-color-border-status-danger-active);
    }
  }
  div.drop-down-container button.drop-down-selected-value {
    border: 1px solid var(--semantic-color-border-base-primary);
    border-radius: 4px;
    background: var(--semantic-color-background-primary);
    align-items: center;
    box-shadow: none;
    justify-content: space-between;
    text-align: left;
    svg:not(:root).svg-inline--fa, svg:not(:host).svg-inline--fa {
      overflow: visible;
      box-sizing: content-box;
      font-size: larger;
    }
  }
  div.drop-down-container ul.drop-down-list li.list-item-search {
    background: transparent;// var(--semantic-color-background-primary);
    color: var(--semantic-color-content-base-primary);
    &:hover {
      background: transparent;// var(--semantic-color-background-primary);
      color: var(--semantic-color-content-base-primary);
    }
  }
  div.drop-down-container ul.drop-down-list li.list-item:hover {
    background: var(--semantic-color-background-primary);
    color: var(--semantic-color-content-base-primary);
  }


  .drop-down-container {
    .drop-down-list li { 
      border-radius: 0px;
      button { text-align: left; }
    }
    .dropdown-search {
      height: 32px;
      border-radius: 8px;
      border: 1px solid var(--semantic-color-border-base-primary);
      background-color: var(--semantic-color-background-primary);
      margin: 10px;
      display: flex;
      max-width: 85%;
      margin-left: auto;
      margin-right: auto;
    }
    .dropdown-search-text {
      border: none !important;
      border-radius: 8px;
      color: var(--semantic-color-content-base-primary);
      background: transparent !important;
      width: 85%;
      padding: 10px 10px 10px 10px;
    }
    .clear-button {
      width: auto;
    }
    .action-buttons{
      float: right;
      white-space: nowrap;
    }
  }


  .drop-down-container ul.drop-down-list.open {
    visibility: visible;
    opacity: 1;
    border: 1px solid var(--semantic-color-border-base-primary);
    border-radius: 4px;
    background-color: var(--semantic-color-background-primary);
    // box-shadow: 0 0 0 1px hsla(0,0%,0%,0.1),0 4px 11px hsla(0,0%,0%,0.1);
    .items-container {
      max-height: 300px;
      overflow-x: hidden;
      overflow-y: auto;
      width: 100%;
    }
    .list-item:hover {
      background-color: var(--semantic-color-background-pale);
      color: var(--semantic-color-content-base-primary)
    }
  }
}
}