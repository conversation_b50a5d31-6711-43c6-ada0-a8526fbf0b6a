import React, {
  useEffect, useRef, useState,
} from 'react';
import PropTypes from 'prop-types';
import i18n from 'utils/i18n';
import { noop } from 'utils/lodash';
import { faSearch, faTimes } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

const defaultProps = {
  show: false,
  value: '',
  onValueChange: noop,
  placeholder: '',
  onSearchClick: noop,
  containerClass: '',
  containerStyle: {},
  onSearchHide: noop,
  inputClass: '',
};

function Search({
  show,
  value,
  onValueChange,
  placeholder,
  onSearchClick,
  containerClass,
  containerStyle,
  onSearchHide,
  inputClass,
}) {
  const searchRef = useRef(null);
  const inputRef = useRef(null);

  const [inputValue, setInputValue] = useState(value);

  const resetState = () => {
    onSearchHide();
  };

  const handleClickOutside = (event) => {
    if (searchRef.current && !searchRef.current.contains(event.target)) {
      resetState();
    }
  };

  useEffect(() => {
    if (show) document.addEventListener('click', handleClickOutside);

    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [show]);

  const updateValue = (newValue) => {
    setInputValue(newValue);

    onValueChange(value);
  };

  const onChangeHandler = (evt) => {
    const { value: newValue } = evt.target;

    updateValue(newValue);
  };

  const onClearClickHandler = () => {
    updateValue('');
  };

  const onSearchClickHandler = () => {
    onSearchClick(inputValue);
  };

  const onKeyUp = (e) => {
    e.preventDefault();

    if (e.keyCode !== 13) return;
    onSearchClick(inputValue);
  };

  if (!show) {
    return null;
  }

  return (
    <article
      ref={searchRef}
      className={`search-container ${containerClass}`}
      style={containerStyle}>
      <section className="input-container">
        <input
          // eslint-disable-next-line jsx-a11y/no-autofocus
          autoFocus
          ref={inputRef}
          className={`input ${inputClass}`}
          type="text"
          value={inputValue}
          onChange={onChangeHandler}
          onKeyUp={onKeyUp}
          placeholder={placeholder || i18n.t('SEARCH_ELLIPSIS')} />

        <div className="clear-container">
          <FontAwesomeIcon icon={faTimes} className="clear" onClick={onClearClickHandler} />
        </div>
      </section>
      <section className="action-container">
        <FontAwesomeIcon icon={faSearch} className="search" onClick={onSearchClickHandler} />
      </section>
    </article>
  );
}

Search.defaultProps = defaultProps;

Search.propTypes = {
  show: PropTypes.bool,
  value: PropTypes.string,
  onValueChange: PropTypes.func,
  placeholder: PropTypes.string,
  onSearchClick: PropTypes.func,
  containerClass: PropTypes.string,
  containerStyle: PropTypes.objectOf(Object),
  onSearchHide: PropTypes.func,
  inputClass: PropTypes.string,
};

export default Search;
