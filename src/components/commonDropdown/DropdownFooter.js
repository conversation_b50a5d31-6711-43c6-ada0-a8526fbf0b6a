import React from 'react';
import PropTypes from 'prop-types';

function DropdownFooter({
  handleFooterClick, isMulti, initialValues, defaultSelected,
}) {
  const onCancel = () => {
    handleFooterClick(initialValues);
  };

  const onClear = () => {
    handleFooterClick(defaultSelected);
  };

  return (
    <div className="dropdown-footer" style={{ justifyContent: !isMulti ? 'center' : 'space-between' }}>
      { isMulti && <button type="button" onClick={() => onCancel()}>Cancel</button> }
      <button type="button" onClick={() => onClear()}>
        Clear
        {isMulti && ' All' }
      </button>
    </div>
  );
}

DropdownFooter.propTypes = {
  isMulti: PropTypes.bool,
  handleFooterClick: PropTypes.func.isRequired,
  initialValues: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
  defaultSelected: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
};

DropdownFooter.defaultProps = {
  isMulti: true,
};

export default DropdownFooter;
