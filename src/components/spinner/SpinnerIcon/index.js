// @flow

import React from 'react';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faLoader } from '@fortawesome/pro-solid-svg-icons/faLoader';
import './index.scss';

// const SpinnerIcon = () => <span className="waiting-spinner" />;
function SpinnerIcon({ size = '3x' }) {
  return (
    <FontAwesomeIcon
      icon={faLoader}
      size={size}
      className="fa-spin waiting-spinner"
      color="var(--semantic-color-content-interactive-primary-default)" />
  );
}

SpinnerIcon.propTypes = {
  size: PropTypes.string,
};

export default SpinnerIcon;
