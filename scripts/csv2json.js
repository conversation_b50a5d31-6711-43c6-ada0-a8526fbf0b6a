/* eslint-disable */

/*
Usage:
nvm use 12.11
node --experimental-modules scripts/csv2json.mjs <location of translated file> > lang.js

lang.js is jp-JP.js, for exmaple

Example:  node --experimental-modules csv2json.mjs /Users/<USER>/Documents/translations/5-4-2020/ja_JP-zdx-ui.csv > ja-JP.js

Take translated CSV file (recevied from documentation team)
and convert it into JSON for app to use
*/

import fs from 'fs';
import csv from 'csvtojson';

const csvFilePath = process.argv[2];

csv({ noheader: true })
.fromFile(csvFilePath)
.then((jsonObj)=>{
    // console.log(jsonObj);

    const keyValMap = {};
    jsonObj.forEach((obj) => {
      // console.log(obj)
      keyValMap[obj.field1] = obj.field3;
    });
    const jsonstr = JSON.stringify({ translation: { ...keyValMap }}, null, 2);
    console.log(`export default ${jsonstr}`);
});