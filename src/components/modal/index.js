import React from 'react';
import ReactModal from 'react-modal';
import PropTypes from 'prop-types';
import { Trans } from 'react-i18next';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';

import './index.scss';

ReactModal.setAppElement('body');

function Modal({
  title, isOpen, closeModal, children, styleClass,
}) {
  return (
    <ReactModal
      isOpen={isOpen}
      contentLabel="Example Modal"
      className={`modal-body ${styleClass}`}
      portalClassName="ec-root-page"
      closeTimeoutMS={400}
      overlayClassName="modal-overlay">
      <div className="modal-header">
        <p>
          <Trans>{title}</Trans>
        </p>
        <div>
          {closeModal && (
            <button type="button" onClick={closeModal} className="close-button">
              <FontAwesomeIcon icon={faTimes} />
            </button>
          )}
        </div>

      </div>
      <div className="modal-content">
        {children}
      </div>
    </ReactModal>
  );
}

Modal.propTypes = {
  title: PropTypes.node,
  children: PropTypes.oneOfType([
    PropTypes.element,
    PropTypes.arrayOf(PropTypes.element),
  ]),
  isOpen: PropTypes.bool,
  closeModal: PropTypes.func,
  styleClass: PropTypes.string,
};

Modal.defaultProps = {
  title: null,
  children: null,
  isOpen: false,
  closeModal: null,
  styleClass: '',
};

export default Modal;
