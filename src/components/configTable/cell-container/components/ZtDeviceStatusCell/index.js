/* eslint-disable jsx-a11y/label-has-for */
import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import i18n from 'utils/i18n';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faCheckCircle,
  faXmarkCircle,
  faPauseCircle,
} from '@fortawesome/pro-solid-svg-icons';
  
const defaultProps = {
  value: {},
};
  
function ZtDeviceStatusCell(props) {
  const { value } = props;
  
  const renderStatus = (item) => {
    const { status } = item;

    if (!status || status === 0) {
      return '---';
    }

    return (
      <label htmlFor="status" className="child-checkbox-label">
        { (status === 'REGISTERED') && (
          <FontAwesomeIcon
            icon={faCheckCircle}
            className="success-icon" />
        )}
        {(status === 'RMA') && (
          <FontAwesomeIcon
            icon={faXmarkCircle}
            className="failed-icon" />
        )}
        {(status === 'UNREGISTERED') && (
          <FontAwesomeIcon
            icon={faPauseCircle}
            className="inactive-icon" />
        )}
        {i18n.t(status)}
      </label>
    );
  };
  
  return (
    <div
      onKeyPress={null}
      // aria-label="Operational Status"
      role="button"
      tabIndex="0">
      {
        renderStatus(value)
      }
    </div>
  );
}
  
ZtDeviceStatusCell.defaultProps = defaultProps;
  
ZtDeviceStatusCell.propTypes = {
  value: PropTypes.shape({}),
};
  
export default withTranslation()(ZtDeviceStatusCell);
