// @import 'scss/widgets.scss';
@import 'scss/pages.scss';
@import "scss/colors.scss";
@import 'scss/mixins.scss';

.ec-root-page {
.remote-assistance-form {
    .form-sections-container {
        padding: 19px 25px;
        background: var(--semantic-color-background-primary);
        border: 1px solid var(--semantic-color-border-base-primary);
        padding: 16px 24px;
        width: 100%;
        max-height: 630px;
        overflow: auto;
        .form-section-label {
        padding: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: var(--semantic-color-content-base-primary);
        font-weight: 400;
        font-size: 13px;
        letter-spacing: 0.5px;
        margin-bottom: 8px;
        text-transform: uppercase;
        width: 100%;
        }
        .form-section {
        flex-direction: column;
        background-color: var(--semantic-color-surface-base-primary);
        border: 1px solid var(--semantic-color-border-base-primary);        
        border-radius: 5px;
        width: 100%;
        margin-bottom: 24px;
        }
    }

    .g-row {  
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        width: 100%;
    
        &:first-child {
          .g-right{
            padding-left: 0;
          }
          .g-left {
            padding-right: 0;
          }
        }
        
        .g-fullwidth{
          padding: 12px 16px;
          width: 100%;
        }
        .g-left,.g-right {
          width: 50%;      
          padding: 12px 16px;
        }

        .input-container,.input-password-container {
          padding: 0;
          input {
            border: 1px solid var(--semantic-color-border-base-primary);
            background: var(--semantic-color-background-pale);
            border-radius: 8px;   
            color: #656666;
            cursor: text;
            display: block;
            height: 32px;
            padding: 9px 8px;
            text-align: left;
            min-width: 220px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            &:active,&:focus {
              background-color: var(--semantic-color-surface-fields-active);
            }
            &:hover {
              background-color: var(--semantic-color-surface-fields-hover);
            }
          }
        }        
        .disabled-input {
          color: var(--semantic-color-content-interactive-primary-disabled);
          padding: 14px 0;
        }
    }
    .tooltip-navlink {
      text-decoration: none;
      color: $anchor-color;
    }
    .remote-assistance-permission-required {
      // position: relative;
      background: var(--semantic-color-background-primary);
      min-height: 300px;
    }
}
}