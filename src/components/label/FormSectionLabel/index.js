/* eslint-disable jsx-a11y/label-has-associated-control */
/* eslint-disable jsx-a11y/label-has-for */
// @flow

import React from 'react';
import PropTypes from 'prop-types';
import ReactTooltip from 'react-tooltip';
import { Trans, withTranslation } from 'react-i18next';
import {
  setToolTipPosition,
} from 'utils/helpers';

import './index.scss';

const handleHover = (event) => {
  setToolTipPosition(event, '.modal-body');
};

function FormSectionLabel(props) {
  const {
    text, styleClass, tooltip, error,
  } = props;

  return (
    <label className="form-field-label-wrapper form-section-label">
      <span className={`form-field-label ${styleClass}`} data-tip data-for={text} onMouseEnter={handleHover}>
        <Trans>{text}</Trans>
        <ReactTooltip
          id={text}
          clickable
          place="top"
          type="light"
          offset={{ top: -10 }}
          effect="solid"
          disable={(tooltip.length === 0)}
          delayHide={30}
          border
          borderColor="#939393"
          className="form-field-tooltip-container">
          <div id="tooltip-top" className="tooltip-top tooltip-top-text">
            {error && <div className="help-error-text has-info-text">{error}</div>}
            <div className="help-text">{tooltip}</div>
          </div>
        </ReactTooltip>
      </span>
    </label>
  );
}

FormSectionLabel.propTypes = {
  text: PropTypes.string,
  tooltip: PropTypes.oneOfType([PropTypes.string, PropTypes.shape({})]),
  styleClass: PropTypes.string,
  error: PropTypes.string,
};

FormSectionLabel.defaultProps = {
  text: '',
  styleClass: '',
  tooltip: '',
  error: null,
};

export default withTranslation()(FormSectionLabel);
