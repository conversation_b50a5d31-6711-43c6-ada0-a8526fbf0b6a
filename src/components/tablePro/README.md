# tablePro

To use this component, follow the below steps

1. Import the below when creating a new table.

```
import TableWrapper from 'components/tablePro/TableWrapper';
import CustomizeColumns from 'components/tablePro/CustomizeColumns';
import CustomizeColsHeader from 'components/tablePro/CustomizeColsHeader';
import RowEditor from 'components/tablePro/RowEditor';
```

2. The below methods needs to be aded into the component

```
  handleOnMouseOverCb = () => this.setState(prevState => ({ showComponent: !prevState.showComponent }))

  getColumns = () => {
    const {
      columns,
      actions,
    } = this.props;
    const {
      editRowHandler,
    } = actions;
    const lastColumn = columns[columns.length - 1];
    const lastColWithElipsis = {
      ...lastColumn,
      headerRenderer: (
        <CustomizeColsHeader
          props={this}
          onMouseOverCb={() => this.handleOnMouseOverCb} />
      ),
      formatter: <RowEditor showSubEdit />,
      events: {
        onClick(ev, row) {
          editRowHandler(row);
        },
      },
    };
    columns[columns.length - 1] = lastColWithElipsis;
    return [...columns];
  }
```

3. Add the below inside Render method

```
  const {
      data,
      actions,
    } = this.props;
  const { showComponent } = this.state;
  const columns = this.getColumns();
  return (
      <div className="table-layout-header -js-header">
        <TableWrapper
          initialRows={data}
          initialColumns={columns}
          updateMenu={actions.updateMenu} />
        <CustomizeColumns
          initialItems={columns}
          showComponent={showComponent}
          dragnDrop={actions.updateMenu}
          minHeight="200" />
      </div>
  );
```

4. Add the 'updateMenu' method under the respective ducks

```
export const updateMenu = columns => (dispatch) => {
  dispatch(dataChanged({
    [constants.COLUMNS]: columns,
  }));
};
```

5. Define columns
```
export const columns = [
  {
    key: 'name',
    name: 'Name',
    draggable: true,
    resizable: true,
    checkbox: true,
    sortable: true,
    visible: true,
  },
  {
    key: 'ecGroup',
    name: 'EdgeConnectors Group',
    draggable: true,
    resizable: true,
    checkbox: true,
    sortable: true,
    visible: true,
  }];
```

Libraries used:

```
"react-data-grid": "^6.1.0",
"react-data-grid-addons": "^6.1.0",
"react-sortable-hoc": "1.9.1",
```

Sample Implementation:-

```
import React from 'react';
import PropTypes from 'prop-types';
import { noop } from 'utils/lodash';
import { withTranslation } from 'react-i18next';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import * as edgeconnectorSelector from 'ducks/edgeconnectors/selectors';
import Loading from 'components/spinner/Loading';
import {
  getEdgeConnectorsData,
  updateMenu,
} from 'ducks/edgeconnectors';

import TableWrapper from 'components/tablePro/TableWrapper';
import CustomizeColumns from 'components/tablePro/CustomizeColumns';
import CustomizeColsHeader from 'components/tablePro/CustomizeColsHeader';
import RowEditor from 'components/tablePro/RowEditor';

import './index.scss';

export class EdgeConnectorGroups extends React.Component {
  static propTypes = {
    actions: PropTypes.shape({
      load: PropTypes.func,
    }),
    tabledata: PropTypes.arrayOf(PropTypes.shape()),
    columns: PropTypes.arrayOf(PropTypes.shape()),
  }

  static defaultProps = {
    actions: {
      load: noop,
    },
    tabledata: [],
    columns: [],
  }
 
  // showComponent flag is to either display or hide the table Menu
  state = {
    showComponent: false,
  }

  componentDidMount() {
    const { actions } = this.props;
    const { load } = actions;
    load();
  } 

  handleOnMouseOverCb = () => this.setState(prevState => ({ showComponent: !prevState.showComponent }))

  getColumns = () => {
    const {
      columns,
      actions,
    } = this.props;
    const {
      editRowHandler,
    } = actions;
    const lastColumn = columns[columns.length - 1];
    const lastColWithElipsis = {
      ...lastColumn,
      headerRenderer: (
        <CustomizeColsHeader
          props={this}
          onMouseOverCb={() => this.handleOnMouseOverCb} />
      ),
      formatter: <RowEditor showSubEdit />,
      events: {
        onClick(ev, row) {
          editRowHandler(row);
        },
      },
    };
    columns[columns.length - 1] = lastColWithElipsis;
    return [...columns];
  }

  render() {
    const {
      tabledata,
      actions,
    } = this.props;
    const { showComponent } = this.state;
    const columns = this.getColumns();
    return (
      <div className="table-layout-header -js-header">
        <TableWrapper
          initialRows={tabledata}
          initialColumns={columns}
          updateMenu={actions.updateMenu} />
        <CustomizeColumns
          initialItems={columns}
          showComponent={showComponent}
          dragnDrop={actions.updateMenu}
          minHeight="200" />
      </div>
     );
  }
}

const mapStateToProps = state => ({ ...edgeconnectorSelector.default(state) });

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    load: getEdgeConnectorsData,
    updateMenu,
  }, dispatch);
  return { actions };
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(EdgeConnectorGroups));
```