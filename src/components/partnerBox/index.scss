@import 'scss/variables.scss';

.ec-root-page {
  .partner-box-container {
    display: flex;
    flex-direction: row;

  .partner-box {
      box-sizing: border-box;
      display: flex;
      align-items: flex-start;
      border-radius: 4px;
      flex-direction: column;

      display: flex;
      // max-width: 380px;
      min-width: 340px;
      max-height: 230px;
      min-height: 230px;
      padding:  16px;
      flex-direction: column;
      gap:  24px;
      flex: 1 0 0;
      margin-right: 8px;
      
      
      padding: 16px;
      border-radius: 8px;
      border-radius: 8px;
      border: 1px solid var(--semantic-color-border-base-primary);
      background: var(--semantic-color-surface-base-primary);
      .box-action {
        visibility: hidden;
        opacity: 0;
        transition: visibility 0s, opacity 0.3s ease-in-out;
        
        color: var(--semantic-color-surface-interactive-primary-default);
        text-align: center;

        /* Paragraph/P1 */
        font-size: var(--semantic-fontSize-small, 14px);
        font-style: normal;
        font-weight: var(--semantic-fontWeight-regular, 400);
        line-height: var(--font-line-height-small, 20px); /* 142.857% */
        min-width: 46px;
      }
      

      &:hover {
        border: 1px solid var(--semantic-color-border-interactive-secondary-hover);
        background: var(--semantic-color-surface-fields-hover);
        .box-action {
          visibility: visible;
          opacity: 1;
        }
      }
      margin-bottom: 16px;
      .partner-box-title{
        display: flex;
        justify-content: space-between;
        font-size: 13px;
        font-weight: 500;
        line-height: 20px;
        text-align: left;

        color: var(--semantic-color-content-base-primary, #191919);

        font-size: var(--semantic-fontSize-base, 16px);
        font-style: normal;
        font-weight: var(--semantic-fontWeight-medium, 500);
        line-height: var(--font-line-height-base, 20px); /* 125% */

        width: 100%;
        .left {
          width: fit-content;
          .svg-inline--fa {
            font-style: normal;
            font-weight: 400;
            font-size: 13px;
            line-height: 20px;
            margin-right: 8px;
            // display: flex;
            // align-items: center;
            // text-align: center;
            color: var(--semantic-color-content-base-primary);
            &.fa-circle-info {
              font-style: normal;
              font-weight: 900;
              font-size: 13px;
              line-height: 20px;
              color: #388ECF;
            }
          }
        }
        .right {
          width: fit-content;
          cursor: pointer;
          padding: 0;
          .svg-inline--fa {
            font-style: normal;
            font-weight: 400;
            font-size: 13px;
            line-height: 20px;
            margin-right: 8px;
            color: var(--semantic-color-content-interactive-primary-default);
          }
        }
      }
      .partner-box-message {
        white-space: break-spaces;
        overflow: auto;
        line-height: 20px;
        word-wrap: break-word;
        max-width: 100%;

        text-overflow: ellipsis;
        font-size: var(--font-size-xs, 12px);
        font-style: normal;
        font-weight: var(--font-weight-text-regular, 400);
        line-height: var(--font-line-height-xs, 16px);
        .title {
          color: var(--semantic-color-content-base-tertiary);        
        }
        .value {
          color: var(--semantic-color-content-base-primary);        
        }
        a {
          text-decoration: none;
          color: var(--semantic-color-content-interactive-primary-default)
        }
      }
    }
  }
}