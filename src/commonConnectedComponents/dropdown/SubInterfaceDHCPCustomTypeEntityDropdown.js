import React from 'react';
import { EntityDropdown } from 'components/entityDropdown';

function SubInterfaceDHCPCustomTypeEntityDropdown(props) {
  const LIST_ITEM = [{ id: 'STRING', name: 'STRING' }, { id: 'IP', name: 'IP' }];

  return (
    <EntityDropdown
      {...props}
      data={LIST_ITEM}
      value={LIST_ITEM[0]}
      disableErrorMsg />
  );
}

export default SubInterfaceDHCPCustomTypeEntityDropdown;
