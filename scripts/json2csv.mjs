/* eslint-disable */

/*
Usage:
nvm use 12.11
node --experimental-modules scripts/json2csv.mjs 
Run from directly above
*/

import fs from 'fs';
import converter from 'convert-array-to-csv';

import en_US from '../src/utils/i18n/en-US.js';
import de_DE from '../src/utils/i18n/de-DE.js';
import es_ES from '../src/utils/i18n/es-ES.js';
import fr_FR from '../src/utils/i18n/fr-FR.js';
import ja_JP from '../src/utils/i18n/ja-JP.js';
import zh_TW from '../src/utils/i18n/zh-TW.js';

import previousFile from '../translations/en-US-Feb-13-2020.js'

const NEEDS_TRANSLATION = 'NEEDS TRANSLATION';

// this is our base translation. We will use this as a diff
const base = en_US.translation;

const prevBase = previousFile.translation;

const toBeTraslated = [
  { lang: 'en_US', json: en_US.translation }, // yes, we are comparing agains base, which is same
  { lang: 'de_DE', json: de_DE.translation },
  { lang: 'es_ES', json: es_ES.translation },
  { lang: 'fr_FR', json: fr_FR.translation },
  { lang: 'ja_JP', json: ja_JP.translation },
  { lang: 'zh_TW', json: zh_TW.translation },
];

const csvProcessor = (base, target, lang) => {
  const keys = Object.keys(base);
  return keys.map((key) => {
    const prevValue = prevBase[key];
    if (!prevValue && lang !== 'en_US') { // new entry
      return [key, base[key], NEEDS_TRANSLATION];
    }
    
    const currentValue = base[key];
    if (prevValue !== currentValue && lang !== 'en_US') {
      // existing entry but the value changed
      return [key, base[key], NEEDS_TRANSLATION];
    }

    // nothing changed, keep as is
    return [key, base[key], target[key]];
  });
}

toBeTraslated.map(({ lang, json }) => {
  const payload = csvProcessor(base, json, lang);
  const csv = converter.convertArrayToCSV(payload);

  const needTranslation = payload.reduce((num, item) => {
    if (item[2] === NEEDS_TRANSLATION) {
      return num += 1;
    }
    return num;
  }, 0);

  const outfile = `./translations/files-for-doc-team/${lang}.csv`;
  fs.writeFile(outfile, csv, (err) => {
    if (err) {
      console.error(`>> error while saving file ${outfile}:\n${err}`);
      return;
    }

    console.log(`>> file ${outfile} was saved successfully. ${needTranslation} labels need translation.`);
  });
})
