/* eslint-disable react/jsx-handler-names */
/* eslint-disable no-return-assign */
import React from 'react';
import PropTypes from 'prop-types';
import { noop } from 'utils/lodash';

import Unselected from './Unselected';
import Selected from './Selected';
import DropdownFooter from './DropdownFooter';

export class DropdownSelector extends React.Component {
  componentDidMount() {
    const {
      // eslint-disable-next-line react/prop-types
      categoryUpdated: resetToDefault = false,
      actions: { load },
    } = this.props;
    load(resetToDefault);
    document.addEventListener('mousedown', this.handleClick);
  }

  componentWillUnmount() {
    // remove click handler and clean up state on unmount
    document.removeEventListener('mousedown', this.handleClick);
    this.performStateCleanup();
  }

  node = null;

  handleClick = (e) => {
    if (this.node && !this.node.contains(e.target)) {
      this.onComplete();
    }
  };

  onComplete = () => {
    // Tell redux-form that we are done making changes to this
    // input. Also perform cleanup
    const {
      input: { onChange },
      selectedValues,
    } = this.props;

    return Promise.resolve()
      .then(() => {
        return onChange(selectedValues);
      })
      .then(() => {
        this.performStateCleanup();
      });
  };

  performStateCleanup = () => {
    // clean needs to be done when form closes or
    // user is done making changes (presses Done button).
    // Essentially put the state in initial state.
    const {
      actions: { cancel },
    } = this.props;
    cancel();
  };

  setNode = (node) => this.node = node;

  render() {
    const { place, noDataAvailableMessage = '', showMessage = false } = this.props;

    return showMessage
      ? (
        <div
          className={`${place} dropdown-selector dropdown-no-data-available`}
          ref={this.setNode}>
          <div>
            {noDataAvailableMessage}
          </div>
        </div>
      )
      : (
        <div
          className={`${place} dropdown-selector`}
          ref={this.setNode}>
          <div className="dropdown-lists">
            <Unselected {...this.props} />
            <Selected {...this.props} />
          </div>
          <DropdownFooter
            {...this.props}
            onComplete={this.onComplete} />
        </div>
      );
  }
}

DropdownSelector.propTypes = {
  actions: PropTypes.shape({
    load: PropTypes.func,
    cancel: PropTypes.func,
  }),
  place: PropTypes.string,
  noDataAvailableMessage: PropTypes.string,
  showMessage: PropTypes.bool,
  input: PropTypes.shape({
    onChange: PropTypes.func,
  }),
  selectedValues: PropTypes.arrayOf(PropTypes.shape({})),
};

DropdownSelector.defaultProps = {
  actions: {
    load: noop,
  },
  place: 'left',
  noDataAvailableMessage: '',
  showMessage: false,
  input: {
    onChange: noop,
  },
  selectedValues: [],
};

export default DropdownSelector;
