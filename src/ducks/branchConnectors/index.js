import {
  createAction,
  loadSuccess,
  loading,
} from 'ducks/generics';
import i18n from 'utils/i18n';
import {
  get, omit, orderBy, isEmpty,
} from 'utils/lodash';
import { destroy, change } from 'redux-form';
import { genericInterface } from 'utils/http';
import { BASE_LAYOUT } from 'config';
import {
  adjustHoursToMeetInterval, convertMinutesToAMPM, convertAMPMtoMinutes,
  hasBCEncryptSku, getRealOperationalStatus, calculateNextUpdate,
  getSubscriptionLicenses, trinary, verifyConfigData,
} from 'utils/helpers';
import { notify, notifyError } from 'ducks/notification';
import * as branchConnectorSelector from 'ducks/branchConnectors/selectors';
import * as loginSelectors from 'ducks/login/selectors';
import * as constants from './constants';
import actionTypes from './action-types';
import { checkActivation } from '../activation';

const form = 'BranchConnectorViewForm';

// we want this to exported for to be used in store and other places
// from index so...elsewhere we can say import {REDUCER_KEY as widget} from 'ducks/widget'
// for exmaple
export { REDUCER_KEY } from './constants';

const actionTypeExists = Object.keys(actionTypes).reduce((hash, key) => ({
  ...hash,
  [actionTypes[key]]: true,
}), {});

// unlike other reducer, we will use
export const reducer = (state = constants.DEFAULT_STATE, action = {}) => {
  // We will merge payload from actions to the state for this widget/page/component
  // that only matches to our actions in actiontypes
  // for example once data is loaded our action below will dispatch
  // action and data
  if (actionTypeExists[action.type]) {
    return {
      ...state,
      ...action.payload,
    };
  }
  return state;
};
export default reducer;

const boundLoading = loading(actionTypes.DATA_LOADING);
const boundSuccess = loadSuccess(actionTypes.DATA_LOAD_SUCCESS);
const dataChanged = createAction(actionTypes.DATA_CHANGED);
const boundError = createAction(actionTypes.DATA_LOAD_ERROR);
const boundModalLoading = createAction(actionTypes.MODAL_LOADING);

export const getBranchConnectorData = () => (dispatch) => {
  dispatch(boundSuccess(constants.DATA));
};

export const toggleForm = (toggle) => (dispatch) => {
  if (toggle) {
    dispatch(dataChanged({
      [constants.SHOW_FORM]: toggle,
    }));
  } else {
    dispatch(dataChanged({
      ...constants.DEFAULT_STATE, // reset everything on close
    }));
  }
};

export const hanldeExpandClick = (toggleValue) => (dispatch) => {
  dispatch(dataChanged({
    [constants.EXPAND_ALL]: toggleValue,
  }));
};

const getParentStatus = (childNode) => {
  if (childNode.some((x) => x.upgradeStatus > 1)) return -1;
  if (childNode.some((x) => x.upgradeStatus !== 1)) return 0;

  return 1;
};

const getOperationalStatusSummary = (ecVM) => {
  const operationalStatus = {
    DISABLED: 0,
    INACTIVE: 0,
    ACTIVE: 0,
    DISABLING: 0,
    ENABLING: 0,
  };

  if (ecVM.length > 0) {
    ecVM.forEach((item) => {
      const { status } = item || [];
      const realOperationalStatus = getRealOperationalStatus(item.operationalStatus, status);

      switch (realOperationalStatus) {
      case 'DELETING':
        operationalStatus.INACTIVE += 1;
        break;
      case 'DISABLED':
        operationalStatus.DISABLED += 1;
        break;
      case 'INACTIVE':
        operationalStatus.INACTIVE += 1;
        break;
      case 'ACTIVE':
        operationalStatus.ACTIVE += 1;
        break;
      case 'ENABLING':
        operationalStatus.ENABLING += 1;
        break;
      case 'DISABLING':
        operationalStatus.DISABLING += 1;
        break;
      default:
        break;
      }
    });
    return operationalStatus;
  }
  return operationalStatus;
};

const sortBranchConnectorGroup = (groupArray, sortedBy, direction, locationArray) => {
  const sortedArray = [];
  let location;
  orderBy(groupArray.filter((x) => !x.parentId), [sortedBy], [direction]).forEach((item) => {
    if (item.ecVMs) {
      if (item.ecVMs.length === 0) {
        location = locationArray && locationArray.length > 0
          ? locationArray.find((x) => x?.id === item.location?.id)
          : {};
        sortedArray.push({
          ...omit(item, 'ecVMs'), expanded: true, parentId: null, scheduled: false, isVisible: true, isEditable: true, location, upgradeStatus: '', upgradeWindow: '', operationalStatus: 0,
        });
      } else {
        const operationalStatusSummary = getOperationalStatusSummary(item.ecVMs);
        orderBy(item.ecVMs, [sortedBy], [direction]).forEach((ecVM, idx) => {
          const upgradeWindow = `${convertMinutesToAMPM(ecVM.upgradeStartTime / 60, true)} - ${convertMinutesToAMPM(ecVM.upgradeEndTime / 60, true)} `;
          const { operationalStatus, status } = ecVM || {};
          
          if (idx === 0) {
            location = locationArray && locationArray.length > 0
              ? locationArray.find((x) => x?.id === item.location?.id)
              : {};
            const upgradeStatus = getParentStatus(item.ecVMs);
            const isMultipleDates = item.ecVMs.length > 1 && item.ecVMs.some(
              (x) => x.upgradeStartTime !== item.ecVMs[0].upgradeStartTime
              || x.upgradeEndTime !== item.ecVMs[0].upgradeEndTime
              || x.upgradeDayOfWeek !== item.ecVMs[0].upgradeDayOfWeek,
            );
            const groupUpgradeWindow = isMultipleDates ? i18n.t('MULTIPLE') : upgradeWindow;
            const upgradeDayOfWeek = isMultipleDates ? '' : item.ecVMs[0].upgradeDayOfWeek;
              
            sortedArray.push({
              ...omit(item, 'ecVMs'),
              expanded: false,
              parentId: null,
              scheduled: false,
              isVisible: true,
              isEditable: true,
              location,
              upgradeStatus,
              upgradeWindow: groupUpgradeWindow,
              operationalStatus: operationalStatusSummary,
              upgradeDayOfWeek,
            });
          }
          sortedArray.push({
            ...omit(item, 'ecVMs'), ...ecVM, expanded: false, parentId: item?.id, scheduled: false, isVisible: false, isEditable: true, location, upgradeWindow, operationalStatus: getRealOperationalStatus(operationalStatus, status),
          });
        });
      }
    } else {
      sortedArray.push(item);
      sortedArray.push(
        ...orderBy(groupArray.filter((x) => x.parentId === item.id, [sortedBy], [direction])),
      );
    }
  });

  return sortedArray;
};

const loadAllLocations = async (dispatch) => {
  let ccLocations = [];
  let locationPage = [];
  let page = 1;
  do {
    const locationPageAPI = genericInterface(constants.LOCATION_API_PAGE(page));
    try {
    // eslint-disable-next-line no-await-in-loop
      const { data } = await locationPageAPI.read();
      locationPage = data;
      ccLocations = [...ccLocations, ...locationPage];
      page += 1;
    } catch (error) {
      const errorMsg = get(error, 'response.data.message', i18n.t('ERROR_BRANCH_BRANCH_CONNECTORS_GROUP_ERROR'));
      dispatch(boundLoading({
        loading: false,
        [constants.MODAL_LOADING]: false,
      }));
      dispatch(notifyError(errorMsg));
    }
  } while (locationPage && locationPage.length === 100);
  dispatch(dataChanged({ [constants.LOAD_ALL_LOCATIONS]: ccLocations }));

  return ccLocations;
};

export const loadBranchConnectorData = (initializeProps, newPage = 1) => async (dispatch, getState) => {
// Rules for Upgarde status
// if any Child has an error set parent to error with message
// "One or more Branch Connector Upgrades Failed"
// If any scheduled parent => scheduled
// If all childs NO PENDING => parent No Pending

  const state = getState();
  let { pages, numberOfLines } = await branchConnectorSelector.baseSelector(state);
  const deployType = window.location.pathname.includes('physical') ? 'ONPREM_PHYSICAL' : 'ONPREM';
  if (initializeProps) {
    state.branchConnectors.branchConnectorsData = [];
    state.branchConnectors.pages = [];
    pages = [];
    numberOfLines = null;
  }
  
  const {
    pageSize, searchText, checkAll, nextBuild,
  } = await branchConnectorSelector.baseSelector(state);
  // const searchText = document.querySelector('.search-container .input-search-box').value;
  const param = searchText !== '' ? {
    name: searchText, page: newPage, pageSize, deployType,
  } : {
    page: newPage, pageSize, deployType,
  };
  const searchParam = searchText !== '' ? { name: searchText, deployType } : { deployType };

  const ccGroupCountdApi = genericInterface(constants.API_CC_GROUP_COUNT_ENDPOINT);
  if (!numberOfLines) {
    try {
      const { data } = await ccGroupCountdApi.read(null, {
        params: searchParam,
      });
      numberOfLines = data || 1;
    } catch (error) {
      const errorMsg = get(error, 'response.data.message', i18n.t('ERROR_BRANCH_BRANCH_CONNECTORS_GROUP_ERROR'));
      dispatch(boundLoading({
        loading: false,
        [constants.MODAL_LOADING]: false,
      }));
      dispatch(notifyError(errorMsg));
    }
  }
  const numberOfPages = Math.ceil(numberOfLines / pageSize);

  dispatch(boundLoading({ loading: true }));

  let { ccLocations } = state.branchConnectors;
  if (isEmpty(ccLocations)) ccLocations = await loadAllLocations(dispatch);

  if (numberOfLines > 0 && pageSize > 0
        && numberOfPages === pages.length
        && !isEmpty(pages[newPage - 1])) {
    dispatch(dataChanged({
      loading: false,
      [constants.DATA_TABLE]: pages[newPage - 1],
      [constants.PAGE_NUMBER]: newPage,
    }));
    return null;
  }
  
  const ccNextBuildApi = genericInterface(constants.API_NEXT_BUILD);
  let ccNextBuild = nextBuild;
  if (!ccNextBuild) {
    try {
      const { data: { version: build } } = await ccNextBuildApi.read();
      ccNextBuild = build || 'N/A';
    } catch (error) {
      const errorMsg = get(error, 'response.data.message', i18n.t('ERROR_BRANCH_BRANCH_CONNECTORS_GROUP_ERROR'));
      dispatch(dataChanged({
        loading: false,
        [constants.MODAL_LOADING]: false,
      }));
      dispatch(notifyError(errorMsg));
    }
  }
  
  const newPages = isEmpty(pages) ? Array(numberOfPages) : pages;
  const newCheckAllList = isEmpty(checkAll) || numberOfPages !== pages.length
    ? Array(numberOfPages).fill(false)
    : [checkAll];

  const ccApi = genericInterface(constants.API_LITE_ENDPOINT);
  return ccApi.read(null, {
    params: param,
  })
    .then((response) => {
      const plainData = sortBranchConnectorGroup(response.data, constants.SORT_FIELD, constants.SORT_DIRECTION, ccLocations);
      newPages[newPage - 1] = plainData;

      dispatch(dataChanged({
        loading: false,
        [constants.DATA_TABLE]: plainData,
        [constants.PAGES]: newPages,
        [constants.PAGE_NUMBER]: newPage,
        [constants.CHECK_ALL]: newCheckAllList,
        [constants.NUMBER_OF_LINES]: numberOfLines,
        [constants.NEXT_BUILD]: ccNextBuild,
      }));
    })
    .catch((error) => {
      const errorMsg = get(error, 'response.data.message', i18n.t('ERROR_BRANCH_BRANCH_CONNECTORS_GROUP_ERROR'));
      dispatch(dataChanged({
        loading: false,
        [constants.MODAL_LOADING]: false,
      }));
      dispatch(boundError(errorMsg));
      dispatch(notifyError(errorMsg));
    });
};

export const loadBCData = (id, loadedBCData) => async (dispatch) => {
  dispatch(boundLoading({ loading: false, modalLoading: true }));
  const ccApi = genericInterface(constants.API_ENDPOINT(
    loadedBCData.parentId ? loadedBCData.parentId : loadedBCData.id,
  ));

  return ccApi.read()
    .then((response) => {
      const updatedData = response.data;
      const selectedData = {
        ...updatedData,
        ecVMs: updatedData.ecVMs.filter((x) => x.id === id),
        location: loadedBCData.location,
        tunnelMode: updatedData.tunnelMode,
        fallBackToTls: updatedData.fallBackToTls,
      };
      dispatch(dataChanged({
        loading: false,
        modalLoading: false,
        [constants.LOADED_BC_DATA]: updatedData,
        [constants.SELECTED_BC_DATA]: selectedData,
      }));
    })
    .catch((error) => {
      const errorMsg = get(error, 'response.data.message', i18n.t('ERROR_BRANCH_BRANCH_CONNECTORS_GROUP_ERROR'));
      dispatch(boundLoading({
        loading: false,
        [constants.MODAL_LOADING]: false,
      }));
      dispatch(boundError(errorMsg));
      dispatch(notifyError(errorMsg));
    });
};

export const toggleSortBy = (sortBy) => async (dispatch, getState) => {
  await dispatch(boundLoading({ loading: true }));
  const state = await getState();
  const originalData = await branchConnectorSelector.dataTableSelector(state);
  const sortField = await branchConnectorSelector.sortFieldSelector(state);
  const sortDirection = await branchConnectorSelector.sortDirectionSelector(state);

  const direction = () => {
    if (sortField !== sortBy) return 'asc';
    return sortDirection === 'desc' ? 'asc' : 'desc';
  };

  const sortData = sortBranchConnectorGroup(originalData, sortBy, direction());
  dispatch(dataChanged({
    loading: false,
    [constants.DATA_TABLE]: sortData,
    [constants.SORT_FIELD]: sortBy,
    [constants.SORT_DIRECTION]: direction(),
  }));
};

export const loadVMHealthData = (formData) => async (dispatch) => {
  dispatch(boundLoading({ loading: false }));
  const vmApi = genericInterface(constants.VM_HEALTH_API(formData.id));

  // eslint-disable-next-line consistent-return
  return vmApi.read()
    .then((response) => {
      const { data } = response;
      dispatch(dataChanged({
        loading: false,
        [constants.VM_HEALTH]: data,
      }));
    })
    .catch((error) => {
      const errorMsg = get(error, 'response.data.message', i18n.t('VM_HEALTH_FETCH_API_ERROR'));
      dispatch(boundLoading({ loading: false }));
      dispatch(notifyError(errorMsg));
    });
};

export const loadVMScheduledVersionData = (formData) => async (dispatch) => {
  dispatch(boundLoading({ loading: false }));
  const vmApi = genericInterface(constants.VM_SCHEDULED_VERSION_API(formData.id));
  return vmApi.read()
    .then((response) => {
      const { data } = response;
      dispatch(dataChanged({
        loading: false,
        [constants.SCHEDULED_VERSION]: data || i18n.t('ERROR_NO_SCHEDULED_VERSION_AVAILABLE'),
      }));
    })
    .catch(() => {
      dispatch(dataChanged({
        [constants.SCHEDULED_VERSION]: i18n.t('ERROR_NO_SCHEDULED_VERSION_AVAILABLE'),
      }));
    });
};

export const toggleShowHideChilds = (parentId, originalData) => async (dispatch, getState) => {
  const state = getState();
  let isExpanded;
  const { pageNumber, pages } = state.branchConnectors;
  let groupInfo;
  dispatch(boundLoading({ loading: true, modalLoading: true }));
  const ccGroupApi = genericInterface(constants.API_ENDPOINT(parentId));
  await ccGroupApi.read()
    .then((response) => {
      const { data } = response;
      groupInfo = data;
      dispatch(dataChanged({
        loading: false, modalLoading: false,
      }));
    })
    .catch(() => {
      dispatch(dataChanged({
        loading: false, modalLoading: false,
      }));
    });
  const modifiedData = originalData.map((x) => {
    if (x.id === parentId) {
      isExpanded = !x.expanded;
      return ({ ...x, expanded: isExpanded });
    }
    if (x.parentId === parentId) {
      const { hardwareInfo } = groupInfo?.ecVMs.find((ecVM) => ecVM.id === x.id);
      return ({
        ...x, isVisible: !x.isVisible, expanded: isExpanded, hardwareInfo,
      });
    }
    return x;
  });
  const newPages = pages;
  newPages[pageNumber - 1] = modifiedData;

  return dispatch(dataChanged({
    [constants.DATA_TABLE]: modifiedData,
    [constants.PAGES]: newPages,
  }));
};

export const updateParentNode = (parentId) => (dispatch, getState) => {
  if (!parentId) return null;
  dispatch(dataChanged({
    loading: true,
    [constants.MODAL_LOADING]: true,

  }));
  const state = getState();
  const { pageNumber, pages } = state.branchConnectors;
  const originalData = branchConnectorSelector.dataTableSelector(state);
  const index = originalData.findIndex((x) => x.id === parentId);
  const modifiedData = originalData;
  modifiedData[index].scheduled = !originalData.some((x) => x.parentId === parentId && !x.scheduled);
  const newPages = pages;
  newPages[pageNumber - 1] = modifiedData;

  return dispatch(dataChanged({
    loading: false,
    [constants.DATA_TABLE]: modifiedData,
    [constants.PAGES]: newPages,
  }));
};

export const toggleCheckConnectorGroup = (id, parentId, checkAll, originalData) => async (dispatch, getState) => {
  const state = getState();
  const {
    searchText, pageNumber, pages, checkAll: checkAllList,
  } = state.branchConnectors;
  const selectedItem = originalData.find((x) => x.id === id);

  const parentIsVisible = (myParentId) => {
    const parent = originalData.find((x) => x.id === myParentId);
    return parent && parent.isVisible;
  };

  let modifiedData;
  const newCheckAllList = checkAllList;
  if (id === 'CHECK_ALL') {
    modifiedData = originalData.map((x) => ({
      ...x,
      scheduled: (x.isVisible || parentIsVisible(x.parentId)) || searchText === '' ? !checkAll : x.scheduled,
    }));
    newCheckAllList[pageNumber - 1] = !checkAll;
  } else if (!parentId) {
    modifiedData = originalData.map((x) => {
      if (x.id === id) return ({ ...x, scheduled: !x.scheduled });
      if (x.parentId === id) return ({ ...x, scheduled: !selectedItem.scheduled });
      return x;
    });
  } else {
    modifiedData = originalData.map((x) => {
      if (x.id === id) return ({ ...x, scheduled: !x.scheduled });
      return x;
    });
  }

  const newPages = pages;
  newPages[pageNumber - 1] = modifiedData;

  const bulkUpdate = newPages.flat().slice(0).reduce((acc, cur, i, arr) => {
    if (acc === 2) { arr.splice(1); }
    return (acc + ((cur.scheduled && Boolean(cur.parentId)) ? 1 : 0));
  }, 0);
  
  return dispatch(dataChanged({
    [constants.DATA_TABLE]: modifiedData,
    ...(id === 'CHECK_ALL' && { [constants.CHECK_ALL]: !checkAll }),
    [constants.BULK_UPDATE]: Boolean(bulkUpdate > 1),
    [constants.PAGES]: newPages,
    [constants.CHECK_ALL]: newCheckAllList,
  }));
};

// export const setSearchText = searchText => async (dispatch, getState) => {
//   await dispatch(boundLoading({ loading: true }));
//   const state = await getState();
//   const originalData = await branchConnectorSelector.dataTableSelector(state);
//   const modifiedData = originalData.map((x) => {
//     if (searchText === '') return ({ ...x, isVisible: !x.parentId || x.expanded });
//     if (!searchText
//          || (x.name).toLowerCase().includes(searchText.toLowerCase())
//          || (!isEmpty(x.location)
//          && (x.location.name).toLowerCase().includes(searchText.toLowerCase()))
//     ) return ({ ...x, isVisible: true });
//     return ({ ...x, isVisible: false });
//   });
//   dispatch(dataChanged({
//     loading: false,
//     [constants.SEARCH_TEXT]: searchText,
//     [constants.DATA_TABLE]: modifiedData,
//   }));
// };

// API SEARCH NOT WORKING
export const handleSearchText = (searchText) => async (dispatch) => {
  await dispatch(dataChanged({
    loading: false,
    [constants.SEARCH_TEXT]: searchText,
  }));
  return dispatch(loadBranchConnectorData(true, 1));
};

export const handlePageNumber = (pg = 1) => async (dispatch) => dispatch(
  loadBranchConnectorData(false, pg),
);

export const handlePageSize = (pgSize) => async (dispatch) => {
  await dispatch(dataChanged({
    loading: false,
    [constants.PAGE_SIZE]: pgSize,
  }));
  return dispatch(loadBranchConnectorData(true, 1));
};

export const selectBCData = (index, data) => (dispatch) => {
  dispatch(dataChanged({
    [constants.SELECTED_BC_DATA]: data[index],
  }));
};

export const localData = () => (dispatch) => {
  dispatch(dataChanged({
    loading: false,
  }));
};

export const toggleDeleteForm = (data, toggle) => (dispatch) => {
  dispatch(boundModalLoading({ [constants.MODAL_LOADING]: !toggle }));
  dispatch(dataChanged({
    [constants.SHOW_DELETE_FORM]: toggle,
    [constants.SELECTED_ROW_ID]: data ? data.id : null,
  }));
};

export const toggleDisableForm = (toggle, origin) => async (dispatch, getState) => {
  if (!toggle) {
    dispatch(dataChanged({
      [constants.SHOW_DISABLE_FORM]: toggle,
      [constants.DISABLE_TYPE]: '',
    }));
    return;
  }
  if (origin) {
    dispatch(dataChanged({
      [constants.SHOW_DISABLE_FORM]: toggle,
      [constants.DISABLE_TYPE]: origin,
    }));
    return;
  }

  const state = await getState();
  const { pages } = await branchConnectorSelector.baseSelector(state);
  const originalData = pages.flat();
  const childIds = originalData.filter((x) => x.parentId && x.scheduled);
  const parentIds = originalData.filter((x) => !x.parentId && x.scheduled);
  const isOneVm = childIds.length === 1 && parentIds.length === 0;
  const isOneGroup = (
    childIds.length >= 1 && parentIds.length === 1
    && childIds.every((x) => x.parentId === parentIds[0].id)
  );
  const isSelected = !isOneVm && !isOneGroup;

  const type = () => {
    if (isOneVm) return 'ONE_CC';
    if (isOneGroup) return 'ONE_CC_GROUP';
    if (isSelected) return 'SELECTED_CC';
    return '';
  };

  dispatch(dataChanged({
    [constants.SHOW_DISABLE_FORM]: toggle,
    [constants.DISABLE_TYPE]: { id: type(), rows: childIds },
  }));
};

export const toggleEnableForm = (toggle, origin) => async (dispatch, getState) => {
  if (!toggle) {
    dispatch(dataChanged({
      [constants.SHOW_ENABLE_FORM]: toggle,
      [constants.ENABLE_TYPE]: '',
    }));
    return;
  }
  if (origin) {
    dispatch(dataChanged({
      [constants.SHOW_ENABLE_FORM]: toggle,
      [constants.ENABLE_TYPE]: origin,
    }));
    return;
  }

  const state = await getState();
  const { pages } = await branchConnectorSelector.baseSelector(state);
  const originalData = pages.flat();
  const childIds = originalData.filter((x) => x.parentId && x.scheduled);
  const parentIds = originalData.filter((x) => !x.parentId && x.scheduled);
  const isOneVm = childIds.length === 1 && parentIds.length === 0;
  const isOneGroup = (
    childIds.length >= 1 && parentIds.length === 1
    && childIds.every((x) => x.parentId === parentIds[0].id)
  );
  const isSelected = !isOneVm && !isOneGroup;

  const type = () => {
    if (isOneVm) return 'ONE_CC';
    if (isOneGroup) return 'ONE_CC_GROUP';
    if (isSelected) return 'SELECTED_CC';
    return '';
  };

  dispatch(dataChanged({
    [constants.SHOW_ENABLE_FORM]: toggle,
    [constants.ENABLE_TYPE]: { id: type(), rows: childIds },
  }));
};

export const deleteBCGroupData = (rowData, subitems = []) => (dispatch) => {
  dispatch(boundModalLoading({ [constants.MODAL_LOADING]: true }));
  const rowId = rowData;
  const { id, parentId } = subitems[0];
  let deleteCCAPI;
  if (parentId) {
    deleteCCAPI = genericInterface(constants.API_DELETE_CC_ENDPOINT(parentId, id));
  } else {
    deleteCCAPI = genericInterface(constants.API_DELETE_ENDPOINT(rowId));
  }

  return deleteCCAPI.del()
    .then(() => {
      dispatch(toggleDeleteForm(false));
      dispatch(destroy('deleteConfirmationForm'));
      dispatch(dataChanged({ [constants.MODAL_LOADING]: false }));
      dispatch(notify(i18n.t('SUCCESSFULLY_DELETED')));
      dispatch(checkActivation());
      dispatch(loadBranchConnectorData(true, 1));
    })
    .catch((error) => {
      const errorMsg = get(error, 'response.data.message', i18n.t('ERROR_BRANCH_BRANCH_CONNECTORS_GROUP_ERROR'));
      dispatch(boundLoading({
        loading: false,
        [constants.MODAL_LOADING]: false,
      }));
      dispatch(boundError(errorMsg));
      dispatch(notifyError(errorMsg));
    });
};

export const toggleViewModal = (type, toggle) => async (dispatch) => {
  await dispatch(dataChanged({
    [constants.FORM_TYPE]: type,
    [constants.SHOW_FORM]: toggle,
    [constants.MODAL_LOADING]: true,
  }));
  if (type && type.id) {
    // invoke health api
    if (type.parentId) {
      dispatch(loadVMHealthData(type));
      dispatch(loadVMScheduledVersionData(type));
    }
    await dispatch(loadBCData(type.id, type));
  }
  dispatch(dataChanged({ [constants.MODAL_LOADING]: false }));
};

// To verify
export const loadBranchConnectorDataStatic = () => (dispatch) => {
  return dispatch(boundSuccess({
    ...[constants.DATA_TABLE],
    [constants.DATA_LOADING]: false,
  }));
};

export const keepMinimumTimeInterval = (base, value) => (dispatch, getState) => {
  const state = getState();
  const formData = branchConnectorSelector.formDataValuesSelector(state);

  const scheduleData = { ...formData, ...value };
  if (isEmpty(scheduleData.hhFrom) || isEmpty(scheduleData.mmFrom)) return;
  const {
    hhFrom: { id: hhFrom },
    mmFrom: { id: mmFrom },
    periodFrom: { id: periodFrom },
    offset,
  } = scheduleData;
  const hhTo = hhFrom;
  const mmTo = mmFrom;
  const periodTo = periodFrom;
  const adjustedTime = adjustHoursToMeetInterval(hhFrom, mmFrom, periodFrom, hhTo, mmTo, periodTo, base, 120);

  if (isEmpty(adjustedTime)) return;
  const { hh, mm, period } = adjustedTime;
  const weekdayValue = (base === 'WEEKDAY') ? value : formData;
  const { weekday } = weekdayValue;

  const startTime = calculateNextUpdate(weekday, hhFrom, mmFrom, periodFrom, offset);

  dispatch(change(form, 'nextUpdate', startTime));
    
  if (base === 'FROM') {
    dispatch(change(form, 'hhTo', { id: hh, name: hh }));
    dispatch(change(form, 'mmTo', { id: mm, name: mm }));
    dispatch(change(form, 'periodTo', { id: period, name: period }));
  }
  if (base === 'TO') {
    dispatch(change(form, 'hhFrom', { id: hh, name: hh }));
    dispatch(change(form, 'mmFrom', { id: mm, name: mm }));
    dispatch(change(form, 'periodFrom', { id: period, name: period }));
  }
};

export const updateGroupTunnel = (rowData) => async (dispatch, getState) => {
  const state = getState();
  const { id: rowId, parentId: rowParentId } = rowData;
  const formData = branchConnectorSelector.formDataValuesSelector(state);
  const { zpaUserTunnel, maxUserTunnelsPerConnector } = formData;

  try {
    if (!rowParentId && rowId) {
      const saveGroupTunnelAPI = genericInterface(constants.API_CC_GROUP_TUNNEL(rowId));
      const apiEncryptionEndPoint = saveGroupTunnelAPI.update;
      await apiEncryptionEndPoint({
        enableTunnel: zpaUserTunnel === 'ENABLE',
        maxUserTunnelsPerConnector: trinary(zpaUserTunnel === 'ENABLE', maxUserTunnelsPerConnector, 0),
      }, {});
    }
  } catch (error) {
    const errorMsg = get(error, 'response.data.message', i18n.t('ERROR_BRANCH_CLOUD_CONNECTORS_GROUP_ERROR'));
    dispatch(boundModalLoading({
      loading: false,
      [constants.MODAL_LOADING]: false,
      [constants.BULK_UPDATE]: false,
      [constants.SHOW_FORM]: false,
      [constants.CHECK_ALL]: [false],
    }));
    dispatch(notifyError(errorMsg));
  }
};

export const saveSchedule = (rowData) => async (dispatch, getState) => {
  dispatch(boundModalLoading({ [constants.MODAL_LOADING]: true }));
  let saveEncrptionOnly = false;

  const state = getState();
  const scheduleData = branchConnectorSelector.formDataValuesSelector(state);
  const { zpaUserTunnel, maxUserTunnelsPerConnector } = scheduleData;
  const {
    loadedBCData, pages, formType, checkAll, pageNumber,
  } = branchConnectorSelector.baseSelector(state);
  const { id: rowId, parentId: rowParentId } = rowData;

  if (isEmpty(scheduleData?.hhFrom) || isEmpty(scheduleData?.mmFrom)) saveEncrptionOnly = true;

  const {
    hhFrom: { id: hhFrom } = { id: '' },
    mmFrom: { id: mmFrom } = { id: '' },
    periodFrom: { id: periodFrom } = {},
    hhTo: { id: hhTo } = { id: '' },
    mmTo: { id: mmTo } = { id: '' },
    periodTo: { id: periodTo } = {},
    weekday,
    ziaTunnelMode,
    fallBackToTls,
    dnsCache,
    failOpen,
  } = scheduleData;

  const startTime = convertAMPMtoMinutes(hhFrom, mmFrom, periodFrom) * 60;
  const endTime = convertAMPMtoMinutes(hhTo, mmTo, periodTo) * 60;

  const accessSubscriptions = loginSelectors.accessSubscriptionSelector(state);
  const hasBCEncryptSubscription = hasBCEncryptSku(accessSubscriptions);

  let vmGroupIds;
  let vmIds;
  if (rowId) {
    // Save one ROW
    vmGroupIds = !rowParentId ? [rowId] : [];
    vmIds = rowParentId ? [rowId] : [];
  } else {
    // Save a Bulk Update
    vmGroupIds = pages.flat()
      .filter((x, idx, array) => !x?.parentId
        // Must have one child
        && ((array.some((item) => x?.id === item?.parentId)
        // All the childs must be marked
        && (array.filter((item) => x?.id === item?.parentId).every((item) => item?.scheduled)))
        // or Must have no child
        || (!array.some((item) => x?.id === item?.parentId)
        // Must be marked
        && x.scheduled)))
      .map(({ id }) => id);
    vmIds = pages.flat()
      .filter((x) => x?.parentId && x?.scheduled && !vmGroupIds.some((id) => id === x?.parentId))
      .map(({ id }) => id);
  }

  const updatedValues = {
    ...(vmGroupIds.length > 0 && { vmGroupIds }),
    ...(vmIds.length > 0 && { vmIds }),
    dayOfWeek: weekday && weekday.val,
    startTime,
    endTime,
  };

  try {
    if (dnsCache) {
      const updatedValuesDnsCache = {
        ...(vmGroupIds.length > 0 && { vmGroupIds }),
        ...(vmIds.length > 0 && { vmIds }),
      };
    
      if (formType === 'BULK_CHANGE_DNS_CACHE') {
        if (checkAll[pageNumber - 1]) {
          const deployType = window.location.pathname.includes('physical') ? 'ONPREM_PHYSICAL' : 'ONPREM';
          const updateDnsCacheAPI = genericInterface(
            constants.VM_DNS_CACHE_ALL_API(dnsCache?.id?.toUpperCase(), deployType),
          );
          const apiDnsCacheEndPoint = updateDnsCacheAPI.update;
          await apiDnsCacheEndPoint();
        } else {
          const updateDnsCacheAPI = genericInterface(
            constants.VM_DNS_CACHE_API(dnsCache?.id?.toUpperCase()),
          );
          const apiDnsCacheEndPoint = updateDnsCacheAPI.update;
          await apiDnsCacheEndPoint(updatedValuesDnsCache, {});
        }

        dispatch(destroy('BranchConnectorViewForm'));
        dispatch(boundModalLoading({
          loading: false,
          [constants.MODAL_LOADING]: false,
          [constants.BULK_UPDATE]: false,
          [constants.SHOW_FORM]: false,
          [constants.CHECK_ALL]: [false],
        }));
      }

      const updateDnsCacheAPI = genericInterface(
        constants.VM_DNS_CACHE_API(dnsCache?.id?.toUpperCase()),
      );
      const apiDnsCacheEndPoint = updateDnsCacheAPI.update;
      await apiDnsCacheEndPoint(updatedValuesDnsCache, {});
    }
  } catch (error) {
    const errorMsg = get(error, 'response.data.message', i18n.t('ERROR_BRANCH_BRANCH_CONNECTORS_GROUP_ERROR'));
    dispatch(boundLoading({
      loading: false,
      [constants.MODAL_LOADING]: false,
    }));
    return dispatch(notifyError(errorMsg));
  }

  const local = localStorage.getItem('configData');
  const configData = (local && local.length) ? JSON.parse(local) : {};
  const enableFailOpen = verifyConfigData({ configData, key: 'enableFailOpen' });
  try {
    if (!!failOpen && (rowParentId || formType === 'BULK_CHANGE_FAIL_OPEN') && enableFailOpen) {
      const updatedValuesFailOpen = {
        ...(vmGroupIds.length > 0 && { vmGroupIds }),
        ...(vmIds.length > 0 && { vmIds }),
      };
    
      if (formType === 'BULK_CHANGE_FAIL_OPEN') {
        if (checkAll[pageNumber - 1]) {
          const deployType = window.location.pathname.includes('physical') ? 'ONPREM_PHYSICAL' : 'ONPREM';
          const updateFailOpenAPI = genericInterface(
            constants.VM_FAIL_OPEN_ALL_API(failOpen?.id?.toUpperCase(), deployType),
          );
          const apiFailOpenEndPoint = updateFailOpenAPI.update;
          await apiFailOpenEndPoint();
        } else {
          const updateFailOpenAPI = genericInterface(
            constants.VM_FAIL_OPEN_API(failOpen?.id?.toUpperCase()),
          );
          const apiFailOpenEndPoint = updateFailOpenAPI.update;
          await apiFailOpenEndPoint(updatedValuesFailOpen, {});
        }

        dispatch(destroy('BranchConnectorViewForm'));
        dispatch(boundModalLoading({
          loading: false,
          [constants.MODAL_LOADING]: false,
          [constants.BULK_UPDATE]: false,
          [constants.SHOW_FORM]: false,
          [constants.CHECK_ALL]: [false],
        }));
      }

      const updateFailOpenAPI = genericInterface(
        constants.VM_FAIL_OPEN_API(failOpen?.id?.toUpperCase()),
      );
      const apiFailOpenEndPoint = updateFailOpenAPI.update;
      await apiFailOpenEndPoint(updatedValuesFailOpen, {});
    }
  } catch (error) {
    const errorMsg = get(error, 'response.data.message', i18n.t('ERROR_BRANCH_BRANCH_CONNECTORS_GROUP_ERROR'));
    dispatch(boundLoading({
      loading: false,
      [constants.MODAL_LOADING]: false,
    }));
    return dispatch(notifyError(errorMsg));
  }

  try {
    if (hasBCEncryptSubscription && rowId && !rowParentId) {
      const updatedEncryptionValues = {
        ...(vmGroupIds.length === 1 && {
          ...loadedBCData,
          tunnelMode: ziaTunnelMode,
          fallBackToTls: fallBackToTls === 'ENABLE' && ziaTunnelMode !== 'TLS',
          enableTunnel: zpaUserTunnel === 'ENABLE',
          maxUserTunnelsPerConnector: trinary(zpaUserTunnel === 'ENABLE', maxUserTunnelsPerConnector, 0),
        }),
      };
      const saveEncryptionAPI = genericInterface(constants.API_ENCRYPTION_ENDPOINT);
      const apiEncryptionEndPoint = saveEncryptionAPI.update;
      await apiEncryptionEndPoint(updatedEncryptionValues, {});
      if (saveEncrptionOnly) {
        dispatch(boundModalLoading({
          loading: false,
          [constants.MODAL_LOADING]: false,
          [constants.BULK_UPDATE]: false,
          [constants.SHOW_FORM]: false,
          [constants.CHECK_ALL]: [false],
        }));
      }
    }
  } catch (error) {
    const errorMsg = get(error, 'response.data.message', i18n.t('ERROR_BRANCH_BRANCH_CONNECTORS_GROUP_ERROR'));
    dispatch(boundModalLoading({
      loading: false,
      [constants.MODAL_LOADING]: false,
      [constants.BULK_UPDATE]: false,
      [constants.SHOW_FORM]: false,
      [constants.CHECK_ALL]: [false],
    }));
    dispatch(notifyError(errorMsg));
  }

  if (updatedValues.dayOfWeek) {
    const saveScheduleAPI = genericInterface(constants.API_SCHEDULE_ENDPOINT);
    const apiEndPoint = saveScheduleAPI.update;
  
    apiEndPoint(updatedValues, {})
      .then(() => {
        dispatch(toggleDeleteForm(false));
        dispatch(dataChanged({
          loading: false,
          [constants.MODAL_LOADING]: false,
          [constants.BULK_UPDATE]: false,
          [constants.SHOW_FORM]: false,
          [constants.CHECK_ALL]: [false],
        }));
      })
      .catch((error) => {
        const errorMsg = get(error, 'response.data.message', i18n.t('ERROR_BRANCH_BRANCH_CONNECTORS_GROUP_ERROR'));
        dispatch(dataChanged({
          loading: false,
          [constants.MODAL_LOADING]: false,
          [constants.BULK_UPDATE]: false,
          [constants.SHOW_FORM]: false,
          [constants.CHECK_ALL]: [false],
        }));
        dispatch(boundError(errorMsg));
        dispatch(notifyError(errorMsg));
      });
  }
  dispatch(updateGroupTunnel(rowData));
  dispatch(toggleDeleteForm(false));
  dispatch(destroy('BranchConnectorViewForm'));
  dispatch(notify(i18n.t('SUCCESSFULLY_SAVED')));
  dispatch(checkActivation());
  dispatch(loadBranchConnectorData(true, 1));
};

export const updateOperationalStatus = (status, parentId) => async (dispatch, getState) => {
  dispatch(dataChanged({
    [constants.SHOW_DISABLE_FORM]: false,
    [constants.SHOW_DISABLE_FORM]: false,
    [constants.MODAL_LOADING]: true,
  }));
  const state = getState();
  const disableEnableType = status === 'DISABLE'
    ? branchConnectorSelector.disableTypeSelector(state)
    : branchConnectorSelector.enableTypeSelector(state);
  const { rows = [] } = disableEnableType || {};

  return Promise.all(rows.map(async (row) => {
    const apiEndPoint = genericInterface(
      constants.VM_STATUS_UPDATE_API(row.parentId || parentId, row.id, status),
    );
    
    try {
      await apiEndPoint.update(null, {});

      return false;
    } catch (error) {
      const errorMsg = get(error, 'response.data.message', i18n.t('ERROR_OPERATIONAL_STATUS_SAVE_ERROR'));
      dispatch(notifyError(errorMsg));
      dispatch(dataChanged({ [constants.MODAL_LOADING]: false }));

      return true;
    }
  }));
};

export const handleDisableEnable = (status) => async (dispatch, getState) => {
  const state = getState();
  const formType = branchConnectorSelector.formTypeSelector(state);
  const { id, parentId } = formType;
  const hasError = await dispatch(updateOperationalStatus(status, parentId || id));

  if (id && hasError.every((error) => !error)) dispatch(saveSchedule({ id, parentId }));
  if (status === 'DISABLE') dispatch(toggleDisableForm(null, false));
  if (status === 'ENABLE') dispatch(toggleEnableForm(null, false));

  dispatch(dataChanged({
    [constants.MODAL_LOADING]: false,
    [constants.BULK_UPDATE]: false,
  }));
  dispatch(checkActivation());
  dispatch(loadBranchConnectorData(true, 1));
};

export const resetConnectorGroups = () => (dispatch) => dispatch(dataChanged(constants.DEFAULTS));

export const tabConfiguration = (accessDetailSubscriptions) => {
  const tab = [];

  const bcDevice400Licenses = getSubscriptionLicenses(accessDetailSubscriptions, 'BC_DEVICE_CONNECTOR_400');
  const bcDevice600Licenses = getSubscriptionLicenses(accessDetailSubscriptions, 'BC_DEVICE_CONNECTOR_600');
  const bcDevice800Licenses = getSubscriptionLicenses(accessDetailSubscriptions, 'BC_DEVICE_CONNECTOR_800');
  const hasAtLeastOneSubscrption = bcDevice400Licenses > 0
                              || bcDevice600Licenses > 0
                              || bcDevice800Licenses > 0;

  if (hasAtLeastOneSubscrption) {
    tab.push({
      value: `${BASE_LAYOUT}/administration/branch-devices/physical`,
      title: 'PHYSICAL',
      visible: true,
      to: `${BASE_LAYOUT}/administration/branch-devices/physical`,
      handleClick: (str) => str,
    });
  }
  tab.push({
    value: `${BASE_LAYOUT}/administration/branch-devices/virtual`,
    title: 'VIRTUAL',
    visible: true,
    to: `${BASE_LAYOUT}/administration/branch-devices/virtual`,
    handleClick: (str) => str,
  });

  return tab;
};

export const handlePagetype = (value) => async (dispatch) => dispatch(change('branchConnectorsPage', 'pageTabs', value));
