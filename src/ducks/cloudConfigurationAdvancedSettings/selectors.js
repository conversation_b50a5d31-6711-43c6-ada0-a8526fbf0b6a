import { createSelector } from 'reselect';
import { getFormValues, getFormSyncErrors, getFormMeta } from 'redux-form';

import * as constants from './constants';

export const baseSelector = (state) => state[constants.REDUCER_KEY];

export const accountId = createSelector(
  baseSelector,
  (state) => state[constants.ACCOUNT_ID],
);

export const subscriptionId = createSelector(
  baseSelector,
  (state) => state[constants.SUBSCRIPTION_ID],
);

export const projectId = createSelector(
  baseSelector,
  (state) => state[constants.PROJECT_ID],
);

export const formValuesSelector = (state) => getFormValues('cloudConfigurationAdvancedSettingsForm')(state);
export const formMetaSelector = (state) => getFormMeta('cloudConfigurationAdvancedSettingsForm')(state);
export const formSyncErrorsSelector = (state) => getFormSyncErrors('cloudConfigurationAdvancedSettingsForm')(state);

export const formDataSelector = createSelector(
  baseSelector,
  formValuesSelector,
  (data, formValues) => formValues || data,
);

// alway return top level selector
export default baseSelector;
