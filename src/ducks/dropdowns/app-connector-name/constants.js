import dropdown from 'ducks/generics/dropdown';
import { BASE_API_PATH } from 'config';

export const REDUCER_KEY = 'appConnectorNameDropdown';
export const API_ENDPOINT = (groupName) => `${BASE_API_PATH}/v1/zpaResources/appConnectorGroupProvKeys/${groupName}`;

export const DROPDOWN = dropdown.createStateConstants();
export const DROPDOWN_DEFAULT_STATE = dropdown.createDefaultState();
export const STATIC_DATA = 'staticData';
export const DATA = 'data';

export const DEFAULT_STATE = {
  ...DROPDOWN_DEFAULT_STATE,
  pageSize: 100,
  loading: false,
};

export const DUMMY_STORE_DATA = {
  [REDUCER_KEY]: {
    ...DEFAULT_STATE,
  },
};
