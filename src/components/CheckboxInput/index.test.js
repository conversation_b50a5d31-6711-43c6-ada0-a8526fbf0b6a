import { render } from '@testing-library/react';
import React from 'react';
import CheckboxInput from './index';

describe('<CheckboxInput/> Component test', () => {
  it('<CheckboxInput> Renders', () => {
    const mockFn = jest.fn();
    const props = {
      label: 'test_label',
      id: 'test_id',
      className: 'className',
      input: {
        value: 'test_value',
        name: 'test_input',
        checked: true,
        onChange: mockFn,
      },
    };
    const checkboxInput = expect(render(<CheckboxInput {...props} />));
    expect(checkboxInput).toBe.ok;
  });
});
