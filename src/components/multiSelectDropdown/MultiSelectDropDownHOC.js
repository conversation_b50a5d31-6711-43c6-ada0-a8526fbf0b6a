import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import { isEmpty } from 'utils/lodash';
import labelHelper from '../../commonConnectedComponents/dropdown/labelHelper';

export default function MultiSelectDropDownHOC(props) {
  const {
    Component,
    selector,
    actions,
  } = props;

  /* eslint-disable react/no-this-in-sfc */
  class ConnectWrapper extends React.Component {
    componentDidMount() {
      const {
        input,
        meta,
        selectedValues,
        actions: { setSelectedValues, setDefaultValues },
      } = this.props;

      if (!input) {
        return;
      }

      // changed values from form
      if (input.value && !meta.touched && !this.settingDefaultValue) {
        this.settingDefaultValue = true;
        setDefaultValues(input.value).then((values) => {
          this.settingDefaultValue = false;
          if (input && input.onChange) {
            input.onChange(values);
          }
        });
      }

      // reset selected values from form
      if (!input.value && selectedValues.length && !this.settingDefaultValue) {
        setSelectedValues([]);
      }
    }

    shouldComponentUpdate(nextProps) {
      const {
        input,
        selectedValues,
        actions: { setSelectedValues, setDefaultValues },
      } = this.props;

      if (!input) {
        return true;
      }

      // changed values from form
      if ((isEmpty(selectedValues) || isEmpty(input.value)) && !isEmpty(nextProps.input.value)
        && !nextProps.meta.touched && !this.settingDefaultValue) {
        this.settingDefaultValue = true;
        setDefaultValues(nextProps.input.value).then((values) => {
          this.settingDefaultValue = false;
          if (input && input.onChange) {
            input.onChange(values);
          }
        });
      }

      // reset selected values from form
      if (!nextProps.input.value && nextProps.selectedValues.length
        && selectedValues.length && !nextProps.isOpen && !this.settingDefaultValue) {
        setSelectedValues([]);
      }

      return true;
    }
    
    settingDefaultValue;

    onChange = (selectedValues) => {
      const {
        input,
        actions: { setSelectedValues },
      } = this.props;

      if (input && input.onChange) {
        input.onChange(selectedValues);
      }

      setSelectedValues(selectedValues);
    };

    cancel = () => {
      const {
        input,
        actions: { cancel },
      } = this.props;

      if (input && input.onBlur) {
        input.onBlur();
      }
      cancel();
    };

    removeValue = (value) => {
      const {
        input,
        selectedValues,
        actions: { removeValue },
      } = this.props;

      if (input && input.onChange) {
        input.onBlur();
        input.onChange(selectedValues.filter((item) => item.id !== value.id));
      }

      removeValue(value);
    };

    renderError = (errorMessage) => {
      const { t } = this.props;

      return (
        <div className="multi-select-drop-down-error-container">
          <p>{t(errorMessage)}</p>
        </div>
      );
    };

    render() {
      const { actions: _actions, meta } = this.props;
      const hasError = !meta.active && meta.touched && !!meta.error;
      const error = meta && meta.error;

      // eslint-disable-next-line no-underscore-dangle
      const _props = {
        ...this.props,
        actions: {
          ..._actions,
          removeValue: this.removeValue,
          setSelectedValues: this.onChange,
          cancel: this.cancel,
        },
      };

      return (
        <div
          className={`${hasError ? 'multi-select-drop-down-container error' : 'multi-select-drop-down-container'}`}>
          <Component {..._props} />
          {hasError ? (this.renderError(error)) : null}
        </div>
      );
    }
  }

  ConnectWrapper.propTypes = {
    id: PropTypes.string.isRequired,
    selectedValues: PropTypes.arrayOf(PropTypes.shape({})),
    input: PropTypes.shape({
      onChange: PropTypes.func,
      onBlur: PropTypes.func,
      value: PropTypes.any,
    }),
    isOpen: PropTypes.bool,
    actions: PropTypes.shape({
      setSelectedValues: PropTypes.func,
      setDefaultValues: PropTypes.func,
      removeValue: PropTypes.func,
      cancel: PropTypes.func,
    }).isRequired,
    t: PropTypes.func,
    meta: PropTypes.shape({
      active: PropTypes.bool,
      dirty: PropTypes.bool,
      error: PropTypes.string,
      touched: PropTypes.bool,
    }),
  };

  ConnectWrapper.defaultProps = {
    selectedValues: [],
    input: null,
    isOpen: null,
    t: (s) => s,
    meta: {},
  };

  const mapDispatchToProps = (dispatch) => {
    // eslint-disable-next-line no-underscore-dangle
    const _actions = bindActionCreators(actions, dispatch);

    return {
      actions: _actions,
    };
  };

  const mapStateToProps = (state, ownProps) => {
    const { t, disabledTooltip, label } = ownProps;
    const dropdownState = selector(state).dropdown;
    const { selectedValues, values } = dropdownState;
    const dropdownLabels = labelHelper(t, selectedValues, values, disabledTooltip, label);
    return {
      selectedValues: selector(state).dropdown.selectedValues,
      values: selector(state).dropdown.values,
      ...dropdownLabels,
      label: dropdownLabels.defaultDisplayLabel,
      isOpen: selector(state).dropdown.open,
      data: selector(state).dropdown.options,
      loading: selector(state).dropdown.loading,
      hasMorePages: selector(state).dropdown.hasMorePages,
      pageNum: selector(state).dropdown.pageNum,
      searchString: selector(state).dropdown.searchString,
      error: selector(state).dropdown.error,
    };
  };

  const ConnectedWrapper = connect(
    mapStateToProps,
    mapDispatchToProps,
  )(ConnectWrapper);

  return withTranslation()(ConnectedWrapper);
}

MultiSelectDropDownHOC.propTypes = {
  id: PropTypes.string.isRequired,
  dataSrc: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.array,
  ]).isRequired,
  Component: PropTypes.func.isRequired,
  actions: PropTypes.shape({}).isRequired,
  selector: PropTypes.func.isRequired,
  t: PropTypes.func,
};

MultiSelectDropDownHOC.defaultProps = {
  t: (s) => s,
};
