// @flow
import React from 'react';
import PropTypes from 'prop-types';
import { AreaChart, Area } from 'recharts';

function ScoreChart(props) {
  const { color, data } = props;

  return (
    <AreaChart
      width={70}
      height={40}
      data={data}>
      <Area type="monotone" dataKey="value" stroke={color} fill={color} fillOpacity=".3" isAnimationActive={false} />
    </AreaChart>
  );
}

ScoreChart.propTypes = {
  color: PropTypes.string,
  data: PropTypes.arrayOf(PropTypes.object),
};

ScoreChart.defaultProps = {
  color: null,
  data: null,
};

export default ScoreChart;
