import React, { useState, Fragment } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { isEmpty } from 'utils/lodash';
import { useTranslation } from 'react-i18next';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCaretDown } from '@fortawesome/pro-solid-svg-icons/faCaretDown';
import { faCaretUp } from '@fortawesome/pro-solid-svg-icons/faCaretUp';
import DropdownPanel from './DropdownPanel';
import DateDropdown from './DateDropdown';
import './index.scss';

const formatDay = 'MM/DD/YYYY';
const formatHour = 'HH:mm:ss';

function Dropdown(props) {
  const {
    id,
    label,
    options,
    value,
    isMulti,
    disabled,
    placeholder,
    isSearchable,
    isRadio,
    loading,
    className,
    onChange,
    error,
    labelClassName,
    width,
    maxWidth,
    designType,
    defaultSelected,
    triggerChangeOnSelect,
  } = props;
  const { t } = useTranslation();

  const [dropdownVisible, setDropdownVisible] = useState(false);
  const [values, setValues] = useState(value);

  const getDropdownValue = (selected = []) => {
    if (selected.length === 1) {
      return options.length !== selected.length || !isMulti
        ? selected[0].label
        : 'All Selected';
    }
    if (selected.length > 1) {
      return options.length === selected.length
        ? 'All Selected'
        : selected.map((obj) => obj.label).join(', ');
    }
    return placeholder;
  };

  // eslint-disable-next-line no-unused-vars
  const getFilterDropdownValue = (selected = []) => {
    if (selected.length === 0) {
      return ' Select';
    }
    if (selected.length === 1) {
      return (
        <span className="row" style={{ marginLeft: '4px' }}>
          {options.length !== selected.length || !isMulti ? (
            <span className="row">{selected[0].label}</span>
          ) : (
            ' All'
          )}
        </span>
      );
    }
    
    return options.length === selected.length ? (
      ' All'
    ) : (
      <>
        <span style={{ marginLeft: '4px' }}>
          {selected.map((obj) => obj.label)[0]}
        </span>
        <span>{`, ... +${selected.length - 1}`}</span>
      </>
    );
  };

  const handleValuesChange = (val) => {
    setValues(val);
  };

  const handleDateValuesChange = (val) => {
    setValues(val);
    onChange(val);
  };

  const triggerChange = (val) => {
    onChange(
      val.map((obj) => {
        return { label: obj.label, value: obj.value };
      }),
    );
  };

  const handleDropdownHide = (val) => {
    setDropdownVisible(false);
    triggerChange(val);
  };

  const handleDropdownHideDate = () => {
    setDropdownVisible(false);
  };

  const handleDropdownClick = (e) => {
    // eslint-disable-next-line no-unused-expressions
    disabled ? e.preventDefault() : setDropdownVisible(!dropdownVisible);
  };

  const renderDropdownPanel = () => {
    return (
      <DropdownPanel
        width={width}
        maxWidth={maxWidth}
        isMulti={isMulti}
        hideDropdown={(val) => handleDropdownHide(val)}
        triggerChange={triggerChange}
        triggerChangeOnSelect={triggerChangeOnSelect}
        value={value}
        isRadio={isRadio}
        showSearch={isSearchable}
        designType={designType}
        options={options}
        loading={loading}
        disabled={disabled}
        defaultSelected={defaultSelected}
        handleChange={handleValuesChange} />
    );
  };

  const renderDatePanel = () => {
    return (
      <DateDropdown
        width={width}
        maxWidth={maxWidth}
        isMulti={isMulti}
        hideDropdown={(val) => handleDropdownHideDate(val)}
        // triggerChange={triggerChange}
        // triggerChangeOnSelect={triggerChangeOnSelect}
        value={value}
        isRadio={isRadio}
        showSearch={isSearchable}
        designType={designType}
        options={options}
        loading={loading}
        disabled={disabled}
        defaultSelected={defaultSelected}
        handleChange={handleDateValuesChange} />
    );
  };

  const renderOptionsLabels = () => {
    return (
      <>
        {values.length > 0 ? (
          // eslint-disable-next-line no-use-before-define
          ` = ${valueText}`
        ) : (
          <>
            &nbsp;&nbsp;
            <FontAwesomeIcon
              icon={dropdownVisible ? faCaretUp : faCaretDown}
              style={{ cursor: 'pointer' }} />
          </>
        )}
      </>
    );
  };

  const renderOptionsLabelsDate = () => {
    if (isEmpty(values)) {
      return (
        <>
          &nbsp;&nbsp;
          <FontAwesomeIcon
            icon={dropdownVisible ? faCaretUp : faCaretDown}
            style={{ cursor: 'pointer' }} />
        </>
      );
    }
    const { startDate: start, endDate: end, label: labelDate } = values || {};
    // const isMinuteLabel = labelDate.includes('LAST');
    const startDate = start ? moment(start).format(formatDay) : moment().startOf('day').format(formatDay);
    const endDate = moment(end).format(formatDay);
    const startHour = start ? moment(start).format(formatHour) : moment().startOf('day').format(formatHour);
    const endHour = moment(end).format(formatHour);

    return (
      startDate === endDate && startHour !== endHour
        ? (
          <Fragment className="display-fle1x">
            <span>
              {`=${t(labelDate)} `}
            </span>
            {/* <div className="startDate">
              {startDate}
              {isMinuteLabel ? ` ${startHour}` : ''}
            </div>
            {' '}
            <div className="endDate">
              {isMinuteLabel ? `- ${endHour}` : ''}
            </div> */}
  
          </Fragment>
        )
        : (
          <Fragment className="display-fl1ex">
            <span>
              {`=${t(labelDate)}: `}
            </span>
            <div className="startDate">{`${startDate} ${startHour === endHour ? startHour : ''}`}</div>
            <span> - </span>
            <div className="endDate">{`${endDate}  ${startHour === endHour ? startHour : ''}`}</div>
          </Fragment>
        )
    );
  };

  const renderNormalDropdown = () => {
    return (
      <div
        className="common-dropdown-wrapper"
        style={{ width: '100%', float: 'left', display: 'contents' }}>
        {label.length > 0 && (
          <p
            className={labelClassName}
            style={{
              marginBottom: '4.8px',
              marginTop: '0px',
              fontSize: '13px',
            }}>
            {label}
          </p>
        )}
        <span style={{ width, display: 'inline-block' }}>
          <button
            id={id}
            type="button"
            style={{ width }}
            className={`${className} common-dropdown ${
              // eslint-disable-next-line no-nested-ternary
              disabled ? 'disabledBorder' : error.isError ? 'errorBorder' : ''
            } ${dropdownVisible ? 'dropdown-opened' : 'dropdown-closed'}`}
            onClick={handleDropdownClick}
            onKeyPress={handleDropdownClick}>
            <span
              className={`dropdown-text-overflow ${
                disabled ? 'text-disabled' : ''
              }`}>
              {getDropdownValue(values)}
            </span>
            <span style={{ color: 'var(--semantic-color-content-interactive-primary-default)', float: 'right' }}>
              <FontAwesomeIcon
                icon={!disabled && (dropdownVisible ? faCaretUp : faCaretDown)} />
            </span>
          </button>
          {dropdownVisible && renderDropdownPanel()}
        </span>
        {error.isError && (
          <p
            style={{
              fontSize: 'x-small',
              color: 'var(--semantic-color-border-status-danger-active)',
              margin: 'auto',
              marginTop: '3.2px',
            }}>
          </p>
        )}
        {error.errorMessage ? error.errorMessage : 'Invalid Selection'}
      </div>
    );
  };

  const renderDateDropdown = () => {
    return (
      <div
        className="common-dropdown-wrapper"
        style={{ width: '100%', float: 'left', display: 'contents' }}>
        <span style={{ display: 'inline-block' }}>
          <button
            id="btn_filter_pill"
            type="button"
            className={`common-pill date-pill ${
              dropdownVisible ? 'pill-selected' : 'default-pill'
            } ${values.length > 0 ? 'pill-with-values' : ''}`}
            onClick={handleDropdownClick}>
            <span
              className={`${dropdownVisible ? 'key-text-selected' : 'key-text'}`}>
              {label}
            </span>
            {renderOptionsLabelsDate()}
          </button>
          {dropdownVisible && renderDatePanel()}
        </span>
      </div>
    );
  };

  const renderFilterDropdown = () => {
    return (
      <div
        className="common-dropdown-wrapper"
        style={{ width: '100%', float: 'left', display: 'contents' }}>
        <span style={{ display: 'inline-block' }}>
          <button
            id={id}
            type="button"
            className={`common-pill ${className} ${
              dropdownVisible ? 'pill-selected' : 'default-pill'
            } ${values.length > 0 ? 'pill-with-values' : ''}`}
            onClick={handleDropdownClick}>
            <span
              className={`${
                dropdownVisible ? 'key-text-selected' : 'key-text'
              }`}>
              {label}
            </span>
            {renderOptionsLabels()}
          </button>
          {dropdownVisible && renderDropdownPanel()}
        </span>
      </div>
    );
  };

  let valueText = '';
  if (Array.isArray(values) && values.length === 1) {
    valueText = `${values[0].label}`;
  } else if (Array.isArray(values) && values.length > 1) {
    valueText = `${values[0].label}, ... +${values.length - 1}`;
  }

  const renderNewFilterDropdown = () => (
    <div
      className="common-dropdown-wrapper"
      style={{ width: '100%', float: 'left', display: 'contents' }}>
      <span style={{ display: 'inline-block' }}>
        <button
          id="btn_filter_pill"
          type="button"
          className={`common-pill ${
            dropdownVisible ? 'pill-selected' : 'default-pill'
          } ${values.length > 0 ? 'pill-with-values' : ''}`}
          onClick={handleDropdownClick}>
          <span
            className={`${dropdownVisible ? 'key-text-selected' : 'key-text'}`}>
            {label}
          </span>
          {renderOptionsLabels()}
        </button>
        {dropdownVisible && renderDropdownPanel()}
      </span>
    </div>
  );

  const renderDesignTypeDropdown = () => {
    if (designType === 'normal') {
      return renderNormalDropdown();
    }
    if (designType === 'newFilter') {
      return renderNewFilterDropdown();
    }
    if (designType === 'date') {
      return renderDateDropdown();
    }
    return renderFilterDropdown();
  };

  return renderDesignTypeDropdown();
}

Dropdown.propTypes = {
  id: PropTypes.string,
  designType: PropTypes.string, // normal, filter
  options: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
      disabled: PropTypes.bool,
    }),
  ).isRequired,
  value: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
    }),
  ).isRequired,
  isMulti: PropTypes.bool,
  isSearchable: PropTypes.bool,
  isRadio: PropTypes.bool,
  loading: PropTypes.bool,
  onChange: PropTypes.func.isRequired,
  placeholder: PropTypes.string,
  className: PropTypes.string,
  labelClassName: PropTypes.string,
  disabled: PropTypes.bool,
  label: PropTypes.string,
  error: PropTypes.shape({
    isError: PropTypes.bool,
    errorMessage: PropTypes.string,
  }),
  width: PropTypes.string,
  maxWidth: PropTypes.string,
  defaultSelected: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
    }),
  ),
  triggerChangeOnSelect: PropTypes.bool,
};

Dropdown.defaultProps = {
  label: '',
  id: 'dropdown',
  className: '',
  labelClassName: '',
  isMulti: true,
  isSearchable: true,
  isRadio: false,
  loading: false,
  placeholder: 'Select',
  disabled: false,
  error: {
    isError: false,
    errorMessage: '',
  },
  width: '224px',
  maxWidth: '400px',
  designType: 'normal',
  defaultSelected: [],
  triggerChangeOnSelect: false,
};

export default Dropdown;
