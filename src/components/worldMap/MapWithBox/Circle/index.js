// @flow
import React from 'react';
import PropTypes from 'prop-types';
import colors from 'scss/colors.scss';

function Circle({
  number, circleColor, cx, cy, x, y,
}) {
  return (
    <>
      <circle
        cx={cx}
        cy={cy}
        r="9"
        fill={circleColor}
        style={{
          pressed: { outline: 0 },
        }} />

      <circle
        cx={cx}
        cy={cy}
        r="11"
        fill={circleColor}
        stroke={circleColor}
        strokeWidth="7"
        strokeOpacity="0.3"
        fillOpacity="0.15"
        style={{
          pressed: { outline: 0 },
        }} />

      <text
        x={x}
        y={y}
        textAnchor="middle"
        fill={colors.white}
        dy="0.325em">
        {number}
      </text>
    </>
  );
}

Circle.propTypes = {
  number: PropTypes.number,
  circleColor: PropTypes.string,
  x: PropTypes.string,
  y: PropTypes.string,
  cx: PropTypes.string,
  cy: PropTypes.string,

};

Circle.defaultProps = {
  number: null,
  circleColor: null,
  x: '0',
  y: '0',
  cx: '0',
  cy: '0',
};

export default Circle;
