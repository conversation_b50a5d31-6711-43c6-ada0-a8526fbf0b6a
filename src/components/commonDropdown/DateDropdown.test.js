import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react';
import { Provider, useDispatch, useSelector } from 'react-redux';
import { createStore } from 'redux';
import { change, getFormValues } from 'redux-form';
import moment from 'moment';
import { LOGS_TIME_FRAME_DATA } from 'config';
import {
  convertStartTime,
  convertEndTime,
} from 'utils/helpers';
import DateDropdown from './DateDropdown';

jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useSelector: jest.fn(),
  useDispatch: jest.fn(),
}));

jest.mock('redux-form', () => ({
  ...jest.requireActual('redux-form'),
  change: jest.fn(),
  getFormValues: jest.fn(),
}));

describe('DateDropdown component', () => {
  const mockDispatch = jest.fn();
  const mockUseSelector = jest.fn();
  const mockGetFormValues = jest.fn();
  const mockChange = jest.fn();

  beforeEach(() => {
    useDispatch.mockImplementation(() => mockDispatch);
    useSelector.mockImplementation(mockUseSelector);
    getFormValues.mockImplementation(mockGetFormValues);
    change.mockImplementation(mockChange);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly', () => {
    const store = createStore(() => ({}));
    const { getByText } = render(
      <Provider store={store}>
        <DateDropdown
          meta={{ form: 'testForm' }}
          id="testId"
          hideDropdown={jest.fn()}
          handleChange={jest.fn()} />
      </Provider>,
    );

    expect(getByText('Last 5 Minutes')).toBeInTheDocument();
    expect(getByText('Custom')).toBeInTheDocument();
  });

  it('calls handleChange with default value on mount', () => {
    const handleChange = jest.fn();
    const store = createStore(() => ({}));
    render(
      <Provider store={store}>
        <DateDropdown
          meta={{ form: 'testForm' }}
          id="testId"
          hideDropdown={jest.fn()}
          handleChange={handleChange} />
      </Provider>,
    );

    const options = LOGS_TIME_FRAME_DATA.map((item) => ({
      ...item,
      value: item.id,
      startDate: moment.unix((convertStartTime(item.id) / 1000)).format(),
      endDate: moment.unix((convertEndTime(item.id) / 1000)).format(),
      label: item.name,
    }));
      
    expect(handleChange).toHaveBeenCalledTimes(1);
    expect(handleChange).toHaveBeenCalledWith(options[8]);
  });

  it('calls onValueChange when dropdown value changes', () => {
    const onValueChange = jest.fn();
    const store = createStore(() => ({}));
    const { getByText } = render(
      <Provider store={store}>
        <DateDropdown
          meta={{ form: 'testForm' }}
          id="testId"
          hideDropdown={jest.fn()}
          handleChange={onValueChange} />
      </Provider>,
    );

    const options = LOGS_TIME_FRAME_DATA.map((item) => ({
      ...item,
      value: item.id,
      startDate: moment.unix((convertStartTime(item.id) / 1000)).format(),
      endDate: moment.unix((convertEndTime(item.id) / 1000)).format(),
      label: item.name,
    }));

    // Always Load Default Value
    expect(onValueChange).toHaveBeenCalledTimes(1);
    // Current Day fails sometimes because the last minute change. Changed to previous day.
    // expect(onValueChange).toHaveBeenCalledWith(options[8]);
    const dropdownOption = getByText('Previous Day');
    fireEvent.click(dropdownOption);

    expect(onValueChange).toHaveBeenCalledTimes(2);
    expect(onValueChange).toHaveBeenCalledWith(options[11]);
  });

  it('calls dispatch with correct action when dropdown value changes', () => {
    const store = createStore(() => ({}));
    const { getByText } = render(
      <Provider store={store}>
        <DateDropdown
          meta={{ form: 'testForm' }}
          id="testId"
          hideDropdown={jest.fn()}
          handleChange={jest.fn()} />
      </Provider>,
    );

    const options = LOGS_TIME_FRAME_DATA.map((item) => ({
      ...item,
      value: item.id,
      startDate: moment.unix((convertStartTime(item.id) / 1000)).format(),
      endDate: moment.unix((convertEndTime(item.id) / 1000)).format(),
      label: item.name,
    }));

    const dropdownOption = getByText('Previous Day');
    fireEvent.click(dropdownOption);

    expect(mockDispatch).toHaveBeenCalledTimes(1);
    expect(mockDispatch).toHaveBeenCalledWith(change('testForm', 'testId', options[11]));
  });

  it('calls hideDropdown when dropdown value changes', () => {
    const hideDropdown = jest.fn();
    const store = createStore(() => ({}));
    const { getByText } = render(
      <Provider store={store}>
        <DateDropdown
          meta={{ form: 'testForm' }}
          id="testId"
          hideDropdown={hideDropdown}
          handleChange={jest.fn()} />
      </Provider>,
    );

    const dropdownOption = getByText('Previous Day');
    fireEvent.click(dropdownOption);

    expect(hideDropdown).toHaveBeenCalledTimes(1);
  });

  it('calculates DateDropdown correctly when value is not custom', () => {
    const store = createStore(() => ({}));
    render(
      <Provider store={store}>
        <DateDropdown
          meta={{ form: 'testForm' }}
          id="testId"
          hideDropdown={jest.fn()}
          handleChange={jest.fn()} />
      </Provider>,
    );

    const options = LOGS_TIME_FRAME_DATA.map((item) => ({
      ...item,
      value: item.id,
      startDate: moment.unix((convertStartTime(item.id) / 1000)).format(),
      endDate: moment.unix((convertEndTime(item.id) / 1000)).format(),
      label: item.name,
    }));

    expect(DateDropdown.startDate).toBe(options.startTime);
    expect(DateDropdown.endDate).toBe(options.endDate);
  });

  // It is failing with a difference of 1 sec, added 2 second tolerance
  it('calculates DateDropdown correctly when value is custom', () => {
    const onValueChange = jest.fn();
    const options = LOGS_TIME_FRAME_DATA.map((item) => ({
      ...item,
      value: item.id,
      startDate: moment.unix((convertStartTime(item.id) / 1000)).format(),
      endDate: moment.unix((convertEndTime(item.id) / 1000)).format(),
      label: item.name,
    }));
    const store = createStore(() => ({ duration: { startTime: options[8].startDate, endtime: options[8].endDate } }));
    const { getByText } = render(
      <Provider store={store}>
        <DateDropdown
          meta={{ form: 'testForm' }}
          id="testId"
          hideDropdown={jest.fn()}
          handleChange={onValueChange} />
      </Provider>,
    );
    const dropdownOption = getByText('Custom');
    fireEvent.click(dropdownOption);

    expect(onValueChange).toHaveBeenCalledTimes(1);
    const startDateDiff = Math.abs(moment(onValueChange.mock.calls[0][0].startDate).diff(moment(options[8].startDate), 'seconds'));
    const endDateDiff = Math.abs(moment(onValueChange.mock.calls[0][0].endDate).diff(moment(options[8].endDate), 'seconds'));
    expect(startDateDiff).toBeLessThan(2);
    expect(endDateDiff).toBeLessThan(2);
  });
});
