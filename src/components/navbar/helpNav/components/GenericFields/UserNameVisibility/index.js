import React from 'react';
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';
import { FormFieldLabel, FormSectionLabel } from 'components/label';
import { withTranslation } from 'react-i18next';
import { getFormValues } from 'redux-form';
import ECRadioGroup from 'components/ecRadioGroup';

function UserNameVisibility({
  viewOnly, form, actions, t,
}) {
  const data = useSelector((state) => getFormValues(form)(state)) || {};
  const { usernameObfuscated } = data;

  return (
    <>
      <FormSectionLabel text={t('USER_NAME_VISIBILITY')} />
      <div className="form-section">
        <div className="g-row">
          <div className="g-left">
            <FormFieldLabel text={t('USERNAMES')} tooltip={t('TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_EMAIL')} />
            {!viewOnly ? (
              <ECRadioGroup
                id="usernameObfuscated"
                name="usernameObfuscated"
                styleClass="full-width"
                onChange={actions.handleUsernameObfuscated}
                options={[{
                  name: 'usernameObfuscated', value: 'VISIBLE', checked: usernameObfuscated === 'VISIBLE', label: t('VISIBLE'),
                },
                {
                  name: 'usernameObfuscated', value: 'OBFUSCATED', checked: usernameObfuscated === 'OBFUSCATED', label: t('OBFUSCATED'),
                }]} />
            )
              : (
                <p className="disabled-input">
                  {usernameObfuscated === 'VISIBLE' ? t('VISIBLE') : t('OBFUSCATED')}
                </p>
              )}
          </div>
        </div>
      </div>
    </>
  );
}

UserNameVisibility.propTypes = {
  viewOnly: PropTypes.bool,
  form: PropTypes.string,
  actions: PropTypes.shape({
    handleUsernameObfuscated: PropTypes.func,
  }),
  t: PropTypes.func,
};
  
UserNameVisibility.defaultProps = {
  viewOnly: true,
  form: '',
  actions: {
    handleUsernameObfuscated: null,
  },
  t: (str) => str,
};

export default withTranslation()(UserNameVisibility);
