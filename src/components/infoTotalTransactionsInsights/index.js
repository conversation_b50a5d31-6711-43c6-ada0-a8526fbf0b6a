import React from 'react';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faDotCircle } from '@fortawesome/pro-regular-svg-icons';
import './index.scss';

function infoTotalTransactionsInsights({ startDate, endDate, recordLength }) {
  return (
    <>
      <div className="insights-time-range-container">
        <span className="insights-time-range-start">
          {startDate}
        </span>
        {' - '}
        <span className="insights-time-range-end">
          {endDate}
        </span>
      </div>
      <div className="insights-record-info">
        <span className="insights-record-info-text">
          <FontAwesomeIcon icon={faDotCircle} className="info-circle" />
          {` ${recordLength} Log Records Found`}
        </span>
      </div>
    </>
  );
}

infoTotalTransactionsInsights.propTypes = {
  recordLength: PropTypes.number,
  startDate: PropTypes.string,
  endDate: PropTypes.string,
};

infoTotalTransactionsInsights.defaultProps = {
  recordLength: 0,
  startDate: '',
  endDate: '',
};
export default infoTotalTransactionsInsights;
