import React from 'react';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCheckCircle } from '@fortawesome/pro-solid-svg-icons';
import { Trans } from 'react-i18next';

function RadioButton(props = {}) {
  const {
    input,
    label,
    disabled,
    checked,
  } = props;

  input.checked = checked;

  return (
    <div className={`radio-button ${disabled ? 'disabled' : ''} checked-${input.checked}`}>
      <label htmlFor={input.label}>
        <input
          type="radio"
          disabled={disabled}
          {...input} />
        <div className="check-circle">
          {input.checked && <FontAwesomeIcon icon={faCheckCircle} />}
        </div>
        <span><Trans>{label}</Trans></span>
      </label>
    </div>
  );
}

RadioButton.defaultProps = {
  label: null,
  disabled: false,
};

RadioButton.propTypes = {
  label: PropTypes.string,
  disabled: PropTypes.bool,
  checked: PropTypes.bool,
  input: PropTypes.shape({
    name: PropTypes.string,
    value: PropTypes.any,
  }).isRequired,
};

export default RadioButton;
