import { createAction, loading } from 'ducks/generics';
import { reset, change } from 'redux-form';
import { genericInterface } from 'utils/http';
import { notifyError } from 'ducks/notification';
import { loader as loadCcAdvancedSettings } from 'ducks/cloudConfigurationAdvancedSettings';
import * as CCAdvancedSettings from 'ducks/cloudConfigurationAdvancedSettings/selectors';
import i18n from 'utils/i18n';
import {
  get, isNull, orderBy, isEmpty,
} from 'utils/lodash';

import {
  getRangesAddresses,
  convertHexaToDecimal,
  convertStartTime,
  convertEndTime,
} from 'utils/helpers';

import { range, validateFrom, validateTo } from 'utils/validations';

import moment from 'moment-timezone';

import * as constants from './constants';
import actionTypes from './action-types';
import * as dnsLogsSelector from './selectors';

export { REDUCER_KEY } from './constants';

const actionTypeExists = Object.keys(actionTypes).reduce(
  (hash, key) => ({
    ...hash,
    [actionTypes[key]]: true,
  }),
  {},
);

export const reducer = (state = constants.DEFAULT_STATE, action = {}) => {
  if (actionTypeExists[action.type]) {
    return {
      ...state,
      ...action.payload,
    };
  }
  return state;
};
export default reducer;

const defaultTimeframe = {
  id: 'last_24_hrs',
  value: 'last_24_hrs',
  label: 'LAST_24_HOURS',
  startTime: moment.unix((convertStartTime('last_24_hrs') / 1000)).unix() * 1000,
  endTime: moment.unix((convertEndTime('last_24_hrs') / 1000)).unix() * 1000,
};

const boundLoading = loading(actionTypes.DATA_LOADING);
const boundDataLoadSuccess = createAction(actionTypes.DATA_LOAD_SUCCESS);
const dataChanged = createAction(actionTypes.DATA_CHANGED);

const txnDataApi = genericInterface(constants.TXN_DATA_API_ENDPOINT);
const dnsLogsApi = genericInterface(constants.TXN_DATA_DNS_LOGS_API_ENDPOINT);

const exportDNSJob = genericInterface(constants.EXPORT_DNS_JOB_API_ENDPOINT);
const exportDNSData = genericInterface(constants.EXPORT_DNS_DATA_API_ENDPOINT);

export const clearData = () => (dispatch) => {
  dispatch({
    type: actionTypes.RESET,
    payload: {
      ...constants.DEFAULTS,
    },
  });
};

export const txnPoll = (dispatch) => {
  return txnDataApi
    .read()
    .then(async (response) => {
      const { data } = response;
      await dispatch(dataChanged({ [constants.PROGRESS]: data }));
      if (data.status === 'EXECUTING') {
        return txnPoll(dispatch);
      }
      if (data.status === 'COMPLETE') {
        return true;
      }
      if (data.status === 'ERRORED') {
        return dispatch(notifyError('HTTP Request failure reported.'));
      }
      return false;
    })
    .catch((error) => {
      const errorMsg = get(
        error,
        'response.data.message',
        'Connection to Central Authority is lost',
      );

      dispatch(notifyError(errorMsg));
      dispatch(dataChanged({ loading: false }));
    });
};

export const loader = () => (dispatch) => {
  dispatch(boundLoading({ ...constants.DEFAULTS, loading: true }));
 
  const { startTime, endTime } = defaultTimeframe;
  const pageSize = 1000;

  return txnDataApi
    .create(
      {
        startTime,
        endTime,
        pageSize,
      },
      {},
    )
    .then((response) => {
      if (response.status === 204) {
        txnPoll(dispatch)
          .then((res) => {
            if (res) {
              return dnsLogsApi.read().then((txnRes) => {
                const { data } = txnRes;
                const actualData = [...data.transactions].map((x, idx) => ({
                  ...x,
                  idx: idx + 1,
                  dnsRequestType: i18n.t(x.dnsRequestType),
                  dnsErrorCode: i18n.t(x.dnsErrorCode),
                }));
                dispatch(
                  dataChanged({
                    loading: false,
                    error: null,
                    [constants.DATA_TABLE]: actualData,
                  }),
                );
              });
            }
            return null;
          })
          .catch((error) => {
            const errorMsg = get(
              error,
              'response.data.message',
              'Connection to Central Authority is lost',
            );

            dispatch(notifyError(errorMsg));
            dispatch(dataChanged({ loading: false }));
          });
      } else if (response.status === 200) {
        return response;
      }
      return 0;
    })
    .catch((error) => {
      const errorMsg = get(
        error,
        'response.data.message',
        'Connection to Central Authority is lost',
      );

      dispatch(notifyError(errorMsg));
      dispatch(dataChanged({ loading: false }));
    });
};

export const exportTxnPoll = (dispatch) => {
  return exportDNSJob
    .read()
    .then(async (response) => {
      const { data } = response;
      await dispatch(dataChanged({ [constants.PROGRESS]: data }));
      if (data.status === 'EXECUTING') {
        return exportTxnPoll(dispatch);
      }
      if (data.status === 'COMPLETE') {
        return true;
      }
      if (data.status === 'ERRORED') {
        dispatch(dataChanged({ loading: false }));
        if (data.errorMsg) return dispatch(notifyError(data.errorMsg));
        if (data.errorCode) return dispatch(notifyError(data.errorCode));
        return dispatch(notifyError('ERROR_HTTP_REQUEST_FAILURE'));
      }
      return false;
    })
    .catch((error) => {
      const errorMsg = get(
        error,
        'response.data.message',
        'Connection to Central Authority is lost',
      );

      dispatch(notifyError(errorMsg));
      dispatch(dataChanged({ loading: false }));
    });
};

export const getDNSLogsData = () => (dispatch) => {
  dispatch(
    boundDataLoadSuccess({
      [constants.DATA]: constants.DATA,
      loading: false,
    }),
  );
};

export const updateMenu = (columns) => (dispatch) => {
  dispatch(
    dataChanged({
      [constants.COLUMNS]: columns,
    }),
  );
};

export const toggleAccordion = () => (dispatch, getState) => {
  const currentState = getState();
  const toggle = dnsLogsSelector.accordionSelector(currentState);

  dispatch(
    boundDataLoadSuccess({
      [constants.SHOW_ACCORDION]: !toggle,
    }),
  );
};

export const getPreselectedFilters = (initialValues) => (dispatch) => {
  const { genericFilters } = initialValues;
  let noOfRecords = '1000';

  if (genericFilters) {
    noOfRecords = genericFilters.noOfRecords || '1000';
  }

  dispatch(
    dataChanged({
      [constants.NO_OF_RECORDS]: noOfRecords,
    }),
  );
};

export const handleStartOver = () => (dispatch) => {
  dispatch(reset('dnsFilersForm')); // requires form name
  const orderdFilters = orderBy(constants.dummyTestfilters.map((x) => ({ ...x, labelTralslation: i18n.t(x.label) })), ['labelTralslation']);

  dispatch(
    boundLoading({
      ...constants.RESOURCE_DEFAULTS,
    }),
  );

  // dispatch(loader());
};

export const handleClearFilters = () => (dispatch, getState) => {
  dispatch(reset('dnsFilersForm')); // requires form name
  const currentState = getState();
  const sessionFormValues = dnsLogsSelector.dnsFilersForm(currentState);
  const { values } = sessionFormValues;
  const selectedTimeFrame = values.timeFrame;

  dispatch(change('dnsFilersForm', 'timeFrame', selectedTimeFrame));
  const orderdFilters = orderBy(constants.dummyTestfilters.map((x) => ({ ...x, labelTralslation: i18n.t(x.label) })), ['labelTralslation']);

  dispatch(
    boundLoading({
      [constants.FILTERS]: orderdFilters,
      [constants.CURRENT_FILTERS]: orderdFilters,
      [constants.SHOW_FILTERS]: constants.defaultFilters,
      [constants.PROGRESS]: {},
    }),
  );
};

export const handleRemoveFilter = (optedFilter) => (dispatch, getState) => {
  const currentState = getState();
  const currentShowFilters = dnsLogsSelector.showFiltersSelector(currentState);
  const dnsFiltersValues = dnsLogsSelector.dnsFiltersFormValues(currentState);

  delete dnsFiltersValues[optedFilter]; // Form element
  if (optedFilter === 'ecName') {
    delete dnsFiltersValues.ecNameEmpty; // remove Null values if any
  } else if (optedFilter === 'requestedDomain') {
    delete dnsFiltersValues.requestedDomainEmpty; // remove Null values if any
  } else if (optedFilter === 'dataCenter') {
    delete dnsFiltersValues.dataCenterEmpty;
  } else if (optedFilter === 'inbytes') {
    delete dnsFiltersValues.inbytesFrom;
    delete dnsFiltersValues.inbytesTo;
  } else if (optedFilter === 'outbytes') {
    delete dnsFiltersValues.outbytesFrom;
    delete dnsFiltersValues.outbytesTo;
  } else if (optedFilter === 'requestDuration') {
    delete dnsFiltersValues.requestDurationFrom;
    delete dnsFiltersValues.requestDurationTo;
  }

  const showSelectedFilters = { ...currentShowFilters };
  showSelectedFilters[optedFilter] = false;

  const filters = constants.dummyTestfilters;
  const currentFilters = dnsLogsSelector.addFilterCurrentDropdownSelector(
    currentState,
  );

  const updateFilters = filters.filter((item) => item.value === optedFilter);
  currentFilters.unshift(...updateFilters);
  const orderdFilters = orderBy(currentFilters.map((x) => ({ ...x, labelTralslation: i18n.t(x.label) })), ['labelTralslation']);

  dispatch(
    dataChanged({
      [constants.SHOW_FILTERS]: showSelectedFilters,
      [constants.FILTERS]: orderdFilters,
      [constants.CURRENT_FILTERS]: orderdFilters,
      loading: false,
    }),
  );
};

export const handleNoOfRecords = (t, appData) => (
  dispatch,
) => {
  dispatch(
    dataChanged({
      [constants.NO_OF_RECORDS]: appData,
    }),
  );
};

export const handleToggleDownloadCSV = (t, appData) => (
  dispatch,
) => {
  dispatch(
    dataChanged({
      [constants.DOWNLOAD_CSV]: appData,
    }),
  );
};

export const getFilters = () => async (dispatch, getState) => {
  const currentState = getState();
  let accountIdEnabled = CCAdvancedSettings.accountId(currentState);
  let subIdEnabled = CCAdvancedSettings.subscriptionId(currentState);
  let projectIdEnabled = CCAdvancedSettings.projectId(currentState);

  if (isNull(accountIdEnabled) || isNull(subIdEnabled) || isNull(projectIdEnabled)) {
    await dispatch(loadCcAdvancedSettings());
    const newState = getState();
    accountIdEnabled = CCAdvancedSettings.accountId(newState);
    subIdEnabled = CCAdvancedSettings.subscriptionId(newState);
    projectIdEnabled = CCAdvancedSettings.projectId(newState);
  }
  const filters = constants.dummyTestfilters
    .filter((x) => !(x.value === 'accountId' && !accountIdEnabled))
    .filter((x) => !(x.value === 'subscriptionId' && !subIdEnabled))
    .filter((x) => !(x.value === 'projectId' && !projectIdEnabled));
  const orderdFilters = orderBy(filters.map((x) => ({ ...x, labelTralslation: i18n.t(x.label) })), ['labelTralslation']);

  dispatch(
    boundDataLoadSuccess({
      [constants.FILTERS]: orderdFilters,
      [constants.CURRENT_FILTERS]: orderdFilters,
    }),
  );
};

export const setSearchString = (searchText) => (dispatch, getState) => {
  const currentState = getState();
  const filters = dnsLogsSelector.addFilterDropdownSelector(currentState);

  const searchedFilters = [];
  filters.forEach((item) => {
    if (i18n.t(item.label).toLowerCase().includes(searchText.toLowerCase())) {
      searchedFilters.push(item);
    }
  });
  const orderdFilters = orderBy(searchedFilters.map((x) => ({ ...x, labelTralslation: i18n.t(x.label) })), ['labelTralslation']);

  dispatch(
    boundDataLoadSuccess({
      [constants.CURRENT_FILTERS]: orderdFilters,
      loading: false,
    }),
  );
};

export const handleSelectedAddFilter = (selectedFilter) => (
  dispatch,
  getState,
) => {
  const currentState = getState();
  const currentShowFilters = dnsLogsSelector.showFiltersSelector(currentState);

  const showSelectedFilters = { ...currentShowFilters };
  showSelectedFilters[selectedFilter.value] = true;

  const filters = dnsLogsSelector.addFilterDropdownSelector(currentState);
  const updateFilters = filters.filter(
    (item) => item.value !== selectedFilter.value,
  );
  const orderdFilters = orderBy(updateFilters.map((x) => ({ ...x, labelTralslation: i18n.t(x.label) })), ['labelTralslation']);

  dispatch(
    dataChanged({
      [constants.SHOW_FILTERS]: showSelectedFilters,
      [constants.FILTERS]: orderdFilters,
      [constants.CURRENT_FILTERS]: orderdFilters,
      loading: false,
    }),
  );
};

export const getLocations = () => (dispatch) => {
  dispatch(
    boundDataLoadSuccess({
      [constants.LOCATIONS]: constants.dlocs,
      loading: false,
    }),
  );
};

export const checkPortRange = () => (getState) => {
  const currentState = getState();
  const from = dnsLogsSelector.formServerPortFrom(currentState);
  const to = dnsLogsSelector.formServerPortTo(currentState);

  if (to && from && parseInt(to, 10) < parseInt(from, 10)) {
    return 'From should be less than To';
  }
  return range(1, 65536)(from);
};

export const validatePortFrom = () => (dispatch, getState) => {
  const currentState = getState();
  const from = dnsLogsSelector.formServerPortFrom(currentState);
  const to = dnsLogsSelector.formServerPortTo(currentState);
  return validateFrom(from, to, 65536); // 65536 is port range
};

export const validatePortTo = () => (dispatch, getState) => {
  const currentState = getState();
  const from = dnsLogsSelector.formServerPortFrom(currentState);
  const to = dnsLogsSelector.formServerPortTo(currentState);
  return validateTo(from, to, 65536); // 65536 is port range
};

export const validateRequestDurationFrom = () => (dispatch, getState) => {
  const currentState = getState();
  const from = dnsLogsSelector.formRequestDurationFrom(currentState);
  const to = dnsLogsSelector.formRequestDurationTo(currentState);
  return validateFrom(from, to, 65536); // need to get the range
};

export const validateRequestDurationTo = () => (dispatch, getState) => {
  const currentState = getState();
  const from = dnsLogsSelector.formRequestDurationFrom(currentState);
  const to = dnsLogsSelector.formRequestDurationTo(currentState);
  return validateTo(from, to, 65536); // formRequestDurationTo
};

export const validateInbytesFrom = () => (dispatch, getState) => {
  const currentState = getState();
  const from = dnsLogsSelector.formInbytesFrom(currentState);
  const to = dnsLogsSelector.formInbytesTo(currentState);
  return validateFrom(from, to, 65536); // need to get the range
};

export const validateInbytesTo = () => (dispatch, getState) => {
  const currentState = getState();
  const from = dnsLogsSelector.formInbytesFrom(currentState);
  const to = dnsLogsSelector.formInbytesTo(currentState);
  return validateTo(from, to, 65536); // formRequestDurationTo
};

export const validateOutbytesFrom = () => (dispatch, getState) => {
  const currentState = getState();
  const from = dnsLogsSelector.formOutbytesFrom(currentState);
  const to = dnsLogsSelector.formOutbytesTo(currentState);
  return validateFrom(from, to, 65536); // need to get the range
};

export const validateOutbytesTo = () => (dispatch, getState) => {
  const currentState = getState();
  const from = dnsLogsSelector.formOutbytesFrom(currentState);
  const to = dnsLogsSelector.formOutbytesTo(currentState);
  return validateTo(from, to, 65536); // formRequestDurationTo
};

const constructPayload = (filtersValues) => {
  // payload
  const payload = {};
  payload.startTime = filtersValues.timeFrame.startTime;
  payload.endTime = filtersValues.timeFrame.endTime;
  payload.pageSize = filtersValues.noOfRecords;

  // locationName [Multi select Filter]
  if (filtersValues.locationName) {
    // "locationIdFilters":{"type":"INCLUDE","values":[-3]}
    const locIds = filtersValues.locationName.map((item) => item.id);
    payload.locationIdFilters = {
      type: filtersValues.locationType,
      values: locIds,
    };
  }

  if (filtersValues.clientIp) {
    payload.clientIp = getRangesAddresses(filtersValues.clientIp);
  }

  // requestedDomain(StringSearch)
  if (filtersValues.requestedDomain) {
    payload.requestedDomain = {
      matchType: filtersValues.requestedDomainMatchType || 'EXACT_MATCH',
      value: filtersValues.requestedDomain,
    };
  } else if (
    filtersValues.requestedDomainEmpty
    && (filtersValues.requestedDomainMatchType === 'IS_NULL'
      || filtersValues.requestedDomainMatchType === 'NOT_NULL')
  ) {
    payload.requestedDomain = {
      matchType: filtersValues.requestedDomainMatchType || 'IS_NULL',
      value: filtersValues.requestedDomainEmpty,
    };
  }

  // serverIp (IP Address Filter)
  if (filtersValues.serverIp) {
    // Res : Input - have to check range of Ip values
    // "ecSrcIp":{"ranges":[],"addresses":[{"value":"********"}],"masks":[]}
    payload.serverIp = getRangesAddresses(filtersValues.serverIp);
  }

  // Rule Name [Multi select Filter]
  if (filtersValues.dnsRuleName) {
    // Res :  from Static JSON
    // {"ruleNames":["Default Firewall DNS Rule","Unknown DNS Traffic"]}
    const reqRules = filtersValues.dnsRuleName.map((item) => item.name);
    payload.ruleNames = reqRules;
  }
  // GW Name [Multi select Filter]
  if (filtersValues.dnsGwName) {
    const dnsGwName = filtersValues.dnsGwName.map((item) => item.id);
    payload.gatewaySlotIds = dnsGwName;
  }
  // GW Flag Name [Multi select Filter]
  if (filtersValues.dnsGwFlag) {
    const dnsGwFlag = filtersValues.dnsGwFlag.map((item) => item.name);
    payload.gatewayFlags = dnsGwFlag;
  }

  // FORWARDING  TYPES
  if (filtersValues.forwardingTypes) {
    const forwardingTypes = filtersValues.forwardingTypes.map((item) => item.id);
    payload.forwardingTypes = forwardingTypes;
  }

  // DNS Response Type(same as ZIA) YET TO DO
  if (filtersValues.dnsResponseType) {
    // {"startTime":1589824800000,"endTime":1589871472761,"pageSize":1000,
    // "dnsResponse":{
    //   "resolvedNames":{"value":"abc","matchType":"EXACT_MATCH"},
    //   "resolvedIpV4Address":{"ranges":[],"addresses":[{"value":"********"}],"masks":[]},
    //   "resolvedIpV6Address":{"value":"*******","matchType":"EXACT_MATCH"},
    //   "dnsErrorCode":["BADMODE","BADTRUNC"]}
    //   }
    const dnsResponse = {
      resolvedNames: {
        value: '',
        matchType: 'EXACT_MATCH',
      },
      resolvedIpV4Address: {
        ranges: [],
        addresses: [],
      },
      resolvedIpV6Address: {
        value: '',
        matchType: 'EXACT_MATCH',
      },
      dnsErrorCode: ['BADMODE', 'BADTRUNC'],
    };

    dnsResponse.resolvedNames = filtersValues.dnsResponseType.map(
      (item) => item.id,
    );
    payload.dnsResponse = dnsResponse;
  }

  // DNS Response Type(same as ZIA) YET TO DO
  if (filtersValues.requestAction) {
    // {"dnsReqRespAction":"BOTH_REQ_RESP_ALLOW"}
    payload.dnsReqRespAction = filtersValues.requestAction;
  }

  // resolverIpOrName(StringSearch)
  if (filtersValues.resolverIpOrName) {
    payload.resolverIpOrName = {
      matchType: filtersValues.resolverIpOrNameMatchType || 'EXACT_MATCH',
      value: filtersValues.resolverIpOrName,
    };
  }

  // resolvedIpOrName(StringSearch)
  if (filtersValues.resolvedIpOrName) {
    payload.resolvedIpOrName = {
      matchType: filtersValues.resolvedIpOrNameMatchType || 'EXACT_MATCH',
      value: filtersValues.resolvedIpOrName,
    };
  }

  // resolvedIpOrName(StringSearch)
  if (filtersValues.resolvedIpOrName) {
    payload.resolvedIpOrName = {
      matchType: filtersValues.resolvedIpOrNameMatchType || 'EXACT_MATCH',
      value: filtersValues.resolvedIpOrName,
    };
  }

  // dnsRequestType [Multi select Filter]
  if (filtersValues.dnsRequestType) {
    // dnsRequestTypes":["DNSREQ_MG","DNSREQ_MR"]
    const dnsReqTypes = filtersValues.dnsRequestType.map((item) => item.name);
    payload.dnsRequestTypes = dnsReqTypes;
  }

  // Server Port - Need to check
  if (filtersValues.serverPortFrom && filtersValues.serverPortTo) {
    // Res : Input Min to Max
    // serverPortFrom: "20"
    // serverPortTo: "133"
    // "serverPorts":{"min":10,"max":50}
    payload.serverPorts = {
      min: filtersValues.serverPortFrom,
      max: filtersValues.serverPortTo,
    };
  }

  // protocolType(simple Dropdown)
  if (filtersValues.protocolType) {
    // protocolType":"TCP"
    payload.protocolType = filtersValues.protocolType;
  }

  // requestDuration
  if (filtersValues.requestDurationFrom && filtersValues.requestDurationTo) {
    // "requestDuration":{"min":3782,"max":348894}
    payload.requestDuration = {
      min: filtersValues.requestDurationFrom,
      max: filtersValues.requestDurationTo,
    };
  }

  // ecName(StringSearch)
  if (filtersValues.ecName) {
    // "ecName":{"value":"ec-1","matchType":"EXACT_MATCH"}
    payload.ecName = {
      matchType: filtersValues.ecNameMatchType || 'EXACT_MATCH',
      value: filtersValues.ecName,
    };
  } else if (
    filtersValues.ecNameEmpty
    && (filtersValues.ecNameMatchType === 'IS_NULL'
      || filtersValues.ecNameMatchType === 'NOT_NULL')
  ) {
    payload.ecName = {
      matchType: filtersValues.ecNameMatchType || 'IS_NULL',
      value: filtersValues.ecNameEmpty,
    };
  }

  // Data Center (StringSearch)
  if (filtersValues.dataCenter) {
    payload.dataCenterName = {
      matchType: filtersValues.dataCenterMatchType || 'EXACT_MATCH',
      value: filtersValues.dataCenter,
    };
  } else if (
    filtersValues.dataCenterEmpty
    && (filtersValues.dataCenterMatchType === 'IS_NULL'
      || filtersValues.dataCenterMatchType === 'NOT_NULL')
  ) {
    payload.dataCenterName = {
      matchType: filtersValues.dataCenterMatchType || 'IS_NULL',
      value: filtersValues.dataCenterEmpty,
    };
  }

  // ecInstance
  if (filtersValues.ecInstance) {
    const ecInstanceIds = filtersValues.ecInstance.map((item) => item.id);
    payload.ecIdFilters = {
      type: filtersValues.ecInstanceType,
      values: ecInstanceIds,
    };
  }

  // ecVm
  if (filtersValues.ecVm) {
    const ecVms = filtersValues.ecVm.map((item) => item.id);
    payload.ecVmFilters = {
      type: filtersValues.ecVmType,
      values: ecVms,
    };
  }

  // ecGroup
  if (filtersValues.ecGroup) {
    const ecGroups = filtersValues.ecGroup.map((item) => item.id);
    payload.ecGroupFilters = {
      type: filtersValues.ecGroupType,
      values: ecGroups,
    };
  }

  // vpcName(StringSearch) -- yet to get backend support
  if (filtersValues.vpcName) {
    // ”vpcName”:{value: "vpc1", matchType: "EXACT_MATCH"}
    payload.vpcName = {
      matchType: filtersValues.vpcNameMatchType || 'EXACT_MATCH',
      value: filtersValues.vpcName,
    };
  }

  // domainCategory [Multi select Filter]
  // if (filtersValues.domainCategory) {
  //   // "domainCategories":["OTHER_ADULT_MATERIAL"]
  //   payload.domainCategories = filtersValues.domainCategory.map(item => item.id);
  // }

  // resRuleName [Multi select Filter]
  // if (filtersValues.resRuleName) {
  //   // "resRuleNames":[""]
  //   payload.resRuleNames = filtersValues.resRuleName.map(item => item.value);
  // }

  // dnsApplication [Multi select Filter]
  // if (filtersValues.dnsApplication) {
  //   // "dnsApplications":["ACTIVESYNC","ADDICTINGGAMES"]
  //   payload.dnsApplications = filtersValues.dnsApplication.map(item => item.id);
  // }

  // responseAction(simple Dropdown) - Yet to do
  if (filtersValues.responseAction) {
    // "dnsReqRespAction":"BOTH_REQ_RESP_ALLOW"
    payload.dnsReqRespAction = filtersValues.responseAction;
  }

  // inbytes
  if (filtersValues.inbytesFrom && filtersValues.inbytesTo) {
    // "inboundBytes":{"min":1,"max":10}
    payload.inboundBytes = {
      min: filtersValues.inbytesFrom,
      max: filtersValues.inbytesTo,
    };
  }

  // outbytes
  if (filtersValues.outbytesFrom && filtersValues.outbytesTo) {
    // "outboundBytes":{"min":1,"max":10}
    payload.outboundBytes = {
      min: filtersValues.outbytesFrom,
      max: filtersValues.outbytesTo,
    };
  }

  // ecVmName(StringSearch) -- May need to implement Multiselect
  if (filtersValues.ecVmName) {
    // "ecVmFilters":{"type":"INCLUDE","values":[66811,66810,66809,66812]}
    payload.ecVmFilters = {
      matchType: filtersValues.ecVmNameMatchType || 'EXACT_MATCH',
      value: filtersValues.ecVmName,
    };
  }

  // platform [Multi select Filter]
  if (filtersValues.platform) {
    // "ecPlatformFilters":{"type":"INCLUDE","values":[1,0]}
    const ecPlatformFilters = filtersValues.platform.map((item) => convertHexaToDecimal(item.id));
    payload.ecPlatformFilters = {
      type: filtersValues.platformType,
      values: ecPlatformFilters,
    };
  }

  // awsRegion
  if (filtersValues.awsRegion) {
    const awsRegion = filtersValues.awsRegion.map((item) => item.id);
    payload.awsRegionFilters = {
      type: filtersValues.awsRegionType,
      values: awsRegion,
    };
  }

  // awsRegion
  if (filtersValues.zone) {
    // eslint-disable-next-line max-len
    const availabilityZones = filtersValues.zone.map((item) => convertHexaToDecimal(item.id));
    payload.awsIdFilters = {
      type: filtersValues.zoneType,
      values: availabilityZones,
    };
  }

  // azureRegion
  if (filtersValues.azureRegion) {
    const azureRegion = filtersValues.azureRegion.map((item) => item.id);
    payload.azureRegionFilters = {
      type: filtersValues.azureRegionType,
      values: azureRegion,
    };
  }

  // azureAvailabilityZone
  if (filtersValues.azureAvailabilityZone) {
    // eslint-disable-next-line max-len
    const azureAvailabilityZone = filtersValues.azureAvailabilityZone.map(
      (item) => convertHexaToDecimal(item.id),
    );
    payload.azureIdFilters = {
      type: filtersValues.azureAvailabilityZoneType,
      values: azureAvailabilityZone,
    };
  }

  // GCP Region
  if (filtersValues.gcpRegion) {
    const gcpRegion = filtersValues.gcpRegion.map((item) => item.id);
    payload.gcpRegionFilters = {
      type: filtersValues.gcpRegionType,
      values: gcpRegion,
    };
  }
  // gcpAvailabilityZone
  if (filtersValues.gcpAvailabilityZone) {
    // eslint-disable-next-line max-len
    const gcpAvailabilityZone = filtersValues.gcpAvailabilityZone.map(
      (item) => convertHexaToDecimal(item.id),
    );
    payload.gcpZoneFilters = {
      type: filtersValues.gcpAvailabilityZoneType,
      values: gcpAvailabilityZone,
    };
  }

  // Account ID
  if (filtersValues.accountId && !isEmpty(filtersValues.accountId)) {
    const accountIds = filtersValues.accountId.map((item) => item.id);
    payload.accountId = {
      type: filtersValues.accountIdType,
      values: accountIds,
    };
  }
  
  // Subscriptition ID
  if (filtersValues.subscriptionId && !isEmpty(filtersValues.subscriptionId)) {
    const subscriptionIds = filtersValues.subscriptionId.map((item) => item.id);
    payload.subscriptionId = {
      type: filtersValues.subscriptionIdType,
      values: subscriptionIds,
    };
  }

  // Project ID
  if (filtersValues.projectId && !isEmpty(filtersValues.projectId)) {
    const projectIds = filtersValues.projectId.map((item) => item.id);
    payload.projectId = {
      type: filtersValues.projectIdType,
      values: projectIds,
    };
  }

  // deviceName(StringSearch)
  if (filtersValues.deviceName) {
    payload.deviceName = {
      matchType: filtersValues.deviceNameMatchType || 'EXACT_MATCH',
      value: filtersValues.deviceName,
    };
  }

  // deviceOsType(StringSearch)
  if (filtersValues.deviceOsType) {
    payload.deviceOsType = {
      matchType: filtersValues.deviceOsTypeMatchType || 'EXACT_MATCH',
      value: filtersValues.deviceOsType,
    };
  }

  // devicePlatform - Maybe MultiSelect -- Yet to do
  if (filtersValues.devicePlatform) {
    payload.devicePlatform = filtersValues.devicePlatform;
  }

  // deviceOsVersion(StringSearch)
  if (filtersValues.deviceOsVersion) {
    payload.deviceOsVersion = {
      matchType: filtersValues.deviceOsVersionMatchType || 'EXACT_MATCH',
      value: filtersValues.deviceOsVersion,
    };
  }

  // deviceModel(StringSearch)
  if (filtersValues.deviceModel) {
    payload.deviceModel = {
      matchType: filtersValues.deviceModelMatchType || 'EXACT_MATCH',
      value: filtersValues.deviceModel,
    };
  }

  // deviceAppVersion(StringSearch)
  if (filtersValues.deviceAppVersion) {
    payload.deviceAppVersion = {
      matchType: filtersValues.deviceAppVersionMatchType || 'EXACT_MATCH',
      value: filtersValues.deviceAppVersion,
    };
  }

  // deviceOwner(StringSearch)
  if (filtersValues.deviceOwner) {
    payload.deviceOwner = {
      matchType: filtersValues.deviceOwnerMatchType || 'EXACT_MATCH',
      value: filtersValues.deviceOwner,
    };
  }

  // deviceHostname(StringSearch)
  if (filtersValues.deviceHostname) {
    payload.deviceHostname = {
      matchType: filtersValues.deviceHostnameMatchType || 'EXACT_MATCH',
      value: filtersValues.deviceHostname,
    };
  }

  return payload;
};

export const downloadCSV = () => (dispatch, getState) => {
  dispatch(boundLoading({ loading: true, isDownload: true }));
  const currentState = getState();
  const filtersValues = dnsLogsSelector.dnsFiltersFormValues(currentState);
  const columns = dnsLogsSelector.columnsSelector(currentState);

  // payload
  const payload = constructPayload(filtersValues);
  payload.pageSize = filtersValues.noOfRecords;

  const checkedColumns = columns.map((item) => (item.visible ? item.exportId : null));
  payload.selectedColumns = checkedColumns.filter(Boolean);

  return exportDNSJob
    .create(payload, {})
    .then((response) => {
      if (response.status === 204) {
        exportTxnPoll(dispatch).then((res) => {
          if (res) {
            exportDNSData.read().then((csvresponse) => {
              const { data, headers } = csvresponse;

              const contents = headers['content-disposition'];
              const filenameSt = contents.split(';');
              const filename = filenameSt[1].split('=');

              const hiddenElement = document.createElement('a');
              const fileContent = 'data:text/csv;charset=utf-8,' + data;
              let encodedUri = encodeURI(fileContent);
              encodedUri = encodedUri.replace(/#/g, '%23'); // Resolve # symbol encoding issue
              hiddenElement.setAttribute('href', encodedUri);
              hiddenElement.setAttribute('download', filename[1]);
              hiddenElement.click();
              hiddenElement.remove();

              return dispatch(
                boundDataLoadSuccess({
                  [constants.DONWLOAD_CSV_DATA]: data,
                  loading: false,
                  isDownload: false,
                  error: null,
                }),
              );
            });
          }
        }).catch((error) => {
          const errorMsg = get(
            error,
            'response.data.message',
            'Request to Nanolog Server has timed out',
          );
    
          dispatch(notifyError(errorMsg));
          dispatch(dataChanged({ loading: false }));
        });
      }
    })
    .catch((error) => {
      const errorMsg = get(
        error,
        'response.data.message',
        'Connection to Central Authority is lost',
      );

      dispatch(notifyError(errorMsg));
      dispatch(dataChanged({ loading: false }));
    });
};

/* Apply Filters action */
// eslint-disable-next-line consistent-return
export const applyFilters = () => (dispatch, getState) => {
  dispatch(boundLoading({ ...constants.RESET_CSV_DOWNLOAD, loading: true }));
  const currentState = getState();
  const filtersValues = dnsLogsSelector.dnsFiltersFormValues(currentState);
  const downloadCSVButton = dnsLogsSelector.downloadCSVSelector(
    currentState,
  );
  // const currentShowFilters = dnsLogsSelector.showFiltersSelector(
  //   currentState,
  // );
  
  // const filters = dnsLogsSelector.addFilterDropdownSelector(currentState);
  
  // dispatch(
  //   dataChanged({
  //     [constants.SHOW_FILTERS]: currentShowFilters,
  //     [constants.FILTERS]: filters,
  //     [constants.CURRENT_FILTERS]: filters,
  //   }),
  // );

  if (filtersValues.accountId && filtersValues.subscriptionId) {
    dispatch(notifyError(i18n.t('ERROR_ACCOUNT_ID_AND_SUBSCRIPTION_ID_NOT_ALLOWED_TOGETHER')));
    dispatch(
      dataChanged({
        loading: false,
        error: null,
      }),
    );
    return null;
  }
  if (filtersValues.projectId && filtersValues.subscriptionId) {
    dispatch(notifyError(i18n.t('ERROR_PROJECT_ID_AND_SUBSCRIPTION_ID_NOT_ALLOWED_TOGETHER')));
    dispatch(
      dataChanged({
        loading: false,
        error: null,
      }),
    );
    return null;
  }
  if (filtersValues.accountId && filtersValues.projectId) {
    dispatch(notifyError(i18n.t('ERROR_ACCOUNT_ID_AND_PROJECT_ID_NOT_ALLOWED_TOGETHER')));
    dispatch(
      dataChanged({
        loading: false,
        error: null,
      }),
    );
    return null;
  }
  
  // invoke download csv if download selected
  if (downloadCSVButton === 'DOWNLOAD') {
    dispatch(downloadCSV());
  } else {
    const payload = constructPayload(filtersValues);
    return (
      txnDataApi
        .create(payload, {})
        // eslint-disable-next-line consistent-return
        .then((response) => {
          if (response.status === 204) {
            txnPoll(dispatch).then((res) => {
              if (res) {
                return dnsLogsApi.read().then((txnRes) => {
                  const { data } = txnRes;
                  const actualData = [...data.transactions].map((x, idx) => ({
                    ...x,
                    idx: idx + 1,
                    dnsRequestType: i18n.t(x.dnsRequestType),
                    dnsErrorCode: i18n.t(x.dnsErrorCode),
                  }));
                  dispatch(
                    dataChanged({
                      loading: false,
                      error: null,
                      [constants.DATA_TABLE]: actualData,
                    }),
                  );
                });
              }
              return null;
            });
          } else if (response.status === 200) {
            return response;
          }
        })
        .catch((error) => {
          const errorMsg = get(
            error,
            'response.data.message',
            'Connection to Central Authority is lost',
          );
    
          dispatch(notifyError(errorMsg));
          dispatch(dataChanged({ loading: false }));
        })
    );
  }
};

export const handleOnSearchFilter = (event, searchText) => (dispatch) => {
  if (event.keyCode && event.keyCode === 13 && searchText.length > 0) {
    return dispatch(dataChanged({
      [constants.SHOW_SEARCH]: false,
    }));
  } if (searchText.length === 0) {
    return dispatch(dataChanged({
      [constants.SHOW_SEARCH]: true,
    }));
  }

  return null;
};

export const handleOnClearFilter = () => (dispatch) => {
  return dispatch(dataChanged({
    [constants.SHOW_SEARCH]: true,
  }));
};

export const handleOnSortClick = () => (dispatch) => {
  return dispatch(dataChanged({
    [constants.IS_SORTED]: true,
  }));
};

export const handleOnResetFilter = () => async (dispatch, getState) => {
  const currentState = getState();
  const { sortable } = dnsLogsSelector.baseSelector(currentState);
  return dispatch(dataChanged({
    [constants.SHOW_SEARCH]: false,
    [constants.IS_SORTED]: false,
    [constants.SORTABLE]: sortable + 1,
  }));
};

export const handleCancelAPI = () => async (dispatch, getState) => {
  const currentState = getState();
  const isDownload = dnsLogsSelector.isDownloadSelector(currentState);
  if (!isDownload) return;

  exportDNSJob.del();
  await dispatch(dataChanged({
    [constants.PROGRESS]: { status: 'CANCELLED' },
    loading: false,
  }));
};
