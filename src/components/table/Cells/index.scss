@import "scss/colors.scss";
@import 'scss/mixins.scss';

.ec-root-page {
.ReactTable.app-table {
  .cell-table-expander {
    cursor: inherit;
    padding: 0;
    text-align: center;
    user-select: none;
    font-size: 15px;
    font-weight: 600;
    color: var(--semantic-color-content-interactive-primary-default);

    .icon-cont {
      height: 17px;
      width: 17px;
      background: $blue9;
      border-radius: 50%;
      margin: 0 auto;

      .svg-inline--fa {
        bottom: 2px;
        position: relative;
        text-align: center;
        vertical-align: middle;

        &.fa-angle-right {
          left: 1px;
        }
      }
    }
  }

  .cell-table-link {
    color: var(--semantic-color-content-interactive-primary-default);
    .svg-inline--fa {
      margin-right: 7px;
    }
    a {
      color: inherit;
      text-decoration: none;
    }
  }

  .cell-table-text-list {
    max-width: 100%;

    .icon-cont.elipsis {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: var(--semantic-color-content-interactive-primary-default);
      font-size: 20px;
    }
    .icon-cont.elipsis>span {
      max-width: 100%;
      color: $black;
      font-size: 13px;
    }
  }

  .cell-table-ux-score {
    @include DisplayFlex;
    align-items: center;
    .score-number {
      margin-right: 5px;
      color: $grey15;
    }

    .score-bar-small {
      align-self: center;
      flex-grow: 1;
      margin: 0 10px;
      min-width: 110px;
    }
  }

  .cell-table-action {
    .disable-icon {
      color: $grey4;
    }
    .eye-icon, .pencil-icon, .delete-icon {
      color: var(--semantic-color-content-interactive-primary-default);
      cursor: pointer;
    }
    .delete-icon{
      margin-left: 7px,
    }
  }

  .predifined-cell {
    margin: 0 auto;
  }

  .cell-status > div {
    display: inline-block;
    padding: 4px;
    border-radius: 5px;
    svg {
      margin-right: 4px;
    }
    &.enabled {
      background: $green6;
      color: $green2;
    }
    &.disabled {
      background: var(—surface-fields-disabled);
      color: var(--semantic-color-content-interactive-primary-disabled);
    }
  }
}
}