import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';

import { noop } from 'utils/lodash';

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCircle, faSearch } from '@fortawesome/free-solid-svg-icons';

import Search from 'components/search';
import FilterActiveInactive from 'components/filterActiveInactive';
import FilterActiveDisabled from 'components/filterActiveDisabled';

import './index.scss';

const defaultProps = {
  column: {},
  columnRef: null,
  setAllFilters: noop,
};

function FilterContainer({
  column: {
    id, defaultCanFilter = false, filterValue, setFilter, meta,
  },
  columnRef,
  setAllFilters,
}) {
  const [showInput, setShowInput] = useState(false);
  const [searchElementPosition, setSearchElementPosition] = useState({});

  const { filterActiveInactive, filterActiveDisabled } = meta || {};

  useEffect(() => {
    if (columnRef && columnRef.current && showInput) {
      const { left, top } = columnRef.current.getBoundingClientRect();

      setSearchElementPosition({
        left: left - 15,
        top: top + 38,
      });
    } else {
      setSearchElementPosition({});
    }
  }, [columnRef.current, showInput]);

  const onShowInputClick = () => {
    setShowInput(true);
  };

  const onSearchHide = () => {
    setShowInput(false);
  };

  const onSearchClick = (searchKey) => {
    setAllFilters([]);
    setFilter(searchKey);
  };

  if (!defaultCanFilter) {
    return null;
  }

  return (
    <div key={id} className="table-filter-container">
      <div className="icon-container">
        <FontAwesomeIcon
          icon={faSearch}
          className={`${filterValue || showInput ? 'active' : ''}`}
          onClick={onShowInputClick} />
        {filterValue && <FontAwesomeIcon icon={faCircle} onClick={onShowInputClick} className="applied-indicator" />}
      </div>

      <FilterActiveInactive
        show={showInput && filterActiveInactive && !filterActiveDisabled}
        value={filterValue}
        onSearchClick={onSearchClick}
        containerClass="table-search-container"
        containerStyle={searchElementPosition}
        onSearchHide={onSearchHide}
        inputClass="table-search-input" />

      <FilterActiveDisabled
        show={showInput && !filterActiveInactive && filterActiveDisabled}
        value={filterValue}
        onSearchClick={onSearchClick}
        containerClass="table-search-container"
        containerStyle={searchElementPosition}
        onSearchHide={onSearchHide}
        inputClass="table-search-input" />

      <Search
        show={showInput && !filterActiveInactive && !filterActiveDisabled}
        value={filterValue}
        onSearchClick={onSearchClick}
        containerClass="table-search-container"
        containerStyle={searchElementPosition}
        onSearchHide={onSearchHide}
        inputClass="table-search-input" />
    </div>
  );
}

FilterContainer.defaultProps = defaultProps;

FilterContainer.propTypes = {
  column: PropTypes.objectOf(Object),
  columnRef: PropTypes.objectOf(Object),
  setAllFilters: PropTypes.func,
};

export default FilterContainer;
