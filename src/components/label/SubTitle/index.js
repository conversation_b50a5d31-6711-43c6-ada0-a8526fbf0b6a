// @flow

import React from 'react';
import PropTypes from 'prop-types';
import { Trans, withTranslation } from 'react-i18next';

import './index.scss';

function SubTitle(props) {
  const { title } = props;

  return (
    <h2 className="sub-title"><Trans>{title}</Trans></h2>
  );
}

SubTitle.propTypes = {
  title: PropTypes.string,
};

SubTitle.defaultProps = {
  title: '',
};

export default withTranslation()(SubTitle);
