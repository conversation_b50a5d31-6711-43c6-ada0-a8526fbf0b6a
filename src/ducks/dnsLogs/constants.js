// this will be hooked onto the relative state tree.
import { BASE_API_PATH } from 'config';
import { orderCustomizeColumns, verifyConfigData } from 'utils/helpers';

export const REDUCER_KEY = 'dnsLogs';

export const API_ENDPOINT = `${BASE_API_PATH}/v1/uem/dnsLogs`;

export const TXN_DATA_API_ENDPOINT = `${BASE_API_PATH}/v1/transactionData/dnsRequest`;
export const TXN_DATA_WEB_API_ENDPOINT = `${BASE_API_PATH}/v1/transactionData/web`;
export const TXN_DATA_DNS_LOGS_API_ENDPOINT = `${BASE_API_PATH}/v1/transactionData/dns`;

export const EXPORT_DNS_JOB_API_ENDPOINT = `${BASE_API_PATH}/v1/transactionData/exportDnsRequest`;
export const EXPORT_DNS_DATA_API_ENDPOINT = `${BASE_API_PATH}/v1/transactionData/exportDns`;

export const DATA_ERROR = 'error';
export const DATA_LOADING = 'loading';
export const DATA_TABLE = 'dnslogstabledata';
export const DATA = 'data';

// this will store data to create and edit form
export const APP_DATA = 'appData';
export const COLUMNS = 'dnsColumns';
export const CURRENT_FILTERS = 'currentFilters';
export const DONWLOAD_CSV_DATA = 'downloadCsvData';
export const DOWNLOAD_CSV = 'downloadCSV';
export const FILTERS = 'filters';
export const IS_DONWLOAD = 'isDownload';
export const IS_SORTED = 'isSorted';
export const LOCATIONS = 'locations';
export const NO_OF_RECORDS = 'noOfRecords';
export const PROGRESS = 'progress';
export const SHOW_ACCORDION = 'showAccordion';
export const SHOW_FILTERS = 'showFilters';
export const SHOW_SEARCH = 'showSearch';
export const SORTABLE = 'sortable';
export const STATE_FORM = 'form';

const sgAppData = [{
  id: '1',
  valid: true,
}];

const local = localStorage.getItem('configData');
const configData = (local && local.length) ? JSON.parse(local) : {};
const disableDnsGateway = verifyConfigData({ configData, key: 'disableDnsGateway' });

export const dnsColumns = [{
  exportId: null,
  key: 'idx',
  name: 'NO',
  data: {},
  width: 60,
  draggable: false,
  resizable: true,
  checkbox: true,
  sortable: false,
  visible: true,
}, {
  key: 'eventTimestamp',
  exportId: 'EVENT_TIME',
  name: 'Event Time', // 'Location',
  data: {},
  draggable: true,
  resizable: true,
  checkbox: true,
  sortable: false,
  visible: true,
}, {
  key: 'locationName',
  exportId: 'LOCATION',
  name: 'LOCATION', // 'Location',
  data: {},
  draggable: true,
  resizable: true,
  checkbox: true,
  sortable: false,
  visible: true,
},
{
  key: 'clientIp',
  exportId: 'CLIENT_IP', // no support from API
  name: 'CLIENT_IP', // 'Client IP',
  data: {},
  draggable: true,
  resizable: true,
  checkbox: false,
  sortable: false,
  visible: false,
},
{
  key: 'requestedDomain',
  exportId: 'REQUESTED_DOMAIN',
  name: 'REQUESTED_DOMAIN', // 'Requested Domain',
  data: {},
  draggable: true,
  resizable: true,
  checkbox: true,
  sortable: false,
  visible: true,
}, {
  key: 'serverIp',
  exportId: 'SERVER_IP', // no support from API
  name: 'SERVER_IP', // 'Server IP serverIp',
  data: {},
  draggable: true,
  resizable: true,
  checkbox: false,
  sortable: false,
  visible: false,
}, {
  key: 'reqRuleName',
  exportId: 'REQ_RULE_NAME',
  name: 'REQ_RULE_NAME', // 'Request Rule Name',
  data: {},
  draggable: true,
  resizable: true,
  checkbox: true,
  sortable: false,
  visible: true,
}, {
  key: 'resRuleName',
  exportId: 'RES_RULE_NAME',
  name: 'RES_RULE_NAME', // 'Res Rule Name',
  data: {},
  draggable: true,
  resizable: true,
  checkbox: true,
  sortable: false,
  visible: true,
}, ...(disableDnsGateway ? []
  : [{
    key: 'gatewayName',
    exportId: 'EC_DNS_GW_NAME',
    name: 'EC_DNS_GW_NAME', // 'Res Rule Name',
    data: {},
    draggable: true,
    resizable: true,
    checkbox: true,
    sortable: false,
    visible: true,
  }, {
    key: 'gatewayFlag',
    exportId: 'EC_DNS_GW_FLAG',
    name: 'EC_DNS_GW_FLAG', // 'Res Rule Name',
    data: {},
    draggable: true,
    resizable: true,
    checkbox: true,
    sortable: false,
    visible: true,
  }]), {
  key: 'requestAction',
  exportId: 'REQUEST_ACTION',
  name: 'REQ_ACTION', // 'Request Action[Allow, Block, Resolve By ZPA ]',
  data: {},
  draggable: true,
  resizable: true,
  checkbox: true,
  sortable: false,
  visible: true,
},
{
  key: 'responseAction',
  exportId: 'RESPONSE_ACTION',
  name: 'RESPONSE_ACTION',
  data: {},
  draggable: true,
  resizable: true,
  checkbox: true,
  sortable: false,
  visible: true,
}, {
  key: 'resolvedIpOrName',
  exportId: 'RESOLVED_IP_OR_NAME',
  name: 'RESOLVED_IP', // 'Resolved IP',
  data: {},
  draggable: true,
  resizable: true,
  checkbox: true,
  sortable: false,
  visible: true,
}, {
  key: 'dnsRequestType',
  exportId: 'DNS_REQUEST_TYPE',
  name: 'DNS_REQ_TYPE', // 'DNS Request Type: Is it IPv6/IPv4/FQDN',
  data: {},
  draggable: true,
  resizable: true,
  checkbox: true,
  sortable: false,
  visible: true,
}, {
  key: 'dnsErrorCode',
  exportId: 'DNS_ERROR_CODE',
  name: 'DNS_ERROR_STATUS', // 'DNS Error Status[Response that DNS server sends to EC]',
  data: {},
  draggable: true,
  resizable: true,
  checkbox: true,
  sortable: false,
  visible: true,
}, {
  key: 'serverPort',
  exportId: 'SERVER_PORT', // no support from API
  name: 'SERVER_PORT', // 'Server Port[ DNS Server Port ]',
  data: {},
  draggable: true,
  resizable: true,
  checkbox: false,
  sortable: false,
  visible: false,
}, {
  key: 'protocolType',
  exportId: 'PROTOCOL_TYPE',
  name: 'PROTOCOL_TYPE', // 'Protocol Type[UDP, TCP]',
  data: {},
  draggable: true,
  resizable: true,
  checkbox: true,
  sortable: false,
  visible: true,
}, {
  key: 'logTimestamp',
  exportId: 'LOG_TIME',
  name: 'LOGGED_TIME', // 'Logged Time',
  data: {},
  draggable: true,
  resizable: true,
  checkbox: true,
  sortable: false,
  visible: true,
}, {
  key: 'requestDuration',
  exportId: 'REQUEST_DURATION',
  name: 'REQ_DURATION', // 'Request Duration',
  data: {},
  draggable: true,
  resizable: true,
  checkbox: true,
  sortable: false,
  visible: true,
}, {
  key: 'ecName',
  exportId: 'EC_INSTANCE_NAME',
  name: 'CONNECTOR_INSTANCE', // 'Edgeconnector Name',
  data: {},
  draggable: true,
  resizable: true,
  checkbox: false,
  sortable: false,
  visible: false,
}, {
  key: 'lanTx',
  exportId: 'LAN_TX', // no support from API
  name: 'LAN_TX', // 'LAN Tx',
  data: {},
  draggable: true,
  resizable: true,
  checkbox: false,
  sortable: false,
  visible: false,
}, {
  key: 'lanRx',
  exportId: 'LAN_RX', // no support from API
  name: 'LAN_RX', // 'LAN Rx',
  data: {},
  draggable: true,
  resizable: true,
  checkbox: false,
  sortable: false,
  visible: false,
}, {
  key: 'ecGroupName',
  exportId: 'EC_GROUP_NAME',
  name: 'CONNECTOR_GROUP',
  data: {},
  draggable: true,
  resizable: true,
  checkbox: false,
  sortable: false,
  visible: false,
}, {
  key: 'forwardType',
  exportId: 'FORWARD_TYPE',
  name: 'FWD_METHOD', // 'Forwarding Method',
  data: {},
  draggable: true,
  resizable: true,
  checkbox: true,
  sortable: false,
  visible: true,
}, {
  key: 'ecVmName',
  exportId: 'EC_VM_NAME',
  name: 'CONNECTOR_VM',
  data: {},
  draggable: true,
  resizable: true,
  checkbox: false,
  sortable: false,
  visible: false,
}, {
  key: 'platform',
  exportId: 'EC_PLATFORM',
  name: 'EC_PLATFORM',
  data: {},
  draggable: true,
  resizable: true,
  checkbox: false,
  sortable: false,
  visible: false,
}, {
  key: 'azureZone',
  exportId: 'EC_AZURE_AVAILABILITY_ZONE',
  name: 'AZURE_AVAILABILITY_ZONE',
  data: {},
  draggable: true,
  resizable: true,
  checkbox: false,
  sortable: false,
  visible: false,
}, {
  key: 'azureRegion',
  exportId: 'EC_AZURE_REGION',
  name: 'AZURE_REGION',
  data: {},
  draggable: true,
  resizable: true,
  checkbox: false,
  sortable: false,
  visible: false,
}, {
  key: 'gcpZone',
  exportId: 'EC_GCP_AVAILABILITY_ZONE',
  name: 'GCP_AVAILABILITY_ZONE',
  data: {},
  draggable: true,
  resizable: true,
  checkbox: false,
  sortable: false,
  visible: false,
}, {
  key: 'gcpRegion',
  exportId: 'EC_GCP_REGION',
  name: 'GCP_REGION',
  data: {},
  draggable: true,
  resizable: true,
  checkbox: false,
  sortable: false,
  visible: false,
}, {
  key: 'awsRegion',
  exportId: 'EC_AWS_REGION',
  name: 'AWS_REGION',
  data: {},
  draggable: true,
  resizable: true,
  checkbox: false,
  sortable: false,
  visible: false,
}, {
  key: 'awsZone',
  exportId: 'EC_AWS_AVAILABILITY_ZONE',
  name: 'AWS_AVAILABILITY_ZONE',
  data: {},
  draggable: true,
  resizable: true,
  checkbox: false,
  sortable: false,
  visible: false,
}, {
  key: 'accountId',
  exportId: 'EC_ACC_ID',
  name: 'ACCOUNT_ID',
  data: {},
  draggable: true,
  resizable: true,
  checkbox: false,
  sortable: false,
  visible: false,
}, {
  key: 'subscriptionId',
  exportId: 'EC_SUBSCRIPTION_ID',
  name: 'SUBSCRIPTION_ID',
  data: {},
  draggable: true,
  resizable: true,
  checkbox: false,
  sortable: false,
  visible: false,
}, {
  key: 'projectId',
  exportId: 'EC_PROJECT_ID',
  name: 'EC_PROJECT_ID',
  data: {},
  draggable: true,
  resizable: true,
  checkbox: false,
  sortable: false,
  visible: false,
}, {
  key: '',
  exportId: '',
  name: '',
  data: {},
  draggable: true,
  resizable: true,
  checkbox: true,
  sortable: false,
  visible: true,
}];

export const dfilters = [{
  value: 'locationName',
  label: 'LOCATION', // 'Location',
  data: {},
  order: 'none',
}, {
  value: 'requestedDomain',
  label: 'REQUESTED_DOMAIN', // 'Requested Domain',
  data: {},
  order: 'none',
}, {
  value: 'dnsRuleName',
  label: 'DNS_RULE_NAME', // 'Request Rule Name',
  data: {},
  order: 'none',
}, ...(disableDnsGateway ? []
  : [{
    value: 'dnsGwName',
    label: 'EC_DNS_GW_NAME', // 'Request Rule Name',
    data: {},
    order: 'none',
  }, {
    value: 'dnsGwFlag',
    label: 'EC_DNS_GW_FLAG', // 'Request Rule Name',
    data: {},
    order: 'none',
  }]), {
  value: 'requestAction',
  label: 'REQ_ACTION', // 'Request Action[Allow, Block, Resolve By ZPA ]',
  data: {},
  order: 'none',
}, {
  value: 'dnsRequestType',
  label: 'DNS_REQ_TYPE', // 'DNS Request Type: Is it IPv6/IPv4/FQDN',
  data: {},
  order: 'none',
}, {
  value: 'protocolType',
  label: 'PROTOCOL_TYPE', // 'Protocol Type[UDP, TCP]',
  data: {},
  order: 'none',
}, {
  value: 'requestDuration',
  label: 'REQ_DURATION', // 'Request Duration',
  data: {},
  order: 'none',
}, {
  value: 'ecName',
  label: 'CONNECTOR_NAME', // 'Edgeconnector Name',
  data: {},
  order: 'none',
}, {
  value: 'ecInstance',
  label: 'CONNECTOR_INSTANCE',
  data: {},
  order: 'none',
}, {
  value: 'ecVm',
  label: 'CONNECTOR_VM',
  data: {},
  order: 'none',
}, {
  value: 'forwardingTypes',
  label: 'FWD_METHOD',
  data: {},
  order: 'none',
}, {
  value: 'ecGroup',
  label: 'CONNECTOR_GROUP',
  data: {},
  order: 'none',
}, {
  value: 'inbytes',
  label: 'INBYTES', // 'inbytes',
  data: {},
  order: 'none',
}, {
  value: 'outbytes',
  label: 'OUTBYTES', // 'outbytes',
  data: {},
  order: 'none',
}, {
  value: 'platform',
  label: 'PLATFORM', // 'platform',
  data: {},
  order: 'none',
}, {
  value: 'awsRegion',
  label: 'AWS_REGION', // 'awsRegion',
  data: {},
  order: 'none',
}, {
  value: 'availabilityZone',
  label: 'AWS_AVAILABILITY_ZONE',
  data: {},
  order: 'none',
}, {
  value: 'azureRegion',
  label: 'AZURE_REGION',
  data: {},
  order: 'none',
}, {
  value: 'azureAvailabilityZone',
  label: 'AZURE_AVAILABILITY_ZONE',
  data: {},
  order: 'none',
}, {
  value: 'accountId',
  label: 'ACCOUNT_ID',
  data: {},
  order: 'none',
}, {
  value: 'gcpRegion',
  label: 'GCP_REGION',
  data: {},
  order: 'none',
}, {
  value: 'gcpAvailabilityZone',
  label: 'GCP_AVAILABILITY_ZONE',
  data: {},
  order: 'none',
}, {
  value: 'projectId',
  label: 'EC_PROJECT_ID',
  data: {},
  order: 'none',
}, {
  value: 'subscriptionId',
  label: 'SUBSCRIPTION_ID',
  data: {},
  order: 'none',
}];

export const dummyTestfilters = dfilters;

export const dlocs = [];

export const defaultFilters = {
  accountId: false,
  awsRegion: false,
  azureAvailabilityZone: false,
  azureRegion: false,
  dnsRequestType: false,
  dnsRuleName: false,
  ecGroup: false,
  ecInstance: false,
  ecName: false,
  ecVm: false,
  inbytes: false,
  locationName: false,
  outbytes: false,
  platform: false,
  protocolType: false,
  requestAction: false,
  requestDuration: false,
  requestedDomain: false,
  subscriptionId: false,
  zone: false,
};

export const RESET_FILTERS = {
  [APP_DATA]: sgAppData,
  [COLUMNS]: orderCustomizeColumns(dnsColumns),
  [CURRENT_FILTERS]: dummyTestfilters,
  [DATA_ERROR]: null, // or error object so we can process and show to user
  [DATA_LOADING]: false,
  [DONWLOAD_CSV_DATA]: [],
  [FILTERS]: dummyTestfilters,
  [IS_DONWLOAD]: false,
  [LOCATIONS]: dlocs,
  [NO_OF_RECORDS]: '1000',
  [SHOW_ACCORDION]: true,
  [SHOW_FILTERS]: true,
};

export const RESOURCE_DEFAULTS = {
  [APP_DATA]: sgAppData,
  [COLUMNS]: orderCustomizeColumns(dnsColumns),
  [CURRENT_FILTERS]: dfilters,
  [DATA_ERROR]: null, // or error object so we can process and show to user
  [DATA_LOADING]: false,
  [DATA_TABLE]: [],
  [DATA]: {},
  [DONWLOAD_CSV_DATA]: [],
  [DOWNLOAD_CSV]: 'DISPLAY',
  [FILTERS]: dfilters,
  [IS_DONWLOAD]: false,
  [IS_SORTED]: false,
  [LOCATIONS]: dlocs,
  [NO_OF_RECORDS]: '1000',
  [PROGRESS]: {},
  [SHOW_ACCORDION]: true,
  [SHOW_FILTERS]: defaultFilters,
  [SHOW_SEARCH]: false,
  [SORTABLE]: 1,
};

export const RESET_CSV_DOWNLOAD = {
  [DATA_TABLE]: [],
  [DONWLOAD_CSV_DATA]: [],
};

export const DEFAULTS = {
  ...RESOURCE_DEFAULTS,
};

export const DEFAULTS_BUT_ACCORDION = {
  ...RESOURCE_DEFAULTS,
  [SHOW_ACCORDION]: true,
};

// default state for this page/widget/component
export const DEFAULT_STATE = {
  ...DEFAULTS,
};

// For testing...to be used later
export const DUMMY_STORE_DATA = {
  [REDUCER_KEY]: {
    ...DEFAULT_STATE,
  },
};
