// @flow

import React from 'react';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlus } from '@fortawesome/pro-solid-svg-icons';

import './index.scss';

function AddNewButton(props) {
  const { label, clickCallback } = props;

  return (
    <button onClick={clickCallback} type="button" className="add-new-button">
      <FontAwesomeIcon icon={faPlus} />
      {' '}
      {label}
    </button>
  );
}

AddNewButton.propTypes = {
  label: PropTypes.node,
  clickCallback: PropTypes.func,
};

AddNewButton.defaultProps = {
  label: null,
  clickCallback: null,
};

export default AddNewButton;
