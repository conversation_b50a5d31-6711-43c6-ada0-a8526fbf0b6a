import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import { get } from 'utils/lodash';

import MultiDropdownWithAsyncSearch from 'components/dropDown/MultiDropdownWithAsyncSearch';
import {
  dropdownActions,
  selector,
  onSearchStringChange,
  onPerformSearch,
} from 'ducks/dropdowns/ec-group-report';
import labelHelper from './labelHelper';

export const getUnselectedValues = (state) => {
  const { selectedValueMap, data } = selector(state).dropdown;
  // map data to see if item is in selecteValues from Excluded Criteria dropdown
  // this is your unselected values
  const unselectedValues = data.map((item, index) => {
    const checked = !!selectedValueMap[get(item, 'id')];
    const disabled = false;
    return {
      original: { ...item, parent: 1 },
      id: get(item, 'id', index),
      name: get(item, 'name', ''),
      checked,
      disabled,
    };
  });
  return unselectedValues;
};

export const mapStateToProps = (state, ownProps) => {
  const { t, label } = ownProps;

  const dropdownState = selector(state).dropdown;
  const { selectedValues, values } = dropdownState;
  const dropdownLabels = labelHelper(t, selectedValues, values, 'TOOLTIP_USED_IN_EXCLUDED_CRITERIA', label || 'ANY');

  return {
    ...dropdownState,
    ...dropdownLabels,
    unselectedValues: getUnselectedValues(state),
  };
};

export const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    ...dropdownActions,
    onSearchStringChange,
    onPerformSearch,
  }, dispatch);

  return {
    actions,
  };
};

export default withTranslation()(connect(
  mapStateToProps,
  mapDispatchToProps,
)(MultiDropdownWithAsyncSearch));
