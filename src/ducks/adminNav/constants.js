// this will be hooked onto the relative state tree.
export const REDUCER_KEY = 'admin_nav';

export const DATA_ERROR = 'error';
export const DATA_LOADING = 'loading';
export const DATA = 'data';
export const TOGGLE_ADMIN_PANEL = 'toggleAdminPanel';

export const RESOURCE_DEFAULTS = {
  [DATA_ERROR]: null, // or error object so we can process and show to user
  [DATA_LOADING]: true,
  [DATA]: {},
  TOGGLE_ADMIN_PANEL: false,
};

export const DEFAULTS = {
  ...RESOURCE_DEFAULTS,
};

// default state for this page/widget/component
export const DEFAULT_STATE = {
  ...DEFAULTS,
};

// For testing...to be used later
export const DUMMY_STORE_DATA = {
  [REDUCER_KEY]: {
    ...DEFAULT_STATE,
  },
};
