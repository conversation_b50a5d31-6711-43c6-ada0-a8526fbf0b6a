import { createSelector } from 'reselect';
import { getFormValues, getFormMeta, getFormSyncErrors } from 'redux-form';

import * as constants from './constants';

export const baseSelector = (state) => state[constants.REDUCER_KEY];

export const loadingSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA_LOADING],
);

export const errorSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA_ERROR],
);

export const dataSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA] || {},
);

export const appDataSelector = createSelector(
  baseSelector,
  (state) => state[constants.APP_DATA] || {},
);

export const dataTableSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA_TABLE] || {},
);

export const modalLoadingSelector = createSelector(
  baseSelector,
  (state) => state[constants.MODAL_LOADING],
);

export const featurePermissionsSelector = createSelector(
  appDataSelector,
  (state) => state[constants.FEATURE_PERMISSIONS] || {},
);

export const addRoleSelector = createSelector(
  baseSelector,
  (state) => state[constants.ADD_ROLE],
);

export const viewRoleSelector = createSelector(
  baseSelector,
  (state) => state[constants.VIEW_ONLY],
);

export const modalTitleRoleSelector = createSelector(
  baseSelector,
  (state) => state[constants.MODAL_TITLE],
);

export const selectedRowSelector = createSelector(
  baseSelector,
  (state) => state[constants.SELECTED_ROW],
);

export const ipAddressListSelector = createSelector(
  baseSelector,
  (state) => state[constants.IP_ADDRESSES_DATA] || [],
);

export const columnsSelector = createSelector(
  baseSelector,
  (state) => state[constants.COLUMNS] || [],
);

export const usersRowSelector = createSelector(
  baseSelector,
  (usersState) => usersState.data || [],
);

export const formValuesSelector = (state) => getFormValues('editApiKeyForm')(state);
export const formMetaSelector = (state) => getFormMeta('editApiKeyForm')(state);
export const formSyncErrorsSelector = (state) => getFormSyncErrors('editApiKeyForm')(state);

export const formDataSelector = createSelector(
  baseSelector,
  formValuesSelector,
  (data, formValues) => formValues || data,
);

// alway return top level selector
export default baseSelector;
