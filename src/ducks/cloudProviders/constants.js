// this will be hooked onto the relative state tree.
import { BASE_API_PATH } from 'config';

export const REDUCER_KEY = 'cloudproviders';
export const API_ENDPOINT = `${BASE_API_PATH}/v1/cloudProvider`;
export const API_PUT_ENDPOINT = `${BASE_API_PATH}/v1/cloudProvider`;
export const API_DELETE_ENDPOINT = (id) => `${BASE_API_PATH}/v1/cloudProvider/${id}`;

export const DATA_ERROR = 'error';
export const DATA_LOADING = 'loading';
export const MODAL_LOADING = 'modalLoading';
export const DATA_CP_TABLE = 'cloudProvidersAWSData';
export const DATA = 'data';

// this will store data to create and edit form
export const APP_DATA = 'appData';
export const SHOW_FORM = 'showForm';
export const CUSTOM_APPTYPE = 'CUSTOM';
export const EXPANDED_APPS = 'expandedApps';
export const CLOUD_TYPE = 'cloudType';
export const FORM_TITLE = 'formTitle';
export const SHOW_DELETE_FORM = 'showDeleteForm';
export const SELECTED_ROW_ID = 'selectedRowID';
export const SHOW_VIEW_FORM = 'showViewForm';

export const DEFAULT_FORM_DATA = {
  name: '',
  active: true,
  type: CUSTOM_APPTYPE,
};

const sgAppData = [{
  id: '1',
  valid: true,
  value: 'Destination IP Group1',
}];

const awsData = [];

export const RESOURCE_DEFAULTS = {
  [DATA_ERROR]: null, // or error object so we can process and show to user
  [DATA_LOADING]: true,
  [APP_DATA]: sgAppData,
  [SHOW_FORM]: false,
  [DATA]: {},
  [DATA_CP_TABLE]: awsData,
  [EXPANDED_APPS]: [],
  [CLOUD_TYPE]: 'AWS',
  [FORM_TITLE]: 'ADD_CLOUD_APP_PROVIDER',
  [MODAL_LOADING]: false,
  [SHOW_DELETE_FORM]: false,
  [SHOW_VIEW_FORM]: false,
  [SELECTED_ROW_ID]: null,
};

export const DEFAULTS = {
  ...RESOURCE_DEFAULTS,
};

// default state for this page/widget/component
export const DEFAULT_STATE = {
  ...DEFAULTS,
};

// For testing...to be used later
export const DUMMY_STORE_DATA = {
  [REDUCER_KEY]: {
    ...DEFAULT_STATE,
  },
};
