import React, { useEffect } from 'react';
import { EntityDropdown } from 'components/entityDropdown';
import { useDispatch, useSelector } from 'react-redux';
import { loader } from 'ducks/dropdowns/dnsGateway';
import { isEmpty } from 'utils/lodash';

export function DnsGatewayDropdown(props) {
  const dispatch = useDispatch();
  const { data, hasMorePages } = useSelector((state) => state.dnsGatewayDropdown);
  useEffect(() => {
    dispatch(loader(true));
  }, []);

  return (isEmpty(data))
    ? <EntityDropdown data={[]} {...props} />
    : (
      <EntityDropdown
        {...props}
        data={data}
        hasNextPage={hasMorePages}
        loadNextPage={() => dispatch(loader(false))} />
    );
}

export default DnsGatewayDropdown;
