@import 'scss/colors.scss';

.ec-root-page {
.data-error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  min-height: 50px;
  
  .text {
    color: var(--semantic-color-content-interactive-primary-disabled);
    text-align: center;
  }

  .small {
    font-size: 11px;
  }

  .medium {
    font-size: 20px;
  }

  .large {
    font-size: 30px;
  }

  .xtra-large {
    font-size: 40px;
  }
}
}