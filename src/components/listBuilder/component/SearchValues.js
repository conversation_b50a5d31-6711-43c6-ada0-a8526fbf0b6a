import React from 'react';
import PropTypes from 'prop-types';
import SimpleSearchInput from 'components/Input/SimpleSearchInput';

function SearchedValues({ onSearchFilter }) {
  return (
    <div className="list-builder-search-container">
      <SimpleSearchInput
        withButton
        onKeyPressCb={onSearchFilter} />
    </div>
  );
}

SearchedValues.propTypes = {
  onSearchFilter: PropTypes.func,
};

export default SearchedValues;
