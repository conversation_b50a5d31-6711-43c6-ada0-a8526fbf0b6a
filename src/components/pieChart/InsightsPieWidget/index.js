import React, { useState } from 'react';
import PropTypes from 'prop-types';
import i18n from 'utils/i18n';
import { ResponsivePie } from '@nivo/pie';
import { withTranslation } from 'react-i18next';
import GenericErrorMessage from 'components/errors/ServerError/GenericErrorMessage';

import { getFormatedNumberWithUnit } from 'utils/helpers/getFormatedData';

import './index.scss';

function ToolTipMessageAction({ selectedDataKey, actionToolTip, DrillDownAction }) {
  return ((selectedDataKey && actionToolTip)
    ? <DrillDownAction filter={selectedDataKey} />
    : <></>);
}

ToolTipMessageAction.propTypes = {
  selectedDataKey: PropTypes.string,
  actionToolTip: PropTypes.bool,
  DrillDownAction: PropTypes.shape({}),
};

function ToolTipMessage({ selectedDataKey, actionToolTip }) {
  return (selectedDataKey && !actionToolTip)
    ? (
      <div id="tooltip" className="tooltip">
        <div className="ec-tooltip-container">
          <div id="tooltip-top" className="tooltip-top tooltip-top-text">{selectedDataKey}</div>
          <div id="tooltip-bottom" className="tooltip-bottom tooltip-bottom-text">{i18n.t('CLICK_FOR_MORE_INFO')}</div>
        </div>
      </div>
    )
    : <></>;
}

ToolTipMessage.propTypes = {
  selectedDataKey: PropTypes.string,
  actionToolTip: PropTypes.bool,
};

const margin = {
  top: 30,
  right: 50,
  bottom: 30,
  left: 50,
};
const styles = {
  root: {
    fontFamily: 'consolas, sans-serif',
    textAlign: 'center',
    position: 'relative',
    marginLeft: '5%',
    width: '100%',
    height: '35em',
    marginTop: '2em',
  },
  overlay: {
    position: 'absolute',
    right: '0.8em',
    bottom: '3.75em',
    left: margin.left,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: 55,
    color: '#9a9190',
    textAlign: 'center',
    // This is important to preserve the chart interactivity
    pointerEvents: 'none',
  },
  totalLabel: {
    color: '#818181',
    fontSize: '13px',
    lineHeight: '20px',
  },
};

const calculateRatio = (data) => {
  let count = 0;
  data.map((e) => {
    count += e.value;
    return count;
  });
  return count;
};

const localize = (data, t) => {
  return t(data);
};

const whatPercent = (x, ofY) => {
  return Math.round((x / ofY) * 100);
};

export function InsightsPieWidget(props) {
  const {
    data,
    colors,
    legendsPos,
    t,
    isInteractive,
    enableArcLabels,
    enableArcLinkLabels,
    DrillDownAction,
  } = props;
  const [selectedDataKey, setSelectedDataKey] = useState('');
  const [actionToolTip, setActionToolTip] = useState(false);
  const [nodeTotal, setNodeTotal] = useState(-1);
  const [nodePercentage, setPercentage] = useState(-1);
  
  if (Array.isArray(data) && (!data.length || (data && data[0] && !data[0].value))) {
    return (
      <GenericErrorMessage />
    );
  }

  const total = calculateRatio(data);
  const locData = data.map((e) => {
    e.id = localize(e.id, t);
    e.ilabeld = t(e.label);
    return e;
  });

  let formatedNumber = getFormatedNumberWithUnit(total, {
    type: data[0].type,
  });

  // const onHandleMouseEnter = (event, axisDetail) => {
  //   if (!actionToolTip) setSelectedDataKey(axisDetail);
  //   const newStyle = initializeStyles(locData);
  //   setPieStyles(() => newStyle.map(item => (item.id === axisDetail
  //     ? { ...item, opacity: 1 }
  //     : { ...item, opacity: 0.3 })));
  //   // showTooltipFromEvent(<ToolTipMessage id="total" selectedDataKey={axisDetail} />, event);
  // };

  const handleOnClick = (e) => {
    const { id } = e;
    setActionToolTip(!actionToolTip);
    setSelectedDataKey(id);
  };

  const handleOnClickDiv = () => {
    setActionToolTip(!actionToolTip);
  };
  
  return (
    <div role="button" className="insight-pie" onClick={handleOnClickDiv} onKeyPress={(e) => e} tabIndex={0}>
      <div style={styles.root}>
        <div className="line-chart-field-tooltip-container">
          <ToolTipMessageAction
            DrillDownAction={DrillDownAction}
            selectedDataKey={selectedDataKey}
            actionToolTip={actionToolTip} />
        </div>
        <ResponsivePie
          data={locData}
          margin={{
            top: 20, right: 20, bottom: 20, left: 20,
          }}
          innerRadius={0.55}
          padAngle={1}
          cornerRadius={0}
          colors={colors}
          borderWidth={1}
          borderColor={{ from: 'color', modifiers: [['darker', 0.2]] }}
          fit
          startAngle={-90}
          isInteractive={isInteractive && !actionToolTip}
          enableArcLabels={enableArcLabels}
          enableArcLinkLabels={enableArcLinkLabels}
          activeOuterRadiusOffset={16}
          enableRadialLabels={false}
          radialLabelsSkipAngle={10}
          radialLabelsTextXOffset={6}
          radialLabelsTextColor="#333333"
          radialLabelsLinkOffset={0}
          radialLabelsLinkDiagonalLength={16}
          radialLabelsLinkHorizontalLength={24}
          radialLabelsLinkStrokeWidth={1}
          radialLabelsLinkColor={{ from: 'color' }}
          slicesLabelsSkipAngle={10}
          enableSlicesLabels={false}
          // slicesLabelsTextColor="#fdfdfd"
          slicesLabelsTextColor={{ from: 'color', modifiers: [['opacity', '0.5']] }}
          animate
          motionStiffness={90}
          motionDamping={15}
          onClick={handleOnClick}
          onMouseEnter={(node) => {
            const { value } = node;
            formatedNumber = getFormatedNumberWithUnit(value, {
              type: data[0].type,
            });
            setNodeTotal(getFormatedNumberWithUnit(value, {
              type: data[0].type,
            }));
            setPercentage(whatPercent(value, total));
          }}
          onMouseLeave={() => {
            setNodeTotal(-1);
            setPercentage(-1);
          }}
          tooltip={({ datum }) => {
            const {
              id, value,
            } = datum || {};
            
            return <ToolTipMessage selectedDataKey={`${whatPercent(value, total)}% - ${id}`} />;
          }}
          legends={[
            {
              onClick: (d) => { handleOnClick(d); },
              anchor: legendsPos,
              direction: 'column',
              translateY: 0,
              translateX: -20,
              itemHeight: 26,
              itemWidth: 54,
              borderRadius: '23px',
              itemTextColor: '#999',
              symbolSize: 23,
              symbolShape: 'square',
              effects: [
                {
                  on: 'hover',
                  style: {
                    itemTextColor: '#000',
                  },
                },
              ],
            },
          ]} />
        <div style={styles.overlay}>
          <span className="insider">
            {`${nodePercentage > 0 ? nodePercentage : 100}%`}
          </span>
          <span style={styles.totalLabel}>{nodeTotal > 0 ? nodeTotal : formatedNumber}</span>
        </div>
      </div>
    </div>
  );
}

InsightsPieWidget.propTypes = {
  data: PropTypes.arrayOf(PropTypes.shape({})),
  colors: PropTypes.arrayOf(PropTypes.string),
  legendsPos: PropTypes.string,
  t: PropTypes.func,
  isInteractive: PropTypes.bool,
  enableArcLabels: PropTypes.bool,
  enableArcLinkLabels: PropTypes.bool,
  DrillDownAction: PropTypes.shape({}),
};

InsightsPieWidget.defaultProps = {
  data: [],
  colors: [],
  legendsPos: 'bottom-left',
  t: (str) => str,
  isInteractive: true,
  enableArcLabels: false,
  enableArcLinkLabels: false,
  DrillDownAction: null,
};

export default withTranslation()(InsightsPieWidget);
