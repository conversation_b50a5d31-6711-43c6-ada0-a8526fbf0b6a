import { genericInterface } from 'utils/http';
import { createAction, loading } from 'ducks/generics';
import genericDropdown from 'ducks/generics/dropdownApiSearch';
import { notifyError } from 'ducks/notification';
import {
  get, isEmpty, uniqBy,
} from 'utils/lodash';
import actionTypes from './action-types';
import selector, * as selectors from './selectors';
import * as constants from './constants';

export { REDUCER_KEY } from './constants';

const actionTypeExists = Object.keys(actionTypes).reduce((hash, key) => ({
  ...hash,
  [actionTypes[key]]: true,
}), {});

export const reducer = (state = constants.DEFAULT_STATE, action = {}) => {
  // Simplifyeverything: actions provide a payload, and we merge that payload.
  if (actionTypeExists[action.type]) {
    return {
      ...state,
      ...action.payload,
    };
  }
  return state;
};
export default reducer;

const http = genericInterface(constants.API_ENDPOINT);

const boundLoading = loading(actionTypes.DATA_LOADING);
const boundError = createAction(actionTypes.DATA_LOAD_ERROR);
const boundDataLoadSuccess = createAction(actionTypes.DATA_LOAD_SUCCESS);
// const boundModalLoading = createAction(actionTypes.MODAL_LOADING);

const onSearchStringChange = (searchString) => (dispatch) => {
  dispatch(boundLoading({ searchString }));
};

export const loader = (initializeProps, search = '') => async (dispatch, getState) => {
  const state = getState();
  if (initializeProps) {
    state.appSegmentGroupDropdown.pageNum = 1;
    state.appSegmentGroupDropdown.data = [];
    state.appSegmentGroupDropdown.hasMorePages = false;
    state.appSegmentGroupDropdown.loading = false;
    state.appSegmentGroupDropdown.searchString = search;
  }
  const { pageNum, pageSize, searchString } = state.appSegmentGroupDropdown;
  const param = search
    ? { page: pageNum, pageSize, search }
    : { page: pageNum, pageSize };
  dispatch(
    boundLoading({
      [constants.DATA]: [],
      ...constants.DEFAULT_STATE,
      searchString,
      loading: true,
    }),
  );
  try {
    const domains = await http.read(null, {
      params: param,
    });
    const { data } = domains || [];
  
    const dataWithId = data.map((x) => ({ id: x.name, ...x }));
  
    const combinedData = [
      ...state.appSegmentGroupDropdown.data,
      ...dataWithId];
    const updatedData = uniqBy(combinedData, 'id');
  
    dispatch(boundDataLoadSuccess({
      [constants.DATA]: updatedData,
      [constants.DROPDOWN.PAGE_NUMBER]: pageNum + 1,
      [constants.DROPDOWN.HAS_MORE_PAGES]: data.length >= pageSize && pageSize > 0,
      searchString: search,
      loading: false,
    }));
  } catch (error) {
    const errorMsg = get(error, 'response.data.message');
    dispatch(boundError({ errorMsg, loading: false }));
    if (isEmpty(errorMsg)) dispatch(notifyError('ERROR_LOADING_DOMAINS'));
    else dispatch(notifyError(errorMsg));
  }
};

const onPerformSearch = () => (dispatch, getState) => {
  const state = getState();
  const { searchString } = state.appSegmentGroupDropdown;
  dispatch(loader(true, searchString));
  dispatch(boundDataLoadSuccess({
    isOpen: true,
  }));
};

const dropdownActions = genericDropdown.createActions({ actionTypes, selector, http });
export {
  selector,
  selectors,
  dropdownActions,
  onSearchStringChange,
  onPerformSearch,
};
