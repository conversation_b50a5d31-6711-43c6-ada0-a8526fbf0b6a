import React from 'react';
import { render, fireEvent } from '@testing-library/react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faEraser } from '@fortawesome/pro-solid-svg-icons';
import ClearSearchAndSort from './ClearSearchAndSort';

jest.mock('@fortawesome/react-fontawesome', () => ({
  FontAwesomeIcon: jest.fn(() => <div>FontAwesomeIcon</div>),
}));

describe('ClearSearchAndSort component', () => {
  const props = {
    label: 'Clear Search and Sort',
    onActionCb: jest.fn(),
    tabIndex: 1,
    tooltip: 'Clear Search and Sort tooltip',
    isVisible: true,
  };

  it('renders correctly', () => {
    const { getByText, getByRole } = render(<ClearSearchAndSort {...props} />);
    expect(getByText('Clear Search and Sort')).toBeInTheDocument();
    expect(getByRole('button')).toBeInTheDocument();
    expect(getByRole('button')).toHaveAttribute('tabindex', '1');
  });

  it('calls onActionCb when clicked', () => {
    const { getByRole } = render(<ClearSearchAndSort {...props} />);
    const button = getByRole('button');
    fireEvent.click(button);
    expect(props.onActionCb).toHaveBeenCalledTimes(1);
  });

  it('calls onActionCb when key pressed', () => {
    const { getByRole } = render(<ClearSearchAndSort {...props} />);
    const button = getByRole('button');
    fireEvent.keyPress(button, { key: 'Enter', code: 'Enter' });
    expect(props.onActionCb).toHaveBeenCalledTimes(1);
  });

  it('renders FontAwesomeIcon', () => {
    const { getByText } = render(<ClearSearchAndSort {...props} />);
    expect(getByText('FontAwesomeIcon')).toBeInTheDocument();
  });

  it('hides when isVisible is false', () => {
    const { queryByText } = render(<ClearSearchAndSort {...props} isVisible={false} />);
    expect(queryByText('Clear Search and Sort')).toHaveClass('hidden');
  });
});
