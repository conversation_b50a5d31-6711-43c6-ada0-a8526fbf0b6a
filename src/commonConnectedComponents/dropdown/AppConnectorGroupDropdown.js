/* eslint-disable react/jsx-handler-names */
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import Loading from 'components/spinner/Loading';
import { PropTypes } from 'prop-types';
import { change, updateSyncErrors } from 'redux-form';
import { loader, handleSearchStringChange, perfomSearch } from 'ducks/dropdowns/app-connector-group';
import selectors from 'ducks/dropdowns/app-connector-group/selectors';
import AdvDropdownWithAsyncSearch from 'components/dropDown/AdvDropdownWithAsyncSearch';

export function AppConnectorGroupDropdown(props) {
  const dispatch = useDispatch();
  const selector = useSelector((state) => selectors(state).dropdown);
  const {
    data, hasMorePages, loading, searchString = '',
  } = selector;
  const { input, meta } = props;
  const { name, value } = input || {};
  const { form } = meta || {};
  
  useEffect(() => {
    dispatch(loader(true));
  }, []);

  return (
    <div className="entity-dropdown-container">
      <Loading loading={loading} />
      <AdvDropdownWithAsyncSearch
        {...props}
        styleClass="full-width"
        items={data}
        currentItems={[]}
        hasNextPage={hasMorePages}
        defaultValue={value}
        onPerformSearch={() => dispatch(perfomSearch())}
        onSearchStringChange={(search) => dispatch(handleSearchStringChange(search))}
        onClickCb={(item) => {
          dispatch(change(form, name, item));
          dispatch(updateSyncErrors(form));
        }}
        onChange={input.onChange}
        onBlur={input.onBlur}
        loading={loading}
        searchString={searchString} />
    </div>
  );
}

AppConnectorGroupDropdown.propTypes = {
  input: PropTypes.shape({}),
  meta: PropTypes.shape({}),
};
  
AppConnectorGroupDropdown.defaultProps = {
  input: {},
  meta: {},
};

export default AppConnectorGroupDropdown;
