import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { isEqual } from 'utils/lodash';
import {
  getActionsAndSelectors,
  initDropdown,
} from 'ducks/multiSelectDropdown';

import MultiSelectDropDownComponent from './multiSelectComponent';
import MultiSelectDropDownHOC from './MultiSelectDropDownHOC';

export class MultiSelectDropDown extends React.Component {
  constructor(props) {
    super(props);
    const { id, dataSrc, actions } = props;
    const isDynamic = typeof dataSrc === 'string';

    const {
      actions: childrenActions,
      selector,
    } = getActionsAndSelectors(id, isDynamic ? dataSrc : '');
    actions.initDropdown(id, isDynamic ? null : dataSrc);
    this.childrenActions = childrenActions;
    this.selector = selector;

    this.MultiSelectDropDownConnected = MultiSelectDropDownHOC({
      ...props,
      Component: MultiSelectDropDownComponent,
      actions: childrenActions,
      selector,
    });
  }

  shouldComponentUpdate(nextProps) {
    const { id, dataSrc, actions } = this.props;
    const isDynamic = typeof dataSrc === 'string';

    if (!isDynamic && !isEqual(nextProps.dataSrc, dataSrc)) {
      actions.initDropdown(id, nextProps.dataSrc);
    }
    return true;
  }

  render() {
    const { MultiSelectDropDownConnected } = this;
    return (
      <MultiSelectDropDownConnected {...this.props} />
    );
  }
}

MultiSelectDropDown.propTypes = {
  id: PropTypes.string.isRequired,
  actions: PropTypes.shape({
    initDropdown: PropTypes.func,
  }).isRequired,
  dataSrc: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.array,
  ]).isRequired,
};

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    initDropdown,
  }, dispatch);

  return {
    actions,
  };
};

export default connect(
  null,
  mapDispatchToProps,
)(MultiSelectDropDown);
