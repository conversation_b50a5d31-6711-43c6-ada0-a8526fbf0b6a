import React, { useEffect } from 'react';
import { EntityDropdown } from 'components/entityDropdown';
import { useDispatch, useSelector } from 'react-redux';
import PropTypes from 'prop-types';
import * as loginSelectors from 'ducks/login/selectors';
import { dropdownActions } from 'ducks/dropdowns/ip-pool';
import { isEmpty } from 'utils/lodash';
import { verifyConfigData } from 'utils/helpers';

const NETWORK_TRAFFIC = (disableDnsGateway) => [
  { id: 'ALLOW', name: 'BOTH_REQ_RESP_ALLOW' },
  { id: 'BLOCK', name: 'EITHER_REQ_RESP_BLOCK' },
  { id: 'REDIR_ZPA', name: 'DNS_RESOLVED_BY_ZPA' },
  ...(disableDnsGateway ? [] : [{ id: 'REDIR_REQ', name: 'REDIRECT_REQUEST' }]),
];

export function NetworkTrafficDropdown(props) {
  const dispatch = useDispatch();
  const { data } = useSelector((state) => state.ipPoolDropdown);
  const configData = useSelector((state) => loginSelectors.configDataSelector(state));
  const { defaultRule } = props;
  const disableDnsGateway = verifyConfigData({ configData, key: 'disableDnsGateway' });
  
  useEffect(() => {
    dispatch(dropdownActions.load());
  }, []);

  const isEmptyIpPoolList = isEmpty(data);
  const networkTrafficOptions = defaultRule ? NETWORK_TRAFFIC(disableDnsGateway)?.filter((x) => (['ALLOW', 'BLOCK'].includes(x?.id))) : NETWORK_TRAFFIC(disableDnsGateway);
  return <EntityDropdown data={!isEmptyIpPoolList ? networkTrafficOptions : networkTrafficOptions.filter((x) => x.id !== 'REDIR_ZPA')} {...props} />;
}

NetworkTrafficDropdown.propTypes = {
  isEmptyIpPoolList: PropTypes.bool,
  defaultRule: PropTypes.bool,
};

NetworkTrafficDropdown.defaultProps = {
  isEmptyIpPoolList: false,
  defaultRule: false,
};

export default NetworkTrafficDropdown;
