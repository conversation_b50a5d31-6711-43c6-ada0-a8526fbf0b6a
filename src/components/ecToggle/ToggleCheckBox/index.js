import React from 'react';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCheck, faTimes } from '@fortawesome/pro-solid-svg-icons';

function ToggleCheckBox(props = {}) {
  const {
    input,
    disabled,
    styleClass,
    checked,
  } = props;
  // To DO: When the value is true the first click is lost
  // After that the component is working perfect.

  return (
    <div className="check-box-container">
      <label htmlFor={input.name}>
        <input
          id={input.name}
          type="checkbox"
          disabled={disabled}
          checked={checked}
          className="hide"
          {...input} />
        <span className={`toggle ${disabled ? 'disabled' : ''}`}>
          <span className={`toggle-label left ${input.value ? 'on' : 'off'}`}><FontAwesomeIcon icon={faCheck} className={styleClass} /></span>
          <span className={`toggle-label right ${input.value ? 'on' : 'off'} `}><FontAwesomeIcon icon={faTimes} className={styleClass} /></span>
          <span className={`toggle-button ${input.value ? 'on' : 'off'}`}>&nbsp;</span>
        </span>
      </label>
    </div>
  );
}

ToggleCheckBox.defaultProps = {
  disabled: false,
  styleClass: '',
};

ToggleCheckBox.propTypes = {
  disabled: PropTypes.bool,
  id: PropTypes.string,
  checked: PropTypes.bool,
  input: PropTypes.shape({
    name: PropTypes.string,
    value: PropTypes.any,
    checked: PropTypes.bool,
  }).isRequired,
  styleClass: PropTypes.string,
};

export default ToggleCheckBox;
