import React, { useRef } from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import i18n from 'utils/i18n';
import { isEmpty } from 'utils/lodash';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCaretDown, faCaretRight } from '@fortawesome/pro-solid-svg-icons';

const defaultProps = {
  value: '',
  skipTranslation: false,
};

function TableCheck({
  value, column, customCellType, skipTranslation, onHandleShowHideChilds, onHandleCheck,
}) {
  const {
    name, id, parentId, expanded, scheduled, hideIcon,
  } = value;
  const linkRef = useRef(null);

  const renderContent = () => (
    <div className={`${parentId ? 'child-checkbox' : 'parent-checkbox'} ${customCellType === 'CHECKBOX_VERSION' ? 'class-checkbox-version' : ''} `}>

      {((customCellType === 'CHECKBOX')
      || (customCellType === 'CHECKBOX_VERSION' && column?.id === value?.status))
        && (
          <label htmlFor={id} className="child-checkbox-label">
            <input
              className="child-checkbox-input"
              id={id}
              name={id}
              // eslint-disable-next-line no-nested-ternary
              aria-label={isEmpty(name) ? id : skipTranslation ? name : i18n.t(name)}
              onChange={() => onHandleCheck(id, parentId)}
              checked={scheduled}
              type="checkbox" />
            { !expanded && !parentId && !hideIcon
          && (
            <FontAwesomeIcon
              icon={faCaretRight}
              title={i18n.t('EDIT')}
              onClick={() => onHandleShowHideChilds(id)}
              className="pencil-icon" />
          )}
            {expanded && !parentId && !hideIcon
            && (
              <FontAwesomeIcon
                icon={faCaretDown}
                title={i18n.t('EDIT')}
                onClick={() => onHandleShowHideChilds(id)}
                className="pencil-icon" />
            )}
            {customCellType === 'CHECKBOX' && (skipTranslation ? name : i18n.t(name))}
          </label>
        )}

    </div>
  );

  return (
    <div ref={linkRef}>
      {renderContent()}
    </div>
  );
}

TableCheck.defaultProps = defaultProps;

TableCheck.propTypes = {
  column: PropTypes.shape({
    id: PropTypes.string,
  }),
  value: PropTypes.shape({
    name: PropTypes.string,
    id: PropTypes.string,
    status: PropTypes.string,
    parentId: PropTypes.string,
    expanded: PropTypes.bool,
    scheduled: PropTypes.bool,
    hideIcon: PropTypes.bool,
  }),
  skipTranslation: PropTypes.bool,
  onHandleShowHideChilds: PropTypes.func,
  onHandleCheck: PropTypes.func,
  customCellType: PropTypes.string,
};

export default withTranslation()(TableCheck);
