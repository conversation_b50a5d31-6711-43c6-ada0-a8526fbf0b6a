import React from 'react';
import PropTypes from 'prop-types';

function LineItem(props) {
  const {
    label,
    styleContainer,
    styleKey,
    styleValue,
    value,
  } = props;
  return (
    <div className={`container-in-line ${styleContainer}`}>
      <div className={`key ${styleKey}`}>
        {label}
      </div>
      <div className={`value ${styleValue}`}>
        {value}
      </div>
    </div>
  );
}
LineItem.propTypes = {
  label: PropTypes.string,
  styleContainer: PropTypes.string,
  styleKey: PropTypes.string,
  styleValue: PropTypes.string,
  value: PropTypes.string,
};
  
LineItem.defaultProps = {
  label: '',
  styleContainer: '',
  styleKey: '',
  styleValue: '',
  value: '',
};

export default LineItem;
