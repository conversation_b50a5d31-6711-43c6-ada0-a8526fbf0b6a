import { TOTAL_EC_DATA, ACTIVE_HEALTH_DATA, EDGECONNECTORS_NW_DATA } from 'config';

// this will be hooked onto the relative state tree.
export const REDUCER_KEY = 'dashboard';

export const DATA_ERROR = 'error';
export const DATA_LOADING = 'loading';
const LIST = 'list';
const EC_DATA = 'totalEcData';
const ACT_HEALTH_DATA = 'activeHealthData';
const EC_NW_DATA = 'ecNwData';
export const SHOW_TRAFFIC_FLOW = 'showTrafficFlow';
export const INCOMING_TRAFFIC = 'incomingTraffic';
export const OUTGOING_TRAFFIC = 'outgoingTraffic';
export const ALL_TRAFFIC = 'allTraffic';
export const EC_TRAFFIC_DATA = 'ecTrafficData';
export const TEST_EC_TRAFFIC_DATA = {
  paths: {
    src: [],
    subnet: [],
    service: [],
    dest: [],
  },
  source: [],
  subnets: [],
  edgeconnector: {},
  service: [],
  destination: [],
  ecLeft: true,
  ecRight: true,
};

export const RESOURCE_DEFAULTS = {
  [ACT_HEALTH_DATA]: ACTIVE_HEALTH_DATA,
  [ALL_TRAFFIC]: true,
  [DATA_ERROR]: '', // or error object so we can process and show to user
  [DATA_LOADING]: true,
  [EC_DATA]: TOTAL_EC_DATA,
  [EC_NW_DATA]: EDGECONNECTORS_NW_DATA,
  [EC_TRAFFIC_DATA]: TEST_EC_TRAFFIC_DATA,
  [INCOMING_TRAFFIC]: true,
  [LIST]: [], // or {}
  [OUTGOING_TRAFFIC]: true,
  [SHOW_TRAFFIC_FLOW]: false,
};

export const DEFAULTS = {
  ...RESOURCE_DEFAULTS,
};

// default state for this page/widget/component
export const DEFAULT_STATE = {
  ...DEFAULTS,
};

// For testing...to be used later
export const DUMMY_STORE_DATA = {
  [REDUCER_KEY]: {
    ...DEFAULT_STATE,
  },
};
