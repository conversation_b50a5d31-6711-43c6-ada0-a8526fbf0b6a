import { createSelector } from 'reselect';
// import moment from 'moment-timezone';

import * as constants from './constants';

// get login state
export const baseSelector = (state) => state[constants.REDUCER_KEY];

export const formSelector = (state) => state[constants.STATE_FORM];

export const loadingSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA_LOADING],
);

export const vmStatusDetailsDataSelector = createSelector(
  baseSelector,
  (state) => state[constants.VM_STATUS_DETAIL_DATA] || [],
);

export const connectorData = createSelector(
  baseSelector,
  (state) => state[constants.CONNECTOR_DATA_TABLE] || [],
);

export const connectorInfoStatusSelector = createSelector(
  baseSelector,
  (state) => state[constants.SHOW_CLOUD_CONNECTOR] || false,
);

export const connectorInfoEcIdSelector = createSelector(
  baseSelector,
  (state) => state[constants.CLOUD_CONNECTOR_DATA] || false,
);

export const cloudInfoSelector = createSelector(
  baseSelector,
  (state) => state[constants.CLOUD_INFO] || false,
);

export const openDrawerSelector = createSelector(
  baseSelector,
  (state) => state[constants.OPEN_DRAWER] || false,
);

export const drawerDataSelector = createSelector(
  baseSelector,
  (state) => state[constants.DRAWER_DATA] || [],
);

export const weblogTime = createSelector(
  baseSelector,
  (state) => state[constants.WEBLOG_TIME] || '',
  // state => state[constants.WEBLOG_TIME] || moment(Date.now()).format('MMM. DD, YYYY hh:mm:ss a'),
);

export const loadingPopUp = createSelector(
  baseSelector,
  (state) => state[constants.LOADING_POPUP] || '',
);

export const filtersFormSelector = createSelector(
  formSelector,
  (state) => state.filterForm || {},
);

export const filtersFormValuesSelector = createSelector(
  filtersFormSelector,
  (state) => state.values || {},
);

export const awsRegionsDropdownSelector = createSelector(
  filtersFormValuesSelector,
  (state) => state.awsRegionsDropdown || [],
);

export const azureRegionsDropdownSelector = createSelector(
  filtersFormValuesSelector,
  (state) => state.azureRegionsDropdown || [],
);

export const gcpRegionsDropdownSelector = createSelector(
  filtersFormValuesSelector,
  (state) => state.gcpRegionsDropdown || [],
);

export const regionsDropdownSelector = createSelector(
  filtersFormValuesSelector,
  (state) => state.regionsDropdown || [],
);

// alway return top level selector
export default baseSelector;
