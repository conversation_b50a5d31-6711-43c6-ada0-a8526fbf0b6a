'use client';

import React, { useEffect } from 'react';
import PropTypes from 'prop-types';

// Import existing providers based on what's found in the project
import { Provider } from 'react-redux';
import { I18nextProvider } from 'react-i18next';

// Import existing store and configuration
import { configureStore } from '../store';
import i18n from '../utils/i18n';
import { initializeHttpClient } from '../utils/http/http';

const APP_CONTAINER_ID = 'edge-ux-library-container';
const PORTAL_ROOT_ID = 'edge-ux-library-portal';

const AppProvider = ({ children, config = {}, store: externalStore }) => {
  // Use external store if provided, otherwise create a new one
  const store = externalStore || configureStore(config.initialState);

  // Initialize library-specific configuration
  useEffect(() => {
    if (config.httpClient) {
      initializeHttpClient(config.httpClient);
    }

    // Set up any global configurations
    if (config.theme) {
      // Apply custom theme if provided
      const root = document.documentElement;
      Object.entries(config.theme).forEach(([key, value]) => {
        root.style.setProperty(`--edge-ux-${key}`, value);
      });
    }

    if (config.i18n) {
      // Apply custom i18n configuration if provided
      if (config.i18n.language) {
        i18n.changeLanguage(config.i18n.language);
      }
    }

    // Set up library mode flag
    if (typeof window !== 'undefined') {
      window.EDGE_UX_LIBRARY_MODE = true;
    }
  }, [config]);

  return (
    <>
      <div id={APP_CONTAINER_ID} className="edge-ux-library-container">
        <Provider store={store}>
          <I18nextProvider i18n={i18n}>
            {children}
          </I18nextProvider>
        </Provider>
      </div>
      <div id={PORTAL_ROOT_ID} className="edge-ux-library-portal" />
    </>
  );
};

AppProvider.propTypes = {
  children: PropTypes.node.isRequired,
  store: PropTypes.object,
  config: PropTypes.shape({
    httpClient: PropTypes.shape({
      baseURL: PropTypes.string.isRequired,
      getToken: PropTypes.func,
      timeout: PropTypes.number,
      headers: PropTypes.object,
    }),
    theme: PropTypes.object,
    i18n: PropTypes.shape({
      language: PropTypes.string,
      resources: PropTypes.object,
    }),
    initialState: PropTypes.object,
  }),
};

AppProvider.defaultProps = {
  store: null,
  config: {},
};

export default AppProvider;
