import React from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { isOneUI } from 'config';

import {
  faTimes,
  faCheck,
  faQuestion,
  faExclamationCircle,
  faExclamationTriangle,
} from '@fortawesome/pro-solid-svg-icons';
import { closeNotification } from 'ducks/notification';
import { NOTIFICATION_TYPE } from 'ducks/notification/constants';

import './index.scss';

const notificationTheme = {
  [NOTIFICATION_TYPE.HELP]: {
    color: 'var(--semantic-color-border-status-neutral-active)', icon: faQuestion,
  },
  [NOTIFICATION_TYPE.SUCCESS]: {
    color: 'var(--semantic-color-border-status-success-active)', icon: faCheck,
  },
  [NOTIFICATION_TYPE.WARNING]: {
    color: 'var(--semantic-color-border-status-warning-active)', icon: faExclamationTriangle,
  },
  [NOTIFICATION_TYPE.ERROR]: {
    color: 'var(--semantic-color-border-status-danger-active)', icon: faExclamationCircle,
  },
};

export function Notification(props) {
  const {
    actions,
    message,
    showPersistent,
    longMessage,
    messagePersistent,
    show,
    type,
    t,
    error,
  } = props;
  const styler = { paddingRight: '1px' };
  const { icon, color } = notificationTheme[type];

  return show || showPersistent ? (
    <div
      className={`ec-notification-container  ${showPersistent ? 'persistent' : ''} ${isOneUI ? 'oneui-banner' : ''}`}
      style={{ '--theme': color }}>
      {showPersistent && (
        <div className={`notification ${showPersistent ? 'persistent' : ''} `}>
          <span className={`notification-message ${showPersistent ? 'persistent' : ''} `}>
            <FontAwesomeIcon icon={faExclamationTriangle} style={{ color: '#C00000' }} />
            {' '}
            <span className="notification-message-content">{t(messagePersistent)}</span>
          </span>
        </div>
      )}
      {show && (
        <>
          <div className="notification-icon">
            <FontAwesomeIcon icon={icon} />
          </div>
          <div className={`notification ${error ? 'error' : ''} `}>
            <span className="notification-message">
              {/* <span className="notification-message-content" style={styler}>{error ? t('ERROR') : ''}</span> */}
              {' '}
              <span className="notification-message-content">
                {t(message)}
                <br />
                {longMessage && (
                  <div className="ec-notification-long-message-content">
                    <pre>
                      <code className="ec-notification-long-message-content">{longMessage}</code>
                    </pre>
                  </div>
                )}
              </span>
            </span>
            <button
              onClick={actions.handleNotification}
              type="button"
              className="notification-close-icon">
              <FontAwesomeIcon icon={faTimes} />
            </button>
          </div>
        </>
      )}
    </div>
  ) : '';
}

Notification.propTypes = {
  actions: PropTypes.shape(),
  message: PropTypes.oneOfType([PropTypes.string, PropTypes.shape()]),
  longMessage: PropTypes.oneOfType([PropTypes.string, PropTypes.shape()]),
  showPersistent: PropTypes.bool,
  messagePersistent: PropTypes.oneOfType([PropTypes.string, PropTypes.shape()]),
  show: PropTypes.bool,
  t: PropTypes.func,
  error: PropTypes.bool,
};

Notification.defaultProps = {
  actions: {},
  message: '',
  longMessage: '',
  messagePersistent: '',
  show: false,
  showPersistent: false,
  t: (str) => str,
  error: false,
};

const mapStateToProps = (state) => {
  return {
    ...state.notification,
  };
};

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    handleNotification: closeNotification,
  }, dispatch);
  return {
    actions,
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withTranslation()(Notification));
