import React from 'react';
import PropTypes from 'prop-types';
import { noop, get } from 'utils/lodash';

import DropdownDisplay from './DropdownDisplay';
import MultiDropdownSelector from './MultiDropdownSelector';

import './dropdown.scss';

export class MultiDropdownWithSearch extends React.Component {
  componentDidMount() {
    const {
      actions: { setSelectedValues },
      input: { value },
    } = this.props;
    setSelectedValues(value);
  }

  shouldComponentUpdate(nextProps) {
    const {
      actions: { setSelectedValues },
      input: { value },
    } = this.props;

    const newValue = get(nextProps, 'input.value', []);

    if (newValue !== value) {
      setSelectedValues(newValue);
    }
    return true;
  }

  showSelector = () => {
    const { open } = this.props;
    if (!open) return null;
    return (
      <MultiDropdownSelector
        {...this.props} />
    );
  };

  // TODO show validation error messages
  render() {
    const {
      id,
      styles,
      classes,
      meta,
    } = this.props;
    const hasError = !meta.active && meta.touched && !!meta.error;

    return (
      <div
        id={id}
        style={styles}
        className={`${classes.join(' ')} dropdown ${hasError ? 'error' : ''}`}>
        <DropdownDisplay {...this.props} />
        { this.showSelector() }
      </div>
    );
  }
}

MultiDropdownWithSearch.propTypes = {
  id: PropTypes.string,
  styles: PropTypes.shape({}),
  classes: PropTypes.arrayOf(PropTypes.string),
  open: PropTypes.bool,
  meta: PropTypes.shape({
    active: PropTypes.bool,
    touched: PropTypes.bool,
    error: PropTypes.string,
  }).isRequired,
  input: PropTypes.shape({
    value: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.arrayOf(PropTypes.shape({})),
    ]),
  }),
  actions: PropTypes.shape({
    setSelectedValues: PropTypes.func,
  }),
  selectedValues: PropTypes.arrayOf(PropTypes.shape({})),
};

MultiDropdownWithSearch.defaultProps = {
  id: '',
  styles: {},
  classes: [],
  open: false,
  input: {
    onChange: noop,
  },
  actions: {
    cancel: noop,
    load: noop,
    setSelectedValues: noop,
  },
  selectedValues: [],
};

export default MultiDropdownWithSearch;
