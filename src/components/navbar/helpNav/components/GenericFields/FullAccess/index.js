import React from 'react';
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';
import { FormFieldLabel, FormSectionLabel } from 'components/label';
import { withTranslation } from 'react-i18next';
import { Field, getFormValues } from 'redux-form';
import { ToggleCheckBox } from 'components/ecToggle';
import SingleDate from 'components/dateTimePicker/singleDate';
import {
  required,
} from 'utils/validations';
import moment from 'moment-timezone';

function FullAccess({ viewOnly, form, t }) {
  const data = useSelector((state) => getFormValues(form)(state)) || {};
  const { enableFullAccess, fullAccessUntil } = data;

  return (
    <>
      <FormSectionLabel text={t('FULL_ACCESS')} />
      <div className="form-section">
        <div className="g-row">
          <div className="g-left">
            <FormFieldLabel text={t('ENABLE_FULL_ACCESS')} tooltip={t('TOOLTIP_HELP_ENABLE_FULL_ACCESS_REMOTE_ASSISTANCE')} />
            {!viewOnly ? (
              <Field
                id="enableFullAccess"
                name="enableFullAccess"
                styleClass="ec-toggle-checkbox"
                component={ToggleCheckBox} />
            )
              : (
                <p className="disabled-input">
                  {enableFullAccess ? t('ENABLED') : t('DISABLED')}
                </p>
              )}
          </div>
        </div>
        {enableFullAccess && (
          <div className="g-row">
            <div className="g-left">
              <FormFieldLabel text={t('FULL_ACCESS_ENABLED_UNTIL')} tooltip={t('TOOLTIP_HELP_REMOTE_FULL_ACCESS_ENABLED_UNTIL')} />
              {!viewOnly ? (
                <Field
                  id="fullAccessUntil"
                  name="fullAccessUntil"
                  component={SingleDate}
                  styleClass="zia"
                  placeholder={t('ENTER_TEXT')}
                  props={{
                    label: t('FULL_ACCESS_ENABLED_UNTIL'),
                  }}
                  validate={[
                    required,
                  ]} />
              )
                : (
                  <p className="disabled-input">
                    {moment(fullAccessUntil).format('MM/DD/YYYY')}
                  </p>
                )}
            </div>
          </div>
        )}
      </div>
    </>
  );
}

FullAccess.propTypes = {
  viewOnly: PropTypes.bool,
  form: PropTypes.string,
  t: PropTypes.func,
};
  
FullAccess.defaultProps = {
  viewOnly: true,
  form: '',
  t: (str) => str,
};

export default withTranslation()(FullAccess);
