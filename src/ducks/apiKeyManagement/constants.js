// this will be hooked onto the relative state tree.
import { BASE_API_PATH } from 'config';
import { useTranslation } from 'react-i18next';
import { isEmpty } from 'utils/lodash';

export const REDUCER_KEY = 'apiKeyManagement';

export const APIKEYMANAGEMENT_API_QUERY = `${BASE_API_PATH}/v1/apiKeys`;
export const APIKEYMANAGEMENT_GENERATE = `${BASE_API_PATH}/v1/apiKeys/generate`;
export const APIKEYMANAGEMENT_API_QUERY_ORG = `${BASE_API_PATH}/v1/orgProvisioning`;

export const DATA_ERROR = 'error';
export const DATA_LOADING = 'loading';
export const MODAL_LOADING = 'modalLoading';
export const DATA_TABLE = 'rbactabledata';
export const DATA = 'data';

// this will store data to create and edit form
export const APP_DATA = 'appData';
export const ADD_ROLE = 'addRole';
export const VIEW_ONLY = 'viewOnly';
export const MODAL_TITLE = 'modalTitle';
export const CLOUD_NAME = 'cloudName';
export const SHOW_FORM = 'showForm';
export const FEATURE_PERMISSIONS = 'featurePermissions';
export const CUSTOM_APPTYPE = 'CUSTOM';
export const IP_ADDRESSES_DATA = 'ipAddressesData';
export const EXPANDED_APPS = 'expandedApps';
export const COLUMNS = 'columns';
export const SHOW_DELETE_FORM = 'showDeleteForm';
export const SHOW_REGENERATE_FORM = 'showRegenerateForm';
export const SHOW_EDIT_FORM = 'showEditForm';
export const SELECTED_ROW = 'selectedRow';

const lastModifiedBy = ({ value }) => {
  const { t } = useTranslation();
  if (isEmpty(value)) return 'System';

  return t(value);
};

const columns = [
  {
    exportId: 'NO',
    key: 'idx',
    name: 'NO',
    data: {},
    width: 40,
    visible: true,
    frozen: false,
    resizable: true,
    sortable: false,
    draggable: false,
  },
  {
    exportId: 'KEY',
    key: 'keyValue',
    name: 'KEY',
    data: {},
    sortable: false,
    visible: true,
    frozen: false,
    draggable: true,
    resizable: true,
    checkbox: true,
  },
  {
    exportId: 'LAST_MODIFIED_BY',
    key: 'lastModifiedBy',
    name: 'LAST_MODIFIED_BY',
    data: {},
    formatter: lastModifiedBy,
    visible: true,
    resizable: true,
    frozen: false,
    sortable: false,
    draggable: true,
    checkbox: true,
  },
  {
    exportId: 'LAST_MODIFIED_ON',
    key: 'lastModifiedTime',
    name: 'LAST_MODIFIED_ON',
    data: {},
    // formatter: lastModifiedTime,
    visible: true,
    resizable: true,
    frozen: false,
    sortable: false,
    draggable: true,
    checkbox: true,
  },
  {
    name: '',
    exportId: '',
    data: {},
    key: 'isNonEditable',
    visible: true,
    width: 50,
  },
];

export const RESOURCE_DEFAULTS = {
  [DATA_ERROR]: null, // or error object so we can process and show to user
  [DATA_LOADING]: true,
  [APP_DATA]: {},
  [ADD_ROLE]: false,
  [CLOUD_NAME]: '',
  [SHOW_FORM]: false,
  [DATA]: {},
  [DATA_TABLE]: [],
  [SHOW_DELETE_FORM]: false,
  [SELECTED_ROW]: null,
  [IP_ADDRESSES_DATA]: {},
  [EXPANDED_APPS]: [],
  [COLUMNS]: columns,
  [MODAL_LOADING]: false,
};

export const DEFAULTS = {
  ...RESOURCE_DEFAULTS,
};

// default state for this page/widget/component
export const DEFAULT_STATE = {
  ...DEFAULTS,
};

// For testing...to be used later
export const DUMMY_STORE_DATA = {
  [REDUCER_KEY]: {
    ...DEFAULT_STATE,
  },
};
