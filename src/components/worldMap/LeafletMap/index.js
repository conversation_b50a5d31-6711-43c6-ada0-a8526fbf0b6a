/* eslint-disable max-len */
/* eslint-disable react/prop-types */
// @flow
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import L from 'leaflet';
import 'leaflet.markercluster';
import { Map, TileLayer } from 'react-leaflet';
import { handlePopupOpen } from 'ducks/connectorMonitoring';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import MarkerClusterGroup from 'react-leaflet-markercluster';
import MapNodes from './MapNodes';

import './index.scss';

const centerPosition = [51.505, -0.09];

const iconPerson = new L.Icon({
  iconUrl: './images/marker-icon.png$',
  iconRetinaUrl: './images/layers.png$',
  iconAnchor: null,
  popupAnchor: null,
  shadowUrl: null,
  shadowSize: null,
  shadowAnchor: null,
  iconSize: new L.Point(60, 75),
  className: 'leaflet-div-icon',
});

export { iconPerson };

class LeafletMap extends PureComponent {
  static propTypes = {
    isZeroTrustGateway: PropTypes.bool,
    showConnectorToolTip: PropTypes.bool,
    showTrafficTooltip: PropTypes.bool,
    data: PropTypes.arrayOf(PropTypes.shape({})),
    actions: PropTypes.shape({}),
  };

  static defaultProps = {
    isZeroTrustGateway: false,
    showConnectorToolTip: false,
    showTrafficTooltip: false,
    data: [],
    actions: {
      handlePopupOpen: null,
      handlePopupClose: null,
    },
  };

  render() {
    const {
      data, showConnectorToolTip, showTrafficTooltip, isZeroTrustGateway, actions,
    } = this.props;

    const selectedMode = document.getElementsByTagName('body')[0].dataset.mode;
    return (
      <Map
        className="markercluster-map"
        role="button"
        center={centerPosition}
        // zoomDelta={0.5}
        zoom={3}
        scrollWheelZoom={false}
        maxZoom={5}
        minZoom={3}
        // dragging={false} // will disable dragging
        fitWorld>
        <TileLayer
          url={selectedMode === 'dark'
            ? 'https://c.basemaps.cartocdn.com/dark_nolabels/{z}/{x}/{y}.png'
            : 'https://a.basemaps.cartocdn.com/light_nolabels/{z}/{x}/{y}.png'} />
        <MarkerClusterGroup
          showCoverageOnHover={false}
          spiderLegPolylineOptions={{ weight: 0.25, opacity: 0.02 }}>
          <MapNodes
            data={data}
            isZeroTrustGateway={isZeroTrustGateway}
            showConnectorToolTip={showConnectorToolTip}
            showTrafficTooltip={showTrafficTooltip}
            actions={actions} />
        </MarkerClusterGroup>
      </Map>
    );
  }
}

export const mapStateToProps = (state, ownProps) => {
  const html = document.getElementsByTagName('html');
  const mode = html[0].className;

  return {
    mode,
  };
};

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    popupOpen: handlePopupOpen,
  }, dispatch);
  return { actions };
};
export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(LeafletMap);
