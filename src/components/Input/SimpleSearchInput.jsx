import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSearch } from '@fortawesome/pro-solid-svg-icons';

const noop = () => undefined;

export class SimpleSearchInput extends React.PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      search: '',
    };
  }

  onChangeInput = (event) => {
    const { onKeyPressCb } = this.props;
    this.setState({ search: event.target.value });
    onKeyPressCb(event, event.target.value); //eslint-disable-line
  };

  onSearch = (event) => {
    const { onKeyPressCb } = this.props;
    onKeyPressCb({...event, keyCode: 13}, this.state.search); //eslint-disable-line
  };

  render() {
    const {
      onKeyPressCb,
      tabIndex,
      placeholder,
      t,
    } = this.props;

    return (
      <span className="search-container">
        <div className="icon-wrapper left">
          <FontAwesomeIcon
            className="search-icon"
            onClick={(event) => this.onSearch(event)}
            icon={faSearch} />
        </div>

        <input
          type="search"
          className="search-input"
          placeholder={t(placeholder)}
          tabIndex={tabIndex}
          onChange={(event) => this.onChangeInput(event)}
          onKeyDown={(event) => onKeyPressCb(event, event.target.value)} />
      </span>
    );
  }
}

SimpleSearchInput.propTypes = {
  onKeyPressCb: PropTypes.func,
  tabIndex: PropTypes.number,
  placeholder: PropTypes.string,
  t: PropTypes.func,
};

SimpleSearchInput.defaultProps = {
  onKeyPressCb: noop,
  tabIndex: 0,
  placeholder: 'SEARCH_ELLIPSIS',
  t: (str) => str,
};

export default withTranslation()(SimpleSearchInput);
