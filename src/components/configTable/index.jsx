/* eslint-disable no-unused-vars */
/* eslint-disable jsx-a11y/no-noninteractive-tabindex */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable no-nested-ternary */
/* eslint-disable react/no-array-index-key */
/* eslint no-return-assign: 1 */
import React, {
  useCallback, useEffect, useMemo, useRef, useState,
} from 'react';
import PropTypes from 'prop-types';
import ReactTooltip from 'react-tooltip';
import { noop } from 'utils/lodash';
import { withTranslation } from 'react-i18next';
import Loading from 'components/spinner/Loading';

import {
  useFlexLayout,
  useColumnOrder,
  useFilters,
  useResizeColumns,
  useSortBy,
  useTable,
  useExpanded,
} from 'react-table';
import InfiniteLoader from 'react-window-infinite-loader';
import { VariableSizeList } from 'react-window';

import ColumnContainer from './column-container';
import CellContainer from './cell-container';

import ColumnLayoutConfig from './components/column-layout-config';
import DragAndDropList from './components/drag-n-drop';

const mergeRefs = (...refs) => {
  const filteredRefs = refs.filter(Boolean);
  if (!filteredRefs.length) return null;
  if (filteredRefs.length === 0) return filteredRefs[0];
  return (inst) => {
    // eslint-disable-next-line no-restricted-syntax
    for (const ref of filteredRefs) {
      if (typeof ref === 'function') {
        ref(inst);
      } else if (ref) {
        ref.current = inst;
      }
    }
  };
};

function ConfigTable({
  permission,
  columns,
  defaultRowHeight,
  initialState,
  onFiltersApply,
  onHandleRowEdit,
  onHandleDuplicateRow,
  onHandleEnableDisableRow,
  onHandleRowDelete,
  onHandleStatusChange,
  onHandleRowRun,
  onHandleRowView,
  onHandleRowViewDiff,
  onHandleRowSubEdit,
  onHandleRowDownload,
  onHandleSortBy,
  onHandleShowHideChilds,
  onHandleRowTestConnectivity,
  onHandleCheck,
  onHandleLink,
  onHandleLinkSelf,
  linkNameParameter,
  onHandleRowEllipsis,
  sortField,
  sortDirection,
  showColumnLayoutConfigurer,
  data,
  renderRowSubComponent,
  checkAll,
  tableHeight,
  maxTableHeight,
  disableTableHeaderDrag,
  moreItemsLoading,
  hasNextPage,
  loadMore,
  pagination,
  pageIndex,
  pageSize,
  isTestConnectivityInProgress,
  notMemoed,
  t,
}) {
  const mounted = useRef();
  const rowData = useMemo(() => data, [data]);
  const columnConfig = notMemoed ? columns : useMemo(() => columns, [columns]);
  const tooltipRef = useRef(null);
  const [tooltipContent, setTooltipContent] = useState('');
  const updatePosition = noop;

  const listRef = useRef({});

  useEffect(() => {
    if (!mounted.current) {
      // do componentDidMount logic
      mounted.current = true;
    } else {
      // do componentDidUpdate logic
      ReactTooltip.rebuild();
    }
  });
  
  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    rows,
    prepareRow,
    toggleHideAllColumns,
    setColumnOrder,
    setAllFilters,
    allColumns,
    totalColumnsWidth,
    state: { filters, selectedRowIds, expanded },
    resetResizing,
  } = useTable(
    {
      columns: columnConfig,
      data: rowData,
      initialState,
      skipPageReset: true,
      autoResetExpanded: false,
    },
    useFilters,
    useSortBy,
    useColumnOrder,
    useResizeColumns,
    useFlexLayout,
    useExpanded,
  );

  useEffect(() => {
    onFiltersApply(filters, setAllFilters);
  }, [filters]);

  // const itemCount = hasNextPage ? rows.length + 1 : rows.length;
  // If there are more rows to be loaded then add an extra row to hold a loading indicator.
  const itemCount = hasNextPage ? rows.length + 1 : rows.length;

  // Only load 1 page of rows at a time.
  // Pass an empty callback to InfiniteLoader in case it asks us to load more than once.
  const loadMoreItems = moreItemsLoading ? () => {} : loadMore;

  // Every row is loaded except for our loading indicator row.
  const isItemLoaded = (index) => {
    return !hasNextPage || index < rows.length;
  };

  const [isResetLayout, setIsResetLayout] = useState(false);

  const calculateRowSize = (input) => input.reduce((height, item, i) => {
    if (item.rowHeight) {
      // eslint-disable-next-line no-param-reassign
      height[i] = item.rowHeight;
    } else {
      // eslint-disable-next-line no-param-reassign
      height[i] = defaultRowHeight || 50; // height for each row
    }
    return height;
  }, {});
  // eslint-disable-next-line no-shadow
  const [rowSizes, setrowSizes] = useState(calculateRowSize(rowData));

  useEffect(() => {
    if (isResetLayout) {
      setColumnOrder(columns.map((col) => col.id));
      toggleHideAllColumns(false);
      setIsResetLayout(false);
    }
  }, [isResetLayout]);

  useEffect(() => {
    setrowSizes(calculateRowSize(rowData));
  }, [rowData]);

  const getRowHeight = (index) => {
    return rowSizes[index] || 50;
  };

  const setRowHeight = (i, size) => {
    if (listRef.current) {
      listRef.current.resetAfterIndex(i);
    }
    setrowSizes((prevRowSizes) => ({
      ...prevRowSizes,
      [i]: prevRowSizes[i] === 50 ? size : 50,
    }));
  };

  const [isDragDisabled, setIsDragDisabled] = useState(false);
  
  const onDragEnter = (evt) => {
    const className = '' + evt.target.className || '';
    if (className.includes('resizer')) {
      setIsDragDisabled(true);
    }
  };

  const onDragLeave = () => {
    if (isDragDisabled) {
      setIsDragDisabled(false);
    }
  };

  const renderColumnSection = (column, idx, provided) => {
    const { disableReordering } = column;

    return (
      <div
        key={idx}
        className="column-cell-container"
        {...column.getHeaderProps()}>
        <div
          className={`cell ${disableReordering ? 'disable-ordering' : ''}`}
          // aria-label={t(column.Header) || 'Drag'}
          // role="button"
          // tabIndex="0"
          ref={provided.innerRef}
          key={column.id}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          onMouseDown={onDragEnter}
          onMouseUp={onDragLeave}>
          {column.render((props) => (
            <ColumnContainer
              {...props}
              checkAll={checkAll}
              setAllFilters={setAllFilters}
              sortField={sortField}
              sortDirection={sortDirection}
              onHandleSortBy={onHandleSortBy}
              onHandleCheck={onHandleCheck}
              filters={filters} />
          ))}
        </div>
      </div>
    );
  };

  const onNewListOrder = (result) => {
    setColumnOrder(result);
  };

  const RenderRow = useCallback(
    ({ index, style }) => {
      if (!isItemLoaded(index) && hasNextPage) {
        return (
          <div style={style} className={`row ${index % 2 ? 'ListItemOdd' : 'ListItemEven'} ${moreItemsLoading ? '' : 'hide'}`}>
            <Loading />
          </div>
        );
      }

      const row = rows[index];
      if (row) prepareRow(row);

      return (
        <>
          {!row.original.parentId && (
            // USING SPAN to be able to color the ParentRows different from  Child Rows
            <span
              data-tip={row.original && row.original.tooltip}
              className={`row ${index % 2 ? 'ListItemOdd' : 'ListItemEven'}`}
              {...row.getRowProps({ style })}>
              {row.cells.map((cell, idx) => {
                return (
                  <div key={idx} {...cell.getCellProps({ className: 'cell' })}>
                    {cell.render((props) => (
                      <CellContainer
                        {...props}
                        pageIndex={pageIndex}
                        pageSize={pageSize}
                        permission={permission}
                        tooltipRef={tooltipRef}
                        tooltipContent={tooltipContent}
                        setTooltipContent={setTooltipContent}
                        updatePosition={updatePosition}
                        onHandleRowEdit={onHandleRowEdit}
                        onHandleDuplicateRow={onHandleDuplicateRow}
                        onHandleEnableDisableRow={onHandleEnableDisableRow}
                        onHandleRowRun={onHandleRowRun}
                        onHandleRowView={onHandleRowView}
                        onHandleRowViewDiff={onHandleRowViewDiff}
                        onHandleRowSubEdit={onHandleRowSubEdit}
                        onHandleRowDelete={onHandleRowDelete}
                        onHandleStatusChange={onHandleStatusChange}
                        onHandleRowDownload={onHandleRowDownload}
                        onHandleShowHideChilds={onHandleShowHideChilds}
                        onHandleCheck={onHandleCheck}
                        onHandleLink={onHandleLink}
                        onHandleLinkSelf={onHandleLinkSelf}
                        linkNameParameter={linkNameParameter}
                        onHandleRowEllipsis={onHandleRowEllipsis}
                        onHandleRowTestConnectivity={onHandleRowTestConnectivity}
                        // eslint-disable-next-line max-len
                        isTestConnectivityInProgress={row && row.original && row.original.id === isTestConnectivityInProgress}
                        isTestConnectivityFeedDisabled={row && row.original && row.original.feedStatus === 'DISABLED'}
                        setSize={setRowHeight}
                        renderIndex={index} />
                    ))}
                  </div>
                );
              })}

              {/*
              If the row is in an expanded state, render a row with a
              column that fills the entire length of the table.
            */}
              {/* <button onClick={() => toggleSize(index)}>Toggle Size</button> */}
              {row.isExpanded ? (
                <div className="row expanded-row">
                  {/*
                  Inside it, call our renderRowSubComponent function. In reality,
                  you could pass whatever you want as props to
                  a component like this, including the entire
                  table instance. But for this example, we'll just
                  pass the row
                */}
                  {renderRowSubComponent({ row })}
                </div>
              ) : null}
            </span>
          )}

          {Boolean(row.original.parentId) && (
            <div
              data-tip={row.original && row.original.tooltip}
              className="child-row"
              {...row.getRowProps({ style })}>
              {row.cells.map((cell, idx) => {
                return (
                  <div key={idx} {...cell.getCellProps({ className: 'cell' })}>
                    {cell.render((props) => (
                      <CellContainer
                        {...props}
                        pageIndex={pageIndex}
                        pageSize={pageSize}
                        tooltipRef={tooltipRef}
                        tooltipContent={tooltipContent}
                        setTooltipContent={setTooltipContent}
                        updatePosition={updatePosition}
                        onHandleRowEdit={onHandleRowEdit}
                        onHandleDuplicateRow={onHandleDuplicateRow}
                        onHandleEnableDisableRow={onHandleEnableDisableRow}
                        onHandleRowRun={onHandleRowRun}
                        onHandleRowView={onHandleRowView}
                        onHandleRowViewDiff={onHandleRowViewDiff}
                        onHandleRowSubEdit={onHandleRowSubEdit}
                        onHandleRowDelete={onHandleRowDelete}
                        onHandleStatusChange={onHandleStatusChange}
                        onHandleRowDownload={onHandleRowDownload}
                        onHandleShowHideChilds={onHandleShowHideChilds}
                        onHandleCheck={onHandleCheck}
                        onHandleLink={onHandleLink}
                        onHandleLinkSelf={onHandleLinkSelf}
                        linkNameParameter={linkNameParameter}
                        setSize={setRowHeight}
                        renderIndex={index} />
                    ))}
                  </div>
                );
              })}

              {/*
              If the row is in an expanded state, render a row with a
              column that fills the entire length of the table.
            */}
              {/* <button onClick={() => toggleSize(index)}>Toggle Size</button> */}
              {row.isExpanded ? (
                <div className="row expanded-row">
                  {/*
                  Inside it, call our renderRowSubComponent function. In reality,
                  you could pass whatever you want as props to
                  a component like this, including the entire
                  table instance. But for this example, we'll just
                  pass the row
                */}
                  {renderRowSubComponent({ row })}
                </div>
              ) : null}
            </div>
          )}
          
        </>
      );
    },
    [prepareRow, rows, selectedRowIds, expanded],
  );

  const virtualizedStyle = {
    overflow: 'overlay',
    width: '100%',
    maxHeight: maxTableHeight || '60vh',
    height: 'auto',
  };

  return (
    <div className="config-table-container">
      <article className="table-container" {...getTableProps()}>
        {showColumnLayoutConfigurer && (
          <ColumnLayoutConfig
            allColumns={allColumns}
            setColumnOrder={setColumnOrder}
            setIsResetLayout={setIsResetLayout}
            onHandleCheck={onHandleCheck}
            resetLayout={resetResizing} />
        )}
        <div className="head">
          {headerGroups.map((headerGroup, idx) => (
            <DragAndDropList
              key={idx}
              list={headerGroup.headers}
              listKeyName="id"
              droppableId="column-droppable"
              direction="horizontal"
              listContainerClass="row"
              listContainerProps={headerGroup.getHeaderGroupProps()}
              renderListItem={renderColumnSection}
              onNewListOrder={onNewListOrder}
              isDragDisabled={disableTableHeaderDrag || isDragDisabled} />
          ))}
        </div>
        <div className="content" {...getTableBodyProps()}>
          {
            (rows.length === 0)
              ? (
                <div role="row" className="row no-items-message">
                  <div role="cell">
                    {t('NO_MATCHING_ITEMS_FOUND')}
                  </div>
                </div>
              )
              : (
                pagination // temporary solution for list expand and pagination
                  ? (
                    <InfiniteLoader
                      isItemLoaded={isItemLoaded}
                      itemCount={itemCount}
                      loadMoreItems={loadMoreItems}>
                      {({ onItemsRendered, ref }) => (
                        <div ref={ref}>
                          <VariableSizeList
                            style={virtualizedStyle}
                            itemCount={itemCount}
                            itemSize={getRowHeight}
                            height={tableHeight || 600}
                            ref={mergeRefs(ref, listRef)}
                            className="list-container-infinite"
                            onItemsRendered={onItemsRendered}
                            width={totalColumnsWidth}>
                            {RenderRow}
                          </VariableSizeList>
                        </div>
                      )}
                    </InfiniteLoader>
                  ) : (
                    <VariableSizeList
                      style={virtualizedStyle}
                      itemCount={itemCount}
                      itemSize={getRowHeight}
                      overscanCount={13}
                      height={tableHeight || 600}
                      ref={listRef}
                      className="list-container"
                      width={totalColumnsWidth}>
                      {RenderRow}
                    </VariableSizeList>
                  ))
          }
        </div>
      </article>
      <ReactTooltip
        place="top"
        type="light"
        offset={{ top: -10 }}
        effect="solid"
        border
        multiline
        className="cc-group-tooltip-container"
        borderColor="#939393" />
      <ReactTooltip
        id="secondaryTooltip"
        place="right"
        type="light"
        effect="solid"
        border
        multiline
        isCapture
        className="cc-group-tooltip-container"
        borderColor="#939393" />
      {/* <ToolTip
        refProp={tooltipRef}
        cssStyle={{ display: 'none' }}
        tooltip={tooltipContent}
        isActive
        setPosition={hover => (updatePosition = hover)} /> */}
    </div>
  );
}

ConfigTable.propTypes = {
  permission: PropTypes.string,
  columns: PropTypes.arrayOf(Object),
  hasEditColumn: PropTypes.bool,
  hasIndexColumn: PropTypes.bool,
  initialState: PropTypes.objectOf(Object),
  onFiltersApply: PropTypes.func,
  showColumnLayoutConfigurer: PropTypes.bool,
  data: PropTypes.arrayOf(Object),
  onHandleRowEdit: PropTypes.func,
  onHandleDuplicateRow: PropTypes.func,
  onHandleEnableDisableRow: PropTypes.func,
  onHandleRowDelete: PropTypes.func,
  onHandleStatusChange: PropTypes.func,
  onHandleRowRun: PropTypes.func,
  onHandleRowView: PropTypes.func,
  onHandleRowViewDiff: PropTypes.func,
  onHandleCheck: PropTypes.func,
  onHandleLink: PropTypes.func,
  onHandleLinkSelf: PropTypes.func,
  linkNameParameter: PropTypes.string,
  onHandleSortBy: PropTypes.func,
  onHandleRowSubEdit: PropTypes.func,
  onHandleRowDownload: PropTypes.func,
  onHandleShowHideChilds: PropTypes.func,
  onHandleRowTestConnectivity: PropTypes.func,
  onHandleRowEllipsis: PropTypes.func,
  renderRowSubComponent: PropTypes.func,
  sortField: PropTypes.string,
  sortDirection: PropTypes.string,
  checkAll: PropTypes.bool,
  tableHeight: PropTypes.number,
  maxTableHeight: PropTypes.string,
  disableTableHeaderDrag: PropTypes.bool,
  t: PropTypes.func,
  defaultRowHeight: PropTypes.number,
  moreItemsLoading: PropTypes.bool,
  hasNextPage: PropTypes.bool,
  loadMore: PropTypes.func,
  pagination: PropTypes.bool,
  isTestConnectivityInProgress: PropTypes.bool,
  isTestConnectivityFeedDisabled: PropTypes.bool,
  notMemoed: PropTypes.bool,
};

ConfigTable.defaultProps = {
  permission: 'NONE',
  columns: [{}],
  hasEditColumn: false,
  hasIndexColumn: true,
  initialState: {},
  onFiltersApply: noop,
  showColumnLayoutConfigurer: false,
  data: [{}],
  onHandleRowEdit: noop,
  onHandleDuplicateRow: noop,
  onHandleEnableDisableRow: noop,
  onHandleRowDelete: noop,
  onHandleStatusChange: noop,
  onHandleRowRun: noop,
  onHandleRowView: noop,
  onHandleRowViewDiff: noop,
  onHandleCheck: noop,
  onHandleLink: noop,
  onHandleLinkSelf: noop,
  linkNameParameter: '',
  onHandleSortBy: null,
  onHandleRowSubEdit: noop,
  onHandleRowDownload: noop,
  onHandleShowHideChilds: noop,
  onHandleRowTestConnectivity: noop,
  onHandleRowEllipsis: noop,
  renderRowSubComponent: noop,
  sortField: '',
  sortDirection: '',
  checkAll: false,
  tableHeight: 0,
  maxTableHeight: '',
  disableTableHeaderDrag: false,
  t: (str) => str,
  defaultRowHeight: 50,
  moreItemsLoading: false,
  hasNextPage: false,
  loadMore: noop,
  pagination: false,
};

export default withTranslation()(ConfigTable);
