import React from 'react';
import { render, fireEvent } from '@testing-library/react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPrint } from '@fortawesome/pro-solid-svg-icons';
import Print from './Print';

jest.mock('@fortawesome/react-fontawesome', () => ({
  FontAwesomeIcon: jest.fn(() => <div>FontAwesomeIcon</div>),
}));

describe('Print component', () => {
  const onActionCb = jest.fn();
  const tabIndex = 1;
  const label = 'Print Label';

  it('renders correctly', () => {
    const { getByText, getByRole } = render(
      <Print
        label={label}
        onActionCb={onActionCb}
        tabIndex={tabIndex} />,
    );

    expect(getByText(label)).toBeInTheDocument();
    expect(getByRole('button')).toBeInTheDocument();
    expect(getByRole('button')).toHaveAttribute('tabindex', tabIndex.toString());
  });

  it('calls onActionCb when clicked', () => {
    const { getByRole } = render(
      <Print
        label={label}
        onActionCb={onActionCb}
        tabIndex={tabIndex} />,
    );

    const button = getByRole('button');
    fireEvent.click(button);

    expect(onActionCb).toHaveBeenCalledTimes(1);
  });

  it('calls onActionCb when Enter key is pressed', () => {
    const { getByRole } = render(
      <Print
        label={label}
        onActionCb={onActionCb}
        tabIndex={tabIndex} />,
    );

    const button = getByRole('button');
    fireEvent.keyPress(button, { key: 'Enter', code: 'Enter' });

    expect(onActionCb).toHaveBeenCalledTimes(1);
  });

  it('renders FontAwesomeIcon', () => {
    const { getByText } = render(
      <Print
        label={label}
        onActionCb={onActionCb}
        tabIndex={tabIndex} />,
    );

    expect(getByText('FontAwesomeIcon')).toBeInTheDocument();
  });

  it('matches snapshot', () => {
    const { container } = render(
      <Print
        label={label}
        onActionCb={onActionCb}
        tabIndex={tabIndex} />,
    );

    expect(container).toMatchSnapshot();
  });
});
