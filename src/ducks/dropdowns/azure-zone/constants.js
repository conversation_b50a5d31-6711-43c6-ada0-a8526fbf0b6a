import dropdown from 'ducks/generics/dropdown';
import AzureAvailabilityZone from '@zscaler/ec-domain/json/AzureAvailabilityZone.json';

export const REDUCER_KEY = 'azureZoneDropdown';
export const STATIC_DATA = 'staticData';

export const DROPDOWN = dropdown.createStateConstants();
export const DROPDOWN_DEFAULT_STATE = dropdown.createDefaultState();

const formattedStaticData = Object.values(AzureAvailabilityZone).map((e) => {
  e.id = e.val;
  return e;
});

export const DEFAULT_STATE = {
  ...DROPDOWN_DEFAULT_STATE,
  [STATIC_DATA]: formattedStaticData,
};

export const DUMMY_STORE_DATA = {
  [REDUCER_KEY]: {
    ...DEFAULT_STATE,
  },
};
