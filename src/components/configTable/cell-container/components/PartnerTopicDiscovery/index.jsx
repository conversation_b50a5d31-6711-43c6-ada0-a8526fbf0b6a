import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import i18n from 'utils/i18n';
import { isEmpty } from 'utils/lodash';
import { getRuleApplicableFor, isRuleOrder5g } from 'ducks/trafficFwdPolicies/constants';
import PolicyItemView from '../PolicyItemView';
import LocationItemView from '../LocationItemView';
import SingleItemView from '../SingleItemView';

function PartnerTopicDiscovery({ value }) {
  const { status } = value || {};
  const { eventTopicName, msgReceived } = status || {};
  
  const renderContent = () => (
    <>
      <PolicyItemView
        label={i18n.t('NAME')}
        value={i18n.t(eventTopicName || '---')} />
        
      <PolicyItemView
        label={i18n.t('MESSAGE_RECEIVED')}
        value={i18n.t(msgReceived)} />

    </>
  );
  
  return (
    <div
      onKeyPress={null}
      // aria-label="Policy Criterias"
      role="button"
      tabIndex="0">
      {renderContent()}
    </div>
  );
}

PartnerTopicDiscovery.propTypes = {
  value: PropTypes.shape({}),
};

PartnerTopicDiscovery.defaultProps = {
  value: {},
};
  
export default withTranslation()(PartnerTopicDiscovery);
