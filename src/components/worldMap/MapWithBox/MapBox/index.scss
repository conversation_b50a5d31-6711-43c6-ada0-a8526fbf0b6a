@import 'scss/colors.scss';
@import 'scss/mixins.scss';

.map-box {
    @include DisplayFlex;
    flex-direction: column;
    border-bottom: 1px solid $grey25;
    background: $blue17;
    width: 200px;
    position: relative;

    .content {
        padding: 10px;
        height: 100%;

        .title {
            @include DisplayFlex;
            align-items: center;

            p {
                color: var(--semantic-color-content-interactive-primary-default);
                font-size: 15px;
                margin-left: 12px;
            }
        }
    }

    .separator {
        height: 3px;
        flex: .8;
        border-radius: 1.5px;
        background-color: $grey25;
        width: 80%;
        margin: 0 auto;
        position: absolute;
        right: 0;
        left: 0;
        bottom:  -2px;
        z-index: 9999;
    }

}