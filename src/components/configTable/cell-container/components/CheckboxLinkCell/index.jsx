import React, { useRef } from 'react';
import { NavLink } from 'react-router-dom';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import i18n from 'utils/i18n';
import { isEmpty, noop } from 'utils/lodash';
import { useDispatch } from 'react-redux';

function CheckboxLinkCell(props) {
  const dispatch = useDispatch();
  const {
    value, original, skipTranslation, onHandleLink, onHandleCheck,
  } = props;
  const {
    id, parentId, scheduled, link,
  } = original;
  const linkRef = useRef(null);

  const renderContent = () => (
    <div className={parentId ? 'child-checkbox' : 'parent-checkbox'}>

      <label htmlFor={id} className="child-checkbox-label">
        <input
          className="child-checkbox-input"
          id={id}
          name={id}
          // eslint-disable-next-line no-nested-ternary
          aria-label={isEmpty(value) ? id : skipTranslation ? value : i18n.t(value)}
          onChange={() => onHandleCheck(id, parentId)}
          checked={scheduled}
          type="checkbox" />
        <NavLink
          className="checkboxlink-cell-url"
          to={link}
          onClick={() => {
            dispatch(onHandleLink(value));
          }}>
          {skipTranslation ? value : i18n.t(value)}
        </NavLink>
        
      </label>
    </div>
  );

  return (
    <div ref={linkRef}>
      {renderContent()}
    </div>
  );
}

CheckboxLinkCell.propTypes = {
  value: PropTypes.string,
  original: PropTypes.shape({
    id: PropTypes.string,
    parentId: PropTypes.string,
    scheduled: PropTypes.bool,
    link: PropTypes.string,
  }),
  skipTranslation: PropTypes.bool,
  onHandleLink: PropTypes.func,
  onHandleCheck: PropTypes.func,
};

CheckboxLinkCell.defaultProps = {
  value: '',
  original: {},
  skipTranslation: false,
  onHandleLink: null,
  onHandleCheck: noop,
};

export default withTranslation()(CheckboxLinkCell);
