// @flow

import React from 'react';
import PropTypes from 'prop-types';
import { Trans, withTranslation } from 'react-i18next';

import './index.scss';

function FormValueLabel(props) {
  const { text, styleClass } = props;

  return (
    <p className={`form-value-label ${styleClass}`}>
      <Trans>{text}</Trans>
    </p>
  );
}

FormValueLabel.propTypes = {
  text: PropTypes.string,
  styleClass: PropTypes.string,
};

FormValueLabel.defaultProps = {
  text: '',
  styleClass: '',
};

export default withTranslation()(FormValueLabel);
