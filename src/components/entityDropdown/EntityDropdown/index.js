// @flow

import Select from 'react-select';
import React from 'react';
import PropTypes from 'prop-types';
import { Trans, withTranslation } from 'react-i18next';
import {
  filter, includes, intersectionBy, isNil,
} from 'utils/lodash';

// eslint-disable-next-line import/no-cycle
import EntityDropdownPlaceholder from '../EntityDropdownPlaceholder';
import './index.scss';

export const changeHandler = (func) => (value) => func(value);

export const transformValue = (value, options, isMulti) => {
  if (!value) return '';

  let filteredOptions = [];

  if (isMulti
    && Array.isArray(value)
    && value.length
    && value[0]
    && typeof value[0] === 'object'
    && 'id' in value[0]) { // value is an array of objects with id
    filteredOptions = intersectionBy(options, value, 'id');
  } else if (isMulti
    && Array.isArray(value)
    && value.length
    && value[0]
    && (typeof value[0] === 'string' || typeof value[0] === 'number')) { // value is an array of string/number
    filteredOptions = filter(options, (option) => includes(value, option.id));
  } else if (!isNil(value.id)) { // value is an object with id
    filteredOptions = filter(options, (option) => option.id === value.id);
  } else if (typeof value === 'string' || typeof value === 'number') { // value is a string/number
    filteredOptions = filter(options, (option) => option.id === value);
  }

  return isMulti ? filteredOptions : filteredOptions[0];
};

class EntityDropdown extends React.PureComponent {
  static propTypes = {
    ariaLabel: PropTypes.string,
    className: PropTypes.string,
    id: PropTypes.string,
    data: PropTypes.arrayOf(PropTypes.shape()).isRequired,
    icon: PropTypes.shape(),
    input: PropTypes.shape().isRequired,
    isMulti: PropTypes.bool,
    meta: PropTypes.shape({
      active: PropTypes.bool,
      touched: PropTypes.bool,
      error: PropTypes.string,
    }).isRequired,
    placeholder: PropTypes.string,
    t: PropTypes.func,
    disable: PropTypes.bool,
    isClearable: PropTypes.bool,
    isSearchable: PropTypes.bool,
    noOptionsMessage: PropTypes.func,
    selectProps: PropTypes.shape({}),
    hasNextPage: PropTypes.bool,
    loadNextPage: PropTypes.func,
    noTranslation: PropTypes.bool,
    disableErrorMsg: PropTypes.bool,
  };

  static defaultProps = {
    id: null,
    ariaLabel: 'select',
    className: '',
    icon: null,
    isMulti: false,
    placeholder: 'SELECT',
    t: (str) => str,
    disable: false,
    isClearable: false,
    isSearchable: true,
    noOptionsMessage: () => null,
    hasNextPage: false,
    loadNextPage: () => null,
    noTranslation: false,
    disableErrorMsg: false,
  };

  handleBottom = () => {
    const { hasNextPage, loadNextPage } = this.props;
    if (!hasNextPage) return null;

    return loadNextPage();
  };

  render() {
    const {
      ariaLabel,
      className,
      data: allOptions,
      icon,
      input,
      meta,
      isMulti,
      placeholder,
      t,
      id,
      disable,
      isClearable,
      isSearchable,
      noOptionsMessage,
      selectProps,
      hasNextPage,
      noTranslation,
      disableErrorMsg,
    } = this.props;
    const {
      name, value, onBlur, onChange, onFocus, data: filteredOptions,
    } = input;
    const options = filteredOptions || allOptions;
    const transformedValue = transformValue(value, options, isMulti);
    const hasError = !meta.active && meta.touched && !!meta.error;
    let unloadedValue;
    if (value && hasNextPage && !options.some((x) => x.id === value.id)) {
      options.unshift(value);
      unloadedValue = value.name;
    }

    return (
      <div className={`select-item entity-dropdown ${hasError ? 'error' : ''} ${disable ? 'entity-dropdown--disabled' : ''}`}>
        <Select
          id={id}
          name={name}
          aria-label={ariaLabel}
          value={transformedValue}
          isMulti={isMulti}
          isDisabled={disable}
          onChange={changeHandler(onChange)}
          options={options}
          onBlur={() => onBlur(transformedValue)}
          onFocus={onFocus}
          getOptionLabel={(option) => (noTranslation ? option.name : t(option.name))}
          getOptionValue={(option) => option.id}
          className={className}
          classNamePrefix="react-select"
          noOptionsMessage={noOptionsMessage}
          placeholder={(
            <EntityDropdownPlaceholder
              icon={icon}
              placeholder={t(unloadedValue || placeholder)} />
          )}
          isClearable={isClearable}
          isSearchable={isSearchable}
          onMenuScrollToBottom={this.handleBottom}
          {...selectProps} />
        <div className="error-container">{hasError && !disableErrorMsg && <p><Trans>{meta.error}</Trans></p>}</div>
      </div>
    );
  }
}

export default withTranslation()(EntityDropdown);
