/* eslint-disable max-len */
/* eslint-disable react/prop-types */
// @flow
import React from 'react';
import PropTypes from 'prop-types';
import L, { divIcon } from 'leaflet';
import 'leaflet.markercluster';
import { Map, TileLayer, Marker } from 'react-leaflet';
import { renderToStaticMarkup } from 'react-dom/server';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faLocationDot } from '@fortawesome/pro-solid-svg-icons';
import Select from 'react-select';
import { isEmpty } from 'utils/helpers';
import { FormFieldLabel } from 'components/label';

import './index.scss';

const iconPerson = new L.Icon({
  iconUrl: './images/marker-icon.png$',
  iconRetinaUrl: './images/layers.png$',
  iconAnchor: null,
  popupAnchor: null,
  shadowUrl: null,
  shadowSize: null,
  shadowAnchor: null,
  iconSize: new L.Point(60, 75),
  className: 'leaflet-div-icon',
});

export { iconPerson };

function mapWithSearch(props) {
  const {
    onChange, onChangeInput, t, location, options,
  } = props;
  const {
    label, latitude = 0, longitude = 0, countryCode,
  } = location || {};
  const hasLocation = !isEmpty(location);

  const position = [latitude, longitude]; // 49.3140, -123.1414,

  const image = renderToStaticMarkup(
    <FontAwesomeIcon icon={faLocationDot} size="3x" className="location-dot" />,
  );

  const icon = divIcon({
    iconSize: 'auto',
    html: image,
    className: 'map-search-leaflet-div-icon',
  });

  const onKeyDown = (e) => {
    if (e.keyCode === 13) {
      e.preventDefault();
      e.stopPropagation();
      onChangeInput(e.target.value);
    }
  };

  const handleBlur = (e) => {
    onChangeInput(e.target.value);
  };

  const changeHandler = (e) => {
    onChange(e);
  };

  return (
    <div className="map-search">
      <div className="map-search-container">
        <div className="map-search-input">
          <Select
            placeholder={t('SEARCH_LOCATION')}
            noOptionsMessage={() => null}
            onChange={changeHandler}
            onBlur={handleBlur}
            onFocus={() => null}
            onKeyDown={onKeyDown}
            isClearable
            isSearchable
            options={options}
            components={{ DropdownIndicator: () => null, IndicatorSeparator: () => null }} />
        </div>
        <div className="map-search-image">
          <Map
            className="markercluster-map-with-search"
            center={position}
            // zoomDelta={0.5}
            zoom={hasLocation ? 7 : 2}
            scrollWheelZoom={false}
            maxZoom={16}
            minZoom={1}
            // dragging={false} // will disable dragging
            fitWorld>
            <TileLayer
              // url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />
              // url="https://cartodb-basemaps-{s}.global.ssl.fastly.net/dark_all/{z}/{x}/{y}.png" />
              url="https://server.arcgisonline.com/ArcGIS/rest/services/Canvas/World_Light_Gray_Base/MapServer/tile/{z}/{y}/{x}.png" />

            {hasLocation && (
              <Marker
                key="{geoLocation}{index}"
                position={position}
                icon={icon} />
            )}
          </Map>
        </div>
      </div>

      <div className="map-info-container">
        <div className="input-container review half-width">
          <FormFieldLabel
            text={t('LATITUDE')} />
          <input type="text" value={hasLocation ? latitude : ''} />
        </div>
        <div className="input-container review half-width">
          <FormFieldLabel
            text={t('LONGITUDE')} />
          <input type="text" value={hasLocation ? longitude : ''} />
        </div>
        <div className="input-container review half-width">
          <FormFieldLabel
            text={t('COUNTRY')} />
          <input type="text" value={hasLocation ? countryCode : ''} />
        </div>
        <div className="input-container review half-width">
          <FormFieldLabel
            text={t('LOCATION_DETAILS')} />
          <input type="text" value={hasLocation ? label : ''} />
        </div>
      </div>
    </div>
  );
}

mapWithSearch.propTypes = {
  onChange: PropTypes.func,
  onChangeInput: PropTypes.func,
  t: PropTypes.func,
  options: PropTypes.arrayOf(PropTypes.shape({})),
};

mapWithSearch.defaultProps = {
  onChange: null,
  onChangeInput: null,
  t: (str) => str,
  options: [],
};

export default mapWithSearch;
