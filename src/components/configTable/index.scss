.ec-root-page{
.table-container .head .column {
  display: flex;
  justify-content: space-between;

  & .left,
  .right {
    display: flex;
    align-items: center;
  }

  &.right {
    margin-right: 5px;
  }

  & .resizer {
    cursor: e-resize !important;
    display: inline-block;
    // background: ;
    width: 20px;
    height: 100%;
    position: absolute;
    right: 0;
    top: 0;
    transform: translateX(50%);
    z-index: 1;
    touch-action: none;
  }
  .fa-info-circle-icon{
    margin-left: 4px;
    color: var(--semantic-color-content-status-info-primary);
  }
}
.list-container,.list-container-infinite{
  overflow: auto;
}
}