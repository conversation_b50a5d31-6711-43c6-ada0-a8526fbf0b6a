import React from 'react';
import PropTypes from 'prop-types';
import { ResponsivePie } from '@nivo/pie';
import { withTranslation } from 'react-i18next';
import {
  getNumberRange,
} from 'utils/helpers';

const margin = {
  top: 30,
  right: 50,
  bottom: 30,
  left: 50,
};
const styles = {
  root: {
    fontFamily: 'consolas, sans-serif',
    textAlign: 'center',
    position: 'relative',
    marginLeft: '5em',
    width: '55em',
    height: '16em',
    marginTop: '2em',
  },
  overlay: {
    position: 'absolute',
    right: '0.8em',
    bottom: '1.5em',
    left: margin.left,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: 55,
    color: '#9a9190',
    textAlign: 'center',
    // This is important to preserve the chart interactivity
    pointerEvents: 'none',
  },
  totalLabel: {
    color: '#1E1F22',
    fontSize: '13px',
    lineHeight: '20px',
  },
};

const calculateRatio = (data) => {
  let count = 0;
  data.map((e) => {
    if (e.originalValue) {
      count += e.originalValue;
    } else {
      count += e.value;
    }
    return count;
  });
  return count;
};

// const localize = (data, t) => {
//   const localizedData = data;
//   localizedData.map((e) => {
//     e.id = t(e.id);
//     e.ilabeld = t(e.label);
//     return e;
//   });
//   return localizedData;
// };

const localizeD = (data, t) => {
  return t(data);
};

// const CustomSymbolShape = ({
//   x, y, size, fill, borderWidth, borderColor,
// }) => (
//   <rect
//     x={x}
//     y={y}
//     rx="10"
//     fill={fill}
//     strokeWidth={borderWidth}
//     stroke={borderColor}
//     width="44"
//     height="26"
//     style={{ pointerEvents: 'none' }} />
// );

export function Pie(props) {
  const {
    data,
    colors,
    innerText,
    legendsPos,
    t,
    isInteractive,
    enableArcLinkLabels,
    activeOuterRadiusOffset,
  } = props;
  let total = calculateRatio(data);
  if ((Math.round(total * 1000) / 1000) !== 0) {
    total = Math.round(total * 1000) / 1000;
  }

  if (total && !Number.isInteger(total)) {
    total = total.toFixed(2);
  }

  if (total && Number.isInteger(total)) {
    total = getNumberRange(total);
  }
  
  const locData = data.map((e) => {
    e.id = localizeD(e.id, t);
    e.ilabeld = t(e.label);
    return e;
  });

  return (
    <div className="section">
      <div style={styles.root}>
        <ResponsivePie
          data={locData}
          innerRadius={0.55}
          padAngle={1}
          cornerRadius={0}
          colors={colors}
          borderWidth={1}
          borderColor={{ from: 'color', modifiers: [['darker', 0.2]] }}
          isInteractive={isInteractive}
          enableArcLinkLabels={enableArcLinkLabels}
          arcLabelsSkipAngle={25}
          // activeInnerRadiusOffset={50}
          activeOuterRadiusOffset={activeOuterRadiusOffset}
          enableRadialLabels={false}
          radialLabelsSkipAngle={10}
          radialLabelsTextXOffset={6}
          radialLabelsTextColor="#333333"
          radialLabelsLinkOffset={0}
          radialLabelsLinkDiagonalLength={16}
          radialLabelsLinkHorizontalLength={24}
          radialLabelsLinkStrokeWidth={1}
          radialLabelsLinkColor={{ from: 'color' }}
          slicesLabelsSkipAngle={10}
          slicesLabelsTextColor="#fdfdfd"
          animate
          motionStiffness={90}
          motionDamping={15}
          legends={[
            {
              anchor: legendsPos,
              direction: 'column',
              translateY: 30,
              translateX: 0,
              itemHeight: 26,
              itemWidth: 54,
              borderRadius: '23px',
              itemTextColor: '#999',
              symbolSize: 23,
              symbolShape: 'square',
              effects: [
                {
                  on: 'hover',
                  style: {
                    itemTextColor: '#000',
                  },
                },
              ],
            },
          ]} />
        <div style={styles.overlay}>
          {
            total && typeof total === 'object'
            && (
              <>
                <span className="totalTxn">
                  {`${total.value}`}
                  {`${total.unit ? total.unit : ''}`}
                </span>
                <span className="pie-inner-text">{innerText}</span>
              </>
            )
          }
          {
            total && typeof total !== 'object'
            && (
              <>
                <span className="totalTxn">{total}</span>
                <span style={styles.totalLabel}>{innerText}</span>
              </>
            )
          }
        </div>
      </div>
    </div>
  );
}

Pie.propTypes = {
  data: PropTypes.arrayOf(PropTypes.shape({})),
  colors: PropTypes.arrayOf(PropTypes.string),
  innerText: PropTypes.string,
  legendsPos: PropTypes.string,
  t: PropTypes.func,
  isInteractive: PropTypes.bool,
  enableArcLinkLabels: PropTypes.bool,
  activeOuterRadiusOffset: PropTypes.number,
};
  
Pie.defaultProps = {
  data: [],
  colors: [],
  innerText: '',
  legendsPos: 'left',
  t: (str) => str,
  isInteractive: false,
  enableArcLinkLabels: false,
  activeOuterRadiusOffset: 16,
};

export default withTranslation()(Pie);
