import React from 'react';
import PropTypes from 'prop-types';
import i18n from 'utils/i18n';
import { useTooltip } from '@nivo/tooltip';
import getTextWidth from '../../../utils/helpers/getTextWidth';

const noop = () => undefined;

function ToolTipMessage({ selectedDataKey, actionToolTip }) {
  return selectedDataKey && !actionToolTip
    ? (
      <div id="tooltip" className="tooltip">
        <div className="ec-tooltip-container">
          <div id="tooltip-top" className="tooltip-top tooltip-top-text">{selectedDataKey}</div>
          <div id="tooltip-bottom" className="tooltip-bottom tooltip-bottom-text">{i18n.t('CLICK_FOR_MORE_INFO')}</div>
        </div>
      </div>
    )
    : <></>;
}

ToolTipMessage.propTypes = {
  selectedDataKey: PropTypes.string,
  actionToolTip: PropTypes.bool,
};

function BarText(
  props,
  sortedData,
  styles,
  setStyles,
  selectedDataKey,
  setSelectedDataKey,
  actionToolTip,
  setActionToolTip,
) {
  const {
    x, y, width, color, data,
  } = props;

  const { showTooltipFromEvent, hideTooltip } = useTooltip();

  const { total } = data && data.data ? data.data : {};

  const axisLabel = data && data.data ? total : '';

  const axisDetail = `${data.indexValue}`;

  const axisLabelWidth = getTextWidth(axisLabel, 12);

  const initializeStyles = () => {
    const iniStyles = [];
    const dataKeys = Object.keys(sortedData);
  
    dataKeys.forEach((key) => {
      // eslint-disable-next-line react/destructuring-assignment
      iniStyles.push({ id: sortedData[key].id, opacity: 1, strokeWidth: 1 });
    });
  
    return iniStyles;
  };

  const onHandleMouseEnter = (event) => {
    if (actionToolTip) return;
    setSelectedDataKey(axisDetail);
    const newStyle = initializeStyles(sortedData);
    setStyles(() => newStyle.map((item) => (item.id === axisDetail
      ? { ...item, opacity: 1 }
      : { ...item, opacity: 0.3 })));
    showTooltipFromEvent(<ToolTipMessage id="total" selectedDataKey={axisDetail} actionToolTip={actionToolTip} />, event);
  };

  const handleOnClick = (event) => {
    if (actionToolTip) setStyles(() => initializeStyles(sortedData));
    setActionToolTip(!actionToolTip);
    setSelectedDataKey(axisDetail);
    onHandleMouseEnter(event);
  };

  return (
    <g
      transform={`translate(${x},${y})`}
      onClick={handleOnClick}
      onMouseEnter={onHandleMouseEnter}
      onMouseLeave={() => {
        hideTooltip();
        if (!actionToolTip) setStyles(() => initializeStyles(sortedData));
      }}
      style={{
        opacity: styles && styles.find((item) => (item.id === axisDetail))
          ? (styles.find((item) => (item.id === axisDetail))).opacity
          : 1,
      }}>
      <rect className="bar-text-rectangle-1" width={width} height="24" fill={color} stroke="#00000" rx="5" />
      <text
        x={-(axisLabelWidth + 10)}
        y={12}
        textAnchor="start"
        dominantBaseline="central"
        fill="#666666"
        style={{
          fontSize: '12px',
        }}>
        {axisLabel}
      </text>
      <text
        x={10}
        y={12}
        textAnchor="start"
        dominantBaseline="central"
        fill="#666666"
        style={{
          fontSize: '12px',
        }}>
        {axisDetail}
      </text>
    </g>
  );
}

BarText.propTypes = {
  x: PropTypes.number,
  y: PropTypes.number,
  width: PropTypes.number,
  color: PropTypes.string,
  data: PropTypes.shape({}),
};

BarText.defaultProps = {
  x: 1,
  y: 1,
  width: 0,
  color: '',
  data: noop,
};

export default BarText;
