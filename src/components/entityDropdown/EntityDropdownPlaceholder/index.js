import React from 'react';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Trans, withTranslation } from 'react-i18next';

function EntityDropdownPlaceholder({ placeholder, icon }) {
  return (
    <div className="drill-down-placeholder">
      {icon && <FontAwesomeIcon icon={icon} />}
      <Trans>{placeholder}</Trans>
    </div>
  );
}

EntityDropdownPlaceholder.propTypes = {
  placeholder: PropTypes.string,
  icon: PropTypes.shape(),
};

EntityDropdownPlaceholder.defaultProps = {
  placeholder: null,
  icon: null,
};

export default withTranslation()(EntityDropdownPlaceholder);
