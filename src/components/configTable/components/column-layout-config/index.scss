.column-layout-config-container {
    display: inline-block;
    position: absolute;
    top: 0px;
    right: 0px;
    height: 38px;
    width: 30px;
    z-index: 1;
    background: var(--semantic-color-surface-table-header-default);
    border-bottom: 1px solid var(--semantic-color-border-base-primary);


    &:hover {
      & .config-container {
        display: none;
      }
    } 

    .icon {
      color: var(--semantic-color-content-status-info-primary);
      cursor: pointer;
      padding: 11px 0px;
      text-align: center;
    }

    .config-container {      
      background-color: var(--semantic-color-background-pale);
      border-radius: .3125rem;
      box-shadow: var(--semantic-color-border-base-primary) 0 .125rem .325rem;
      color: var(--brand-default);
      cursor: pointer;
      display: table;
      -ms-flex-wrap: nowrap;
      -webkit-text-decoration: none;
      min-width: 15.625rem;
      padding: .8rem .8rem .5rem;
      position: absolute;
      right: 0;
      text-align: left;
      z-index: 99;
      display: none;
      // background: #f1f2f3;
      // border: 1px solid #e0e1e3;
      // font-size: 13px;
      // right: -1px;
      // max-height: 200px;
      // min-width: 300px;
      // width: auto;
      // overflow-y: auto;
      // overflow-x: hidden;
      // position: absolute;
      // text-align: left;
      // text-transform: none;
      // top: 30px;
      // white-space: nowrap;
      // z-index: 9999;
      // border-radius: 5px 0 5px 5px;
      // color: var(--semantic-color-content-base-primary);
      // cursor: default;
      &.display-block {
        display: block;
        overflow:  visible;
        padding: 0;
        min-width: 15.625rem;
        .column-config{
          background: (--semantic-color-background-pale);
          &:hover {
            background-color: var(--semantic-color-surface-fields-hover);
          }
        }
      }

      & .actions {
        background: var(--brand-default);
        border-radius: .3125rem .3125rem 0 0;
        font-size: 11px;
        padding: 8px;
        cursor: default;        
        display: flex;
        &.table-column-menu-footer {
          border-radius: 0 0 .3125rem .3125rem;
          box-shadow: var(--semantic-color-border-base-primary) 0 .125rem .325rem;
          background: var(--semantic-color-surface-base-primary);          
          padding: .3125rem;
          .table-column-menu-footer-buttons {
            width: 100%;
            display: flex;
            justify-content: space-between;
          }
          button.cancel {
            background-color: transparent;
            border: none;
            border-radius: .3125rem;
            color: var(--semantic-color-content-interactive-primary-default);
            font-size: .8125rem;
            padding: .375rem .9375rem;
          }
        }
        & .action {
          color: var(--semantic-color-content-status-info-primary);
          cursor: pointer;
          font-size: 13px;
          margin-right: 25px;
          position: static;
          top: 5px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          &:last-child {
            margin-right: 0;
          }

           & .action-icon {
            font-size: 16px;
            margin-right: 5px;
            position: relative;
            top: 1px;
           }
        }
      }
      .table-column-menu-header-text {
        background: var(--brand-default);
        border-radius: .3125rem .3125rem 0 0;
        // color: var(--semantic-color-background-primary);
        color: var(--semantic-color-content-interactive-primary-default);
        padding: .5rem;
      }

    }

    & .table-column-menu-header i.fas {
      color: var(--semantic-color-content-status-info-primary);
      vertical-align: middle;
      cursor: pointer;
    }

    & .table-column-menu-header-text {
      display: inline-block;
      font-size: 13px;
      text-align: left;
      width: 148px;
      vertical-align: middle;
    }

    & .config {
      overflow-y: auto;
      & .column-config {
        padding: 3px 12px;
        position: relative;
        align-items: center;
        font-size: 13px;
        color: var(--semantic-color-content-interactive-primary-default);
        background-color: var(--semantic-color-surface-table-row-default);
        cursor: default;
        white-space: nowrap;
        &:hover {
          cursor: move;
          background-color: var(--semantic-color-surface-table-row-default);
        }

        & .checkbox {
          height: 30px;
          line-height: 30px;
          margin-right: 8px;
          vertical-align: top;
          cursor: pointer;
          font-size: 16px;
        }

        & .name {
          height: 30px;
          line-height: 30px;
          color: #656666;
          display: inline-block;
          vertical-align: middle;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          width: 125px;
          padding-right: 5px;
        }

        & .icon {
          font-size: 10px;
          color: #747272;
        }
      }
    }
}
