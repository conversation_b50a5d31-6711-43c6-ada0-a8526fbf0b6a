// @flow
import { NavLink } from 'react-router-dom';
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import NAVBAR_ROUTES from 'config';
import EdgeConnectorLogo from 'images/ec_logo_admin_ui.png';
import './index.scss';
import { toggleActivationPanel } from 'ducks/activation';
import * as loginSelectors from 'ducks/login/selectors';
import ActivationNav from './activationNav';
import Logout from './logout/Logout';
import AdminNav from './adminNav';
import PolicyNav from './policyNav';
import AnalyticsNav from './analyticsNav';
import SearchNav from './searchNav';
import HelpNav from './helpNav';

class Navbar extends Component {
  static propTypes = {
    actions: PropTypes.shape({}),
    data: PropTypes.shape({}),
    location: PropTypes.shape({ pathname: PropTypes.string }),
    t: PropTypes.func,
    accessPermissions: PropTypes.shape({}),
  };

  static defaultProps = {
    actions: {},
    data: {},
    location: null,
    t: (str) => str,
    accessPermissions: {},
  };

  // routes will come from top level route
  active = (prop) => {
    // const { location } = window;
    const { location } = this.props;
    if (!location) return '';
    const { pathname } = location;
    // we will make following dynamic based on url
    if (pathname.includes(prop.path)) {
      return 'active';
    }
    return '';
  };

  getRoutes = (pageroutes) => pageroutes.map((prop) => {
    const className = this.active(prop);
    const {
      t,
      accessPermissions,
    } = this.props;

    let nestedElement;
    if (prop.name === 'FORWARDING' && accessPermissions.EDGE_CONNECTOR_FORWARDING === 'NONE') return <></>;
    if (prop.name === 'ANALYTICS' && accessPermissions.EDGE_CONNECTOR_DASHBOARD === 'NONE') return <></>;
    if (prop.name === 'DASHBOARD' && accessPermissions.EDGE_CONNECTOR_DASHBOARD === 'NONE') return <></>;
    if (prop.name === 'ADMINISTRATION'
      && accessPermissions.EDGE_CONNECTOR_ADMIN_MANAGEMENT === 'NONE'
      && accessPermissions.EDGE_CONNECTOR_CLOUD_PROVISIONING === 'NONE'
      && accessPermissions.EDGE_CONNECTOR_LOCATION_MANAGEMENT === 'NONE'
      && accessPermissions.EDGE_CONNECTOR_NSS_CONFIGURATION === 'NONE'
      && accessPermissions.EDGE_CONNECTOR_FORWARDING === 'NONE'
      && accessPermissions.EDGE_CONNECTOR_TEMPLATE === 'NONE'
      && accessPermissions.EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT === 'NONE'
    ) return <></>;

    if (prop.name === 'ACTIVATION') {
      nestedElement = <ActivationNav />;
    } else if (prop.name === 'ADMINISTRATION') {
      nestedElement = <AdminNav />;
    } else if (prop.name === 'ANALYTICS') {
      nestedElement = <AnalyticsNav />;
    } else if (prop.name === 'FORWARDING') {
      nestedElement = <PolicyNav />;
    } else if (prop.name === 'SEARCH') {
      nestedElement = <SearchNav />;
    } else if (prop.name === 'HELP') {
      nestedElement = <HelpNav />;
    }

    return (
      <li key={prop.path || prop.name} className={`${className} ${prop.liclass ? prop.liclass : ''}`}>
        {(prop.name === 'ANALYTICS' || prop.name === 'ACTIVATION' || prop.name === 'ADMINISTRATION' || prop.name === 'FORWARDING' || prop.name === 'SEARCH' || prop.name === 'HELP')
          ? nestedElement
          : (
            <NavLink to={`${prop.redirectTo ? prop.redirectTo : (prop.layout + prop.path)}`} aria-label={t(prop.name || prop.hiddenName)} activeclassname="active">
              {prop.icon && <FontAwesomeIcon className={prop.iclass} icon={prop.icon} />}
              <span className="text-align-center">{t(prop.name)}</span>
            </NavLink>
          )}
      </li>
    );
  });

  render() {
    return (
      <nav className="ec-sidebar">
        <ul className="sidemenu">
          <li className="zlogo">
            <img src={EdgeConnectorLogo} className="logo" alt="Branch and Cloud Connector" />
          </li>
          {this.getRoutes(NAVBAR_ROUTES)}
          <Logout />
        </ul>
      </nav>
    );
  }
}

const mapStateToProps = (state) => ({
  accessPermissions: loginSelectors.accessPermissionsSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    toggleActivationPanel,
  }, dispatch);

  return {
    actions,
  };
};

export default withTranslation()(connect(
  mapStateToProps,
  mapDispatchToProps,
)(Navbar));

export { Navbar };
