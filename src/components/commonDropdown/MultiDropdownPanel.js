/* eslint-disable jsx-a11y/label-has-for */
import React, {
  useEffect, useMemo,
} from 'react';
import PropTypes from 'prop-types';

function MultiDropdownPanel(props) {
  const {
    options, selectedValues, setSelectedValues, dropdownRef, hideDropdown,
    searchText, defaultSelected, triggerChangeOnSelect, triggerChange,
  } = props;

  useEffect(() => {
    const listener = (e) => {
      if (!dropdownRef.current.contains(e.target)) {
        hideDropdown(selectedValues);
      }
    };

    document.addEventListener('click', listener);
    // document.addEventListener('focusin', listener);
    return () => {
      document.removeEventListener('click', listener);
      // document.removeEventListener('focusin', listener);
    };
  }, [selectedValues]);

  const optionsWithSelection = useMemo(() => {
    if (triggerChangeOnSelect) {
      triggerChange(selectedValues);
    }
    return options.map((x) => ({
      ...x,
      selected: selectedValues.map((item) => item.value).includes(x.value),
    }));
  }, [options, selectedValues]);

  const filteredOptions = useMemo(() => {
    return optionsWithSelection?.filter(
      (x) => x?.label?.toLowerCase().includes(searchText?.toLowerCase()),
    );
  }, [searchText, optionsWithSelection]);

  function Checkbox({
    // eslint-disable-next-line react/prop-types
    value, label, selected, onChange, disabled,
  }) {
    return (
      <div className="option-row-dropdown" key={value}>
        <input
          type="checkbox"
          id={`chk-${value}`}
          checked={selected}
          onChange={() => onChange(!selected)}
          disabled={disabled}
          style={{ cursor: disabled ? 'not-allowed' : 'pointer' }} />
        <label htmlFor={`chk-${value}`} className={`${selected ? 'option-text' : (disabled && 'text-muted')}`} style={{ cursor: disabled ? 'not-allowed' : 'pointer' }}>{label}</label>
      </div>
    );
  }

  const isSelectedAll = useMemo(
    () => (optionsWithSelection.filter((x) => x.selected).length === [
      ...options.filter((obj) => !obj.disabled),
      ...defaultSelected].length) && options.filter((obj) => !obj.disabled).length > 0,
    [options, optionsWithSelection],
  );

  const getSelectedAllCount = (Options) => {
    return `Select All  (${[...Options.filter((obj) => !obj.disabled), ...defaultSelected].length})`;
  };

  const handleSelectAll = (selected) => {
    if (selected) {
      setSelectedValues([...options.filter((obj) => !obj.disabled), ...defaultSelected]);
    } else {
      setSelectedValues(defaultSelected);
    }
  };

  useEffect(() => {
    if (document.getElementById('chk-all')) {
      if (isSelectedAll || selectedValues.length === 0) {
        document.getElementById('chk-all').indeterminate = false;
      } else {
        document.getElementById('chk-all').indeterminate = true;
      }
    }
  }, [selectedValues, filteredOptions]);

  const handleChange = (val, checked) => {
    if (checked) {
      setSelectedValues((prevValue) => Array.from(new Set([...prevValue, val])));
    } else {
      setSelectedValues((prevValue) => prevValue.filter((x) => x.value !== val.value));
    }
  };

  return (
    <>
      {filteredOptions.length > 0 ? (
        <>
          <div className="dropdown-select-all-container">
            <Checkbox value="all" label={getSelectedAllCount(options)} selected={isSelectedAll} disabled={options.filter((obj) => !obj.disabled).length === 0} onChange={handleSelectAll} />
          </div>
          <div key={filteredOptions} className="dropdown-options-container">
            {filteredOptions.map((option) => (
              <Checkbox
                key={option.value}
                value={option.value}
                disabled={option.disabled}
                label={option.label}
                selected={option.selected}
                onChange={(val) => handleChange(option, val)} />
            ))}
          </div>
        </>
      )
        : <p className="dropdown-noOptions">No Options</p>}
    </>
  );
}

MultiDropdownPanel.propTypes = {
  options: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
      disabled: PropTypes.bool,
    }),
  ).isRequired,
  selectedValues: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
    }),
  ).isRequired,
  setSelectedValues: PropTypes.func.isRequired,
  dropdownRef: PropTypes.func.isRequired,
  searchText: PropTypes.string,
  hideDropdown: PropTypes.func.isRequired,
  defaultSelected: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
    }),
  ).isRequired,
  triggerChangeOnSelect: PropTypes.bool,
  triggerChange: PropTypes.func.isRequired,
};

MultiDropdownPanel.defaultProps = {
  searchText: '',
  triggerChangeOnSelect: false,
};

export default MultiDropdownPanel;
