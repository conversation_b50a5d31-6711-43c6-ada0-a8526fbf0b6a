import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import { get } from 'utils/lodash';

import MultiDropdownWithAsyncSearch from 'components/dropDown/MultiDropdownWithAsyncSearch';
import {
  dropdownActions,
  selector,
  onSearchStringChange,
  onPerformSearch,
} from 'ducks/dropdowns/locationsSublocations';
import labelHelper from './labelHelper';

export const getUnselectedValues = (state) => {
  const { selectedValueMap, data } = selector(state).dropdown;

  const unselectedValues = data.filter((x) => x.id > 0).map((item, index) => {
    const checked = !!selectedValueMap[get(item, 'id')];
    const disabled = false;
    // const disabled = !!excludeCriteriaSelectedValueMap[get(item, 'id')];
    return {
      original: { ...item, parent: 1 },
      id: get(item, 'id', index),
      name: get(item, 'name', ''),
      checked,
      disabled,
    };
  });
  return unselectedValues;
};

export const mapStateToProps = (state, ownProps) => {
  const {
    t, meta, label, tooltip,
  } = ownProps;

  const dropdownState = selector(state).dropdown;
  const { selectedValues, values } = dropdownState;
  const dropdownLabels = labelHelper(t, selectedValues, values, tooltip || 'TOOLTIP_USED_IN_EXCLUDED_CRITERIA', label || 'ALL_ZSCALER_LOCATIONS');

  return {
    ...dropdownState,
    ...dropdownLabels,
    unselectedValues: getUnselectedValues(state),
    meta,
  };
};

export const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    ...dropdownActions,
    onSearchStringChange,
    onPerformSearch,
  }, dispatch);

  return {
    actions,
  };
};

export default withTranslation()(connect(
  mapStateToProps,
  mapDispatchToProps,
)(MultiDropdownWithAsyncSearch));
