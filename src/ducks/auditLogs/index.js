/* eslint-disable no-param-reassign */
import { createAction, loading, loadError } from 'ducks/generics';

import { genericInterface } from 'utils/http';
import { notifyError } from 'ducks/notification';
import {
  get, isString, isEmpty, isEqual, concat, isNull, isUndefined,
} from 'utils/lodash';

import {
  convertStartTime,
  convertEndTime,
} from 'utils/helpers';

import moment from 'moment-timezone';

import * as constants from './constants';
import actionTypes from './action-types';

export { REDUCER_KEY } from './constants';

const actionTypeExists = Object.keys(actionTypes).reduce(
  (hash, key) => ({
    ...hash,
    [actionTypes[key]]: true,
  }),
  {},
);

export const reducer = (state = constants.DEFAULT_STATE, action = {}) => {
  if (actionTypeExists[action.type]) {
    return {
      ...state,
      ...action.payload,
    };
  }
  return state;
};
export default reducer;

const boundLoading = loading(actionTypes.DATA_LOADING);
const boundDataLoadSuccess = createAction(actionTypes.DATA_LOAD_SUCCESS);
const dataChanged = createAction(actionTypes.DATA_CHANGED);
const boundError = loadError(actionTypes.DATA_LOAD_ERROR);

const exportAuditLogsJob = genericInterface(constants.EXPORT_AUDIT_JOB_API);
const exportAuditLogsData = genericInterface(constants.EXPORT_AUDIT_DATA_API);
const exportDownloadApi = genericInterface(constants.EXPORT_AUDIT_DATA_DOWNLOAD_API);
export const clearData = () => (dispatch) => {
  dispatch({
    type: actionTypes.RESET,
    payload: {
      ...constants.DEFAULTS,
    },
  });
};

const canUpdateData = (currentFormValue, PrevFormValue) => {
  if (isUndefined(currentFormValue)) return true;
  // prevent form load when multi-select filters open and close without change the value
  if ((
    isUndefined(PrevFormValue.actionTypes)
    && currentFormValue.actionTypes
    && currentFormValue.actionTypes.length < 1)
  || (
    isUndefined(PrevFormValue.subcategories)
    && currentFormValue.subcategories
    && currentFormValue.subcategories.length < 1)) {
    return false;
  }
  return true;
};

const categoryUpdated = (currentFormValue, PrevFormValue) => {
  if (isUndefined(currentFormValue) || isUndefined(PrevFormValue)) return false;

  if (isEqual(currentFormValue.category, PrevFormValue.category)) {
    return false;
  }
  return true;
};

const constructPayload = (filtersValues, PrevFormValue) => {
  // payload
  const payload = {};
  payload.startTime = convertStartTime(filtersValues.timeFrame);
  payload.endTime = convertEndTime(filtersValues.timeFrame) || moment().unix() * 1000;
  payload.pageSize = filtersValues.noOfRecords || 500;

  // actions
  if (!isEmpty(filtersValues.actionTypes)) {
    payload.actionTypes = filtersValues.actionTypes.map((el) => {
      return el.id;
    });
  }

  // categories
  if (!isEmpty(filtersValues.category) && filtersValues.category.id !== 'all') {
    payload.category = filtersValues.category.id;
  }

  // sub-categories
  if (!isEmpty(filtersValues.subcategories) && !categoryUpdated(filtersValues, PrevFormValue)) {
    payload.subcategories = filtersValues.subcategories.map((el) => {
      return el.id;
    });
  }

  // interface
  if (!isEmpty(filtersValues.actionInterface) && filtersValues.actionInterface.id !== 'all') {
    payload.actionInterface = filtersValues.actionInterface.id;
  }

  // results
  if (!isEmpty(filtersValues.actionResult) && filtersValues.actionResult.id !== 'all') {
    payload.actionResult = filtersValues.actionResult.id;
  }

  const searchVal = document.getElementsByClassName('input-search-box')[0].value;
  if (filtersValues && filtersValues.auditLogsSearch === 'objectName') {
    if (searchVal !== '') payload.objectName = searchVal;
  } else if (filtersValues && filtersValues.auditLogsSearch === 'clientIP') {
    if (searchVal !== '') payload.clientIP = searchVal;
  } else if (searchVal !== '') payload.adminName = searchVal;

  return payload;
};

export const loadAuditLogsTableData = (formData, PrevFormValue) => (dispatch) => {
  const isCategoryUpdated = categoryUpdated(formData, PrevFormValue);
  if (!canUpdateData(formData, PrevFormValue)) {
    return;
  }

  dispatch(boundLoading({ loading: true }));
  const txnDataApi = genericInterface(constants.API_ENDPOINT);
  let payload = {};
  if (formData) {
    if (!formData.timeFrame) formData.timeFrame = 'current_day';
    payload = constructPayload(formData, PrevFormValue);
  } else {
    payload = constructPayload({ timeFrame: 'current_day' });
    payload.page = 1;
  }

  // eslint-disable-next-line consistent-return
  return txnDataApi
    .create(
      payload,
      {},
    )
    .then((response) => {
      const selectedCategory = JSON.parse(response.config.data).category || null;
      dispatch(
        dataChanged({
          loading: false,
          error: null,
          [constants.DATA_TABLE]: response.data,
          [constants.SELECTED_CATEGORY]: { category: selectedCategory },
          [constants.CATEGORY_UPDATED]: isCategoryUpdated,
        }),
      );
    })
    .catch((error) => {
      const errorMsg = get(
        error,
        'response.data.message',
        'Connection to Central Authority is lost',
      );

      dispatch(notifyError(errorMsg));
      dispatch(boundError(error));
    });
};

export const exportTxnPoll = () => {
  return exportAuditLogsJob
    .read()
    .then((response) => {
      const { data } = response;
      if (data.status === 'EXECUTING') {
        return exportTxnPoll();
      }
      if (data.status === 'COMPLETE') {
        return true;
      }
      return false;
    })
    .catch((error) => error);
};

export const getAuditLogsData = () => (dispatch) => {
  dispatch(
    boundDataLoadSuccess({
      loading: false,
    }),
  );
};

export const updateMenu = (columns) => (dispatch) => {
  dispatch(
    dataChanged({
      [constants.COLUMNS]: columns,
    }),
  );
};

export const handleNoOfRecords = (t, appData) => (
  dispatch,
) => {
  dispatch(
    dataChanged({
      [constants.NO_OF_RECORDS]: appData,
    }),
  );
};

export const downloadCSV = (formData) => (dispatch) => {
  dispatch(boundLoading({ loading: true }));

  let payload = {};
  if (formData) {
    if (!formData.timeFrame) formData.timeFrame = 'current_day';
    payload = constructPayload(formData);
  } else {
    payload = constructPayload({ timeFrame: 'current_day' });
    payload.page = 1;
  }

  return exportAuditLogsData
    .create(payload, {})
    .then((response) => {
      if (response.status === 204) {
        exportTxnPoll().then((res) => {
          if (res) {
            exportDownloadApi.read().then((csvresponse) => {
              const { data, headers } = csvresponse;

              const contents = headers['content-disposition'];
              const filenameSt = contents.split(';');
              const filename = filenameSt[1].split('=');

              const hiddenElement = document.createElement('a');
              const fileContent = 'data:text/csv;charset=utf-8,' + data;
              let encodedUri = encodeURI(fileContent);
              encodedUri = encodedUri.replace(/#/g, '%23'); // Resolve # symbol encoding issue
              hiddenElement.setAttribute('href', encodedUri);
              hiddenElement.setAttribute('download', filename[1]);
              hiddenElement.click();
              hiddenElement.remove();

              return dispatch(
                boundDataLoadSuccess({
                  [constants.DONWLOAD_CSV_DATA]: data,
                  loading: false,
                  error: null,
                }),
              );
            }).catch((error) => {
              const errorMsg = get(
                error,
                'response.data.message',
                'Request to Nanolog Server has timed out',
              );
        
              dispatch(notifyError(errorMsg));
              dispatch(boundLoading({ loading: false }));
            });
          }
        }).catch((error) => {
          const errorMsg = get(
            error,
            'response.data.message',
            'Request to Nanolog Server has timed out',
          );
    
          dispatch(notifyError(errorMsg));
          dispatch(boundLoading({ loading: false }));
        });
      }
    })
    .catch((error) => {
      const errorMsg = get(
        error,
        'response.data.message',
        'Connection to Central Authority is lost',
      );

      dispatch(notifyError(errorMsg));
      dispatch(boundError(error));
    });
};

export const getCategoryDropdownData = () => (dispatch) => {
  const categoryDropdownApi = genericInterface(constants.CATEGORY_DROPDOWN_API);
  dispatch(boundLoading({}));
  
  return categoryDropdownApi.read()
    .then((response) => {
      let updatedData = response.data.map((i) => {
        if (isString(i)) {
          const obj = {};
          obj.id = i;
          obj.name = i;
          return obj;
        }
        return i;
      });

      updatedData = concat([{ id: 'all', name: 'ALL' }], updatedData);
      dispatch(dataChanged({
        [constants.CATEGORY_DROPDOWN_DATA]: updatedData,
      }));
    })
    .catch((error) => {
      const errorMsg = get(
        error,
        'response.data.message',
        'There is a problem with loading the data, Please try again later.',
      );
      dispatch(boundError(errorMsg));
    });
};

export const getInterfaceDropdownData = () => (dispatch) => {
  const interfaceDropdownApi = genericInterface(constants.INTERFACE_DROPDOWN_API);
  dispatch(boundLoading({}));
  
  return interfaceDropdownApi.read()
    .then((response) => {
      let updatedData = response.data.map((i) => {
        if (isString(i)) {
          const obj = {};
          obj.id = i;
          obj.name = i;
          return obj;
        }
        return i;
      });
      updatedData = concat([{ id: 'all', name: 'ALL' }], updatedData);
      dispatch(dataChanged({
        [constants.INTERFACE_DROPDOWN_DATA]: updatedData,
      }));
    })
    .catch((error) => {
      const errorMsg = get(
        error,
        'response.data.message',
        'There is a problem with loading the data, Please try again later.',
      );
      dispatch(boundError(errorMsg));
    });
};

export const getResultDropdownData = () => (dispatch) => {
  const resultDropdownApi = genericInterface(constants.RESULT_DROPDOWN_API);
  dispatch(boundLoading({}));
  
  return resultDropdownApi.read()
    .then((response) => {
      let updatedData = response.data.map((i) => {
        if (isString(i)) {
          const obj = {};
          obj.id = i;
          obj.name = i;
          return obj;
        }
        return i;
      });
      updatedData = concat([{ id: 'all', name: 'ALL' }], updatedData);
      dispatch(dataChanged({
        [constants.RESULT_DROPDOWN_DATA]: updatedData,
      }));
    })
    .catch((error) => {
      const errorMsg = get(
        error,
        'response.data.message',
        'There is a problem with loading the data, Please try again later.',
      );
      dispatch(boundError(errorMsg));
    });
};

export const toggleViewModal = (appData, toggle) => (dispatch) => {
  if (typeof appData !== 'undefined' && !isNull(appData)) {
    const formattedTime = (appData.timestamp && moment(appData.timestamp).format('MMM DD h:mm A')) || '';
    dispatch(dataChanged({
      [constants.SHOW_VIEW_FORM]: toggle,
      [constants.APP_DATA]: appData,
      [constants.FORM_TITLE]: `Configuration Changes - ${formattedTime} by ${appData.userName}`,
    }));
  } else {
    dispatch(dataChanged({
      [constants.SHOW_VIEW_FORM]: toggle,
      [constants.APP_DATA]: {},
    }));
  }
};
