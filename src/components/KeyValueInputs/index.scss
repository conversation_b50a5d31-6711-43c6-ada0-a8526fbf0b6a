
.ec-root-page {
.add-edit-cloud-nss-feeds {
    .form-sections-container {
        .form-section {
            .checkbox-container {
                .key-value-pair-container {
                    margin-top: 10px;
                    // .input-wrapper input {
                    //     padding-right: 40px;
                    //     text-overflow: unset;
                    // }
                    .input-wrapper div {
                        display: flex;
                        input {
                            padding-right: 40px;
                            text-overflow: unset;
                        }
                    }
                    .reveal-show-hide-icon {
                        margin-left: -25px;
                        line-height: 32px;
                    }
                    .custom-delete-icon {
                        display: inline-block;
                        margin-left: 15px;
                        color: #939393;
                        .delete-icon {
                            font-size: 14px;
                            cursor: pointer;
                        }
                    }
                }
            }
        }
    }
}
}