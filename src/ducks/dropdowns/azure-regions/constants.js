import dropdown from 'ducks/generics/dropdown';
import AzureRegion from '@zscaler/ec-domain/json/AzureRegion.json';

export const REDUCER_KEY = 'azureRegionsDropdown';
export const STATIC_DATA = 'staticData';

export const DROPDOWN = dropdown.createStateConstants();
export const DROPDOWN_DEFAULT_STATE = dropdown.createDefaultState();

const formattedStaticData = Object.values(AzureRegion).map((e) => {
  e.id = e.val;
  return e;
});

export const DEFAULT_STATE = {
  ...DROPDOWN_DEFAULT_STATE,
  [STATIC_DATA]: formattedStaticData,
};

export const DUMMY_STORE_DATA = {
  [REDUCER_KEY]: {
    ...DEFAULT_STATE,
  },
};
