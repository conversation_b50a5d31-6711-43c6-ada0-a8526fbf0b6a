import React from 'react';
import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faAngleLeft, faAngleRight, // faAnglesLeft, faAnglesRight,
} from '@fortawesome/pro-solid-svg-icons';

import './index.scss';

function PaginationBar(props) {
  const {
    numberOfLines,
    canPreviousPage,
    canNextPage,
    gotoPage,
    previousPage,
    nextPage,
    pageCount,
    pageSize,
    setPageSize,
    pageIndex,
    pageOptions,
  } = props;
  const { t } = useTranslation();

  const pageOfText = t('PAGE_OF').split(/{[0-9]}/g);

  return (
    <div className="pagination-bar">
      <div className="pagination-bar-left">
        <button type="button" onClick={() => previousPage()} disabled={!canPreviousPage}>
          {/* <FontAwesomeIcon icon={faAngleLeft} className="fontStyle" /> */}
          {t('PREVIOUS')}
        </button>
      </div>
      <div className="pagination-bar-center">
        <span className="page-of">
          <span className="page-number-total">
            {pageOfText[0]}
            {' '}
            {pageIndex + 1}
            {' '}
            {pageOfText[1]}
            {' '}
            {pageOptions && pageOptions.length}
          </span>
        </span>
      </div>
      <div className="pagination-bar-right">
        <button type="button" onClick={() => nextPage()} disabled={!canNextPage}>
          {/* <FontAwesomeIcon icon={faAngleRight} className="fontStyle" /> */}
          {t('NEXT')}
        </button>
      </div>
    </div>
  );
}

PaginationBar.propTypes = {
  numberOfLines: PropTypes.number,
  canPreviousPage: PropTypes.bool,
  canNextPage: PropTypes.bool,
  gotoPage: PropTypes.func,
  previousPage: PropTypes.func,
  nextPage: PropTypes.func,
  pageCount: PropTypes.number,
  pageSize: PropTypes.number,
  setPageSize: PropTypes.func,
  pageIndex: PropTypes.number,
  pageOptions: PropTypes.arrayOf(PropTypes.number),
};

PaginationBar.defaultProps = {
  numberOfLines: 0,
  canPreviousPage: false,
  canNextPage: false,
  gotoPage: null,
  previousPage: null,
  nextPage: null,
  pageCount: 0,
  pageSize: 0,
  setPageSize: null,
  pageIndex: 0,
  pageOptions: [0],
};

export default PaginationBar;
