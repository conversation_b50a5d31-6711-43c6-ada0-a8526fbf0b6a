import React, { useRef } from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import i18n from 'utils/i18n';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faInfoCircle } from '@fortawesome/pro-light-svg-icons';
import { noop, isEmpty } from 'utils/lodash';

import FilterContainer from '../components/filter';
import TableSortComponent from '../components/sort';

import '../index.scss';

const defaultProps = {
  column: {},
  setAllFilters: noop,
  t: (str) => str,
};

function ColumnContainer({
  column, t, setAllFilters, onHandleSortBy, onHandleCheck, checkAll, sortField, sortDirection,
}) {
  const columnContainerRef = useRef(null);

  const { Header = '', meta = {}, id } = column || {};
  const { skipTranslation = false, customCellType = '', informationToolTip = '' } = meta;
 
  return (
    <div key={`column-container-${id}`} ref={columnContainerRef} className="column">

      <div className="left">
        {(customCellType === 'CHECKBOX') && (
          <input
            className="child-checkbox-input"
            id={Header}
            name={Header}
            // eslint-disable-next-line no-nested-ternary
            aria-label={isEmpty(Header) ? 'Select All' : skipTranslation ? Header : t(Header)}
            onChange={() => onHandleCheck('CHECK_ALL')}
            checked={checkAll}
            type="checkbox" />
        )}
        {skipTranslation ? Header : t(Header)}
        {informationToolTip !== '' && (
          <div className="failed-scheduled" data-tip={skipTranslation ? informationToolTip : i18n.t(informationToolTip)}>
            <FontAwesomeIcon icon={faInfoCircle} className="fa-info-circle-icon" />
          </div>
        )}
      </div>

      <div className="right">
        <FilterContainer
          column={column}
          columnRef={columnContainerRef}
          setAllFilters={setAllFilters} />

        <TableSortComponent
          column={column}
          onHandleSortBy={onHandleSortBy}
          sortField={sortField}
          sortDirection={sortDirection} />

        <div {...(column.getResizerProps && column.getResizerProps())} className="resizer" />
      </div>
    </div>
  );
}

ColumnContainer.defaultProps = defaultProps;

ColumnContainer.propTypes = {
  column: PropTypes.objectOf(Object),
  setAllFilters: PropTypes.func,
  t: PropTypes.func,
  onHandleSortBy: PropTypes.func,
  onHandleCheck: PropTypes.func,
  checkAll: PropTypes.bool,
  sortField: PropTypes.string,
  sortDirection: PropTypes.string,
};

export default withTranslation()(ColumnContainer);
