import React from 'react';
import PropTypes from 'prop-types';

const noop = () => undefined;

function PrimaryButton(props) {
  const {
    label,
    onActionCb,
    cclass,
    enable,
  } = props;
  return (
    <button
      type="submit"
      className={['button primary -js-password', cclass].join(' ')}
      onClick={onActionCb}
      disabled={!enable}>
      {label}
    </button>
  );
}

PrimaryButton.propTypes = {
  label: PropTypes.string,
  cclass: PropTypes.string,
  onActionCb: PropTypes.func,
  enable: PropTypes.bool,
};

PrimaryButton.defaultProps = {
  label: '',
  cclass: '',
  onActionCb: noop,
  enable: true,
};

export default PrimaryButton;
