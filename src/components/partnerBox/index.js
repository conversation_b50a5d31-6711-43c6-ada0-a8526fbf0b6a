import React from 'react';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faInfoCircle } from '@fortawesome/pro-solid-svg-icons';

import './index.scss';

export function partnerBox(props) {
  const {
    info,
    title,
    styleClass,
    action,
    actionTitle,
    actionIcon,
  } = props;

  return (
    <div className={`partner-box ${styleClass}`}>
      <div className="partner-box-title">
        <div className="left">
          {title}
        </div>
        {/* DISABLE FOR NOW */}
        {/* <div
          className="box-action right"
          role="button"
          aria-label="Close"
          tabIndex="0"
          onKeyPress={action}
          onClick={action}>
          <FontAwesomeIcon icon={actionIcon} className="info-circle" />
          {actionTitle}
        </div> */}
      </div>
  
      <div className="partner-box-message">
        {info}
      </div>
    </div>
  );
}

partnerBox.propTypes = {
  info: PropTypes.string,
  styleClass: PropTypes.string,
};

partnerBox.defaultProps = {
  info: '',
  styleClass: '',
};

export default partnerBox;
