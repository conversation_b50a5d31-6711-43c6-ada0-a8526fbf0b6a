/* eslint-disable react/jsx-handler-names */
/* eslint-disable react/no-array-index-key */
import React from 'react';
import PropTypes from 'prop-types';
import { noop, isEmpty } from 'utils/lodash';

import Loading from 'components/spinner/Loading';
import ServerError from 'components/errors/ServerError';
import { CheckboxInput } from 'components/CheckboxInput';
import ReactTooltip from 'react-tooltip';

import { withTranslation } from 'react-i18next';
import HighlightedText from 'components/highlightedText';
import DropdownHeader from './DropdownHeader';
import MultiDropdownSearch from './MultiDropdownSearch';

export class Unselected extends React.Component {
  itemRenderer = (item) => {
    const {
      searchString,
      isViewOnly,
      actions: { onChange },
      disabledTooltip,
      t,
    } = this.props;
    const label = <HighlightedText text={t(item.name)} searchString={searchString} />;
    const tooltip = item.disabled ? disabledTooltip : '';
    const isParent = isEmpty(item.original.parent);

    return (
      <div
        className={`multiselect-list-item ${isParent ? 'parent' : 'child'}  ${!isParent && !item.checked ? '' : 'active'}`}
        key={`checkbox-${item.id}`}>
        <ReactTooltip
          place="top"
          type="light"
          className="react-tooltip"
          id={`disabled-${item.id}`}
          disable={(tooltip.length === 0)}>
          {tooltip}
        </ReactTooltip>
        <div
          data-tip
          data-for={`disabled-${item.id}`}>
          <CheckboxInput
            id={item.id}
            label={label}
            className="unselected"
            disable={item.disabled || isViewOnly}
            hidden={item.hidden}
            input={{
              onChange: (event) => onChange(event, item),
              value: item.name,
              name: item.name,
              checked: item.checked,
            }} />
        </div>
      </div>
    );
  };
  
  loadNextPage = () => {
    const {
      actions: {
        nextPage,
        load,
      },
    } = this.props;
    return Promise.resolve()
      .then(() => {
        nextPage();
      })
      .then(() => {
        load();
      });
  };
  
  renderContent = () => {
    const {
      searchString,
      unselectedValues,
      noDataLabel,
    } = this.props;
    
    if (searchString.length > 0 && unselectedValues.length === 0) {
      return <div className="unselected">{noDataLabel}</div>;
    }
    return unselectedValues.map(this.itemRenderer);
  };
  
  renderPagination = () => {
    const {
      hasMorePages,
      loadMoreLabel,
    } = this.props;
    if (!hasMorePages) return null;
    return (
      <div className="unselected">
        <button
          type="button"
          className="load-more flat-button"
          onClick={this.loadNextPage}>
          {loadMoreLabel}
        </button>
      </div>
    );
  };
  
  render() {
    const {
      unselectedHeader,
    } = this.props;

    return (
      <div className="dropdown-unselected">
        <DropdownHeader text={unselectedHeader} />
        <MultiDropdownSearch {...this.props} />
        <Loading {...this.props}>
          <ServerError {...this.props} size="small">
            <div className="multiselect-selected-list">
              {this.renderContent()}
              {this.renderPagination()}
            </div>
          </ServerError>
        </Loading>
      </div>
    );
  }
}

Unselected.propTypes = {
  isViewOnly: PropTypes.bool,
  searchString: PropTypes.string,
  noDataLabel: PropTypes.string,
  disabledTooltip: PropTypes.string,
  actions: PropTypes.shape({
    onChange: PropTypes.func,
    load: PropTypes.func,
    nextPage: PropTypes.func,
  }),
  unselectedHeader: PropTypes.string,
  unselectedValues: PropTypes.arrayOf(PropTypes.shape({})),
  hasMorePages: PropTypes.bool,
  loadMoreLabel: PropTypes.string,
  t: PropTypes.func,
};

Unselected.defaultProps = {
  isViewOnly: false,
  searchString: '',
  noDataLabel: 'NO_DATA',
  actions: {
    onChange: noop,
    load: noop,
  },
  unselectedHeader: '',
  unselectedValues: [],
  disabledTooltip: '',
  hasMorePages: false,
  loadMoreLabel: 'LOAD_MORE',
  t: (str) => str,
};

export default withTranslation()(Unselected);
