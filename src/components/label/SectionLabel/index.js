/* eslint no-return-assign: 1 */
import { noop } from 'utils/lodash';
import PropTypes from 'prop-types';
import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faInfoCircle } from '@fortawesome/pro-regular-svg-icons';

import { withTranslation } from 'react-i18next';

import ToolTip from '../../tooltip/ToolTipPane';

class SectionLabel extends React.PureComponent {
  constructor(props) {
    super(props);

    this.labelRef = React.createRef();
    this.iconRef = React.createRef();
    this.tooltipTimeout = null;

    this.state = {
      isActive: false,
    };
  }

  keepTooltipVisible = () => {
    clearTimeout(this.tooltipTimeout);
  };

  handleOnMouseOver = (type) => {
    const { isActive } = this.state;
    if (!isActive) {
      this.setState(
        (prevState) => ({
          isActive: !prevState.isActive,
          type,
        }),
        () => {
          if (this.setPosition) {
            this.setPosition(type
              ? this.iconRef.current
              : this.labelRef.current, { left: type ? -30 : 0 });
          }
        },
      );
    } else {
      this.keepTooltipVisible();
    }
  };

  handleOnMouseOut = () => {
    this.tooltipTimeout = setTimeout(() => {
      this.setState((prevState) => ({
        isActive: !prevState.isActive,
        type: false,
      }));
    }, 300);
  };

  render() {
    const {
      label, tooltip, infoText, infoTextParam, addInfo, width, customLabelStyle, isInvalid, t,
      showExtraInfo, extraInfoTooltip,
    } = this.props;
    const { isActive, type } = this.state;
    return (
      <div className={`form-input-label ${isInvalid ? 'invalid' : ''} ${width}`} style={customLabelStyle}>
        <span className="tooltip-right">
          <span
            className={`${tooltip ? 'cursorHelp' : ''} toggleTooltip tooltip-right`}
            ref={this.labelRef}
            onMouseOver={() => this.handleOnMouseOver()}
            onMouseOut={this.handleOnMouseOut}
            onBlur={noop}
            onFocus={noop}>
            {t(label)}
          </span>
          {showExtraInfo && (
            <span ref={this.iconRef} className="toggleTooltip tooltip-right cursorHelp">
              <FontAwesomeIcon
                style={{ color: 'var(--semantic-color-content-interactive-primary-default)' }}
                className="ml-10"
                icon={faInfoCircle}
                onFocus={() => this.handleOnMouseOver('extra')}
                onMouseOver={() => this.handleOnMouseOver('extra')}
                onBlur={() => this.handleOnMouseOver('extra')}
                onMouseOut={this.handleOnMouseOut} />
            </span>
          )}
        </span>
        {(tooltip || infoText) ? (
          <ToolTip
            tooltip={type ? extraInfoTooltip : tooltip}
            isActive={isActive}
            tooltipOptions={{
              isValidation: true,
            }}
            infoText={`${infoTextParam ? t(infoText, infoTextParam) : infoText}`}
            setPosition={(hover) => (this.setPosition = hover)}
            openTooltip={this.keepTooltipVisible}
            // eslint-disable-next-line react/jsx-handler-names
            closeTooltip={this.handleOnMouseOut}
            addInfo={type ? false : addInfo} />
        ) : (
          ''
        )}
      </div>
    );
  }
}

SectionLabel.propTypes = {
  label: PropTypes.string,
  tooltip: PropTypes.string,
  width: PropTypes.string,
  infoTextParam: PropTypes.number,
  isInvalid: PropTypes.bool,
  infoText: PropTypes.string,
  addInfo: PropTypes.bool,
  extraInfoTooltip: PropTypes.string,
  showExtraInfo: PropTypes.bool,
  customLabelStyle: PropTypes.objectOf(Object),
  t: PropTypes.func,
};

SectionLabel.defaultProps = {
  label: '',
  tooltip: '',
  width: '',
  isInvalid: false,
  infoText: '',
  addInfo: false,
  infoTextParam: 0,
  customLabelStyle: {},
  t: (str) => str,
};
export default withTranslation()(SectionLabel);
