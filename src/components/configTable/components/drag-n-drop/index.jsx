import React from 'react';
import PropTypes from 'prop-types';

import { noop } from 'utils/lodash';

import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';

const reorder = (list, startIndex, endIndex) => {
  const result = Array.from(list);
  const [removed] = result.splice(startIndex, 1);
  result.splice(endIndex, 0, removed);

  return result;
};

const defaultProps = {
  list: [],
  listKeyName: 'id',
  droppableId: 'column-droppable',
  direction: 'horizontal',
  listContainerClass: 'row',
  listContainerProps: {},
  renderListItem: noop,
  onNewListOrder: noop,
  isDragDisabled: false,
};

function DragAndDropList({
  list,
  listKeyName,
  droppableId,
  direction,
  listContainerClass,
  listContainerProps,
  renderListItem,
  onNewListOrder,
  isDragDisabled,
}) {
  const onDragEnd = (result) => {
    if (!result.destination) {
      return;
    }
    if (result.source.index === result.destination.index) {
      return;
    }

    const newListOrder = reorder(list, result.source.index, result.destination.index);

    onNewListOrder(
      newListOrder.map((listOrder) => listOrder[listKeyName]),
      newListOrder,
    );
  };

  const renderListDetailContainer = (detail, idx) => {
    const { disableReordering } = detail;
    
    return (
      <Draggable
        key={detail[listKeyName]}
        draggableId={detail[listKeyName]}
        index={idx}
        isDragDisabled={disableReordering || isDragDisabled}>
        {(provided) => renderListItem(detail, idx, provided)}
      </Draggable>
    );
  };

  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <Droppable direction={direction} droppableId={droppableId} isDropDisabled={isDragDisabled}>
        {(provided) => (
          <article
            ref={provided.innerRef}
            className={listContainerClass}
            {...provided.droppableProps}
            {...listContainerProps}>
            {list.map(renderListDetailContainer)}
            {provided.placeholder}
          </article>
        )}
      </Droppable>
    </DragDropContext>
  );
}

DragAndDropList.defaultProps = defaultProps;

DragAndDropList.propTypes = {
  list: PropTypes.arrayOf(Object),
  listKeyName: PropTypes.string,
  droppableId: PropTypes.string,
  direction: PropTypes.string,
  listContainerClass: PropTypes.string,
  listContainerProps: PropTypes.objectOf(Object),
  renderListItem: PropTypes.func,
  onNewListOrder: PropTypes.func,
  isDragDisabled: PropTypes.bool,
};

export default DragAndDropList;
