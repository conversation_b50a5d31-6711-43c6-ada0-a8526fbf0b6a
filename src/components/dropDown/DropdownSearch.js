/* eslint-disable react/jsx-handler-names */
import React from 'react';
import PropTypes from 'prop-types';
import { noop, debounce } from 'utils/lodash';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlusCircle, faSearch, faTimes } from '@fortawesome/pro-solid-svg-icons';
import { withTranslation } from 'react-i18next';
import i18n from 'utils/i18n';

export class DropdownSearch extends React.Component {
  constructor(props) {
    super(props);
    const { searchString } = this.props;
    this.state = {
      search: searchString || '',
    };
    this.debounceInput = debounce(this.performSearch, 600);
  }

  componentWillUnmount() {
    const {
      searchParamName,
      actions: {
        setSearchString,
      },
    } = this.props;
    setSearchString('', searchParamName);
  }

  clearSearch = () => {
    this.setState({ search: '' }, this.performSearch);
  };

  onChange = (event) => {
    this.setState({ search: event.target.value }, this.debounceInput);
    if (event.key === 'Enter') {
      event.preventDefault();
    }
  };

  performSearch = () => {
    const { search } = this.state;
    const {
      searchParamName,
      actions: {
        setSearchString,
        searchData,
      },
      t,
    } = this.props;

    return Promise.resolve()
      .then(() => {
        setSearchString(search, searchParamName);
      })
      .then(() => {
        if (searchData) searchData(t);
      });
  };

  renderClearButton = () => {
    const { search } = this.state;
    if (search.length === 0) return null;
    return (
      <button
        onClick={this.clearSearch}
        type="button"
        className="clear-button">
        <FontAwesomeIcon icon={faTimes} />
      </button>
    );
  };

  render() {
    const { search } = this.state;
    const {
      addButton,
      onAddButtonClick,
      tabIndex,
      placeholder,
    } = this.props;

    return addButton ? (
      <div className="dropdown-search">
        <div className="dropdown-search-add-button">
          <input
            className="dropdown-search-text-add-button"
            type="text"
            value={search}
            tabIndex={tabIndex}
            placeholder={placeholder}
            onChange={this.onChange} />
          <span className="action-buttons">
            {this.renderClearButton()}
            <button
              onClick={this.performSearch}
              type="button"
              className="remove-button">
              <FontAwesomeIcon icon={faSearch} />
            </button>
          </span>
        </div>
        <FontAwesomeIcon
          width={20}
          height={20}
          onClick={onAddButtonClick}
          transform="grow-8"
          className="dropdown-search-text-add-button-icon"
          icon={faPlusCircle} />
      </div>
    ) : (
      <div className="dropdown-search">
        <input
          className="dropdown-search-text"
          type="text"
          value={search}
          tabIndex={tabIndex}
          placeholder={placeholder}
          onKeyPress={this.onChange}
          onChange={this.onChange} />
        <span className="action-buttons">
          {this.renderClearButton()}
          <button
            onClick={this.performSearch}
            type="button"
            className="remove-button">
            <FontAwesomeIcon icon={faSearch} />
          </button>
        </span>
      </div>
    );
  }
}

DropdownSearch.propTypes = {
  addButton: PropTypes.bool,
  searchString: PropTypes.string,
  searchParamName: PropTypes.string,
  actions: PropTypes.shape({
    setSearchString: PropTypes.func,
    searchData: PropTypes.func,
  }),
  tabIndex: PropTypes.number,
  placeholder: PropTypes.string,
  onAddButtonClick: PropTypes.func,
  t: PropTypes.func,
};

DropdownSearch.defaultProps = {
  addButton: false,
  searchString: '',
  searchParamName: 'search',
  actions: {
    setSearchString: noop,
    searchData: noop,
  },
  tabIndex: -1,
  placeholder: i18n.t('SEARCH'),
  onAddButtonClick: noop,
  t: (str) => str,
};

export default withTranslation()(DropdownSearch);
