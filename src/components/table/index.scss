@import "scss/colors.scss";
@import 'scss/mixins.scss';

.ec-root-page {
.app-table-container {
  margin-top: 30px;
  .title {
    color: $grey4;
    font-size: 16px;
    letter-spacing: .5px;
    margin-bottom: 12px;
  }

  .ReactTable.app-table {
    border-radius: 5px 5px 0 0;
    border: 1px solid $grey12;

    .rt-thead {
      border-bottom: 1px solid $grey12;
      border-radius: 5px 5px 0 0;
      box-shadow: none;
      background-color: var(--semantic-color-content-inverted-base-secondary);
      min-height:  39px;

      .rt-th {
        padding: 6px 12px;
        box-shadow: none;
        line-height: 28px;
        .rt-resizer {
          width: 10px;
          right:  -7px;
        }
      }

      .header-simple {
        position: relative;
        text-align: left;
        display: block;
        background: inherit;
        width: 100%;
        height: 100%;
        line-height: inherit;
        border: none;
        outline: 0;
        font-size: 14px;
        .sorting-icon-cont {
          position: absolute;
          right: 0;
          top: 5px;
          font-size: 10px;

          .sorting-arrow-up,
          .sorting-arrow-down {
            color: $grey10;
            line-height: 1px;
          }
        }
      }
      .header-title {
        position: relative;
        text-align: left;
        display: block;
        background: inherit;
        width: 100%;
        height: 100%;
        line-height: inherit;
        border: none;
        outline: 0;

        .sorting-icon-cont {
          position: absolute;
          right: 0;
          top: 5px;
          font-size: 10px;

          .sorting-arrow-up,
          .sorting-arrow-down {
            color: $grey10;
            line-height: 1px;
          }
        }
      }

      .rt-th.-sort-asc .sorting-arrow-up {
        color: var(--semantic-color-content-interactive-primary-default);
      }

      .rt-th.-sort-desc .sorting-arrow-down {
        color: var(--semantic-color-content-interactive-primary-default);
      }
    }

    .rt-tbody {
      .rt-tr-group {
        .rt-tr {
          @include DisplayFlex;

          &.-odd {
            background: $white;
          }

          &.-even {
            background: var(--semantic-color-content-inverted-base-secondary);
          }

          .rt-td {
            min-height: 40px;
            padding: 0 18px;
            align-self: center;
            border-right: .5px solid $white;
            align-items: center;
            @include DisplayFlex;

            &.rt-expandable {
              justify-content: center;
            }

            &.rt-pivot,
            &:last-child {
              border-right: none;
            }

            &.rt-expandable {
              width: 100%;
              padding: 0;
            }
          }
        }
      }
    }

    .pagination-bottom {
      .-pagination {
        box-shadow: none;
        border-top: 1px solid $grey12;
      }
    }

    &.has-nested-true .rt-thead {
      .rt-th:nth-child(1) {
        display: none;
      }

      .rt-th:nth-child(2) {
        width: 130px !important;
      }

    }

    .rt-noData {
      padding: 0;
      top:  calc(50% + 2rem);
    }
  }
}
}