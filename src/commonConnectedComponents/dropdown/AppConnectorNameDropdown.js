/* eslint-disable react/jsx-handler-names */
import React, { useEffect } from 'react';
import { PropTypes } from 'prop-types';
import { isEmpty } from 'utils/lodash';
import { useDispatch, useSelector } from 'react-redux';
import Loading from 'components/spinner/Loading';
import { change, updateSyncErrors } from 'redux-form';
import { loader, handleSearchStringChange, perfomSearch } from 'ducks/dropdowns/app-connector-name';
import selectors from 'ducks/dropdowns/app-connector-name/selectors';
import AdvDropdownWithAsyncSearch from 'components/dropDown/AdvDropdownWithAsyncSearch';

export function AppConnectorNameDropdown(props) {
  const dispatch = useDispatch();
  const { groupName } = props;
  const selector = useSelector((state) => selectors(state).dropdown);
  const {
    data, hasMorePages, loading, searchString = '',
  } = selector;
  const { input, meta } = props;
  const { name, value } = input || {};
  const { form } = meta || {};
  
  useEffect(() => {
    if (!isEmpty(groupName)) {
      dispatch(loader(true, groupName));
    }
  }, [groupName]);

  return (
    <div className="entity-dropdown-container">
      <Loading loading={loading} />
      <AdvDropdownWithAsyncSearch
        {...props}
        styleClass="full-width"
        items={data}
        currentItems={[]}
        hasNextPage={hasMorePages}
        defaultValue={value}
        onPerformSearch={() => dispatch(perfomSearch(groupName))}
        onSearchStringChange={(search) => dispatch(handleSearchStringChange(search))}
        onClickCb={(item) => {
          dispatch(change(form, name, item));
          dispatch(updateSyncErrors(form));
        }}
        onChange={input.onChange}
        onBlur={input.onBlur}
        loading={loading}
        searchString={searchString} />
    </div>
  );
}

AppConnectorNameDropdown.propTypes = {
  groupName: PropTypes.string,
  input: PropTypes.shape({}),
  meta: PropTypes.shape({}),
};
  
AppConnectorNameDropdown.defaultProps = {
  groupName: '',
  input: {},
  meta: {},
};

export default AppConnectorNameDropdown;
