import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react';
import PrimaryButton from './ButtonNew';

jest.mock('components/tooltip/ToolTipPane', () => ({
  __esModule: true,
  default: jest.fn(() => <div>Tooltip</div>),
}));

jest.mock('@fortawesome/react-fontawesome', () => ({
  __esModule: true,
  FontAwesomeIcon: jest.fn(() => <div>FontAwesomeIcon</div>),
}));

describe('PrimaryButton component', () => {
  const props = {
    label: 'Primary Button',
    cclass: 'primary-button',
    onActionCb: jest.fn(),
    getRef: jest.fn(),
    enable: true,
    icon: 'fa-icon',
    preIcon: 'fa-pre-icon',
    preIconComponent: { icon: 'fa-pre-icon-component' },
    tooltip: 'Primary Button Tooltip',
  };

  it('renders correctly', () => {
    const { getByText } = render(<PrimaryButton {...props} />);
    expect(getByText('Primary Button')).toBeInTheDocument();
    expect(getByText('FontAwesomeIcon')).toBeInTheDocument();
    expect(getByText('FontAwesomeIcon')).toBeInTheDocument();
    expect(getByText('Tooltip')).toBeInTheDocument();
  });

  it('calls onActionCb when clicked', () => {
    const { getByText } = render(<PrimaryButton {...props} />);
    const button = getByText('Primary Button');
    fireEvent.click(button);
    expect(props.onActionCb).toHaveBeenCalledTimes(1);
  });

  it('renders disabled button when enable is false', () => {
    const { getByText } = render(<PrimaryButton {...props} enable={false} />);
    const button = getByText('Primary Button');
    expect(button).toHaveClass('disabled');
  });

  it('renders tooltip when tooltip is provided', () => {
    const { getByText } = render(<PrimaryButton {...props} />);
    expect(getByText('Tooltip')).toBeInTheDocument();
  });

  it('renders preIcon when preIcon is provided', () => {
    const { getByText } = render(<PrimaryButton {...props} />);
    expect(getByText('FontAwesomeIcon')).toBeInTheDocument();
  });

  it('renders preIconComponent when preIconComponent is provided', () => {
    const { getByText } = render(<PrimaryButton {...props} />);
    expect(getByText('FontAwesomeIcon')).toBeInTheDocument();
  });
});
