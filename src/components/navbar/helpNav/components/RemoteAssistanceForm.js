import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { reduxForm } from 'redux-form';
import { withTranslation } from 'react-i18next';
import Loading from 'components/spinner/Loading';
import moment from 'moment-timezone';
import PermissionRequired from 'components/PermissionRequired';
import * as selectors from 'ducks/helpNav/selectors';
import * as loginSelectors from 'ducks/login/selectors';

import {
  loaderRemoteAssistance, saveRemoteAssistance, toggleShowForm, handleUsernameObfuscated,
} from 'ducks/helpNav';

import {
  ViewOnlyAccess, FullAccess, UserNameVisibility,
} from './GenericFields';

export function BasicCustomAppForm(props) {
  const {
    t, actions, handleSubmit, submitting, loading, initialValues, formData, accessPermissions,
  } = props;
  const {
    enableViewOnlyAccess, viewOnlyUntil, enableFullAccess, fullAccessUntil, usernameObfuscated,
  } = formData || {};
  
  const untouched = initialValues
    && enableViewOnlyAccess === initialValues.enableViewOnlyAccess
    && viewOnlyUntil === initialValues.viewOnlyUntil
    && enableFullAccess === initialValues.enableFullAccess
    && fullAccessUntil === initialValues.fullAccessUntil
    && usernameObfuscated === initialValues.usernameObfuscated;

  const onSubmit = () => {
    actions.saveRemoteAssistance();
  };

  const cancelHandle = () => {
    actions.toggleShowForm(false);
  };

  useEffect(() => {
    const { initialized } = props;
    if (!initialized) actions.loaderRemoteAssistance();
  }, []);

  if (accessPermissions.REMOTE_ASSISTANCE_MANAGEMENT === 'NONE') {
    return (
      <div className="remote-assistance-permission-required">
        <PermissionRequired accessPermissions={accessPermissions} />
      </div>
    );
  }
  const viewOnly = accessPermissions.REMOTE_ASSISTANCE_MANAGEMENT === 'READ_ONLY';

  return (
    <Loading loading={loading}>
      <form onSubmit={handleSubmit(onSubmit)} className="adminManagement-form">
        <div className="form-sections-container">
          <ViewOnlyAccess viewOnly={viewOnly} {...props} />
          <FullAccess viewOnly={viewOnly} {...props} />
          <UserNameVisibility viewOnly={viewOnly} {...props} />
        </div>
        {!viewOnly && (
          <div className="dialog-footer">
            <div className="dialog-footer-left">
              <button type="submit" disabled={untouched || submitting} className="submit">{t('SAVE')}</button>
              <button type="button" className="cancel" onClick={cancelHandle}>{t('CANCEL')}</button>
            </div>
          </div>
        )}
      </form>
    </Loading>
  );
}

BasicCustomAppForm.propTypes = {
  t: PropTypes.func,
  handleSubmit: PropTypes.func,
  submitting: PropTypes.bool,
  loading: PropTypes.bool,
  initialValues: PropTypes.shape({}),
  formData: PropTypes.shape({}),
  actions: PropTypes.shape({
    loaderRemoteAssistance: PropTypes.func,
    saveRemoteAssistance: PropTypes.func,
    toggleShowForm: PropTypes.func,
    handleUsernameObfuscated: PropTypes.func,
  }),
  accessPermissions: PropTypes.shape({}),
};
  
BasicCustomAppForm.defaultProps = {
  t: (str) => str,
  handleSubmit: (str) => str,
  submitting: false,
  loading: true,
  actions: {
    loaderRemoteAssistance: null,
    saveRemoteAssistance: null,
    toggleShowForm: null,
    handleUsernameObfuscated: null,
  },
  accessPermissions: {},
};

const BasicCustomForm = reduxForm({
  form: 'remoteAssistance',
  enableReinitialize: true,
})(BasicCustomAppForm);

const RemoteAssistanceForm = connect(
  (state) => {
    const {
      fullAccessUntil,
      viewOnlyUntil,
      usernameObfuscated,
    } = selectors.baseSelector(state) || {};

    return ({
      initialValues: {
        enableFullAccess: fullAccessUntil > 0,
        fullAccessUntil: fullAccessUntil === 0 ? moment().endOf('day').toDate() : fullAccessUntil,
        enableViewOnlyAccess: viewOnlyUntil > 0,
        viewOnlyUntil: viewOnlyUntil === 0 ? moment().endOf('day').toDate() : viewOnlyUntil,
        usernameObfuscated: usernameObfuscated ? 'OBFUSCATED' : 'VISIBLE',
      },
    });
  },
)(BasicCustomForm);

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    loaderRemoteAssistance,
    saveRemoteAssistance,
    toggleShowForm,
    handleUsernameObfuscated,
  }, dispatch);

  return {
    actions,
  };
};

const mapStateToProps = (state) => ({
  ...selectors.baseSelector(state),
  formData: selectors.formValuesSelector(state),
  accessPermissions: loginSelectors.accessPermissionsSelector(state),
});

// eslint-disable-next-line max-len
export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(RemoteAssistanceForm));
