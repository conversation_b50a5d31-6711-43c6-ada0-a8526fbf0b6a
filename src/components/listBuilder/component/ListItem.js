import React, { useState, useRef, useEffect } from 'react';
import PropTypes from 'prop-types';
import ReactTooltip from 'react-tooltip';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimesCircle, faQuestionCircle } from '@fortawesome/pro-solid-svg-icons';

function ListItems({
  handleRemove, validation, item, disabled,
}) {
  const labelRef = useRef(null);
  const [isEllipsisActive, setIsEllipsisActive] = useState(false);

  useEffect(() => {
    if (!labelRef.current) return;
    if (labelRef.current.offsetWidth < labelRef.current.scrollWidth) {
      setIsEllipsisActive(true);
    }
  }, [labelRef, item]);

  const errorMessage = validation(item);
  
  return (
    <div
      data-tip={isEllipsisActive}
      data-for={item}
      ref={labelRef}
      className={`list-builder-list-item ${errorMessage ? 'list-builder-invalid' : ''}`}>
      { errorMessage && (
        <span className="list-builder-list-item list-builder-invalid" data-tip data-for={`${item}_tooltip`}>
          <FontAwesomeIcon icon={faQuestionCircle} />
          <ReactTooltip
            id={`${item}_tooltip`}
            place="top"
            type="light"
            offset={{ top: -10 }}
            effect="solid"
            border
            borderColor="#939393"
            className="list-builder-tooltip-container">
            {errorMessage}
          </ReactTooltip>
        </span>
      )}
      {item}
      {isEllipsisActive && (
        <ReactTooltip
          id={item}
          clickable
          place="right"
          type="light"
          offset={{ top: -10 }}
          effect="solid"
          border
          borderColor="#939393"
          className="list-builder-tooltip-container">
          {item}
        </ReactTooltip>
      )}
      <span className="list-builder-remove">
        {!disabled && (
          <FontAwesomeIcon
            icon={faTimesCircle}
            data-value={item}
            onClick={() => handleRemove(item)} />
        )}
      </span>
    </div>
  );
}

ListItems.propTypes = {
  handleRemove: PropTypes.func,
  validation: PropTypes.func,
  item: PropTypes.string,
  disabled: PropTypes.bool,
};
  
ListItems.defaultProps = {
  handleRemove: null,
  validation: null,
  item: '',
  disabled: true,
};

export default ListItems;
