const labelHelper = (t, selectedValues, values, disabledTooltip, defaultDisplayLabel) => ({
  unselectedHeader: t('UNSELECTED_ITEMS'),
  doneLabel: t('DONE'),
  cancelLabel: t('CANCEL'),
  resetLabel: t('RESET'),
  loadMoreLabel: t('LOAD_MORE'),
  noDataLabel: t('NO_DATA'),
  selectAllLabel: t('SELECT_ALL'),
  clearAllLabel: t('CLEAR_ALL'),
  allLabel: t('ALL'),
  noneLabel: t('NONE'),
  selectedHeader: t('SELECTED_ITEMS', { count: selectedValues.length }),
  displayLabel: selectedValues.length === 1 ? t('NUMBER_OF_SELECTED_ITEM', { count: selectedValues.length }) : t('NUMBER_OF_SELECTED_ITEM_PLURAL', { count: selectedValues.length }),

  disabledTooltip: t(disabledTooltip),
  defaultDisplayLabel: t(defaultDisplayLabel),
});

export default labelHelper;
