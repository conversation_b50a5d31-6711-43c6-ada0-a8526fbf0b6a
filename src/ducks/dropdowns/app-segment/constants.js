import dropdown from 'ducks/generics/dropdown';
import { BASE_API_PATH } from 'config';

export const REDUCER_KEY = 'appSegmentDropdown';
export const API_ENDPOINT = `${BASE_API_PATH}/v1/zpaResources/applicationSegments`;

export const DROPDOWN = dropdown.createStateConstants();
export const DROPDOWN_DEFAULT_STATE = dropdown.createDefaultState();
export const SEARCH_PARAM_NAME = 'searchParamName';
export const SEARCH_STRING = 'searchString';
export const DATA = 'data';

export const DEFAULT_STATE = {
  ...DROPDOWN_DEFAULT_STATE,
  [SEARCH_PARAM_NAME]: 'search',
  selectAll: {},
};

export const DUMMY_STORE_DATA = {
  [REDUCER_KEY]: {
    ...DEFAULT_STATE,
    selectAll: {},
  },
};
