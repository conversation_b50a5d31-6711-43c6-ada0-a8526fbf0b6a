@import "scss/colors.scss";
@import "scss/mixins.scss";

.ec-root-page {
.input-password-container {
  width: 100%;
  padding: 0 15px;

  .input-password-label {
    @include DisplayFlex;
    align-items: center;
    color: $grey1;
    font-weight: 500;
    margin: 15px 0 5px;

    &.required {
      position: relative;
      left: - 13px;
  
      .fa-asterisk {
        color: $grey7;
        margin-right: 3px;
        font-size: 10px;
      }
    }
  }

  .input-password-wrapper {
    @include DisplayFlex;
    align-items: center;
    border-bottom: 1px solid var(--semantic-color-content-interactive-primary-default);

    input {
      width: 100%;
      height: 30px;
      border: none;
      color: $grey1;
      font-size: 13px;
      outline-offset: 1px;
      &:disabled {
        color: $grey7;
      }
    }

    input[type=password]::-ms-reveal,
    input[type=password]::-ms-clear
    {
      display: none;
    }

    .show-or-hide-password {
      width:  130px;
    }
  }

  .error-container {
    color: $red4;
    padding-top: 2px;
  }

  &.error {
    input {
      border-color: $red4;
    }
  }

  &.no-margin-top {
    .input-password-label {
      margin-top: 0;
    }
  }
}
}