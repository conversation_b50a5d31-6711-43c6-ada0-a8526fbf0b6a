/* eslint-disable jsx-a11y/label-has-for */
import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { getHaStatusIcon } from 'utils/helpers';

function HaStatus(props) {
  const { value } = props;
  const { haStatus } = value || {};
 
  return (
    <div
      onKeyPress={null}
      aria-label="HA Status"
      role="button"
      tabIndex="0">
      { getHaStatusIcon(haStatus)}
    </div>
  );
}
  
HaStatus.defaultProps = {
  value: {},
};
  
HaStatus.propTypes = {
  value: PropTypes.shape({}),
};
  
export default withTranslation()(HaStatus);
