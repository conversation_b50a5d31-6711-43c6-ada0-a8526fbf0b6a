import React from 'react';
import PropTypes from 'prop-types';
import { noop } from 'utils/lodash';

export function DropdownFooter(props) {
  const {
    isViewOnly,
    tabIndex,
    doneLabel,
    cancelLabel,
    resetLabel,
    selectAllLabel,
    clearAllLabel,
    actions: {
      cancel,
      reset,
      selectAll,
      unselect,
    },
    onComplete,
    unselectedValues,
    CustomFooterComponent,
  } = props;

  return (
    <div className="dropdown-footer">
      <div>
        { !isViewOnly && (
          <button
            type="button"
            className="done"
            onClick={onComplete}
            tabIndex={tabIndex}>
            {doneLabel}
          </button>
        )}
        <button
          type="button"
          className="cancel flat-button"
          onClick={cancel}
          tabIndex={tabIndex + 1}>
          {cancelLabel}
        </button>
      </div>
      <div>
        {CustomFooterComponent }
      </div>
      { !isViewOnly && (
        <div>
          <button
            type="button"
            className="select-all flat-button"
            onClick={() => selectAll(unselectedValues)}
            tabIndex={tabIndex + 2}>
            {selectAllLabel}
          </button>
          <button
            type="button"
            className="clear-all flat-button"
            onClick={unselect}
            tabIndex={tabIndex + 3}>
            {clearAllLabel}
          </button>
          <button
            type="button"
            className="reset flat-button"
            onClick={reset}
            tabIndex={tabIndex + 4}>
            {resetLabel}
          </button>
        </div>
      )}
    </div>
  );
}

DropdownFooter.propTypes = {
  isViewOnly: PropTypes.bool,
  tabIndex: PropTypes.number,
  doneLabel: PropTypes.string,
  cancelLabel: PropTypes.string,
  resetLabel: PropTypes.string,
  selectAllLabel: PropTypes.string,
  clearAllLabel: PropTypes.string,
  actions: PropTypes.shape({
    cancel: PropTypes.func,
    reset: PropTypes.func,
    selectAll: PropTypes.func,
    unselect: PropTypes.func,
  }),
  onComplete: PropTypes.func,
  unselectedValues: PropTypes.arrayOf(PropTypes.shape({})),
  CustomFooterComponent: PropTypes.element,
};

DropdownFooter.defaultProps = {
  isViewOnly: false,
  tabIndex: -3,
  doneLabel: 'DONE',
  cancelLabel: 'CANCEL',
  resetLabel: 'RESET',
  selectAllLabel: 'SELECT_ALL',
  clearAllLabel: 'CLEAR_ALL',
  actions: {
    cancel: noop,
    reset: noop,
    selectAll: noop,
    unselect: noop,
  },
  onComplete: noop,
  unselectedValues: [],
  CustomFooterComponent: <></>,
};

export default DropdownFooter;
