import {
  createAction,
  loadSuccess,
  loading,
  loadError,
} from 'ducks/generics';
import moment from 'moment-timezone';
import { genericInterface } from 'utils/http';
import { loader as loadCcAdvancedSettings } from 'ducks/cloudConfigurationAdvancedSettings';
import * as CCAdvancedSettings from 'ducks/cloudConfigurationAdvancedSettings/selectors';
import * as trafficSelector from 'ducks/trafficMonitoring/selectors';
import { toggleTrafficMointoringExit } from 'ducks/trafficMonitoring';
import { isNull } from 'utils/lodash';
import { verifyConfigData, combine } from 'utils/helpers';
import i18n from 'utils/i18n';
import * as constants from './constants';
import actionTypes from './action-types';
import * as connectorMonitoringSelector from './selectors';

export { REDUCER_KEY } from './constants';

const actionTypeExists = Object.keys(actionTypes).reduce((hash, key) => ({
  ...hash,
  [actionTypes[key]]: true,
}), {});

export const reducer = (state = constants.DEFAULT_STATE, action = {}) => {
  if (actionTypeExists[action.type]) {
    return {
      ...state,
      ...action.payload,
    };
  }
  return state;
};
export default reducer;

const boundLoading = loading(actionTypes.DATA_LOADING);
const boundSuccess = loadSuccess(actionTypes.DATA_LOAD_SUCCESS);
const dataChanged = createAction(actionTypes.DATA_CHANGED);
const boundError = loadError(actionTypes.DATA_LOAD_ERROR);

/**
 * EC Device Detail
 * Parent: GeoView PopUp || Cloud Connector Info
 * @param {*} dataType,
 * @param {*} dataType,
 * @param {*} awsRegionFilters,
 * @param {*} azureRegionFilters,
 * @param {*} gcpRegionFilters,
 * @param {*} branchLocFilters,
 * @param {*} ecVmFilters,
 */
const getEcDeviceDetail = (
  dataClass,
  dataType,
  awsRegionFilters,
  azureRegionFilters,
  gcpRegionFilters,
  branchLocFilters,
  ecVmFilters,
  cloudInfo,
) => async (dispatch, getState) => {
  const currentState = getState();
  const accountIdEnabled = CCAdvancedSettings.accountId(currentState);
  const subIdEnabled = CCAdvancedSettings.subscriptionId(currentState);
  const projectIdEnabled = CCAdvancedSettings.projectId(currentState);

  if (isNull(accountIdEnabled) || isNull(subIdEnabled) || isNull(projectIdEnabled)) {
    await dispatch(loadCcAdvancedSettings());
  }

  const ecReportDataDeviceApi = genericInterface(constants.EC_REPORT_DATA_DEVICE_API_ENDPOINT);
  
  const deviceTimestamp = moment().format('MMM. DD, YYYY hh:mm:ss a');
  const local = localStorage.getItem('configData');
  const configData = (local && local.length) ? JSON.parse(local) : {};
  const enableTunnelMonitoring = verifyConfigData({ configData, key: 'enableTunnelMonitoring' });

  return ecReportDataDeviceApi.create({
    dataClass,
    dataType,
    awsRegionFilters,
    azureRegionFilters,
    gcpRegionFilters,
    branchLocFilters,
    ecVmFilters,
  }, {})
    .then(async (response) => {
      const { data } = response;
      const { deployAsGateway, ecGroupId, ecId } = data || {};
      let network;
      let tunnel = [];
      if (deployAsGateway) {
        if (enableTunnelMonitoring) {
          try {
            const { data: tunnelData } = await genericInterface(
              constants.EC_REPORT_DATA_DEVICE_TUNNEL_API_ENDPOINT(ecId),
            ).read(null, { });
            tunnel = tunnelData || [];
          } catch (error) {
            tunnel = [];
          }
        }
        
        const { data: intfs } = await genericInterface(
          constants.EC_REPORT_DATA_DEVICE_INTERFACES_API_ENDPOINT(ecGroupId, ecId),
        ).read(null, { });
        const { data: routes } = await genericInterface(
          constants.EC_REPORT_DATA_DEVICE_ROUTES_API_ENDPOINT(ecGroupId, ecId),
        ).read(null, { });
        network = {
          intfs, routes, tunnel,
        };
      }
      const mergedData = { ...data, ...cloudInfo, ...network };
      dispatch(dataChanged({
        [constants.EC_INFO]: data,
        [constants.CLOUD_CONNECTOR_DATA]: mergedData,
        [constants.DEVICE_STATUS_TIME]: deviceTimestamp,
        loading: false,
      }));
    })
    .catch((error) => dispatch(boundError(error)));
};

/**
 * Cloud Monitoring Table Data (Device Data Lite)
 * Parent: Branch & Cloud Connector Monitoring
 * @param {*} dataType,
 * @param {*} dataType,
 * @param {*} awsRegionFilters,
 * @param {*} azureRegionFilters,
 * @param {*} gcpRegionFilters,
 * @param {*} branchLocFilters,
 */
export const getDeviceDataLite = (
  dataClass,
  dataType,
  awsRegionFilters,
  azureRegionFilters,
  gcpRegionFilters,
  branchLocFilters,
) => (dispatch, getState) => {
  const currentState = getState();
  const vmStatusDetailsData = connectorMonitoringSelector.vmStatusDetailsDataSelector(currentState);
  const ecReportDataDeviceApi = genericInterface(constants.EC_REPORT_DATA_DEVICE_LITE_API_ENDPOINT);

  return ecReportDataDeviceApi.create({
    dataClass,
    dataType,
    awsRegionFilters,
    azureRegionFilters,
    gcpRegionFilters,
    branchLocFilters,
  }, {})
    .then((response) => {
      let { data } = response;
      const dataIDs = data.map((x) => x.ecId);
      data = combine(data, vmStatusDetailsData.filter((x) => dataIDs.includes(x.id)), 'ecId');

      dispatch(dataChanged({
        [constants.CONNECTOR_DATA_TABLE]: data,
        loading: false,
      }));
    })
    .catch((error) => dispatch(boundError(error)));
};

/**
 * Deployment & Status Data (Pie Charts) & Table Data
 * Parent: Branch & Cloud Connector Monitoring
 * @param {*} dataType,
 * @param {*} dataType,
 * @param {*} awsRegionFilters,
 * @param {*} azureRegionFilters,
 * @param {*} gcpRegionFilters,
 * @param {*} branchLocFilters,
 * @param {*} timestamp,
 */
const getConnectorMonitoringData = (
  dataClass,
  dataType,
  awsRegionFilters,
  azureRegionFilters,
  gcpRegionFilters,
  branchLocFilters,
  timestamp,
) => (dispatch, getState) => {
  const ecDeploymentData = {
    pieData: [
      {
        id: i18n.t('DEPLOYED'),
        orgId: 'DEPLOYED',
        label: i18n.t('DEPLOYED'),
        value: 0,
      },
      {
        id: i18n.t('NOT_DEPLOYED'),
        orgId: 'NOT_DEPLOYED',
        label: i18n.t('NOT_DEPLOYED'),
        value: 0,
      },
    ],
    colors: ['#6AA920', '#FFB700'],
    innerText: i18n.t('TOTAL_ENTITLED'),
  };

  const ecDeploymentFilteredData = {
    pieData: [
      {
        id: i18n.t('DEPLOYED_FILTERED'),
        orgId: 'DEPLOYED',
        label: i18n.t('DEPLOYED_FILTERED'),
        value: 0,
      },
      {
        id: i18n.t('DEPLOYED'),
        orgId: 'DEPLOYED_OTHER',
        label: i18n.t('DEPLOYED'),
        value: 0,
      },
      {
        id: i18n.t('NOT_DEPLOYED'),
        orgId: 'NOT_DEPLOYED',
        label: i18n.t('NOT_DEPLOYED'),
        value: 0,
      },
    ],
    colors: ['#6AA920', '#D8D8D8', '#FFB700'],
    innerText: i18n.t('TOTAL_ENTITLED'),
  };

  const ecActiveHealthData = {
    pieData: [
      {
        id: i18n.t('ACTIVE'),
        orgId: 'ACTIVE',
        label: i18n.t('ACTIVE'),
        value: 0,
      },
      {
        id: i18n.t('INACTIVE'),
        orgId: 'INACTIVE',
        label: i18n.t('INACTIVE'),
        value: 0,
      },
      {
        id: i18n.t('DISABLED'),
        orgId: 'DISABLED',
        label: i18n.t('DISABLED'),
        value: 0,
      },
    ],
    colors: ['#73A13C', '#EC564F', '#6e6e6e'],
    innerText: i18n.t('TOTAL_DEPLOYED'),
  };
  let manualTotal = 0; // count DEPLOYED + NOT_DEPLOYED
  let total = 0;

  const eCHwNotification = genericInterface(constants.EC_HARDWARE_NOTIFICATION);
  eCHwNotification.read()
    .then((response) => {
      const { data } = response;
      dispatch(dataChanged({
        [constants.HARDWARE_NOTIFICATIONS]: data,
      // loading: false,
      }));
    })
    .catch((error) => dispatch(boundError(error)));

  const ecReportDataApi = genericInterface(constants.EC_REPORT_DATA_STATUS_API_ENDPOINT);
  ecReportDataApi.create({
    dataClass,
    dataType,
    awsRegionFilters,
    azureRegionFilters,
    gcpRegionFilters,
    branchLocFilters,
  }, {})
    .then((response) => {
      const { data } = response;

      let active = 0;
      let disabled = 0;
      let inactive = 0;
      let lastModifiedTime = '';
      // eslint-disable-next-line no-unused-expressions
      data?.statusDetails?.forEach((vm) => {
        lastModifiedTime = vm.lastModifiedTime > lastModifiedTime
          ? vm.lastModifiedTime
          : lastModifiedTime;
        if (vm.status?.toUpperCase() === 'ACTIVE') active += 1;
        if (vm.status?.toUpperCase() === 'INACTIVE') inactive += 1;
        if (vm.status?.toUpperCase() === 'DISABLED') disabled += 1;
      });

      const consolidatedData = [
        {
          name: 'TOTAL',
          total: data?.deploymentStatus?.TOTAL,
        },
        {
          name: 'DEPLOYED',
          total: data?.deploymentStatus?.DEPLOYED,
        },
        {
          name: 'NOT_DEPLOYED',
          total: data?.deploymentStatus?.NOT_DEPLOYED,
        },
        {
          name: 'ACTIVE',
          total: active,
        },
        {
          name: 'INACTIVE',
          total: inactive,
        },
        {
          name: 'DISABLED',
          total: disabled,
        },
      ];

      consolidatedData.forEach((j) => {
        if (j.name === 'DEPLOYED' || j.name === 'NOT_DEPLOYED') {
          ecDeploymentData.pieData.forEach((item) => {
            if (item.orgId === j.name) {
              // eslint-disable-next-line no-param-reassign
              item.value = j.total > 0 ? j.total : 0;
              manualTotal += j.total > 0 ? j.total : 0;
            }
          });
        } else if (j.name === 'TOTAL') {
          // eslint-disable-next-line prefer-destructuring
          total = j.total > 0 ? j.total : 0;
        } else {
          ecActiveHealthData.pieData.forEach((item) => {
            if (item.orgId === j.name) {
              // eslint-disable-next-line no-param-reassign
              item.value = j.total > 0 ? j.total : 0;
            }
          });
        }
        // const { lastModifiedTime } = data?.statusDetails[0];
        // eslint-disable-next-line no-param-reassign
        timestamp = lastModifiedTime === '' ? '' : moment(lastModifiedTime).format('MMM. DD, YYYY hh:mm:ss a');
      });

      if (manualTotal !== total) {
        consolidatedData.forEach((j) => {
          if (j.name === 'DEPLOYED' || j.name === 'NOT_DEPLOYED') {
            ecDeploymentFilteredData.pieData.forEach((item) => {
              if (item.orgId === j.name) {
                // eslint-disable-next-line no-param-reassign
                item.value = j.total > 0 ? j.total : 0;
              }
            });
          }
        });
  
        ecDeploymentFilteredData.pieData.forEach((item) => {
          if (item.orgId === 'DEPLOYED_OTHER') {
            // eslint-disable-next-line no-param-reassign
            item.value = total - manualTotal;
          }
        });
      }
      const currentState = getState();
      const connectorData = connectorMonitoringSelector.connectorData(currentState);
      const newData = combine(connectorData, data?.statusDetails, 'ecId');
      dispatch(dataChanged({
        [constants.EC_DATA]: (manualTotal !== total) ? ecDeploymentFilteredData : ecDeploymentData,
        [constants.CONNECTOR_DATA_TABLE]: newData,
        [constants.ACT_HEALTH_DATA]: ecActiveHealthData,
        [constants.VM_STATUS_DETAIL_DATA]: data?.statusDetails,
        [constants.WEBLOG_TIME]: timestamp,
        // loading: false,
      }));
    })
    .catch((error) => dispatch(boundError(error)));

  /*  get Device data Lite:- */
  dispatch(
    getDeviceDataLite(
      dataClass,
      dataType,
      awsRegionFilters,
      azureRegionFilters,
      gcpRegionFilters,
      branchLocFilters,
    ),
  );
  // const ecReportDataDeviceApi = genericInterface(constants.EC_REPORT_DATA_DEVICE_API_ENDPOINT);

  // return ecReportDataDeviceApi.create({
  //   dataClass,
  //   dataType,
  //   awsRegionFilters,
  //   azureRegionFilters,
  //   gcpRegionFilters,
  //   branchLocFilters,
  // }, {})
  //   .then((response) => {
  //     const { data } = response;

  //     dispatch(dataChanged({
  //       [constants.CONNECTOR_DATA_TABLE]: data,
  //       loading: false,
  //     }));
  //   })
  //   .catch(error => dispatch(boundError(error)));
};

// View action from 'Cloud connector' Table to 'Cloud Connector Details' page
export const viewCloudConnectorDetails = (cloudInfo) => async (dispatch, getState) => {
  dispatch(dataChanged({ [constants.DATA_LOADING]: true }));
  const currentState = getState();
  const awsFilters = connectorMonitoringSelector.awsRegionsDropdownSelector(currentState);
  const azureFilters = connectorMonitoringSelector.azureRegionsDropdownSelector(currentState);
  const gcpFilters = connectorMonitoringSelector.gcpRegionsDropdownSelector(currentState);
  const regionsFilters = connectorMonitoringSelector.regionsDropdownSelector(currentState);
  
  const awsValues = awsFilters.map((item) => item.id);
  const azureValues = azureFilters.map((item) => item.id);
  const gcpValues = gcpFilters.map((item) => item.id);
  const regionValues = regionsFilters.map((item) => item.id);
  
  const dataClass = 'EC_DASHBOARD';
  const dataType = 'EC_DEVICE';
  const awsRegionFilters = { type: 'INCLUDE', values: awsValues };
  const azureRegionFilters = { type: 'INCLUDE', values: azureValues };
  const gcpRegionFilters = { type: 'INCLUDE', values: gcpValues };
  const branchLocFilters = { type: 'INCLUDE', values: regionValues };
  const ecVmFilters = { type: 'INCLUDE', values: [cloudInfo && cloudInfo.ecId] };

  await dispatch(
    getEcDeviceDetail(
      dataClass,
      dataType,
      awsRegionFilters,
      azureRegionFilters,
      gcpRegionFilters,
      branchLocFilters,
      ecVmFilters,
      cloudInfo,
    ),
  );
  
  dispatch(dataChanged({
    [constants.CLOUD_INFO]: cloudInfo,
    [constants.SHOW_CLOUD_CONNECTOR]: true,
    [constants.DATA_LOADING]: false,
  }));

  // Old
  // dispatch(dataChanged({
  //   loading: false,
  //   [constants.SHOW_CLOUD_CONNECTOR]: true,
  //   [constants.CLOUD_CONNECTOR_DATA]: cloudInfo,
  // }));
};

// Refresh action from 'Cloud Connector Details' page
export const refreshCloudConnectorDetails = () => async (dispatch, getState) => {
  dispatch(dataChanged({ [constants.DATA_LOADING]: true }));
  const currentState = getState();
  const cloudInfo = connectorMonitoringSelector.cloudInfoSelector(currentState);
  const awsFilters = connectorMonitoringSelector.awsRegionsDropdownSelector(currentState);
  const azureFilters = connectorMonitoringSelector.azureRegionsDropdownSelector(currentState);
  const gcpFilters = connectorMonitoringSelector.gcpRegionsDropdownSelector(currentState);
  const regionsFilters = connectorMonitoringSelector.regionsDropdownSelector(currentState);
  
  const awsValues = awsFilters.map((item) => item.id);
  const azureValues = azureFilters.map((item) => item.id);
  const gcpValues = gcpFilters.map((item) => item.id);
  const regionValues = regionsFilters.map((item) => item.id);
  
  const dataClass = 'EC_DASHBOARD';
  const dataType = 'EC_DEVICE';
  const awsRegionFilters = { type: 'INCLUDE', values: awsValues };
  const azureRegionFilters = { type: 'INCLUDE', values: azureValues };
  const gcpRegionFilters = { type: 'INCLUDE', values: gcpValues };
  const branchLocFilters = { type: 'INCLUDE', values: regionValues };
  const ecVmFilters = { type: 'INCLUDE', values: [cloudInfo && cloudInfo.ecId] };

  await dispatch(
    getEcDeviceDetail(
      dataClass,
      dataType,
      awsRegionFilters,
      azureRegionFilters,
      gcpRegionFilters,
      branchLocFilters,
      ecVmFilters,
      cloudInfo,
    ),
  );
  
  dispatch(dataChanged({
    [constants.SHOW_CLOUD_CONNECTOR]: true,
    [constants.DATA_LOADING]: false,
  }));
};
export const applyFilters = () => async (dispatch, getState) => {
  dispatch(boundLoading({ loading: true }));
  const currentState = getState();
  const awsFilters = connectorMonitoringSelector.awsRegionsDropdownSelector(currentState);
  const azureFilters = connectorMonitoringSelector.azureRegionsDropdownSelector(currentState);
  const gcpFilters = connectorMonitoringSelector.gcpRegionsDropdownSelector(currentState);
  const regionsFilters = connectorMonitoringSelector.regionsDropdownSelector(currentState);

  // const awsValues = awsFilters.map(item => item.id).flat();
  // const azureValues = azureFilters.map(item => item.id).flat();
  // const regionValues = regionsFilters.map(item => item.id).flat();

  // for AvailabilityZone STATIC API, need to convert HEXA to DECIMAL
  // const awsValues = awsFilters.map(item => convertHexaToDecimal(item.id));
  // const azureValues = azureFilters.map(item => convertHexaToDecimal(item.id));
  // const regionValues = regionsFilters.map(item => convertHexaToDecimal(item.id));
  
  const awsValues = awsFilters.map((item) => item.id);
  const azureValues = azureFilters.map((item) => item.id);
  const gcpValues = gcpFilters.map((item) => item.id);
  const regionValues = regionsFilters.map((item) => item.id);
  
  const timestamp = connectorMonitoringSelector.weblogTime(currentState);
  const dataClass = 'EC_DASHBOARD';
  const dataType = 'EC_DEVICE';
  const awsRegionFilters = { type: 'INCLUDE', values: awsValues };
  const azureRegionFilters = { type: 'INCLUDE', values: azureValues };
  const gcpRegionFilters = { type: 'INCLUDE', values: gcpValues };
  const branchLocFilters = { type: 'INCLUDE', values: regionValues };

  await dispatch(
    getConnectorMonitoringData(
      dataClass,
      dataType,
      awsRegionFilters,
      azureRegionFilters,
      gcpRegionFilters,
      branchLocFilters,
      timestamp,
    ),
  );

  dispatch(dataChanged({
    [constants.AWS_REGIONS_DROPDOWN]: awsFilters,
    [constants.AZURE_REGIONS_DROPDOWN]: azureFilters,
    [constants.GCP_REGIONS_DROPDOWN]: gcpFilters,
    [constants.REGIONS_DROPDOWN]: regionsFilters,
  }));
};

export const loadStatic = () => (dispatch, getState) => {
  const currentState = getState();
  // If redirected from Traffic Monitoring > Traffic Flow page
  const trafficFlowStatus = trafficSelector.trafficFlowStatusSelector(currentState);
  
  if (trafficFlowStatus) {
    const trafficFlowEcId = trafficSelector.ecIdSelector(currentState);
    const connectorData = connectorMonitoringSelector.connectorData(currentState);
    const cloudInfo = connectorData.find((ele) => (ele.ecId === trafficFlowEcId));
  
    return dispatch(
      viewCloudConnectorDetails(cloudInfo),
    );
  }
  
  return dispatch(boundSuccess({
    ...[constants.EC_DATA],
    ...[constants.ACT_HEALTH_DATA],
    ...[constants.EC_NW_DATA],
    ...[constants.EC_GEO_DATA],
    [constants.DATA_LOADING]: false,
  }));
};

// Branch & Cloud -> Status API (Pie Charts data)
// eslint-disable-next-line consistent-return
export const load = (reset) => (dispatch, getState) => {
  dispatch(boundLoading({ loading: true }));
  
  const currentState = getState();
  // If redirected from Traffic Monitoring > Traffic Flow page
  const trafficFlowStatus = trafficSelector.trafficFlowStatusSelector(currentState);
  
  if (trafficFlowStatus && !reset) {
    const trafficFlowEcId = trafficSelector.ecIdSelector(currentState);
    const connectorData = connectorMonitoringSelector.connectorData(currentState);
    const cloudInfo = connectorData.find((ele) => (ele.ecId === trafficFlowEcId));
    
    return dispatch(
      viewCloudConnectorDetails(cloudInfo),
    );
  }
  
  // Some random navigation is reseting the pie to default 0 0 rather than the data.
  // dispatch(dataChanged(constants.RESET_PIE));

  const timestamp = connectorMonitoringSelector.weblogTime(currentState);
  
  const dataClass = 'EC_DASHBOARD';
  const dataType = 'EC_DEVICE';
  
  const awsFilters = connectorMonitoringSelector.awsRegionsDropdownSelector(currentState);
  const azureFilters = connectorMonitoringSelector.azureRegionsDropdownSelector(currentState);
  const gcpFilters = connectorMonitoringSelector.gcpRegionsDropdownSelector(currentState);
  const regionsFilters = connectorMonitoringSelector.regionsDropdownSelector(currentState);
  
  const awsValues = awsFilters.map((item) => item.id);
  const azureValues = azureFilters.map((item) => item.id);
  const gcpValues = gcpFilters.map((item) => item.id);
  const regionValues = regionsFilters.map((item) => item.id);
    
  const awsRegionFilters = { type: 'INCLUDE', values: awsValues };
  const azureRegionFilters = { type: 'INCLUDE', values: azureValues };
  const gcpRegionFilters = { type: 'INCLUDE', values: gcpValues };
  const branchLocFilters = { type: 'INCLUDE', values: regionValues };
  
  dispatch(
    getConnectorMonitoringData(
      dataClass,
      dataType,
      awsRegionFilters,
      azureRegionFilters,
      gcpRegionFilters,
      branchLocFilters,
      timestamp,
    ),
  );
  
  // make sure to turn off the Flag when its returned subsequently
  // from Traffic Monitoring direct link
  dispatch(dataChanged({
    [constants.SHOW_CLOUD_CONNECTOR]: false,
  }));
};

// Back Button ('<-') from 'Cloud connector' to 'Branch & Cloud Connector Monitoring' page
// eslint-disable-next-line consistent-return
export const toggleConnectorMointoring = () => (dispatch, getState) => {
  const currentState = getState();
  const connectorData = connectorMonitoringSelector.connectorData(currentState);
  
  dispatch(dataChanged({
    [constants.SHOW_CLOUD_CONNECTOR]: false,
  }));

  if (Array.isArray(connectorData) && !connectorData.length) {
    return dispatch(load());
  }
};

// GeoView -> Popup Open
export const handlePopupOpen = (cloudInfo) => async (dispatch, getState) => {
  const { isZeroTrustGateway } = cloudInfo || {};
  if (isZeroTrustGateway) {
    return dispatch(dataChanged({
      [constants.EC_INFO]: cloudInfo,
      [constants.CLOUD_CONNECTOR_DATA]: cloudInfo,
      // [constants.DEVICE_STATUS_TIME]: deviceTimestamp,
      loading: false,
    }));
  }
    
  dispatch(dataChanged({ [constants.LOADING_POPUP]: true }));
  const currentState = getState();
  const awsFilters = connectorMonitoringSelector.awsRegionsDropdownSelector(currentState);
  const azureFilters = connectorMonitoringSelector.azureRegionsDropdownSelector(currentState);
  const regionsFilters = connectorMonitoringSelector.regionsDropdownSelector(currentState);
  
  const awsValues = awsFilters.map((item) => item.id);
  const azureValues = azureFilters.map((item) => item.id);
  const gcpValues = azureFilters.map((item) => item.id);
  const regionValues = regionsFilters.map((item) => item.id);
  
  const dataClass = 'EC_DASHBOARD';
  const dataType = 'EC_DEVICE';
  const awsRegionFilters = { type: 'INCLUDE', values: awsValues };
  const azureRegionFilters = { type: 'INCLUDE', values: azureValues };
  const gcpRegionFilters = { type: 'INCLUDE', values: gcpValues };
  const branchLocFilters = { type: 'INCLUDE', values: regionValues };
  const ecVmFilters = { type: 'INCLUDE', values: [cloudInfo && cloudInfo.ecId] };

  await dispatch(
    getEcDeviceDetail(
      dataClass,
      dataType,
      awsRegionFilters,
      azureRegionFilters,
      gcpRegionFilters,
      branchLocFilters,
      ecVmFilters,
      cloudInfo,
    ),
  );
  dispatch(dataChanged({ [constants.LOADING_POPUP]: false }));
};

// GeoView -> Popup Open
export const handlePopupOpenStatic = (cloudInfo) => (dispatch) => {
  const data = [];
  const mergedData = { ...data, ...cloudInfo };
  dispatch(dataChanged({
    [constants.EC_INFO]: data,
    [constants.CLOUD_CONNECTOR_DATA]: mergedData,
    loading: false,
  }));
};

export const handleSetLastUrl = (url) => async (dispatch) => {
  await dispatch(dataChanged({
    [constants.LAST_URL]: url,
    loading: false,
  }));
};

export const handleResetDeviceId = () => async (dispatch) => {
  await dispatch(toggleTrafficMointoringExit());
  return dispatch(dataChanged({
    [constants.SHOW_CLOUD_CONNECTOR]: false,
  }));
};

export const handleCurrentPageChange = (page) => (dispatch) => {
  dispatch(dataChanged({
    [constants.CURRENT_PAGE]: page,
  }));
};

export const handleCloseApplianceNotification = () => (dispatch) => {
  dispatch(dataChanged({
    [constants.HARDWARE_NOTIFICATIONS]: [],
    loading: false,
  }));
};

export const handleDrawer = (value, drawerData) => async (dispatch) => {
  return dispatch(boundLoading({
    [constants.OPEN_DRAWER]: value,
    [constants.DRAWER_DATA]: drawerData,
  }));
};
