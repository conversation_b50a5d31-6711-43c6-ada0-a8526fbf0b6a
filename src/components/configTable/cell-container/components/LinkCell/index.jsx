import React, { useRef } from 'react';
import { NavLink } from 'react-router-dom';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import i18n from 'utils/i18n';
import { useDispatch } from 'react-redux';
import { isEmpty } from 'utils/lodash';

function LinkCell(props) {
  const dispatch = useDispatch();
  const {
    value, skipTranslation, onHandleLink, linkNameParameter, currentRow,
    linkSelf, column, onHandleLinkSelf,
  } = props;
  const {
    name, parentId, link,
  } = value;
  const linkRef = useRef(null);
  const { values } = currentRow || {};
  const { id: columnId } = column || {};

  const linkName = () => {
    if (linkSelf) {
      return skipTranslation
        ? values[columnId]
        : i18n.t(values[columnId]);
    }
    if (isEmpty(linkNameParameter)) return skipTranslation ? name : i18n.t(name);

    return skipTranslation
      ? value[linkNameParameter]
      : i18n.t(value[linkNameParameter]);
  };

  const onHandle = () => {
    if (linkSelf) return onHandleLinkSelf(columnId);
    return onHandleLink(name);
  };

  const renderContent = () => (
    <div className={parentId ? 'child-checkbox' : 'parent-checkbox'}>
      {
        link && (
          <NavLink
            className="checkboxlink-cell-url"
            to={link}
            onClick={() => {
              dispatch(onHandle());
            }}>
            {linkName()}
          </NavLink>
        )
      }
      {
        !link && (
          <a
            className="checkboxlink-cell-url"
            role="link"
            tabIndex="0"
            onKeyDown={() => {
              dispatch(onHandle());
            }}
            onClick={() => {
              dispatch(onHandle());
            }}>
            {linkName()}
          </a>
        )
      }
    </div>
  );

  return (
    <div ref={linkRef}>
      {renderContent()}
    </div>
  );
}

LinkCell.propTypes = {
  value: PropTypes.shape({
    name: PropTypes.string,
    parentId: PropTypes.string,
    link: PropTypes.string,
  }),
  column: PropTypes.shape({}),
  currentRow: PropTypes.shape({}),
  linkNameParameter: PropTypes.string,
  linkSelf: PropTypes.bool,
  onHandleLink: PropTypes.func,
  onHandleLinkSelf: PropTypes.func,
  skipTranslation: PropTypes.bool,
};

LinkCell.defaultProps = {
  value: '',
  skipTranslation: false,
  onHandleLink: null,
  onHandleLinkSelf: null,
  linkNameParameter: '',
};

export default withTranslation()(LinkCell);
