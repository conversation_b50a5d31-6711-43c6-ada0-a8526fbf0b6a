import React, { useEffect } from 'react';
import { EntityDropdown } from 'components/entityDropdown';
import { useDispatch, useSelector } from 'react-redux';
import { dropdownActions } from 'ducks/dropdowns/gatewayLogAndControl';
import { isEmpty } from 'utils/lodash';

export function GatewayLogAndControl(props) {
  const dispatch = useDispatch();
  const { data } = useSelector((state) => state.gatewayLogAndControlDropdown);

  useEffect(() => {
    dispatch(dropdownActions.load());
  }, []);

  if (isEmpty(data)) return <></>;

  return <EntityDropdown data={data} {...props} />;
}

export default GatewayLogAndControl;
