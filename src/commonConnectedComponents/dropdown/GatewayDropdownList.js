import React, { useEffect } from 'react';
import { EntityDropdown } from 'components/entityDropdown';
import { useDispatch, useSelector } from 'react-redux';
import { dropdownActions } from 'ducks/dropdowns/gatewayZia';
import { isEmpty } from 'utils/lodash';

export function GatewayDropdownList(props) {
  const dispatch = useDispatch();
  const { data } = useSelector((state) => state.gatewayZiaDropdown);

  useEffect(() => {
    dispatch(dropdownActions.load());
  }, []);

  if (isEmpty(data)) return <></>;

  return <EntityDropdown data={data} noTranslation {...props} />;
}

export default GatewayDropdownList;
