/* eslint-disable react/jsx-handler-names */
import React, { useEffect } from 'react';
import Loading from 'components/spinner/Loading';
import { useDispatch, useSelector } from 'react-redux';
import { PropTypes } from 'prop-types';
import { change, updateSyncErrors } from 'redux-form';
import { loader, handleSearchStringChange, perfomSearch } from 'ducks/dropdowns/system-user';
import selectors from 'ducks/dropdowns/system-user/selectors';
import AdvDropdownWithAsyncSearch from 'components/dropDown/AdvDropdownWithAsyncSearch';

export function IdPNameDropdown(props) {
  const dispatch = useDispatch();
  const selector = useSelector((state) => selectors(state).dropdown);
  const {
    data, hasMorePages, loading, searchString = '',
  } = selector;
  const {
    input, meta, disabled, groupName, displayFilterLabel, styleClass,
  } = props;
  const { name, value } = input || {};
  const { form } = meta || {};

  useEffect(() => {
    dispatch(loader(true, '', groupName));
  }, []);

  return (
    <>
      <Loading loading={loading} />
      <AdvDropdownWithAsyncSearch
        {...props}
        items={data}
        disabled={disabled}
        currentItems={[]}
        hasNextPage={hasMorePages}
        defaultValue={value}
        styleClass={styleClass}
        onPerformSearch={() => dispatch(perfomSearch())}
        onSearchStringChange={(search) => dispatch(handleSearchStringChange(search))}
        onClickCb={(item) => {
          dispatch(change(form, name, item));
          dispatch(updateSyncErrors(form));
        }}
        onChange={input.onChange}
        onBlur={input.onBlur}
        loading={loading}
        searchString={searchString}
        displayFilterLabel={displayFilterLabel} />
    </>
  );
}

IdPNameDropdown.propTypes = {
  disabled: PropTypes.bool,
  input: PropTypes.shape({
    onChange: PropTypes.func,
    onBlur: PropTypes.func,
  }),
  meta: PropTypes.shape({}),
  groupName: PropTypes.string,
  styleClass: PropTypes.string,
  displayFilterLabel: PropTypes.string,
};
  
IdPNameDropdown.defaultProps = {
  disabled: false,
  input: {},
  meta: {},
  groupName: 'System User',
  styleClass: '',
};

export default IdPNameDropdown;
