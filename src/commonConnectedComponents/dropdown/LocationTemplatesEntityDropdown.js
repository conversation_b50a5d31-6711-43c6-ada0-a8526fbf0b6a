/* eslint-disable react/jsx-handler-names */
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { PropTypes } from 'prop-types';
import { change, updateSyncErrors } from 'redux-form';
import Loading from 'components/spinner/Loading';
import { loader, handleSearchStringChange, perfomSearch } from 'ducks/dropdowns/locationTemplatesEntityDropdown';
import selectors from 'ducks/dropdowns/locationTemplatesEntityDropdown/selectors';
import AdvDropdownWithAsyncSearch from 'components/dropDown/AdvDropdownWithAsyncSearch';

export function LocationTemplatesEntityDropdown(props) {
  const dispatch = useDispatch();
  const selector = useSelector((state) => selectors(state).dropdown);
  const {
    data, hasMorePages, loading, searchString = '',
  } = selector;
  const { input, meta } = props;
  const { name, value } = input || {};
  const { form } = meta || {};

  useEffect(() => {
    dispatch(loader(true, ''));
  }, []);

  return (
    <>
      <Loading loading={loading} />
      <AdvDropdownWithAsyncSearch
        {...props}
        items={data}
        currentItems={[]}
        hasNextPage={hasMorePages}
        defaultValue={value}
        onPerformSearch={() => dispatch(perfomSearch())}
        onSearchStringChange={(search) => dispatch(handleSearchStringChange(search))}
        onClickCb={(item) => {
          dispatch(change(form, name, item));
          dispatch(updateSyncErrors(form));
        }}
        onChange={input.onChange}
        onBlur={input.onBlur}
        loading={loading}
        searchString={searchString} />
    </>
  );
}

LocationTemplatesEntityDropdown.propTypes = {
  input: PropTypes.shape({
    onChange: PropTypes.func,
    onBlur: PropTypes.func,
  }),
  meta: PropTypes.shape({}),
};
  
LocationTemplatesEntityDropdown.defaultProps = {
  input: {},
  meta: {},
};

export default LocationTemplatesEntityDropdown;
