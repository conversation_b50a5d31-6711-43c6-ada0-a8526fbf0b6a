import { noop } from 'utils/lodash';
import PropTypes from 'prop-types';
import React from 'react';

import { objectsAreSame } from 'utils/helpers';

// import Localized from 'utils/i18n';
import { withTranslation } from 'react-i18next';

import Spinner from '../spinner';

class MultiSelectApiFilters extends React.Component {
  constructor(props) {
    super(props);
    const {
      allOptionsSelectedLabel, defaultValue, items, t,
    } = props;
    const idSelected = defaultValue.map((value) => value.id);
    let labelSelectedItems = defaultValue.map((value) => t(value.label)).join(', ');

    if (defaultValue.length === 0) {
      labelSelectedItems = t(allOptionsSelectedLabel || 'ALL');
    }
    const listItems = items.map((item) => {
      const itemObj = item;
      itemObj.isChecked = idSelected.indexOf(itemObj.id) !== -1;
      return itemObj;
    });
    this.state = {
      labelSelectedItems,
      listItems,
      search: '',
      openList: false,
      changeInput: false,
      allChecked: false,
    };
    this.wrapperRef = null;
  }

  componentDidMount() {
    document.addEventListener('mousedown', this.handleMousedown);
  }

  componentDidUpdate(prevProps) {
    const {
      allOptionsSelectedLabel, defaultValue, hasApiSearch, isLoading, items, t, reset,
    } = this.props;
    const { search } = this.state;
    let listItems;
    const idSelected = defaultValue.map((value) => value.id);
    const hasDefaultValueSame = objectsAreSame(prevProps.defaultValue, defaultValue);
    if (!hasDefaultValueSame || prevProps.isLoading !== isLoading) {
      let labelSelectedItems = defaultValue.map((value) => t(value.label)).join(', ');
      if (defaultValue.length === 0 || defaultValue.length === items.length) {
        labelSelectedItems = t(allOptionsSelectedLabel);
      }
      // if the selectedItem doesn't match from dropdownList items,
      // will be selected and concatenated and placed at the top
      if (
        items.length
        && defaultValue.length === 1
        && items.findIndex((item) => item.id === defaultValue[0].id) === -1
      ) {
        items.unshift(defaultValue[0]);
      }
      listItems = items.map((item) => {
        const itemObj = item;
        itemObj.isChecked = idSelected.indexOf(itemObj.id) !== -1;
        return itemObj;
      });
      const stateVar = {
        isLoading,
        labelSelectedItems,
        listItems,
        search: hasApiSearch ? search : '',
      };
      if (!hasDefaultValueSame) {
        stateVar.openList = false;
      }
      // eslint-disable-next-line
      this.setState(stateVar);
    }
    if (prevProps.reset !== reset) {
      listItems = items.map((item) => {
        // eslint-disable-next-line
        item.isChecked = false;
        return item;
      });
      const labelSelectedItems = t(allOptionsSelectedLabel || 'ALL');
      // eslint-disable-next-line
      this.setState({
        labelSelectedItems,
        listItems,
        search: '',
        openList: false,
      });
    }
  }

  componentWillUnmount() {
    document.removeEventListener('mousedown', this.handleMousedown);
  }

  handleClosePanel = () => {
    this.setState({ openList: false });
  };

  handleDoneAction = () => {
    const { listItems } = this.state;
    const { t, setValue, allOptionsSelectedLabel } = this.props;
    const selectedListItems = listItems.filter((item) => item.isChecked === true);
    let updatedLabels = selectedListItems.map((item) => t(item.label)).join(', ');
    if (selectedListItems.length === 0) {
      updatedLabels = '';
    } else if (selectedListItems.length === listItems.length) {
      updatedLabels = t(allOptionsSelectedLabel || 'ALL');
    }
    this.setState({ openList: false, labelSelectedItems: updatedLabels, changeInput: false });
    setValue(selectedListItems);
  };

  setWrapperRef = (node) => {
    this.wrapperRef = node;
  };

  handleMousedown = (e) => {
    const containRef = this.wrapperRef.contains(e.target);
    const { changeInput } = this.state;
    if (!containRef) {
      this.setState({ openList: false, changeInput: false });

      if (changeInput) {
        this.handleDoneAction();
      }
    }
  };

  handleOpenList = () => {
    const { openList } = this.state;
    const { fetchOptionsCallback } = this.props;
    const stateVar = { openList: !openList };
    this.setState(stateVar);
    fetchOptionsCallback();
  };

  handleChangeInput = (event) => {
    const inputVal = event.target.value;
    const { hasApiSearch, searchCallback } = this.props;
    const { search } = this.state;
    this.setState({ search: inputVal });
    if (hasApiSearch && search !== inputVal) {
      searchCallback(inputVal.trim());
    }
  };

  changeSelectedHandler = (event, option) => {
    const { checked } = event.target;
    this.setState((prevState) => {
      let { allChecked, listItems } = prevState;
      if (option === 'all') {
        allChecked = checked;
        listItems = listItems.map((item) => ({ ...item, isChecked: checked }));
      } else {
        listItems = listItems.map((item) => (
          item.id === option.id ? { ...item, isChecked: checked } : item));
        allChecked = listItems.every((item) => item.isChecked);
      }
      return { allChecked, listItems, changeInput: true };
    });
    // this.setState({ changeInput: true });
  };

  render() {
    const {
      hasApiSearch, hasSearch, isLoading, searchProperty, t,
      removeFilterCallback, hasLocalSort, labelSelectedValue, showLabelInSelectedValue,
    } = this.props;
    const {
      labelSelectedItems, openList, search, allChecked,
    } = this.state;
    let { listItems } = this.state;
    if (hasLocalSort) {
      listItems.sort((a, b) => {
        return a.label.localeCompare(b.label);
      });
    }
    if (hasSearch && search) {
      listItems = listItems.filter(
        (item) => item[searchProperty].toLowerCase().indexOf(search.toLowerCase()) !== -1,
      );
    }
    const dropdownOptions = (
      <div className="multiselect-dropdown-list-content">
        {isLoading && <Spinner size="medium" />}
        <div className="multiselect-dropdown-list-item">
          <input
            type="checkbox"
            className="multi-select-list-item-checkbox"
            checked={allChecked}
            onChange={(evt) => this.changeSelectedHandler(evt, 'all')} />
          <span className="multi-select-list-item-value">{t('ALL')}</span>
        </div>
        {listItems.map((item) => (
          <div key={item.id} className="multiselect-dropdown-list-item">
            <input
              type="checkbox"
              className="multi-select-list-item-checkbox"
              onChange={(evt) => this.changeSelectedHandler(evt, item)}
              checked={item.isChecked} />
            <span className="multi-select-list-item-value">{t(item.label)}</span>
          </div>
        ))}
      </div>
    );
    return (
      <div className="drop-down-container" ref={this.setWrapperRef}>
        <button
          className="drop-down-selected-value"
          onClick={this.handleOpenList}
          onKeyPress={this.handleOpenList}
          type="button">
          <span className="label-selected-items">
            {showLabelInSelectedValue && (
              <span className="label-selected-value">
                {labelSelectedValue}
                =
              </span>
            )}
            {labelSelectedItems}
          </span>
        </button>
        {removeFilterCallback && (
          // eslint-disable-next-line jsx-a11y/control-has-associated-label
          <div
            className="far fa-times-circle remove-filter-icon"
            role="button"
            tabIndex={0}
            onClick={removeFilterCallback}
            onKeyPress={removeFilterCallback}>
          </div>
        )}
        <div className="dropdown-box">
          <div className={`drop-down-list ${openList ? 'open' : ''}`}>
            {(hasSearch || hasApiSearch) && (
              <div className="dropdown-list-header">
                <div className="dropdown-search">
                  <input
                    className="dropdown-input-search"
                    type="text"
                    value={search}
                    placeholder="Search ..."
                    onChange={this.handleChangeInput} />
                  <span className="search-icon fa fa-search"></span>
                </div>
              </div>
            )}
            {dropdownOptions}
          </div>
        </div>
      </div>
    );
  }
}

MultiSelectApiFilters.propTypes = {
  fetchOptionsCallback: PropTypes.func,
  searchCallback: PropTypes.func,
  allOptionsSelectedLabel: PropTypes.string,
  hasApiSearch: PropTypes.bool,
  hasSearch: PropTypes.bool,
  isLoading: PropTypes.bool,
  hasLocalSort: PropTypes.bool,
  items: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.oneOfType([
        PropTypes.string,
        PropTypes.arrayOf(PropTypes.string),
        PropTypes.shape({}),
      ]),
      label: PropTypes.string,
    }),
  ),
  t: PropTypes.func,
  setValue: PropTypes.func,
  searchProperty: PropTypes.string,
  defaultValue: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.oneOfType([
        PropTypes.string,
        PropTypes.arrayOf(PropTypes.string),
        PropTypes.shape({}),
      ]),
      label: PropTypes.string,
      id: PropTypes.string,
    }),
  ),
  labelSelectedValue: PropTypes.string,
  showLabelInSelectedValue: PropTypes.bool,
  removeFilterCallback: PropTypes.func,
  reset: PropTypes.bool,
};

MultiSelectApiFilters.defaultProps = {
  fetchOptionsCallback: noop,
  allOptionsSelectedLabel: 'ALL',
  hasApiSearch: false,
  hasSearch: false,
  isLoading: true,
  items: [],
  t: (str) => str,
  hasLocalSort: true,
  searchCallback: noop,
  searchProperty: 'label',
  setValue: noop,
  defaultValue: [],
  removeFilterCallback: noop,
  reset: false,
};

export default withTranslation()(MultiSelectApiFilters);
