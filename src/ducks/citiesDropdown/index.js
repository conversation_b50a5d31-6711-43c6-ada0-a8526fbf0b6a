import { loading, loadSuccess, loadError } from 'ducks/generics';
import { genericInterface } from 'utils/http';
// import * as filterSelector from 'ducks/filters/selectors';
import * as durationSelector from 'ducks/duration/selectors';
import * as constants from './constants';
import actionTypes from './action-types';
import template from './query';

export { REDUCER_KEY } from './constants';

const actionTypeExists = Object.keys(actionTypes).reduce((hash, key) => ({
  ...hash,
  [actionTypes[key]]: true,
}), {});

export const reducer = (state = constants.DEFAULT_STATE, action = {}) => {
  if (actionTypeExists[action.type]) {
    return {
      ...state,
      ...action.payload,
    };
  }
  return state;
};
export default reducer;

const boundLoading = loading(actionTypes.DATA_LOADING);
// eslint-disable-next-line
const boundSuccess = loadSuccess(actionTypes.DATA_LOAD_SUCCESS);
const boundError = loadError(actionTypes.DATA_LOAD_ERROR);

const citiesApi = genericInterface(constants.API_ENDPOINT);

export const getCities = () => (dispatch, getState) => {
  dispatch(boundLoading(constants.DEFAULTS));
  const state = getState();
  const { startTime, endTime, interval } = durationSelector.timeRange(state);
  const filters = {
    // ...filterSelector.filters(state),
    time: {
      range: [[startTime, endTime]],
    },
  };

  const query = template(filters, interval);

  return citiesApi.create(query, {})
    .then((response) => dispatch(boundSuccess(constants.DATA, response.data)))
    .catch((error) => dispatch(boundError(error)));
};
