import React from 'react';
import PropTypes from 'prop-types';
import { noop } from 'utils/lodash';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faAngleUp, faAngleDown } from '@fortawesome/pro-solid-svg-icons';

export function DropdownDisplay(props) {
  const {
    open,
    actions,
    tabIndex,
    defaultDisplayLabel,
    displayLabel,
    values,
    disabled,
    selectedValues,
  } = props;
  const label = (values.length > 0 || (selectedValues && selectedValues.length > 0))
    ? displayLabel : defaultDisplayLabel;

  const handleClick = (e) => {
    if (disabled) { return; }
    if (open) {
      actions.cancel();
    } else {
      actions.open();
    }
  };

  return (
    <div
      className="dropdown-display">
      <div
        disabled={disabled}
        className={`dropdown-value ${disabled ? 'disabled' : ''}`}
        // aria-label="Display Options"
        onClick={(e) => (handleClick(e))}
        role="button"
        tabIndex="0"
        onKeyUp={(e) => (handleClick(e))}>
        {label}
      </div>
      <button
        disabled={disabled}
        className={`dropdown-icon ${disabled ? 'disabled' : ''}`}
        onClick={(e) => (handleClick(e))}
        type="button"
        aria-label="dropdown-icon"
        tabIndex={tabIndex}>
        <FontAwesomeIcon
          width={20}
          height={20}
          transform="grow-8"
          icon={open ? faAngleUp : faAngleDown} />
      </button>
    </div>
  );
}

DropdownDisplay.propTypes = {
  open: PropTypes.bool,
  disabled: PropTypes.bool,
  defaultDisplayLabel: PropTypes.string,
  actions: PropTypes.shape({
    cancel: PropTypes.func,
    open: PropTypes.func,
  }),
  tabIndex: PropTypes.number,
  displayLabel: PropTypes.string,
  values: PropTypes.arrayOf(PropTypes.shape({})),
  selectedValues: PropTypes.arrayOf(PropTypes.shape({})),
};

DropdownDisplay.defaultProps = {
  open: false,
  disabled: false,
  defaultDisplayLabel: 'SELECT',
  actions: {
    open: noop,
  },
  tabIndex: -1,
  displayLabel: '',
  values: [],
  selectedValues: [],
};

export default DropdownDisplay;
