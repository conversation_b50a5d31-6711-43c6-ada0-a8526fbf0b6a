// @flow

import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';
import Draggable from 'react-draggable';
import { faMinus, faTicket, faExternalLinkSquare } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { get } from 'utils/lodash';
import * as helpSelectors from 'ducks/help/selectors';
import inlineHelpLogo from 'images/inline_help_icon.png';
import inlineHelpResizeIcon from 'images/inline_help_resize_icon.png';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import ReactDOM from 'react-dom';
import { ResizableBox } from 'react-resizable';
import 'react-resizable/css/styles.css';
import { setPosition, setShowHelp } from 'ducks/help';
import './index.scss';
import { withTranslation } from 'react-i18next';

export class Help extends Component {
  static propTypes = {
    actions: PropTypes.shape({}),
    article: PropTypes.string,
    helpSite: PropTypes.string,
    position: PropTypes.shape({
      x: PropTypes.number,
      y: PropTypes.number,
    }),
    showPageHelp: PropTypes.bool,
    t: PropTypes.func,
  };

  static defaultProps = {
    actions: {},
    article: 'about-cloud-connector-admin-portal',
    helpSite: 'help.zscaler.com/cloud-connector/',
    position: {
      x: 0,
      y: 0,
    },
    showPageHelp: false,
    t: (v) => v,
  };

  constructor(props) {
    super(props);

    this.helpRef = React.createRef();
    this.el = document.createElement('div');
    this.el.className = 'inline-help-container';

    this.state = {
      width: 360,
      height: 480,
    };
  }

  componentDidMount() {
    const dimension = JSON.parse(window.localStorage.getItem('helpBrowserSize'));
    const mainPageRoot = document.getElementsByClassName('page')[0];

    if (dimension) {
      const { width, height } = dimension;
      this.setState({ width, height });
    }
    mainPageRoot.appendChild(this.el);
    this.updateBrowserPosition();
  }

  componentWillUnmount() {
    const mainPageRoot = document.getElementsByClassName('page')[0];
    mainPageRoot.removeChild(this.el);
  }

  handleShowHelp = () => {
    const { actions, showPageHelp } = this.props;
    actions.setShowHelp(!showPageHelp);
  };

  updateBrowserPosition = () => {
    const { actions } = this.props;
    const { current } = this.helpRef;
    const helpIcon = current.getBoundingClientRect();
    const data = {
      x: helpIcon.left - 310,
      y: helpIcon.top - 425,
    };

    actions.setPosition(data);
  };

  handleStop = (event, data) => {
    const { actions } = this.props;
    actions.setPosition(data);
  };

  // On top layout
  handleResize = (event, element) => {
    const { size } = element;
    const dimension = {
      width: size.width,
      height: size.height,
    };

    this.setState(
      {
        ...dimension,
      },
      () => {
        window.localStorage.setItem('helpBrowserSize', JSON.stringify(dimension));
      },
    );
  };

  renderContent = () => {
    const {
      t,
      helpSite,
      article,
      showPageHelp,
      position,
    } = this.props;
    const { height, width } = this.state;
    const protocol = get(window.location, 'protocol', 'https');
    const helpUrl = `${protocol}//${helpSite}${article}?source=admin-ui`;

    return (
      <>
        <div
          className="inline-help-icons-container -js-inline-help-icons-container"
          style={{ display: `${showPageHelp ? 'none' : 'block'}` }}
          ref={this.helpRef}
          onClick={this.handleShowHelp}
          onKeyPress={this.handleShowHelp}
          role="button"
          tabIndex={0}>
          <div className="inline-help-text-icon text-help-container -js-text-help" data-help-property="inlineHelp">
            <img className="inline-help-logo" src={inlineHelpLogo} alt="inlineHelpLogo" />
            <span className="inline-help-text">{t('HELP')}</span>
          </div>
        </div>
        {
          showPageHelp
          && (
            <Draggable
              defaultPosition={{ x: position.x, y: position.y }}
              bounds="parent"
              onStop={this.handleStop}>
              <ResizableBox
                height={height}
                width={width}
                onResize={this.handleResize}
                style={{
                  zIndex: 999999,
                  minHeight: 480,
                  minWidth: 360,
                  position: 'absolute',
                }}
                resizeHandles={['nw']}>
                <div
                  className="inline-help-iframes-container-outer"
                  style={{ width: width + 'px', height: height + 'px' }}>
                  <div className="inline-help-iframes-container-inner">
                    <div className="inline-help-iframes-header">
                      <div className="iframe-header-icon">
                        <img
                          className="resize-logo"
                          src={inlineHelpResizeIcon}
                          alt="inlineHelpResizeIcon" />
                      </div>
                      <div className="iframe-title inline-block half-width">
                        <span>{t('HELP')}</span>
                      </div>
                      <div className="iframe-controls inline-block half-width">
                        <span className="control-icon">
                          <FontAwesomeIcon icon={faMinus} onClick={this.handleShowHelp} />
                        </span>
                      </div>
                    </div>
                    <div className="inline-help-iframes-content-body">
                      <div className="iframes-content -js-iframes-content">
                        <iframe
                          id="helpPortalIframe"
                          name="inlineHelpIframe"
                          style={{ width: '100%', height: '100%', border: 'none' }}
                          src={helpUrl}
                          title="Help" />
                      </div>
                    </div>
                    <div className="inline-help-iframes-footer">
                      <div className="iframes-footer-options inline-block half-width">
                        <FontAwesomeIcon icon={faTicket} />
                        &nbsp;
                        <span className="iframes-footer-title">{t('SUBMIT_A_TICKET')}</span>
                      </div>
                      <div className="iframes-footer-options inline-block half-width">
                        <FontAwesomeIcon icon={faExternalLinkSquare} />
                        &nbsp;
                        <span className="iframes-footer-title" data-link-property="">{t('OPEN_A_NEW_TAB')}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </ResizableBox>
            </Draggable>
          )
        }

      </>
    );
  };

  render() {
    return ReactDOM.createPortal(this.renderContent(), this.el);
  }
}

const mapStateToProps = (state) => ({
  article: helpSelectors.articleSelector(state),
  showPageHelp: helpSelectors.showHelpSelector(state),
  position: helpSelectors.positionSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    setPosition,
    setShowHelp,
  }, dispatch);
  return { actions };
};

export default withTranslation()(connect(mapStateToProps, mapDispatchToProps)(Help));
