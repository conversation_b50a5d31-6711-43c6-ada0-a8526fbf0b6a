/* eslint-disable react/jsx-handler-names */
/* eslint-disable react/no-array-index-key */
import React from 'react';
import PropTypes from 'prop-types';
import { noop, isEmpty } from 'utils/lodash';

import Loading from 'components/spinner/Loading';
import ServerError from 'components/errors/ServerError';
import { CheckboxInput } from 'components/CheckboxInput';
import ReactTooltip from 'react-tooltip';
import { FixedSizeList as List } from 'react-window';

import { withTranslation } from 'react-i18next';
import HighlightedText from 'components/highlightedText';
import DropdownHeader from './DropdownHeader';
import MultiDropdownAsyncSearch from './MultiDropdownAsyncSearch';

export function UnselectedBigData(props) {
  const {
    actions: {
      onChange,
      nextPage,
      load,
    },
    disabledTooltip,
    hasMorePages,
    isViewOnly,
    loadMoreLabel,
    noDataLabel,
    searchString,
    t,
    unselectedHeader,
    unselectedValues,
  } = props;

  const itemRenderer = (item, style) => {
    const label = <HighlightedText text={t(item.name)} searchString={searchString} />;
    const tooltip = item.disabled ? disabledTooltip : item.name;
    const isParent = isEmpty(item.original.parent);

    return !item.deleted && (
      <div
        style={style}
        className={`multiselect-list-item ${isParent ? 'parent' : 'child'}  ${!isParent && !item.checked ? '' : 'active'}`}
        key={`checkbox-${item.id}`}>
        <ReactTooltip
          place="top"
          type="light"
          className="react-tooltip"
          id={`disabled-${item.id}`}
          disable={(tooltip.length === 0)}>
          {tooltip}
        </ReactTooltip>
        <div>
          <CheckboxInput
            id={item.id}
            label={label}
            className="unselected"
            disable={item.disabled || isViewOnly}
            input={{
              onChange: (event) => onChange(event, item),
              value: item.name,
              name: item.name,
              checked: item.checked,
            }} />
        </div>
      </div>
    );
  };

  const loadNextPage = () => {
    return Promise.resolve()
      .then(() => {
        nextPage();
      })
      .then(() => {
        load();
      });
  };

  const renderPagination = (style) => {
    if (!hasMorePages) return <></>;
    return (
      <div className="unselected" style={style}>
        <button
          type="button"
          className="load-more flat-button"
          onClick={loadNextPage}>
          {loadMoreLabel}
        </button>
      </div>
    );
  };

  const renderContent = ({ index, style }) => {
    if (index === unselectedValues.length) {
      return renderPagination(style);
    }

    if (searchString.length > 0 && unselectedValues.length === 0) {
      return <div className="unselected">{noDataLabel}</div>;
    }
    // return unselectedValues.map(itemRenderer);
    const items = [unselectedValues[index]].map((item) => itemRenderer(item, style));

    return items;
  };

  return (
    <div className="dropdown-unselected">
      <DropdownHeader text={unselectedHeader} />
      <MultiDropdownAsyncSearch {...props} />
      <Loading {...props}>
        <ServerError {...props} size="small">
          <div className="unselected-items no-overflow">
            <List
              className="List"
              height={310}
              // height={hasMorePages ? 280 : 310}
              itemCount={unselectedValues.length + 1}
              itemSize={38}
              width={280}>
              {renderContent}
            </List>
          </div>
        </ServerError>
      </Loading>
    </div>
  );
}

UnselectedBigData.propTypes = {
  isViewOnly: PropTypes.bool,
  searchString: PropTypes.string,
  noDataLabel: PropTypes.string,
  disabledTooltip: PropTypes.string,
  actions: PropTypes.shape({
    onChange: PropTypes.func,
    load: PropTypes.func,
    nextPage: PropTypes.func,
  }),
  unselectedHeader: PropTypes.string,
  unselectedValues: PropTypes.arrayOf(PropTypes.shape({})),
  hasMorePages: PropTypes.bool,
  loadMoreLabel: PropTypes.string,
  t: PropTypes.func,
};

UnselectedBigData.defaultProps = {
  isViewOnly: false,
  searchString: '',
  noDataLabel: 'NO_DATA',
  actions: {
    onChange: noop,
    load: noop,
  },
  unselectedHeader: '',
  unselectedValues: [],
  disabledTooltip: '',
  hasMorePages: false,
  loadMoreLabel: 'LOAD_MORE',
  t: (str) => str,
};

export default withTranslation()(UnselectedBigData);
