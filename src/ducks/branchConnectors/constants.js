// this will be hooked onto the relative state tree.
import { BASE_API_PATH } from 'config';

export const REDUCER_KEY = 'branchConnectors';

export const API_NEXT_BUILD = `${BASE_API_PATH}/v1/ecbuild`;
export const API_LITE_ENDPOINT = `${BASE_API_PATH}/v1/ecgroup/summary/`;
export const API_CC_GROUP_COUNT_ENDPOINT = `${BASE_API_PATH}/v1/ecgroup/count`;
export const API_SCHEDULE_ENDPOINT = `${BASE_API_PATH}/v1/ecgroup/scheduleupgrade`;
export const API_ENDPOINT = (id) => `${BASE_API_PATH}/v1/ecgroup/${id}`;
export const API_ENCRYPTION_ENDPOINT = `${BASE_API_PATH}/v1/ecgroup`;
export const API_DELETE_ENDPOINT = (id) => `${BASE_API_PATH}/v1/ecgroup/${id}`;
export const API_DELETE_CC_ENDPOINT = (group, cc) => `${BASE_API_PATH}/v1/ecgroup/${group}/vm/${cc}`;
export const LOCATION_API_PAGE = (page) => `${BASE_API_PATH}/v1/location/lite?page=${page}`;
export const VM_HEALTH_API = (vmId) => `${BASE_API_PATH}/v1/ecVm/health/${vmId}`;
export const VM_SCHEDULED_VERSION_API = (vmId) => `${BASE_API_PATH}/v1/cloudRepo/scheduledversion/vm/${vmId}`;
export const VM_DNS_CACHE_API = (status) => `${BASE_API_PATH}/v1/ecgroup/dnsCache/${status}`;
export const VM_DNS_CACHE_ALL_API = (status, deployType) => `${BASE_API_PATH}/v1/ecgroup/dnsCache/bulkUpdate/${status}?deployType=${deployType}`;
export const VM_FAIL_OPEN_API = (status) => `${BASE_API_PATH}/v1/ecgroup/failOpen/${status}`;
export const VM_FAIL_OPEN_ALL_API = (status, deployType) => `${BASE_API_PATH}/v1/ecgroup/failOpen/bulkUpdate/${status}?deployType=${deployType}`;
export const VM_STATUS_UPDATE_API = (ecGroupId, ecVMId, status) => `${BASE_API_PATH}/v1/ecgroup/${ecGroupId}/vm/${ecVMId}/${status}`;

export const DATA_ERROR = 'error';
export const DATA_LOADING = 'loading';
export const MODAL_LOADING = 'modalLoading';
export const DATA_TABLE = 'branchConnectorsData';
export const DATA = 'data';

export const SHOW_FORM = 'showForm';
export const FORM_TYPE = 'formType';
export const EXPAND_ALL = 'expandAll';
export const CHECK_ALL = 'checkAll';
export const SEARCH_TEXT = 'searchText';
export const SORT_FIELD = 'sortField';
export const SORT_DIRECTION = 'sortDirection';
export const BULK_UPDATE = 'bulkUpdate';
export const CUSTOM_APPTYPE = 'CUSTOM';
export const APP_DATA = 'appData';
export const BRANCH_TYPE = 'branchType';
export const SHOW_DELETE_FORM = 'showDeleteForm';
export const SHOW_DISABLE_FORM = 'showDisableForm';
export const SHOW_ENABLE_FORM = 'showEnableForm';
export const DISABLE_TYPE = 'disableType';
export const ENABLE_TYPE = 'enableType';
export const SELECTED_ROW_ID = 'selectedRowID';
export const LOADED_BC_DATA = 'loadedBCData';
export const SELECTED_BC_DATA = 'selectedBCData';
export const NEXT_BUILD = 'nextBuild';
export const STATUS = 'status';
export const VM_HEALTH = 'vmHealth';
export const MORE_ITEMS_LOADING = 'moreItemsLoading';
export const HAS_NEXT_PAGE = 'hasNextPage';
export const PAGE_NUMBER = 'pageNumber';
export const PAGE_SIZE = 'pageSize';
export const PAGES = 'pages';
export const LOAD_ALL_LOCATIONS = 'ccLocations';
export const NUMBER_OF_LINES = 'numberOfLines';
export const SCHEDULED_VERSION = 'scheduledVersion';

const pageSize = 10;

export const DEFAULT_FORM_DATA = {
  name: '',
  active: true,
  type: CUSTOM_APPTYPE,
};

export const branchConnectorsColumns = (t) => [
  {
    id: 'name',
    accessor: (row) => (row.name) || '',
    Header: t('NAME'),
    width: 160,
    disableReordering: true,
    // defaultCanSort: false,
    disableSortBy: false,
    meta: {
      sortFieldName: 'name',
      skipTranslation: true,
      customCellType: 'CHECKBOX',
    },
  },
  {
    id: 'provUrlData',
    accessor: (row) => (row?.provUrlData?.cellEdgeDeploy ? 'CellEdge' : (row?.branchType || '')),
    Header: 'BRANCH_TYPE',
    width: 60,
    disableSortBy: true,
    meta: {
      skipTranslation: false,
      // customCellType: 'CLOUD_NAME',
    },
  },
  ...(window.location.pathname.includes('branch-devices/physical') ? [{
    id: 'serialNum',
    accessor: (row) => (row.serialNum) || '',
    Header: 'SERIAL_NUMBER',
    width: 100,
    disableSortBy: true,
    meta: {
      skipTranslation: false,
      // customCellType: 'CLOUD_NAME',
    },
  }] : []),
  {
    id: 'location.name',
    accessor: (row) => (row.location && row.location.name) || '',
    Header: 'LOCATION',
    width: 90,
    defaultCanSort: false,
    disableSortBy: true,
    meta: {
      sortFieldName: 'location.name',
      skipTranslation: false,
    },
  },
  {
    id: 'upgradeWindow',
    accessor: (row) => (row.upgradeWindow !== t('MULTIPLE') ? row.upgradeWindow + t(row.location ? row.location.tz : '') : row.upgradeWindow),
    Header: t('UPGRADE_WINDOW'),
    width: 90,
    defaultCanSort: false,
    disableSortBy: true,
    meta: {
      sortFieldName: 'upgradeWindow',
      skipTranslation: true,
      informationToolTip: t('TOOLTIP_UPGRADE_WINDOW'),
    },
  },
  {
    id: 'operationalStatus',
    accessor: (row) => {
      return row.operationalStatus === 0 ? '' : (row.operationalStatus || 'ENABLED');
    },
    Header: 'OPERATIONAL_STATUS',
    width: 90,
    defaultCanSort: false,
    disableSortBy: true,
    meta: {
      skipTranslation: false,
      sortFieldName: 'operationalStatus',
      customCellType: 'OPERATIONAL_STATUS',
    },
  },
  ...(window.location.pathname.includes('branch') ? [{
    id: 'haStatus',
    accessor: (row) => row.haStatus && row.haStatus.toUpperCase(),
    Header: 'HA_STATUS',
    width: 80,
    defaultCanSort: false,
    disableSortBy: true,
    meta: {
      skipTranslation: false,
      customCellType: 'HA_STATUS',
    },
    filter: 'equals',
  }] : []),
  {
    id: 'upgradeStatus',
    accessor: (row) => row.upgradeStatus || 'Scheduled',
    Header: 'UPGRADE_STATUS',
    width: 90,
    defaultCanSort: false,
    disableSortBy: true,
    meta: {
      skipTranslation: false,
      sortFieldName: 'upgradeStatus',
      customCellType: 'UPGRADE_STATUS',
    },
  },
];

const editColumn = {
  id: 'editor',
  Header: '',
  width: 90,
  disableReordering: true,
  disableResizing: true,
  disableSortBy: true,
  meta: {
    customCellType: 'ROW_ACTIONS',
    pageName: 'BRANCH_CONNECTORS',
  },
};

export const BRANCH_CONNECTORS_TABLE_CONFIGS = (t) => ({
  columns: [...(branchConnectorsColumns(t)), editColumn],
  initialState: {
    expanded: { 0: true },
    // hiddenColumns: ['haStatus'],
    pageSize,
  },
  disableTableHeaderDrag: true, // disable table column drag and drop
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: true,
  isParentChildTable: true,
});

export const RESOURCE_DEFAULTS = {
  [APP_DATA]: DEFAULT_FORM_DATA,
  [DATA_ERROR]: null, // or error object so we can process and show to user
  [DATA_LOADING]: true,
  [DATA]: {},
  [SHOW_FORM]: false,
  [DATA_TABLE]: [],
  [EXPAND_ALL]: false,
  [CHECK_ALL]: [],
  [SEARCH_TEXT]: '',
  [SORT_FIELD]: 'name',
  [SORT_DIRECTION]: '',
  [BULK_UPDATE]: false,
  [MODAL_LOADING]: false,
  [SHOW_DELETE_FORM]: false,
  [SHOW_DISABLE_FORM]: false,
  [SHOW_ENABLE_FORM]: false,
  [DISABLE_TYPE]: {},
  [ENABLE_TYPE]: {},
  [SELECTED_ROW_ID]: null,
  [LOADED_BC_DATA]: [],
  [SELECTED_BC_DATA]: {},
  [NEXT_BUILD]: null,
  [STATUS]: 'ENABLE',
  [VM_HEALTH]: {},
  [MODAL_LOADING]: false,
  [HAS_NEXT_PAGE]: false,
  [MORE_ITEMS_LOADING]: false,
  [PAGE_NUMBER]: 1,
  [PAGE_SIZE]: pageSize,
  [PAGES]: [],
  [BRANCH_TYPE]: 'VIRTUAL',
  // [LOAD_ALL_LOCATIONS]: [],
  [NUMBER_OF_LINES]: 0,
};

export const DEFAULTS = {
  ...RESOURCE_DEFAULTS,
};

// default state for this page/widget/component
export const DEFAULT_STATE = {
  ...DEFAULTS,
};
