/* eslint-disable react/jsx-handler-names */
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { PropTypes } from 'prop-types';
import { change, updateSyncErrors } from 'redux-form';
import { loader, handleSearchStringChange, perfomSearch } from 'ducks/dropdowns/vdiDeviceOsTypeEntity';
import selectors from 'ducks/dropdowns/vdiDeviceOsTypeEntity/selectors';
import AdvDropdownWithAsyncSearch from 'components/dropDown/AdvDropdownWithAsyncSearch';
// import {
//   setLocationsState,
// } from 'ducks/provisioningTemplatesBranch';

export function VdiDeviceOsEntityDropdown(props) {
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const selector = useSelector((state) => selectors(state).dropdown);
  const {
    data, hasMorePages, loading, searchString = '',
  } = selector;
  const { input, meta } = props;
  const { name, value } = input || {};
  value.name = t(value?.name);
  const { form } = meta || {};
  useEffect(() => {
    dispatch(loader(true, ''));
  }, []);

  return (
    <AdvDropdownWithAsyncSearch
      {...props}
      items={data}
      currentItems={[]}
      hasNextPage={hasMorePages}
      defaultValue={value}
      onPerformSearch={() => dispatch(perfomSearch())}
      onSearchStringChange={(search) => dispatch(handleSearchStringChange(search))}
      onClickCb={(item) => {
        dispatch(change(form, name, item));
        dispatch(updateSyncErrors(form));
      }}
      onChange={input.onChange}
      onBlur={input.onBlur}
      loading={loading}
      searchString={searchString} />
  );
}

VdiDeviceOsEntityDropdown.propTypes = {
  input: PropTypes.shape({
    onChange: PropTypes.func,
    onBlur: PropTypes.func,
  }),
  meta: PropTypes.shape({}),
};
  
VdiDeviceOsEntityDropdown.defaultProps = {
  input: {},
  meta: {},
};

export default VdiDeviceOsEntityDropdown;
