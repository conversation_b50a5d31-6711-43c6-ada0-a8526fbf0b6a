@import 'scss/colors.scss';

.ec-root-page {
.line-chart-container {
	position: relative;
	cursor: default;
	width: 100%;
	// height: 18.000em;
	// width: 2250px; height: 288px;
	.bubble {
		// height: 8em;
		width: 164px;
		box-shadow: 0 10px 24px 0 #CACBCC;
		background-color: var(--semantic-color-surface-base-primary);
		position: relative;
		border-radius: 4px;
		padding: 1em;
		text-align: left;
	  }
	
	.bubble-header {
		// height: 20px;
		width: 25px;
		color: var(--semantic-color-content-base-primary);
		font-size: 11px;
		letter-spacing: 0;
		line-height: 20px;
		span {
			white-space: nowrap;
			display: block;
		}
	}
	.data {
		height: 20px;
		width: 151px;
		color: #656666;
		font-size: 11px;
		font-weight: bold;
		letter-spacing: 0;
		line-height: 20px;
    margin-top: 1em;
	}
	.link {
		// height: 20px;
		// width: 91px;
		color: var(--semantic-color-content-interactive-primary-default);
		font-size: 11px;
		letter-spacing: 0;
		line-height: 20px;
		margin: 1em 0em 0em 0em;
	}
	.line-chart-basic-tooltip{
		padding: 12px;
	}
	// .recharts-surface {
	// 	overflow: visible;
	// }
	// svg > g.recharts-layer.recharts-cartesian-axis.recharts-yAxis.yAxis {
	// 	transform: translate(0.5rem, 0);
	// }
  }

.dot:hover { fill: #2B98F0; stroke: white; stroke-width: .2em; }
}