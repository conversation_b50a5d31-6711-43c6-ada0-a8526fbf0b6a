// @flow

import React from 'react';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faAngleUp, faAngleDown } from '@fortawesome/pro-solid-svg-icons';
import './index.scss';

function TableHeader({ title, sortable, onSortClick }) {
  return (
    <button type="button" className="header-title" onClick={onSortClick}>
      <p>
        {title}
      </p>
      {sortable && (
        <div className="sorting-icon-cont">
          <div className="sorting-arrow-up"><FontAwesomeIcon icon={faAngleUp} /></div>
          <div className="sorting-arrow-down"><FontAwesomeIcon icon={faAngleDown} /></div>
        </div>
      )}
    </button>
  );
}

TableHeader.propTypes = {
  title: PropTypes.string,
  sortable: PropTypes.bool,
  onSortClick: PropTypes.func,
};

TableHeader.defaultProps = {
  title: null,
  sortable: true,
  onSortClick: (str) => str,
};

export default TableHeader;
