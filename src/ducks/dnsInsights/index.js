import { createAction, loading } from 'ducks/generics';
import { reset, change } from 'redux-form';
import { genericInterface } from 'utils/http';
import { notifyError } from 'ducks/notification';
import { loader as loadCcAdvancedSettings } from 'ducks/cloudConfigurationAdvancedSettings';
import * as CCAdvancedSettings from 'ducks/cloudConfigurationAdvancedSettings/selectors';
import i18n from 'utils/i18n';

import {
  convertTrendInterval,
  getTrend,
  convertHexaToDecimal,
  convertStartTime,
  convertEndTime,
} from 'utils/helpers';
import {
  cloneDeep, get, isNull, orderBy, isEmpty,
} from 'utils/lodash';
import moment from 'moment-timezone';

import {
  formatNumberList,
  getFormatedNumberWithUnit,
} from 'utils/helpers/getFormatedData';

import {
  REQ_ACTION, RESOLVER, // EC_TRAFFIC_TYPE, TRAFFIC_DIRECTION
} from 'config';
import { selector as accountIdDropdown, dropdownActions as accountIdDropdownActions } from 'ducks/dropdowns/account-id';
import { selector as subscriptionIdDropdown, dropdownActions as subscriptionIdDropdownActions } from 'ducks/dropdowns/subscription-id';
import { selector as projectIdDropdown, dropdownActions as projectIdDropdownActions } from 'ducks/dropdowns/project-id';
import { selector as locationsDropdown, dropdownActions as locationsDropdownActions } from 'ducks/dropdowns/locationsInsights';
import { selector as ruleDropdown, dropdownActions as ruleDropdownActions } from 'ducks/dropdowns/req-rule';
import { selector as ecVmDropdown, dropdownActions as ecVmDropdownActions } from 'ducks/dropdowns/ec-vm';
import { selector as ecInstanceDropdown, dropdownActions as ecInstanceDropdownActions } from 'ducks/dropdowns/ec-instance';
import { selector as ecGroupDropdown, dropdownActions as ecGroupDropdownActions } from 'ducks/dropdowns/ec-group';
import { selector as ecPlatformDropdown, dropdownActions as ecPlatformDropdownActions } from 'ducks/dropdowns/platform';
import * as constants from './constants';
import actionTypes from './action-types';
import * as insightsSelector from './selectors';

export { REDUCER_KEY } from './constants';

const actionTypeExists = Object.keys(actionTypes).reduce(
  (hash, key) => ({
    ...hash,
    [actionTypes[key]]: true,
  }),
  {},
);

export const reducer = (state = constants.DEFAULT_STATE, action = {}) => {
  if (actionTypeExists[action.type]) {
    return {
      ...state,
      ...action.payload,
    };
  }
  return state;
};
export default reducer;

const defaultTimeframe = {
  id: 'current_day',
  value: 'current_day',
  label: 'CURRENT_DAY',
  startTime: moment.unix((convertStartTime('current_day') / 1000)).unix() * 1000,
  endTime: moment.unix((convertEndTime('current_day') / 1000)).unix() * 1000,
};
const boundLoading = loading(actionTypes.DATA_LOADING);
const boundDataLoadSuccess = createAction(actionTypes.DATA_LOAD_SUCCESS);
const dataChanged = createAction(actionTypes.DATA_CHANGED);

const exportSessionJob = genericInterface(
  constants.EXPORT_DNS_JOB_API_ENDPOINT,
);

/**
 * Insights Data
 * Parent: Analytics > Insights
 * @param {*} payload
 */
const getInsightsTrendData = (payload, sessionHistory) => (
  dispatch,
  getState,
) => {
  const {
    dataClass,
    dataType,
    units,
    startTime,
    endTime,
    trendInterval,
    includeTrend,
  } = payload;
  const { chartType, history } = sessionHistory;
  const insightsDataApi = genericInterface(constants.INSIGHTS_API_ENDPOINT);

  insightsDataApi
    .create(payload, {})
    .then((response) => {
      const { data } = response;

      data.forEach((detail) => {
        const { entries = [] } = detail;

        const reportData = {};
        const trendData = {};
        const totalTrendData = [];
        const totalReportData = [];

        const noOfEntries = entries.length;

        entries.forEach((entry) => {
          let { name } = entry || {};
          const { trend, total } = entry || {};
          name = i18n.t(name);

          if (name) {
            if (!reportData[name]) {
              reportData[name] = {};
            }

            if (trend && !trendData[name]) {
              trendData[name] = [];
            }

            reportData[name] = entry;

            if (trend) {
              getTrend(trend, trendData[name], name);
              getTrend(trend, totalTrendData, 'TOTAL');
            }

            // for Pie
            reportData[name].id = name;
            reportData[name].name = name;
            reportData[name].label = name;
            reportData[name].value = total;

            getFormatedNumberWithUnit(reportData[name], {
              dataKey: 'total',
              type: units === 'BYTES' ? 'BYTES' : 'NUMBER',
            });

            totalReportData.push(reportData[name]);
          }
        });
        
        formatNumberList(totalTrendData, {
          dataKey: 'TOTAL',
          type: units === 'BYTES' ? 'BYTES' : 'NUMBER',
        });
        
        const unitToConvert = totalTrendData[0] ? totalTrendData[0].unit : '';
        
        const trendDataKeys = Object.keys(trendData);
        
        if (unitToConvert) {
          trendDataKeys.forEach((keyName) => {
            formatNumberList(trendData[keyName], {
              dataKey: keyName,
              type: units === 'BYTES' ? 'BYTES' : 'NUMBER',
              unit: unitToConvert,
            });
          });
        }
        
        if (noOfEntries === 1 && trendDataKeys.indexOf('TOTAL') !== -1) {
          trendData.TOTAL = totalTrendData;
        }
        
        // eslint-disable-next-line no-param-reassign
        sessionHistory.reportData = totalReportData;
        // eslint-disable-next-line no-param-reassign
        sessionHistory.trendData = trendData;

        const currentState = getState();
        /* SHOW_FILTERS */
        const showFilters = insightsSelector.showFiltersSelector(currentState);
        /* Filters Form Values */
        const formFiltersValues = insightsSelector.filtersFormValues(
          currentState,
        );
        /* currentFilters */
        const currentFilters = insightsSelector.addFilterCurrentDropdownSelector(
          currentState,
        );

        const card = {
          chart: chartType,
          current: true,
          dataClass,
          dataType,
          units,
          startTime,
          endTime,
          trendInterval,
          includeTrend,
          showFilters,
          formFiltersValues,
          currentFilters,
          sessionHistory,
        };
        const historyClean = history.map((item) => ({ ...item, current: false }));
        historyClean.push(card); // this is not required for the very first time
        const chosenCardId = historyClean.length - 1;

        return dispatch(
          dataChanged({
            loading: false,
            error: null,
            [constants.CARD_ID]: chosenCardId,
            [constants.REPORT_DATA]: totalReportData,
            [constants.TREND_DATA]: trendData,
            [constants.HISTORY]: historyClean,
          }),
        );
      });
    })
    .catch((error) => {
      const errorMsg = get(
        error,
        'response.data.message',
        'Connection to Central Authority is lost',
      );

      dispatch(notifyError(errorMsg));
      dispatch(dataChanged({ loading: false }));
    });
};

export const loader = () => (dispatch, getState) => {
  dispatch(
    boundLoading({
      [constants.HISTORY]: [],
      ...constants.DEFAULTS,
      loading: true,
    }),
  );
  dispatch(reset('dnsInsightsFiltersForm'));
  // Prev Day;
  const currentState = getState();
  const sessionHistory = insightsSelector.baseSelector(currentState);

  sessionHistory.history = [];
  // Default payload -> LINE Chart
  const payload = {};
  payload.startTime = defaultTimeframe.startTime;
  payload.endTime = defaultTimeframe.endTime;
  payload.units = 'TRANSACTIONS';
  payload.dataClass = 'EC_DNS';
  payload.dataType = 'NO_GROUPING';
  payload.trendInterval = 'HOUR';
  payload.includeTrend = true;

  return dispatch(getInsightsTrendData(payload, sessionHistory));

  // To Debug Locally
  // return dispatch(
  //   getMockDataLite(payload, sessionHistory),
  // );

  // return dispatch(
  //   getInsightsGroupData(payload, sessionHistory),
  // );
};

export const exportTxnPoll = () => {
  return exportSessionJob
    .read()
    .then((response) => {
      const { data } = response;
      if (data.status === 'EXECUTING') {
        return exportTxnPoll();
      }
      if (data.status === 'COMPLETE') {
        return true;
      }
      return false;
    })
    .catch((error) => error);
};

export const updateMenu = (columns) => (dispatch) => {
  dispatch(
    dataChanged({
      [constants.COLUMNS]: columns,
    }),
  );
};

export const toggleAccordion = () => (dispatch, getState) => {
  const currentState = getState();
  const toggle = insightsSelector.accordionSelector(currentState);

  dispatch(
    boundDataLoadSuccess({
      [constants.SHOW_ACCORDION]: !toggle,
    }),
  );
};

export const togglePrintPreview = (toggler) => (dispatch) => {
  // const currentState = getState();
  // const toggle = insightsSelector.accordionSelector(currentState);

  dispatch(
    dataChanged({
      [constants.SHOW_PREVIEW]: toggler,
    }),
  );
};

export const getPreselectedFilters = (initialValues) => (dispatch) => {
  const { genericFilters } = initialValues;
  let units = 'TRANSACTIONS';
  let chartType = 'line';

  if (genericFilters) {
    units = genericFilters.units || 'TRANSACTIONS';
    chartType = genericFilters.chartType || 'line';
  }

  dispatch(
    dataChanged({
      [constants.UNITS]: units,
      [constants.CHART_TYPE]: chartType,
    }),
  );
};

export const handleStartOver = () => (dispatch) => {
  dispatch(reset('dnsInsightsFiltersForm'));
  dispatch(
    dataChanged({
      ...constants.RESOURCE_DEFAULTS,
      
    }),
  );

  const payload = {};
  payload.startTime = defaultTimeframe.startTime;
  payload.endTime = defaultTimeframe.endTime;
  payload.units = 'TRANSACTIONS';
  payload.dataClass = 'EC_DNS';
  payload.dataType = 'NO_GROUPING';
  payload.trendInterval = 'HOUR';
  payload.includeTrend = true;

  dispatch(getInsightsTrendData(payload, constants.RESOURCE_DEFAULTS));
};

export const handleClearFilters = () => (dispatch) => {
  dispatch(reset('dnsInsightsFiltersForm'));
  dispatch(
    change('dnsInsightsFiltersForm', 'timeFrame', defaultTimeframe),
  );
  const orderdFilters = orderBy(constants.dummyTestfilters.map((x) => ({ ...x, labelTralslation: i18n.t(x.label) })), ['labelTralslation']);

  dispatch(
    boundLoading({
      [constants.FILTERS]: orderdFilters,
      [constants.CURRENT_FILTERS]: orderdFilters,
      [constants.TIMEFRAME]: defaultTimeframe,
      [constants.SHOW_FILTERS]: constants.defaultFilters,
    }),
  );
};

export const handleRemoveFilter = (optedFilter) => (dispatch, getState) => {
  const currentState = getState();
  const currentShowFilters = insightsSelector.showFiltersSelector(currentState);
  const filtersValues = insightsSelector.filtersFormValues(
    currentState,
  );

  delete filtersValues[optedFilter]; // Form element

  const showSelectedFilters = { ...currentShowFilters };
  showSelectedFilters[optedFilter] = false;

  const filters = constants.dummyTestfilters;
  const currentFilters = insightsSelector.addFilterCurrentDropdownSelector(
    currentState,
  );

  const updateFilters = filters.filter((item) => item.value === optedFilter);
  currentFilters.unshift(...updateFilters);
  const orderdFilters = orderBy(currentFilters.map((x) => ({ ...x, labelTralslation: i18n.t(x.label) })), ['labelTralslation']);

  dispatch(
    dataChanged({
      [constants.SHOW_FILTERS]: showSelectedFilters,
      [constants.FILTERS]: orderdFilters,
      [constants.CURRENT_FILTERS]: orderdFilters,
    }),
  );
};

// Trend Tooltip > 'Click more info' Filter
export const drilldownSessionLogs = (filter) => async (dispatch, getState) => {
  await dispatch(dataChanged({ loading: true }));
  const currentState = getState();
  const chosenDataType = insightsSelector.dataTypeSelector(currentState);
  const currentShowFilters = insightsSelector.showFiltersSelector(currentState);
  if (chosenDataType === 'NO_GROUPING') return;

  const newFilter = constants.dummyTestfilters.find((x) => x.label === chosenDataType).value;

  // ACTION
  if (chosenDataType === 'EC_SUBSCRIPTION_ID') {
    await dispatch(change('dnsInsightsFiltersForm', 'subscriptionIdType', 'INCLUDE'));
    await dispatch(subscriptionIdDropdownActions.load());
    const { dropdown: { cachedData } } = await subscriptionIdDropdown(getState()) || {};
    await dispatch(change('dnsInsightsFiltersForm', newFilter, cachedData.filter((x) => x.name === filter)));
  }
  if (chosenDataType === 'EC_ACC_ID') {
    await dispatch(change('dnsInsightsFiltersForm', 'accountIdType', 'INCLUDE'));
    await dispatch(accountIdDropdownActions.load());
    const { dropdown: { cachedData } } = await accountIdDropdown(getState()) || {};
    await dispatch(change('dnsInsightsFiltersForm', newFilter, cachedData.filter((x) => x.name === filter)));
  }
  if (chosenDataType === 'EC_PROJECT_ID') {
    await dispatch(change('dnsInsightsFiltersForm', 'projectIdType', 'INCLUDE'));
    await dispatch(projectIdDropdownActions.load());
    const { dropdown: { cachedData } } = await projectIdDropdown(getState()) || {};
    await dispatch(change('dnsInsightsFiltersForm', newFilter, cachedData.filter((x) => x.name === filter)));
  }
  if (chosenDataType === 'DNS_REQ_RESP_ACTION') {
    const filterCode = REQ_ACTION.find((x) => i18n.t(x.name) === filter);
    await dispatch(change('dnsInsightsFiltersForm', newFilter, filterCode.id));
  }
  if (chosenDataType === 'DNS_RESOLVER') {
    const filterCode = RESOLVER.find((x) => i18n.t(x.name) === filter);
    await dispatch(change('dnsInsightsFiltersForm', newFilter, filterCode.id));
  }
  if (chosenDataType === 'DNS_RULE') {
    await dispatch(ruleDropdownActions.load());
    const { dropdown: { cachedData } } = await ruleDropdown(getState()) || {};
    await dispatch(change('dnsInsightsFiltersForm', newFilter, cachedData.filter((x) => x.name === filter)));
  }
  if (chosenDataType === 'EC_VM') {
    await dispatch(change('dnsInsightsFiltersForm', 'ecVmType', 'INCLUDE'));
    await dispatch(ecVmDropdownActions.load());
    const { dropdown: { cachedData } } = await ecVmDropdown(getState()) || {};
    await dispatch(change('dnsInsightsFiltersForm', newFilter, cachedData.filter((x) => x.name === filter)));
  }
  if (chosenDataType === 'EC_INSTANCE') {
    await dispatch(change('dnsInsightsFiltersForm', 'ecInstanceType', 'INCLUDE'));
    await dispatch(ecInstanceDropdownActions.load());
    const { dropdown: { cachedData } } = await ecInstanceDropdown(getState()) || {};
    await dispatch(change('dnsInsightsFiltersForm', newFilter, cachedData.filter((x) => x.name === filter)));
  }
  if (chosenDataType === 'EC_GROUP') {
    await dispatch(change('dnsInsightsFiltersForm', 'ecGroupType', 'INCLUDE'));
    await dispatch(ecGroupDropdownActions.load());
    const { dropdown: { cachedData } } = await ecGroupDropdown(getState()) || {};
    await dispatch(change('dnsInsightsFiltersForm', newFilter, cachedData.filter((x) => x.name === filter)));
  }
  if (chosenDataType === 'EC_PLATFORM') {
    await dispatch(change('dnsInsightsFiltersForm', 'platformType', 'INCLUDE'));
    await dispatch(ecPlatformDropdownActions.load());
    const { dropdown: { cachedData } } = await ecPlatformDropdown(getState()) || {};
    const seletedPlatform = cachedData.filter((x) => i18n.t(x.name) === filter);
    await dispatch(change('dnsInsightsFiltersForm', newFilter, seletedPlatform));
  }
  if (chosenDataType === 'LOCATION') {
    await dispatch(change('dnsInsightsFiltersForm', 'locationType', 'INCLUDE'));
    await dispatch(locationsDropdownActions.load());
    const { dropdown: { cachedData } } = await locationsDropdown(getState()) || {};
    await dispatch(change('dnsInsightsFiltersForm', newFilter, cachedData.filter((x) => x.name === filter)));
  }

  const showSelectedFilters = { ...currentShowFilters };
  showSelectedFilters[newFilter] = true;
  const filters = insightsSelector.addFilterDropdownSelector(currentState);
  const updateFilters = filters.filter(
    (item) => item.value !== newFilter,
  );
  const orderdFilters = orderBy(updateFilters.map((x) => ({ ...x, labelTralslation: i18n.t(x.label) })), ['labelTralslation']);

  await dispatch(
    dataChanged({
      [constants.FILTERS]: orderdFilters,
      [constants.CURRENT_FILTERS]: orderdFilters,
      [constants.SHOW_FILTERS]: showSelectedFilters,
      loading: false,
    }),
  );
};

export const getFilters = () => async (dispatch, getState) => {
  const currentState = getState();
  let accountIdEnabled = CCAdvancedSettings.accountId(currentState);
  let subIdEnabled = CCAdvancedSettings.subscriptionId(currentState);
  let projectIdEnabled = CCAdvancedSettings.projectId(currentState);

  if (isNull(accountIdEnabled) || isNull(subIdEnabled) || isNull(projectIdEnabled)) {
    await dispatch(loadCcAdvancedSettings());
    const newState = getState();
    accountIdEnabled = CCAdvancedSettings.accountId(newState);
    subIdEnabled = CCAdvancedSettings.subscriptionId(newState);
    projectIdEnabled = CCAdvancedSettings.projectId(newState);
  }
  const filters = constants.dummyTestfilters
    .filter((x) => !(x.value === 'accountId' && !accountIdEnabled))
    .filter((x) => !(x.value === 'subscriptionId' && !subIdEnabled))
    .filter((x) => !(x.value === 'projectId' && !projectIdEnabled));
  const orderdFilters = orderBy(filters.map((x) => ({ ...x, labelTralslation: i18n.t(x.label) })), ['labelTralslation']);

  dispatch(
    boundDataLoadSuccess({
      [constants.FILTERS]: orderdFilters,
      [constants.CURRENT_FILTERS]: orderdFilters,
    }),
  );
};

export const setSearchString = (searchText) => (dispatch, getState) => {
  const currentState = getState();
  const filters = insightsSelector.addFilterDropdownSelector(currentState);

  const searchedFilters = [];
  filters.forEach((item) => {
    if (i18n.t(item.label).toLowerCase().includes(searchText.toLowerCase())) {
      searchedFilters.push(item);
    }
  });
  const orderdFilters = orderBy(searchedFilters.map((x) => ({ ...x, labelTralslation: i18n.t(x.label) })), ['labelTralslation']);

  dispatch(
    boundDataLoadSuccess({
      [constants.CURRENT_FILTERS]: orderdFilters,
    }),
  );
};

export const handleSelectedAddFilter = (selectedFilter) => (
  dispatch,
  getState,
) => {
  const currentState = getState();
  const currentShowFilters = insightsSelector.showFiltersSelector(currentState);

  const showSelectedFilters = { ...currentShowFilters };
  showSelectedFilters[selectedFilter.value] = true;

  const filters = insightsSelector.addFilterDropdownSelector(currentState);
  const updateFilters = filters.filter(
    (item) => item.value !== selectedFilter.value,
  );
  const orderdFilters = orderBy(updateFilters.map((x) => ({ ...x, labelTralslation: i18n.t(x.label) })), ['labelTralslation']);

  dispatch(
    dataChanged({
      [constants.SHOW_FILTERS]: showSelectedFilters,
      [constants.FILTERS]: orderdFilters,
      [constants.CURRENT_FILTERS]: orderdFilters,
    }),
  );
};

export const getDataTypeFilters = () => async (dispatch, getState) => {
  const currentState = getState();
  let accountIdEnabled = CCAdvancedSettings.accountId(currentState);
  let subIdEnabled = CCAdvancedSettings.subscriptionId(currentState);
  let projectIdEnabled = CCAdvancedSettings.projectId(currentState);

  if (isNull(accountIdEnabled) || isNull(subIdEnabled) || isNull(projectIdEnabled)) {
    await dispatch(loadCcAdvancedSettings());
    const newState = getState();
    accountIdEnabled = CCAdvancedSettings.accountId(newState);
    subIdEnabled = CCAdvancedSettings.subscriptionId(newState);
    projectIdEnabled = CCAdvancedSettings.projectId(newState);
  }
  const updatedFilterType = constants.allDataTypeFilters
    .filter((x) => !(x.value === 'EC_ACC_ID' && !accountIdEnabled))
    .filter((x) => !(x.value === 'EC_SUBSCRIPTION_ID' && !subIdEnabled))
    .filter((x) => !(x.value === 'EC_PROJECT_ID' && !projectIdEnabled));
  const orderdFilters = orderBy(updatedFilterType.map((x) => ({ ...x, labelTralslation: i18n.t(x.label) })), ['labelTralslation']);

  dispatch(
    boundDataLoadSuccess({
      [constants.DATA_TYPE_FILTERS]: orderdFilters,
      [constants.CURRENT_DATA_TYPE_FILTERS]: orderdFilters,
    }),
  );
};

export const setDataTypeSearchString = (searchText) => (dispatch, getState) => {
  const currentState = getState();
  const filters = insightsSelector.dataTypeFiltersDropdownSelector(
    currentState,
  );

  const searchedFilters = [];
  filters.forEach((item) => {
    if (i18n.t(item.label).toLowerCase().includes(searchText.toLowerCase())) {
      searchedFilters.push(item);
    }
  });

  dispatch(
    boundDataLoadSuccess({
      [constants.CURRENT_DATA_TYPE_FILTERS]: searchedFilters,
    }),
  );
};

// eslint-disable-next-line max-len
export const handleUnits = (t, chosenUnit) => (
  dispatch,
  getState,
) => {
  dispatch(boundLoading({ [constants.UNITS]: chosenUnit, loading: true }));

  const currentState = getState();
  const history = insightsSelector.historySelector(currentState);
  const cardId = insightsSelector.cardIdSelector(currentState);
  const dataType = 'NO_GROUPING';

  const chosenChart = history[cardId].chart;
  history[cardId].current = false;

  const filtersValues = insightsSelector.filtersFormValues(
    currentState,
  );
  const sessionHistory = insightsSelector.baseSelector(currentState);
  const chosenDataType = insightsSelector.dataTypeSelector(currentState);

  // payload
  const payload = {};
  payload.startTime = filtersValues.timeFrame.startTime;
  payload.endTime = filtersValues.timeFrame.endTime;
  payload.units = chosenUnit;
  payload.dataClass = 'EC_DNS';
  payload.dataType = chosenDataType || dataType;

  if (chosenChart === 'line') {
    payload.trendInterval = convertTrendInterval(payload.startTime, payload.endTime);
    payload.includeTrend = true;
  }

  return dispatch(getInsightsTrendData(payload, sessionHistory));
};

export const restoreHistory = (idx) => (dispatch, getState) => {
  const currentState = getState();
  let history = insightsSelector.historySelector(currentState);
  dispatch(boundLoading({ loading: true }));

  let chosenCard = {};

  if (history[idx].current === true) {
    // if selected card is Active
    // Do nothing
    return dispatch(dataChanged({ loading: false }));
  }
  // Turn off current card from Active
  history = history.map((item) => ({ ...item, current: false }));

  chosenCard = history[idx];
  const { sessionHistory } = chosenCard;
  // Turn on
  history[idx].current = true;
  const chosenFormFields = chosenCard.formFiltersValues;
  const datatypeVals = constants.allDataTypeFilters.filter(
    (i) => i.value === chosenCard.dataType,
  );

  Object.entries(chosenFormFields).forEach(async ([key, value]) => {
    await dispatch(change(
      'dnsInsightsFiltersForm',
      key,
      value,
    ));
  });

  // align filters and form
  return dispatch(
    dataChanged({
      [constants.HISTORY]: history,
      [constants.CARD_ID]: idx,
      [constants.SHOW_FILTERS]: chosenCard.showFilters,
      [constants.FILTERS]: chosenCard.currentFilters,
      [constants.CURRENT_FILTERS]: chosenCard.currentFilters,
      // [constants.DATA_TYPE]: sesssionHistory.dataType,
      [constants.UNITS]: chosenCard.units,
      [constants.CHART_TYPE]: chosenCard.chart,
      [constants.DATA_TYPE]: chosenCard.dataType,
      // [constants.DATA_TYPE_FILTERS]: sesssionHistory.defaultDataTypeFilters,
      // [constants.CURRENT_DATA_TYPE_FILTERS]: sesssionHistory.currentDataTypeFilters,
      [constants.DEFUALT_DATA_TYPE_VALUE]: datatypeVals[0],
      [constants.REPORT_DATA]: sessionHistory.reportData,
      [constants.TREND_DATA]: sessionHistory.trendData,
      loading: false,
    }),
  );
};

export const removeHistoryCard = (idx) => (dispatch, getState) => {
  dispatch(boundLoading({ loading: true }));

  const currentState = getState();
  const history = cloneDeep(insightsSelector.historySelector(currentState));
  if (history.length === 1) {
    return dispatch(dataChanged({ loading: false }));
  }
  const cardId = insightsSelector.cardIdSelector(currentState);

  let chosenCard = {};
  let chosenCardId = 0;

  /* Active is selected for DELETION */
  if (history[idx].current === true) {
    history.forEach((e, id) => {
      if (id === idx && history[id + 1]) {
        // if Right Node exist
        history[id + 1].current = true;
        chosenCard = { ...history[id + 1] };
        // chosenCard = history[id + 1];
        chosenCardId = id + 1;
      } else if (id === idx && history.length > 1) {
        // if Left Node exist and total cards are more than One
        history[id - 1].current = true;
        chosenCard = { ...history[id - 1] };
        // chosenCard = history[id - 1];
        chosenCardId = id - 1;
      }
    });

    if (history.length > 1) {
      // Remove Card from History iff total cards are more than ONE
      history.splice(idx, 1);
    }

    const { sessionHistory } = chosenCard;
    const datatypeVals = constants.allDataTypeFilters.filter(
      (i) => i.value === chosenCard.dataType,
    );

    // align filters and form
    return dispatch(
      dataChanged({
        [constants.HISTORY]: history,
        [constants.CARD_ID]: chosenCardId,
        [constants.SHOW_FILTERS]: chosenCard.showFilters,
        [constants.FILTERS]: chosenCard.currentFilters,
        [constants.UNITS]: chosenCard.units,
        [constants.CHART_TYPE]: chosenCard.chart,
        [constants.DATA_TYPE]: chosenCard.dataType,
        [constants.DEFUALT_DATA_TYPE_VALUE]: datatypeVals[0],
        [constants.CURRENT_FILTERS]: chosenCard.currentFilters,
        [constants.REPORT_DATA]: sessionHistory.reportData,
        [constants.TREND_DATA]: sessionHistory.trendData,
        loading: false,
      }),
    );
  }

  /* NOT an Active card && is less than Current Card ID */
  if (history.length > 1 && idx < cardId) {
    let makeCurrentCard = {};
    let makeCardId = 0;

    makeCurrentCard = { ...history[cardId] };
    // makeCurrentCard = history[cardId];

    // Remove Card from History iff total cards are more than ONE
    history.splice(idx, 1);
    // Adjust current card Id since we have updated the Array
    makeCardId = cardId - 1;

    return dispatch(
      dataChanged({
        [constants.HISTORY]: history,
        [constants.CARD_ID]: makeCardId,
        [constants.SHOW_FILTERS]: makeCurrentCard.showFilters,
        [constants.FILTERS]: makeCurrentCard.currentFilters,
        [constants.CURRENT_FILTERS]: makeCurrentCard.currentFilters,
        loading: false,
      }),
    );
  }

  // Remove Card from History iff total cards are more than ONE
  history.splice(idx, 1);

  return dispatch(
    dataChanged({
      loading: false,
      [constants.HISTORY]: history,
    }),
  );
};

// eslint-disable-next-line max-len
export const applyDataType = (t, chosenChart) => (
  dispatch,
  getState,
) => {
  dispatch(dataChanged({ loading: true }));
  const currentState = getState();

  dispatch(
    boundLoading({ [constants.CHART_TYPE]: chosenChart, loading: true }),
  );
  const dataType = 'NO_GROUPING';
  const units = 'TRANSACTIONS';

  const filtersValues = insightsSelector.filtersFormValues(
    currentState,
  );
  const sessionHistory = insightsSelector.baseSelector(currentState);
  const chosenDataType = insightsSelector.dataTypeSelector(currentState);

  // payload
  const payload = {};
  payload.startTime = filtersValues.timeFrame.startTime;
  payload.endTime = filtersValues.timeFrame.endTime;
  payload.units = filtersValues.units || units;
  payload.dataClass = 'EC_DNS';
  payload.dataType = chosenDataType || dataType;

  if (chosenChart === 'line') {
    payload.trendInterval = convertTrendInterval(payload.startTime, payload.endTime);
    payload.includeTrend = true;
  }

  return dispatch(getInsightsTrendData(payload, sessionHistory));
};

// eslint-disable-next-line max-len
export const applyFilters = () => (dispatch, getState) => {
  dispatch(boundLoading({ loading: true }));
  const currentState = getState();
  const filtersValues = insightsSelector.filtersFormValues(
    currentState,
  );
  const chosenDataType = insightsSelector.dataTypeSelector(currentState);
  const sessionHistory = insightsSelector.baseSelector(currentState);

  const { chartType } = sessionHistory;

  if (filtersValues.accountId && filtersValues.subscriptionId) {
    dispatch(notifyError(i18n.t('ERROR_ACCOUNT_ID_AND_SUBSCRIPTION_ID_NOT_ALLOWED_TOGETHER')));
    dispatch(
      dataChanged({
        loading: false,
        error: null,
      }),
    );
    return null;
  }
  if (filtersValues.projectId && filtersValues.subscriptionId) {
    dispatch(notifyError(i18n.t('ERROR_PROJECT_ID_AND_SUBSCRIPTION_ID_NOT_ALLOWED_TOGETHER')));
    dispatch(
      dataChanged({
        loading: false,
        error: null,
      }),
    );
    return null;
  }
  if (filtersValues.accountId && filtersValues.projectId) {
    dispatch(notifyError(i18n.t('ERROR_ACCOUNT_ID_AND_PROJECT_ID_NOT_ALLOWED_TOGETHER')));
    dispatch(
      dataChanged({
        loading: false,
        error: null,
      }),
    );
    return null;
  }

  // payload
  const payload = {};
  payload.startTime = filtersValues.timeFrame.startTime;
  payload.endTime = filtersValues.timeFrame.endTime;
  payload.units = filtersValues.units;
  payload.dataClass = 'EC_DNS';
  payload.dataType = chosenDataType;

  if (chartType === 'line') {
    payload.trendInterval = convertTrendInterval(payload.startTime, payload.endTime);
    payload.includeTrend = true;
  }

  // dnsRuleName
  if (filtersValues.dnsRuleName) {
    const dnsRuleName = filtersValues.dnsRuleName.map((item) => item.name);
    payload.ruleNames = dnsRuleName;
  }

  // DNS Response Type(same as ZIA) YET TO DO
  if (filtersValues.requestAction) {
    payload.dnsReqRespAction = filtersValues.requestAction;
  }
  // ecInstance
  if (filtersValues.ecInstance) {
    const ecInstanceIds = filtersValues.ecInstance.map(
      (item) => item.id,
    );
    payload.ecIdFilters = {
      type: filtersValues.ecInstanceType,
      values: ecInstanceIds,
    };
  }

  // ecVm
  if (filtersValues.ecVm) {
    const ecVms = filtersValues.ecVm.map((item) => item.id);
    // payload.dataClass = 'EC_OFW';
    payload.ecVmFilters = {
      type: filtersValues.ecVmType,
      values: ecVms,
    };
  }

  // ecGroup
  if (filtersValues.ecGroup) {
    const ecGroups = filtersValues.ecGroup.map((item) => item.id);
    payload.ecGroupFilters = {
      type: filtersValues.ecGroupType,
      values: ecGroups,
    };
  }

  // platform
  if (filtersValues.platform) {
    // eslint-disable-next-line max-len
    const ecPlatformFilters = filtersValues.platform.map((item) => convertHexaToDecimal(item.id));
    payload.ecPlatformFilters = {
      type: filtersValues.platformType,
      values: ecPlatformFilters,
    };
  }

  // awsRegion
  if (filtersValues.awsRegion) {
    const awsRegion = filtersValues.awsRegion.map((item) => item.id);
    payload.awsRegionFilters = {
      type: filtersValues.awsRegionType,
      values: awsRegion,
    };
  }

  // availabilityZone
  if (filtersValues.availabilityZone) {
    // eslint-disable-next-line max-len
    const availabilityZones = filtersValues.availabilityZone.map(
      (item) => convertHexaToDecimal(item.id),
    );
    payload.awsIdFilters = {
      type: filtersValues.availabilityZoneType,
      values: availabilityZones,
    };
  }

  // azureRegion
  if (filtersValues.azureRegion) {
    const azureRegion = filtersValues.azureRegion.map((item) => item.id);
    payload.azureRegionFilters = {
      type: filtersValues.azureRegionType,
      values: azureRegion,
    };
  }

  // azureAvailabilityZone
  if (filtersValues.azureAvailabilityZone) {
    // eslint-disable-next-line max-len
    const azureAvailabilityZone = filtersValues.azureAvailabilityZone.map(
      (item) => convertHexaToDecimal(item.id),
    );
    payload.azureIdFilters = {
      type: filtersValues.azureAvailabilityZoneType,
      values: azureAvailabilityZone,
    };
  }

  // GCP Region
  if (filtersValues.gcpRegion) {
    const gcpRegion = filtersValues.gcpRegion.map((item) => item.id);
    payload.gcpRegionFilters = {
      type: filtersValues.gcpRegionType,
      values: gcpRegion,
    };
  }
  // gcpAvailabilityZone
  if (filtersValues.gcpAvailabilityZone) {
    // eslint-disable-next-line max-len
    const gcpAvailabilityZone = filtersValues.gcpAvailabilityZone.map(
      (item) => convertHexaToDecimal(item.id),
    );
    payload.gcpZoneFilters = {
      type: filtersValues.gcpAvailabilityZoneType,
      values: gcpAvailabilityZone,
    };
  }

  // Location
  if (filtersValues.locationName) {
    const locationIds = filtersValues.locationName.map((item) => item.id);
    payload.locationIdFilters = {
      type: filtersValues.locationType,
      values: locationIds,
    };
  }

  // Account ID
  if (filtersValues.accountId && !isEmpty(filtersValues.accountId)) {
    const accountIds = filtersValues.accountId.map((item) => item.id);
    payload.accountId = {
      type: filtersValues.accountIdType,
      values: accountIds,
    };
  }

  // Subscriptition ID
  if (filtersValues.subscriptionId && !isEmpty(filtersValues.subscriptionId)) {
    const subscriptionIds = filtersValues.subscriptionId.map((item) => item.id);
    payload.subscriptionId = {
      type: filtersValues.subscriptionIdType,
      values: subscriptionIds,
    };
  }

  // Project ID
  if (filtersValues.projectId && !isEmpty(filtersValues.projectId)) {
    const projectIds = filtersValues.projectId.map((item) => item.id);
    payload.projectId = {
      type: filtersValues.projectIdType,
      values: projectIds,
    };
  }

  // Resolver
  if (filtersValues.dnsResolver) {
    payload.dnsResolver = filtersValues.dnsResolver;
  }

  return dispatch(getInsightsTrendData(payload, sessionHistory));
};

export const handleSelectedDataTypeFilter = (chosenDataType) => async (dispatch, getState) => {
  const currentState = getState();
  let accountIdEnabled = CCAdvancedSettings.accountId(currentState);
  let subIdEnabled = CCAdvancedSettings.subscriptionId(currentState);
  let projectIdEnabled = CCAdvancedSettings.projectId(currentState);

  if (isNull(accountIdEnabled) || isNull(subIdEnabled) || isNull(projectIdEnabled)) {
    await dispatch(loadCcAdvancedSettings());
    const newState = getState();
    accountIdEnabled = CCAdvancedSettings.accountId(newState);
    subIdEnabled = CCAdvancedSettings.subscriptionId(newState);
    projectIdEnabled = CCAdvancedSettings.projectId(newState);
  }
  const updatedFilterType = constants.allDataTypeFilters
    .filter((x) => !(x.value === 'EC_ACC_ID' && !accountIdEnabled))
    .filter((x) => !(x.value === 'EC_SUBSCRIPTION_ID' && !subIdEnabled))
    .filter((x) => !(x.value === 'EC_PROJECT_ID' && !projectIdEnabled))
    .filter((x) => x.value !== chosenDataType.value);
  const orderdFilters = orderBy(updatedFilterType.map((x) => ({ ...x, labelTralslation: i18n.t(x.label) })), ['labelTralslation']);

  await dispatch(
    dataChanged({
      [constants.DATA_TYPE_FILTERS]: orderdFilters,
      [constants.CURRENT_DATA_TYPE_FILTERS]: orderdFilters,
      [constants.DEFUALT_DATA_TYPE_VALUE]: chosenDataType,
      [constants.DATA_TYPE]: chosenDataType.value,
    }),
  );

  return dispatch(applyFilters());
};

export const handleChartSelection = (chosenChart) => async (dispatch) => {
  await dispatch(
    dataChanged({
      [constants.CHART_TYPE]: chosenChart,
      loading: true,
    }),
  );

  await dispatch(applyFilters());
};
