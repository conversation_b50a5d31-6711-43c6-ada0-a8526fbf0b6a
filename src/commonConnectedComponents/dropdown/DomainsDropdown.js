/* eslint-disable react/jsx-handler-names */
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { PropTypes } from 'prop-types';
import { change, updateSyncErrors } from 'redux-form';
import { loader, handleSearchStringChange, perfomSearch } from 'ducks/dropdowns/domains';
import selectors from 'ducks/dropdowns/domains/selectors';
import AdvDropdownWithAsyncSearch from 'components/dropDown/AdvDropdownWithAsyncSearch';

export function DomainsDropdown(props) {
  const dispatch = useDispatch();
  // const { data, hasMorePages, searchString } = useSelector(state => state.domainsDropdown);
  const {
    data, hasMorePages, loading, searchString = '',
  } = useSelector((state) => selectors(state).dropdown);
  const { input, meta } = props;
  const { name, value } = input || {};
  const { form } = meta || {};

  useEffect(() => {
    dispatch(loader(true, ''));
  }, []);

  return (
    <AdvDropdownWithAsyncSearch
      {...props}
      // isSearchable
      items={data}
      currentItems={[]}
      hasNextPage={hasMorePages}
      defaultValue={value}
      onPerformSearch={() => dispatch(perfomSearch())}
      onSearchStringChange={(search) => dispatch(handleSearchStringChange(search))}
      onClickCb={(item) => {
        dispatch(change(form, name, item));
        dispatch(updateSyncErrors(form));
      }}
      onChange={input.onChange}
      onBlur={input.onBlur}
      loading={loading}
      searchString={searchString} />
  );
}

DomainsDropdown.propTypes = {
  input: PropTypes.shape({}),
  meta: PropTypes.shape({}),
};
  
DomainsDropdown.defaultProps = {
  input: {},
  meta: {},
};

export default DomainsDropdown;
