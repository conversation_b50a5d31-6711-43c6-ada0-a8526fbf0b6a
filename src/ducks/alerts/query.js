const query = (filters) => ({
  pageid: 'ALERT_EVENTS',
  widgetid: 'ALERT_EVENT_SUMMARY',
  filters,
  top: [
    {
      dimension: ['alert_severity', 'alert_rule_id', 'alert_app_id', 'alert_geoloc_count', 'alert_device_count', 'alert_id'],
      aggregate: [
        {
          metric: 'started_on', agg: 'min',
        },
        {
          metric: 'ended_on', agg: 'max',
        },
      ],
      orderby: [
        {
          metric: 'started_on', order: 'desc',
        },
        {
          metric: 'alert_severity', order: 'desc',
        },
      ],
      limit: 64,
    },
  ],
});

export default query;
