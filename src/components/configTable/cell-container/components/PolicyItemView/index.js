import React, { useState, useRef, useEffect } from 'react';
import { isEmpty } from 'utils/lodash';
import PropTypes from 'prop-types';

function PolicyItemView({
  label, value, valueObj, tooltip,
}) {
  const labelRef = useRef(null);
  const valueRef = useRef(null);
  const [isLabelEllipsisActive, setIsLabelEllipsisActive] = useState(false);
  const [isValueEllipsisActive, setIsValueEllipsisActive] = useState(false);

  useEffect(() => {
    if ((!labelRef.current) || (!valueRef.current)) return;
    
    if (labelRef.current.offsetWidth < labelRef.current.scrollWidth) {
      setIsLabelEllipsisActive(true);
    }
    if (valueRef.current.offsetWidth < valueRef.current.scrollWidth) {
      setIsValueEllipsisActive(true);
    }
  }, [labelRef, valueRef, label, value]);
  
  return (
    <div style={{ position: 'relative' }}>
      <div className="policy-table-criteria-item">
        {!isEmpty(label) && (
          <div className="policy-table-criteria-item-label">
            <span
              ref={labelRef}
              data-for="secondaryTooltip"
              data-tip={isLabelEllipsisActive ? label : ''}
              className={`policy-table-cell-text ${isLabelEllipsisActive ? 'has-elipsis' : ''}`}>
              {label}
            </span>
          </div>
        )}
        <div className="policy-table-criteria-item-data">
          <span
            ref={valueRef}
            data-for="secondaryTooltip"
            data-tip={isValueEllipsisActive ? tooltip || value : ''}
            className={`policy-table-cell-data ${isValueEllipsisActive ? 'has-elipsis' : ''}`}>
            <span className="policy-table-criteria-item-data-item">
              {valueObj || value}
            </span>
          </span>
        </div>
      </div>
    </div>
  );
}

PolicyItemView.propTypes = {
  label: PropTypes.string,
  value: PropTypes.string,
  valueObj: PropTypes.arrayOf(Object),
  tooltip: PropTypes.string,
};
  
PolicyItemView.defaultProps = {
  label: '',
  value: '',
  valueObj: null,
  tooltip: '',
};

export default PolicyItemView;
