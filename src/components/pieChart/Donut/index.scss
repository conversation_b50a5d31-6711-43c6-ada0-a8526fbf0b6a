@import 'scss/colors.scss';
@import 'scss/mixins.scss';

.ec-root-page {
.ux-donut {
  width: 100%;
  .section {
    @include DisplayFlex;
    margin-top: 18px;

    .sub-section {
      flex-grow: 1;
      margin: 0 11px 0 16px;

      .score-bar-big {
        margin-top: 7px;
      }
    }
  }
  .recharts-wrapper {
    margin-top: 13px;
  }
  .pie-inner-text {
    color: var(--semantic-color-content-base-primary);
    font-size: 13px;
    line-height: 20px;
  }
}
h2 .box-label {
  text-transform: none;
  color: var(--semantic-color-content-base-primary);
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 1px;
  line-height: 19px;
  padding: 30px;
}

.totalTxn {
    height: 40px;
    color: var(--semantic-color-content-base-primary); 
    font-size: 24px;
    font-weight: 500;
    letter-spacing: .5px;
    line-height: 45px;
    text-align: center;
}
}