@import 'scss/colors.scss';

.ec-root-page {
  .select-item-old   {
    width: 200px;
    margin-left: 46px;
    .css-dvua67-singleValue {
      color:  var(--semantic-color-content-base-primary);
    } 
    .css-1szy77t-control {
      box-shadow: none;
      outline: none;
      background: none;
      border-left: none;
      border-top: none;
      border-right: none;
      border-radius: 0;
    }
    .css-1szy77t-control:focus {
      box-shadow: none;
      outline: none;
      background: none;
    }
    .css-1pcexqc-container {
      .css-bg1rzq-control {
        border-style: hidden;
        border-bottom: 1px solid var(--semantic-color-content-interactive-primary-default);
        border-radius: unset;
        .css-151xaom-placeholder{
          color: var(--semantic-color-content-interactive-primary-default);
        }
      }
      .css-16pqwjk-indicatorContainer {
        color: var(--semantic-color-content-interactive-primary-default);
      }
      .css-bgvzuu-indicatorSeparator {
        display: none;
      }
      .css-kj6f9i-menu {
        margin: 0;
        min-height: fit-content;
        max-height: none;
        .css-11unzgr {
          padding: 0;
          min-height: fit-content;
          max-height: none;
        }
      }
    }
  }

  .drill-down-placeholder {
    svg {
      margin-right: 5px;
    }
  }
}
// react select menu list colors
.ec-select__menu {
  .ec-select__menu-list {
    background: var(--semantic-color-surface-base-secondary);
    .ec-select__option {
      color: var(--semantic-color-content-base-primary);
      background: var(--semantic-color-surface-table-row-default);
      &.ec-select__option--is-selected {
        color: var(--semantic-color-content-base-primary);
        background: var(--semantic-color-surface-interactive-secondary-active);
        &.ec-select__option--is-focused {
          background: var(--semantic-color-surface-interactive-secondary-active);
        }
      }
      &.ec-select__option--is-focused {
        background: var(--semantic-color-background-secondary);
      }
    }
  }
}
.ec-select__control {
  background: var(--semantic-color-background-primary) !important;
  border: 1px solid var(--semantic-color-border-base-primary) !important;
 &.ec-select__control--menu-is-open {
    color: var(--semantic-color-content-base-primary);
    background: var(--semantic-color-background-primary) !important;
    &.ec-select__control--is-focused {
      border: 1px solid var(--semantic-color-surface-interactive-secondary-active) !important;
      box-shadow: 0 0 0 1px var(--semantic-color-surface-interactive-secondary-active) !important;
     }
  }
  .ec-select__single-value {
    color: var(--semantic-color-content-base-primary);
  }
}


// --dropdown-bkgrd-color: var(--semantic-color-surface-base-secondary);
//   --dropdown-panel-bkgrd-color: var(--semantic-color-surface-base-primary);
//   --dropdown-text-color: var(--semantic-color-content-base-primary);
//   --dropdown-text-hover-color : var(--semantic-color-content-base-secondary);
//   --dropdown-border-color : var(--semantic-color-background-secondary);
//   --dropdown-list-item-text-color : var(--semantic-color-content-base-primary);
//   --dropdown-list-item-text-hover-color : var(--semantic-color-content-base-primary);
//   --dropdown-list-item-bkgrd-hover-color : var(--semantic-color-brand-pale03);
//   --dropdown-list-item-bkgrd-active-color : var(--semantic-color-brand-pale03);
//   --dropdown-list-item-text-active-color : var(--semantic-color-content-base-primary);
//   --dropdown-selected-bkgrd-color: var(--semantic-color-content-interactive-primary-default);

//   --dropdown-list-item-text-disabled-color : var(--semantic-color-content-interactive-primary-disabled);
//   --dropdown-list-item-bkgrd-disabled-color : var(--semantic-color-content-interactive-primary-disabled);