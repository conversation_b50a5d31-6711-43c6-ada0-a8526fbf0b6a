// this will be hooked onto the relative state tree.
import { BASE_API_PATH } from 'config';

export const REDUCER_KEY = 'appliances';

export const APPLIANCES_QUERY = `${BASE_API_PATH}/v1/applianceInfo`;

export const DATA_ERROR = 'error';
export const DATA_LOADING = 'loading';
export const MODAL_LOADING = 'modalLoading';
export const DATA_TABLE = 'appliancesData';
export const DATA = 'data';

// this will store data to create and edit form
export const APP_DATA = 'appData';
export const ADD_ROLE = 'addRole';
export const VIEW_ONLY = 'viewOnly';
export const MODAL_TITLE = 'modalTitle';
export const SHOW_FORM = 'showForm';
export const FEATURE_PERMISSIONS = 'featurePermissions';
export const CUSTOM_APPTYPE = 'CUSTOM';
export const EXPANDED_APPS = 'expandedApps';
export const COLUMNS = 'columns';
export const SHOW_ADD_FORM = 'showAddForm';
export const SHOW_DELETE_FORM = 'showDeleteForm';
export const SHOW_EDIT_FORM = 'showEditForm';
export const SHOW_VIEW_FORM = 'showViewForm';
export const SELECTED_ROW = 'selectedRow';
export const SELECTED_ROW_ID = 'selectedRowID';
export const MORE_ITEMS_LOADING = 'moreItemsLoading';
export const HAS_NEXT_PAGE = 'hasNextPage';
export const PAGE_NUMBER = 'page';
export const SEARCH_TEXT = 'search';

export const appliancesColumns = [
  {
    id: 'name',
    // accessor: 'name',
    accessor: (row) => row.name,
    Header: 'APPLIANCE_NAME',
    width: 120,
    defaultCanSort: true,
    meta: {
      skipTranslation: false,
    },
  },
  {
    id: 'serialNumber',
    accessor: (row) => row.serialNumber ?? '---',
    Header: 'SERIAL_NUMBER',
    width: 80,
    defaultCanSort: true,
    meta: {
      skipTranslation: false,
    },
  },
  {
    id: 'modelType',
    accessor: (row) => (row.modelType) || '---',
    Header: 'DEVICE_MODEL',
    width: 80,
    defaultCanSort: true,
    meta: {
      skipTranslation: false,
    },
  },
  {
    id: 'location',
    accessor: (row) => (row.locationName) || '---',
    Header: 'LOCATION',
    width: 80,
    defaultCanSort: true,
    meta: {
      skipTranslation: false,
    },
  },
  {
    id: 'provTemplate',
    accessor: (row) => (row.provTemplateName) || '---',
    Header: 'TEMPLATE',
    width: 120,
    defaultCanSort: false,
    meta: {
      skipTranslation: false,
    },
  },
  {
    id: 'status',
    accessor: (row) => (row.status) || '---',
    Header: 'STATUS',
    width: 120,
    defaultCanSort: false,
    meta: {
      skipTranslation: false,
      customCellType: 'ZT_DEVICE_STATUS',
    },
  },
];

const editColumn = {
  id: 'editor',
  Header: '',
  width: 100,
  disableReordering: true,
  disableResizing: true,
  disableSortBy: true,
  meta: {
    customCellType: 'ROW_ACTIONS',
    pageName: 'PROVISIONING_TEMPLATES',
  },
};

export const APPLIANCES_TABLE_CONFIGS = {
  columns: [...appliancesColumns, editColumn],
  // columns: ProvTemplatesColumns,
  initialState: {
    // sortBy: [{ id: 'name' }],
  },
  disableTableHeaderDrag: true, // disable table column drag and drop
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: true,
};

export const RESOURCE_DEFAULTS = {
  [DATA_ERROR]: null, // or error object so we can process and show to user
  [DATA_LOADING]: true,
  [APP_DATA]: {},
  [ADD_ROLE]: false,
  [SHOW_FORM]: false,
  [DATA]: {},
  [DATA_TABLE]: [],
  [SHOW_DELETE_FORM]: false,
  [SELECTED_ROW]: null,
  [EXPANDED_APPS]: [],
  [COLUMNS]: appliancesColumns,
  [MODAL_LOADING]: false,
  [SHOW_ADD_FORM]: false,
  [SHOW_DELETE_FORM]: false,
  [SHOW_EDIT_FORM]: false,
  [SHOW_VIEW_FORM]: false,
  [HAS_NEXT_PAGE]: false,
  [MORE_ITEMS_LOADING]: false,
  [PAGE_NUMBER]: 1,
  [SEARCH_TEXT]: null,
};

export const DEFAULTS = {
  ...RESOURCE_DEFAULTS,
};

// default state for this page/widget/component
export const DEFAULT_STATE = {
  ...DEFAULTS,
};

// For testing...to be used later
export const DUMMY_STORE_DATA = {
  [REDUCER_KEY]: {
    ...DEFAULT_STATE,
  },
};
