/* eslint-disable react/jsx-handler-names */
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { PropTypes } from 'prop-types';
import { isEmpty } from 'utils/lodash';
import { change, updateSyncErrors } from 'redux-form';
import { loader, handleSearchStringChange, perfomSearch } from 'ducks/dropdowns/aws-supported-regions-entity-dropdown';
import selectors from 'ducks/dropdowns/aws-supported-regions-entity-dropdown/selectors';
import AdvDropdownWithAsyncSearch from 'components/dropDown/AdvDropdownWithAsyncSearch';

export function AwsSupportedRegionsEntityDropdown(props) {
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const selector = useSelector((state) => selectors(state).dropdown);
  const {
    data, hasMorePages, loading, searchString = '',
  } = selector;
  const { input, meta } = props;
  const { name, value } = input || {};
  const { form } = meta || {};

  useEffect(() => {
    dispatch(loader(true, ''));
  }, []);

  return (
    <AdvDropdownWithAsyncSearch
      {...props}
      items={data.filter(
        (item) => (
          isEmpty(searchString)
          || t(item.name).toLowerCase().includes(searchString.toLowerCase())),
      )}
      currentItems={[]}
      hasNextPage={hasMorePages}
      defaultValue={value}
      onPerformSearch={() => dispatch(perfomSearch())}
      onSearchStringChange={(search) => dispatch(handleSearchStringChange(search))}
      onClickCb={(item) => {
        dispatch(change(form, name, item));
        dispatch(updateSyncErrors(form));
      }}
      onChange={input.onChange}
      onBlur={input.onBlur}
      loading={loading}
      searchString={searchString} />
  );
}

AwsSupportedRegionsEntityDropdown.propTypes = {
  input: PropTypes.shape({
    onChange: PropTypes.func,
    onBlur: PropTypes.func,
  }),
  meta: PropTypes.shape({}),
};
  
AwsSupportedRegionsEntityDropdown.defaultProps = {
  input: {},
  meta: {},
};

export default AwsSupportedRegionsEntityDropdown;
