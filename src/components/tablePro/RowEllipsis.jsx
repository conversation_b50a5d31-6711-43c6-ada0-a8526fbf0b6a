import React from 'react';
import PropTypes from 'prop-types';

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faEllipsisVertical } from '@fortawesome/pro-regular-svg-icons';

const noop = () => undefined;
// RowEdit is responsible to support EDIT feature of the respective row
function RowEllipsis(props) {
  const {
    onClickCallBack,
    isEllipsisable,
  } = props;

  if (isEllipsisable) {
    return (
      <FontAwesomeIcon
        icon={faEllipsisVertical}
        title="More Actions"
        onMouseDown={(e) => {
          onClickCallBack({ x: e.clientX, y: e.clientY });
        }}
        className="pencil-icon" />
    );
  }
  return '';
}

RowEllipsis.propTypes = {
  onClickCallBack: PropTypes.func,
  isEllipsisable: PropTypes.bool,
};

RowEllipsis.defaultProps = {
  onClickCallBack: noop,
  isEllipsisable: false,
};

export default RowEllipsis;
