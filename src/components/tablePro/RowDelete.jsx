import React from 'react';
import PropTypes from 'prop-types';
import i18n from 'utils/i18n';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTrash } from '@fortawesome/pro-regular-svg-icons';

const noop = () => undefined;
// RowDelete.jsx is responsible to support DELETE feature of the respective row
function RowDelete(props) {
  const {
    onClickCallBack,
    isDeletable,
  } = props;
  
  if (isDeletable) {
    return (
      <FontAwesomeIcon
        icon={faTrash}
        title={i18n.t('DELETE')}
        onClick={onClickCallBack}
        className="delete-icon" />
    );
  }
  return '';
}

RowDelete.propTypes = {
  onClickCallBack: PropTypes.func,
  isDeletable: PropTypes.bool,
};

RowDelete.defaultProps = {
  onClickCallBack: noop,
  isDeletable: false,
};

export default RowDelete;
