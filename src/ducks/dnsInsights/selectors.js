import { createSelector } from 'reselect';
import { getFormValues, getFormSyncErrors, getFormMeta } from 'redux-form';
import * as constants from './constants';

export const baseSelector = (state) => state[constants.REDUCER_KEY];

export const formSelector = (state) => state[constants.STATE_FORM];

export const loadingSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA_LOADING],
);

export const errorSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA_ERROR],
);

export const dataSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA] || {},
);

export const appDataSelector = createSelector(
  baseSelector,
  (state) => state[constants.APP_DATA],
);

export const accordionSelector = createSelector(
  baseSelector,
  (state) => state[constants.SHOW_ACCORDION] || false,
);

export const columnsSelector = createSelector(
  baseSelector,
  (state) => state[constants.COLUMNS] || [],
);

export const showFiltersSelector = createSelector(
  baseSelector,
  (state) => state[constants.SHOW_FILTERS] || {},
);

export const timeFrameDefaultSelector = createSelector(
  baseSelector,
  (state) => state[constants.TIMEFRAME] || { id: 'current_day', value: 'current_day', label: 'CURRENT_DAY' },
);

export const addFilterDropdownSelector = createSelector(
  baseSelector,
  (state) => state[constants.FILTERS] || [],
);

export const dataTypeFiltersDropdownSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA_TYPE_FILTERS] || [],
);

export const historySelector = createSelector(
  baseSelector,
  (state) => state[constants.HISTORY] || [],
);

export const cardIdSelector = createSelector(
  baseSelector,
  (state) => state[constants.CARD_ID] || 0,
);

export const defaultDataTypValueSelector = createSelector(
  baseSelector,
  (state) => state[constants.DEFUALT_DATA_TYPE_VALUE],
);

export const dataTypeSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA_TYPE] || 'NO_GROUPING',
);

export const currentDataTypeDropdownSelector = createSelector(
  baseSelector,
  (state) => state[constants.CURRENT_DATA_TYPE_FILTERS] || state[constants.DATA_TYPE_FILTERS] || [],
);

// NOT in USE
export const addFilterSwitchDropdownSelector = createSelector(
  baseSelector,
  (state) => {
    if (state[constants.CURRENT_FILTERS].length > 0) {
      return state[constants.CURRENT_FILTERS];
    }
    return state[constants.FILTERS] || [];
  },
);

export const addFilterCurrentDropdownSelector = createSelector(
  baseSelector,
  (state) => state[constants.CURRENT_FILTERS] || state[constants.FILTERS] || [],
);

export const locationDropdownSelector = createSelector(
  baseSelector,
  (state) => state[constants.LOCATIONS] || [],
);

export const filtersForm = createSelector(
  formSelector,
  (state) => state.dnsInsightsFiltersForm || [],
);

export const filtersFormValues = createSelector(
  filtersForm,
  (state) => state.values || {},
);

export const formGatewayPortFrom = createSelector(
  filtersFormValues,
  (state) => state.gatewayDestinationPortFrom || '',
);

export const formGatewayPortTo = createSelector(
  filtersFormValues,
  (state) => state.gatewayDestinationPortTo || '',
);

export const formEcSourcePortFrom = createSelector(
  filtersFormValues,
  (state) => state.ecSourcePortFrom || '',
);

export const formEcSourcePortTo = createSelector(
  filtersFormValues,
  (state) => state.ecSourcePortTo || '',
);

export const formServerDestinationPortFrom = createSelector(
  filtersFormValues,
  (state) => state.serverDestinationPortFrom || '',
);

export const formServerDestinationPortTo = createSelector(
  filtersFormValues,
  (state) => state.serverDestinationPortTo || '',
);

export const formServerSourcePortFrom = createSelector(
  filtersFormValues,
  (state) => state.serverSourcePortFrom || '',
);

export const formServerSourcePortTo = createSelector(
  filtersFormValues,
  (state) => state.serverSourcePortTo || '',
);

export const formClientDestinationPortFrom = createSelector(
  filtersFormValues,
  (state) => state.clientDestinationPortFrom || '',
);

export const formClientDestinationPortTo = createSelector(
  filtersFormValues,
  (state) => state.clientDestinationPortTo || '',
);

export const formClientSourcePortFrom = createSelector(
  filtersFormValues,
  (state) => state.clientSourcePortFrom || '',
);

export const formClientSourcePortTo = createSelector(
  filtersFormValues,
  (state) => state.clientSourcePortTo || '',
);
export const formValuesSelector = (state) => getFormValues('dnsInsightsFiltersForm')(state);
export const formMetaSelector = (state) => getFormMeta('dnsInsightsFiltersForm')(state);
export const formSyncErrorsSelector = (state) => getFormSyncErrors('dnsInsightsFiltersForm')(state);
// alway return top level selector
export default baseSelector;
