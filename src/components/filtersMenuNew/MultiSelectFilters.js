import PropTypes from 'prop-types';
import React from 'react';
import { objectsAreSame } from 'utils/helpers';
import RadioButton from 'components/button/RadioButtonNew';
import { withTranslation } from 'react-i18next';

import { noop, isEqual } from 'utils/lodash';

class MultiSelectFilters extends React.Component {
  constructor(props) {
    super(props);
    const {
      defaultValue, items, t, toggleList,
    } = props;
    let idSelected = [];
    let labelSelectedItems = '';
    if (defaultValue.length === 0 || defaultValue.length === items.length) {
      labelSelectedItems = t('ALL');
    } else if (defaultValue[0]) {
      idSelected = defaultValue.map((value) => value.id);
      labelSelectedItems = defaultValue.map((value) => t(value.label)).join(', ');
    }
    const listItems = items.map((item) => {
      // eslint-disable-next-line
      item.isChecked = idSelected.indexOf(item.id) !== -1;
      return item;
    });

    this.state = {
      labelSelectedItems,
      listItems,
      search: '',
      openList: false,
      toggleItems: toggleList,
      changeInput: false,
      allChecked: false,
    };
    this.wrapperRef = null;
  }

  componentDidMount() {
    document.addEventListener('mousedown', this.handleMousedown);
  }

  componentDidUpdate(prevProps) {
    const {
      reset, defaultValue, items, t,
    } = this.props;
    let listItems;
    let labelSelectedItems;
    const idSelected = defaultValue.map((value) => (value.id));
    if (!objectsAreSame(prevProps.defaultValue, defaultValue)) {
      labelSelectedItems = defaultValue.map((value) => t(value.label)).join(', ');
      if (defaultValue.length === 0) {
        labelSelectedItems = prevProps.defaultValue && prevProps.defaultValue.length
          ? t('ALL') : '';

        // uncheck the "all" box when defaultValue is cleared
        this.resetAllChecked();
      } else if (defaultValue.length === items.length) {
        labelSelectedItems = t('ALL');
      }
      listItems = items.map((item) => {
        // eslint-disable-next-line
        item.isChecked = idSelected.indexOf(item.id) !== -1;
        return item;
      });
      // eslint-disable-next-line
      this.setState({
        labelSelectedItems,
        listItems,
        search: '',
        openList: false,
      });
    }

    if (!isEqual(prevProps.items, items)) {
      listItems = items.map((item) => {
        // eslint-disable-next-line
        item.isChecked = idSelected.indexOf(item.id) !== -1;
        return item;
      });
      // eslint-disable-next-line react/no-did-update-set-state
      this.setState({
        listItems,
        openList: false,
        search: '',
        labelSelectedItems: t('ALL'),
      });
    }
    if (prevProps.reset !== reset) {
      listItems = items.map((item) => {
        // eslint-disable-next-line
        item.isChecked = false;
        return item;
      });
      labelSelectedItems = t('ALL');
      // eslint-disable-next-line
      this.setState({
        labelSelectedItems,
        listItems,
        search: '',
        openList: false,
      });
    }
  }

  componentWillUnmount() {
    document.removeEventListener('mousedown', this.handleMousedown);
  }

  resetAllChecked() {
    this.setState({
      allChecked: false,
    });
  }

  closePanel = () => {
    const { t } = this.props;
    let { listItems, labelSelectedItems } = this.state;
    labelSelectedItems = labelSelectedItems.split(', ');
    listItems = listItems.map((item) => (
      labelSelectedItems.indexOf(t(item.label)) !== -1
        ? { ...item, isChecked: true } : { ...item, isChecked: false }
    ));
    this.setState({
      listItems,
      openList: false,
    });
  };

  handleDoneAction = () => {
    const { listItems } = this.state;
    const { t, setValue } = this.props;
    const selectedListItems = listItems.filter((item) => item.isChecked === true);
    let updatedLabels = selectedListItems.map((item) => t(item.label)).join(', ');
    if (selectedListItems.length === 0) {
      updatedLabels = t('ALL');
    } else if (selectedListItems.length === listItems.length) {
      updatedLabels = t('ALL');
    }
    this.setState({ openList: false, labelSelectedItems: updatedLabels, changeInput: false });
    setValue(selectedListItems);
  };

  onToggle = (name, status) => {
    let { toggleItems } = this.state;
    const { setValue } = this.props;
    toggleItems = toggleItems.map((item) => (
      item.id === status.id ? { ...item, classNames: ['active'] } : { ...item, classNames: [''] }));
    this.setState({ toggleItems });
    setValue(status.value);
  };

  setWrapperRef = (node) => {
    this.wrapperRef = node;
  };

  handleMousedown = (e) => {
    const containRef = this.wrapperRef.contains(e.target);
    const { changeInput } = this.state;
    if (!containRef) {
      this.setState({ openList: false });
      if (changeInput) {
        this.handleDoneAction();
      }
    }
  };

  handleOpenList = () => {
    const { openList } = this.state;
    this.setState({ openList: !openList });
  };

  handleOnChangeInput = (event) => {
    this.setState({ search: event.target.value.trim() });
  };

  changeSelectedHandler = (event, option) => {
    const { checked } = event.target;
    this.setState((prevState) => {
      let { allChecked, listItems } = prevState;
      if (option === 'all') {
        allChecked = checked;
        listItems = listItems.map((item) => ({ ...item, isChecked: checked }));
      } else if (option.isParent) {
        listItems = listItems.map((item) => (
          item.parent === option.value ? { ...item, isChecked: checked } : item
        ));
      } else {
        listItems = listItems.map((item) => (
          item.id === option.id ? { ...item, isChecked: checked } : item));
        allChecked = listItems.every((item) => item.isChecked);
      }
      return { allChecked, listItems, changeInput: true };
    });
  };

  render() {
    const {
      hasSearch, t, searchProperty, hasToggle, removeFilterCallback,
      hasLocalSort, showLabelInSelectedValue, labelSelectedValue,
    } = this.props;
    const {
      allChecked, labelSelectedItems, openList, search,
    } = this.state;
    let { listItems } = this.state;
    if (hasLocalSort) {
      listItems.sort((a, b) => {
        return a.label.localeCompare(b.label);
      });
    }
    const { toggleItems } = this.state;
    if (hasSearch && search) {
      listItems = listItems.filter((item) => item[searchProperty]
        .toLowerCase().indexOf(search.toLowerCase()) !== -1);
    }
    const dropdownOptions = (
      <div className="multiselect-dropdown-list-content">
        <div className="multiselect-dropdown-list-item">
          <input
            type="checkbox"
            className="multi-select-list-item-checkbox"
            checked={allChecked}
            onChange={(evt) => this.changeSelectedHandler(evt, 'all')} />
          <span className="multi-select-list-item-value">{t('ALL')}</span>
        </div>
        {listItems.map((item) => (
          <div key={item.id} className="multiselect-dropdown-list-item">
            <input
              type="checkbox"
              className="multi-select-list-item-checkbox"
              onChange={(evt) => this.changeSelectedHandler(evt, item)}
              checked={item.isChecked} />
            <span className="multi-select-list-item-value">{t(item.label)}</span>
          </div>
        ))}
      </div>
    );
    return (
      <div>
        {hasToggle && (
          <div className="application-type-dropdown">
            <RadioButton
              name="include"
              propertyName="INCLUDE"
              list={toggleItems}
              actionCallback={(param1, param2) => { this.onToggle(param1, param2); }}
              // cclass="sr-schedule-status"
              t={t} />
          </div>
        )}
        <div className="drop-down-container" ref={this.setWrapperRef}>
          <button
            className="drop-down-selected-value"
            onClick={this.handleOpenList}
            onKeyPress={this.handleOpenList}
            type="button">
            <span className="label-selected-items">
              {showLabelInSelectedValue && (
                <span className="label-selected-value">
                  {labelSelectedValue}
                  =
                </span>
              )}
              {labelSelectedItems}
            </span>
          </button>
          {removeFilterCallback && (
            // eslint-disable-next-line jsx-a11y/control-has-associated-label
            <span
              className="far fa-times-circle remove-filter-icon"
              role="button"
              tabIndex={0}
              onClick={removeFilterCallback}
              onKeyPress={removeFilterCallback}>
            </span>
          )}
          <div className="dropdown-box">
            <div className={`drop-down-list ${openList ? 'open' : ''}`}>
              {hasSearch
              && (
                <div className="dropdown-list-header">
                  <div className="dropdown-search">
                    <input
                      className="dropdown-input-search"
                      type="text"
                      value={search}
                      placeholder="Search ..."
                      onChange={this.handleOnChangeInput} />
                    <span className="search-icon fa fa-search"></span>
                  </div>
                </div>
              )}
              {dropdownOptions}
            </div>
          </div>
        </div>
      </div>
    );
  }
}

MultiSelectFilters.propTypes = {
  hasSearch: PropTypes.bool,
  items: PropTypes.arrayOf(PropTypes.shape({
    value: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.arrayOf(PropTypes.string),
      PropTypes.shape({}),
    ]),
    label: PropTypes.string,
  })),
  hasLocalSort: PropTypes.bool,
  t: PropTypes.func,
  setValue: PropTypes.func,
  searchProperty: PropTypes.string,
  toggleList: PropTypes.arrayOf(PropTypes.shape({})),
  hasToggle: PropTypes.bool,
  defaultValue: PropTypes.arrayOf(PropTypes.shape({
    value: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.arrayOf(PropTypes.string),
      PropTypes.shape({}),
    ]),
    label: PropTypes.string,
  })),
  reset: PropTypes.bool,
  labelSelectedValue: PropTypes.string,
  showLabelInSelectedValue: PropTypes.bool,
  removeFilterCallback: PropTypes.func,
};

MultiSelectFilters.defaultProps = {
  hasSearch: false,
  items: [],
  t: (str) => str,
  searchProperty: 'label',
  setValue: noop,
  defaultValue: [],
  reset: false,
  hasLocalSort: true,
  labelSelectedValue: '',
  showLabelInSelectedValue: false,
  removeFilterCallback: noop,
};

export default withTranslation()(MultiSelectFilters);
