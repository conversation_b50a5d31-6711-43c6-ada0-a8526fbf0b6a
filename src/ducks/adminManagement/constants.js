// this will be hooked onto the relative state tree.
import { BASE_API_PATH } from 'config';
import i18n from 'utils/i18n';
import { useTranslation } from 'react-i18next';
import { isEmpty } from 'utils/lodash';

export const REDUCER_KEY = 'adminManagement';

export const ADMINMANAGEMENT_API_QUERY = `${BASE_API_PATH}/v1/adminUsers`;
export const ADMINMANAGEMENT_API_QUERY_DEFAULT_OPTS = '?page=1&pageSize=100&includeAuditorUsers=false&includeAdminUsers=true';
export const ADMINMANAGEMENT_API_ENDPOINT = `${BASE_API_PATH}/v1/adminUsers`;
export const ADMINMANAGEMENT_SAML_ENDPOINT = `${BASE_API_PATH}/v1/ecAdminSamlSettings`;
export const ADMINMANAGEMENT_SAML_ENDPOINT_DOWNLOAD = `${BASE_API_PATH}/v1/ecAdminSamlSettings/downloadXmlMetaData?`;
export const ADMINMANAGEMENT_SAML_CERT_UPLOAD_ENDPOINT = `${BASE_API_PATH}/v1/ecAdminSamlSettings/uploadCert/text`;
export const ADMINMANAGEMENT_API_PASSWORD_EXPIRE_ENDPOINT = `${BASE_API_PATH}/v1/passwordExpiry`;
export const ORG_PROV_API = `${BASE_API_PATH}/v1/orgProvisioning`;

export const DATA_ERROR = 'error';
export const DATA_LOADING = 'loading';
export const MODAL_LOADING = 'modalLoading';

export const PASSWORD_EXPIRATION = 'passwordExpiration';
export const DATA_TABLE = 'rbactabledata';
export const DATA = 'data';

// this will store data to create and edit form
export const APP_DATA = 'appData';
export const ADD_ADMIN = 'addAdmin';
export const VIEW_ONLY = 'viewOnly';
export const MODAL_TITLE = 'modalTitle';
export const SEARCH_DATA = 'searchData';
export const SHOW_FORM = 'showForm';
export const SHOW_SAML_CETIFICATE_MODAL = 'showSamlCertificateModal';
export const SAML_ENABLED = 'samlEnabled';
export const SAML_PRODUCTID = 'samlProductId';
export const SAML_CERT_FILENAME = 'samlCertFileName';
export const SAML_ISSUERS = 'samlIssuers';
export const SAML_INITIAL_ISSUERS = 'samlInitialIssuers';
export const DONWLOAD_XML = 'downlaodXml';
export const CUSTOM_APPTYPE = 'CUSTOM';
export const EXPANDED_APPS = 'expandedApps';
export const COLUMNS = 'columns';
export const SHOW_DELETE_FORM = 'showDeleteForm';
export const SHOW_PASSWORD_EXPIRE_FORM = 'showPasswordExpireForm';
export const SHOW_ADMIN_EXISTS_CONFIRM = 'showAdminExistsConfirm';
export const SELECTED_ROW_ID = 'selectedRowID';
export const SELECTED_TAB = 'selectedTab';
export const MORE_ITEMS_LOADING = 'moreItemsLoading';
export const HAS_NEXT_PAGE = 'hasNextPage';
export const PAGE_NUMBER = 'page';
export const IS_ONE_IDENTITY_ENABLED = 'isOneIdentityEnabled';
export const ONE_IDENTITY_UI_URL = 'oneIdentityUiUrl';

const translate = ({ value }) => {
  const { t } = useTranslation();

  return t(value);
};

const roleName = ({ value }) => {
  if (isEmpty(value)) return '';
  return value;
};

const isPasswordExpired = ({ value }) => {
  const { t } = useTranslation();
  return value ? t('TRUE') : t('FALSE');
};

const isPasswordLoginAllowed = ({ value, row }) => {
  const { samlEnabled } = row || {};
  const { t } = useTranslation();
  const samlText = samlEnabled ? t('SAML') : '';
  const passwordText = value ? t('PASSWORD') : '';
  return passwordText && samlText ? `${samlText}, ${passwordText}` : `${samlText}${passwordText}`;
};

const disabled = ({ value }) => {
  const { t } = useTranslation();
  return value ? t('DISABLED') : t('ENABLED');
};

const comments = ({ value }) => {
  if (isEmpty(value)) return '---';

  return value;
};

const columns = [
  {
    exportId: 'NO',
    key: 'idx',
    name: 'NO',
    data: {},
    width: 60,
    visible: true,
    frozen: false,
    resizable: false,
    sortable: false,
    draggable: false,
  }, {
    exportId: 'LOGIN_ID',
    key: 'loginName',
    name: 'LOGIN_ID',
    data: {},
    visible: true,
    resizable: false,
    frozen: false,
    sortable: true,
    draggable: true,
    checkbox: true,
  }, {
    exportId: 'NAME',
    key: 'userName',
    name: 'NAME',
    data: {},
    visible: true,
    resizable: false,
    frozen: false,
    sortable: true,
    draggable: true,
    checkbox: true,
  }, {
    exportId: 'ROLE',
    key: 'role',
    name: 'ROLE',
    formatter: roleName,
    data: {},
    visible: true,
    resizable: false,
    frozen: false,
    sortable: true,
    draggable: true,
    checkbox: true,
  }, {
    exportId: 'SCOPE',
    key: 'adminScopeType',
    name: 'SCOPE',
    formatter: translate,
    data: {},
    visible: true,
    resizable: false,
    frozen: false,
    sortable: true,
    draggable: true,
    checkbox: true,
  }, {
    exportId: 'LOGIN_TYPE',
    key: 'isPasswordLoginAllowed',
    name: 'LOGIN_TYPE',
    data: {},
    formatter: isPasswordLoginAllowed,
    visible: true,
    resizable: false,
    frozen: false,
    sortable: true,
    draggable: true,
    checkbox: true,
  }, {
    exportId: 'TYPE',
    key: 'roleType',
    name: 'TYPE',
    formatter: translate,
    data: {},
    visible: true,
    resizable: false,
    frozen: false,
    sortable: true,
    draggable: true,
    checkbox: true,
  }, {
    exportId: 'COMMENTS',
    key: 'comments',
    name: 'COMMENTS',
    data: {},
    formatter: comments,
    visible: true,
    resizable: false,
    frozen: false,
    sortable: true,
    draggable: true,
    checkbox: true,
  }, {
    exportId: 'PASSWORD_EXPIRED',
    key: 'isPasswordExpired',
    name: 'PASSWORD_EXPIRED',
    data: {},
    formatter: isPasswordExpired,
    visible: true,
    resizable: false,
    frozen: false,
    sortable: true,
    draggable: true,
    checkbox: true,
  }, {
    exportId: 'STATUS',
    key: 'disabled',
    name: 'STATUS',
    data: {},
    formatter: disabled,
    visible: true,
    resizable: false,
    frozen: false,
    sortable: true,
    draggable: true,
    checkbox: true,
  }, {
    name: '',
    key: 'isNonEditable',
    exportId: '',
    data: {},
    visible: true,
    width: 50,
  },
];

export const adminManagementColumns = [
  {
    id: 'dataIdx',
    accessor: (row, i) => i + 1,
    Header: 'NUMBER_ABBR',
    width: 50,
    disableReordering: true,
    disableResizing: true,
    disableSortBy: true,
    meta: {
      skipTranslation: false,
      customCellType: 'RENDER_INDEX',
    },
  },
  {
    id: 'loginName',
    accessor: (row) => row.loginName,
    Header: 'LOGIN_ID',
    width: 120,
    defaultCanSort: false,
    disableSortBy: true,
    meta: {
      skipTranslation: false,
    },
  },
  {
    id: 'userName',
    accessor: (row) => row.userName,
    Header: 'NAME',
    width: 120,
    defaultCanSort: false,
    disableSortBy: true,
    meta: {
      skipTranslation: false,
    },
  },
  {
    id: 'role',
    accessor: (row) => row.role && row.role.name,
    Header: 'ROLE',
    width: 120,
    defaultCanSort: false,
    disableSortBy: true,
    meta: {
      skipTranslation: false,
    },
  },
  {
    id: 'adminScopeType',
    accessor: (row) => row.adminScopeType,
    Header: 'SCOPE',
    width: 120,
    defaultCanSort: false,
    disableSortBy: true,
    meta: {
      skipTranslation: false,
    },
  },
  {
    id: 'isPasswordLoginAllowed',
    accessor: (row) => `${row.samlEnabled ? i18n.t('SAML') : ''} ${row.samlEnabled && row.isPasswordLoginAllowed ? ',' : ''} ${row.isPasswordLoginAllowed ? i18n.t('PASSWORD') : ''} `,
    Header: 'LOGIN_TYPE',
    width: 120,
    defaultCanSort: false,
    disableSortBy: true,
    meta: {
      skipTranslation: false,
    },
  },
  {
    id: 'roleType',
    accessor: (row) => row.roleType,
    Header: 'TYPE',
    width: 120,
    defaultCanSort: false,
    disableSortBy: true,
    meta: {
      skipTranslation: false,
    },
  },
  {
    id: 'comments',
    accessor: (row) => row.comments,
    Header: 'COMMENTS',
    width: 120,
    defaultCanSort: false,
    disableSortBy: true,
    meta: {
      skipTranslation: false,
    },
  },
  {
    id: 'isPasswordExpired',
    accessor: (row) => (row.isPasswordExpired ? i18n.t('TRUE') : i18n.t('FALSE')),
    Header: 'PASSWORD_EXPIRED',
    width: 120,
    defaultCanSort: false,
    disableSortBy: true,
    meta: {
      skipTranslation: false,
    },
  },
  {
    id: 'disabled',
    accessor: (row) => (row.disabled ? i18n.t('DISABLED') : i18n.t('ENABLED')),
    Header: 'STATUS',
    width: 70,
    defaultCanSort: false,
    disableSortBy: true,
    meta: {
      skipTranslation: false,
    },
  },
];

const editColumn = {
  id: 'editor',
  Header: '',
  width: 90,
  disableReordering: true,
  disableResizing: true,
  disableSortBy: true,
  meta: {
    customCellType: 'ROW_ACTIONS',
    pageName: 'LOCATION_TEMPLATES',
  },
};

export const ADMIN_MANAGEMENT_TABLE_CONFIGS = {
  columns: [...adminManagementColumns, editColumn],
  initialState: {
    // sortBy: [{ id: 'name' }],
  },
  disableTableHeaderDrag: true, // disable table column drag and drop
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: true,
};

export const DEFAULT_FORM_DATA = {
  loginId: '',
  domain: '',
  active: true,
  type: CUSTOM_APPTYPE,
};

export const RESOURCE_DEFAULTS = {
  [DATA_ERROR]: null, // or error object so we can process and show to user
  [DATA_LOADING]: true,
  [APP_DATA]: {}, // sgAppData,
  [ADD_ADMIN]: false,
  [SEARCH_DATA]: '',
  [SHOW_FORM]: false,
  [SHOW_SAML_CETIFICATE_MODAL]: false,
  [SAML_ISSUERS]: [],
  [DATA]: {},
  [DATA_TABLE]: [],
  [SHOW_DELETE_FORM]: false,
  [SELECTED_ROW_ID]: null,
  [EXPANDED_APPS]: [],
  [COLUMNS]: columns,
  [DATA_LOADING]: true,
  [MODAL_LOADING]: false,
  [SHOW_ADMIN_EXISTS_CONFIRM]: false,
  [IS_ONE_IDENTITY_ENABLED]: true,
  [ONE_IDENTITY_UI_URL]: '',
};

export const DEFAULTS = {
  ...RESOURCE_DEFAULTS,
};

// default state for this page/widget/component
export const DEFAULT_STATE = {
  ...DEFAULTS,
};

// For testing...to be used later
export const DUMMY_STORE_DATA = {
  [REDUCER_KEY]: {
    ...DEFAULT_STATE,
  },
};
