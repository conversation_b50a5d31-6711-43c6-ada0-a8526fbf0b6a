// @flow

import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSearch, faTimes } from '@fortawesome/pro-solid-svg-icons';
import i18n from 'utils/i18n';

import './index.scss';

class SearchBox extends Component {
  static propTypes = {
    placeholder: PropTypes.string,
    clickCallback: PropTypes.func,
  };

  static defaultProps = {
    placeholder: i18n.t('SEARCH_ELLIPSIS'),
    clickCallback: null,
  };

  state = {
    showTimes: false,
    searchText: '',
  };

  handleKeyPress = (e) => {
    const { clickCallback } = this.props;
    const searchText = e.currentTarget.value.trim();

    e.preventDefault();

    if (searchText !== '') {
      this.setState(() => ({
        showTimes: true,
        searchText,
      }));
    } else {
      this.setState(() => ({
        showTimes: false,
        searchText,
      }));

      if (clickCallback && searchText.trim() !== '') clickCallback();
    }
  };

  handleSearchHandler = (e) => {
    const { clickCallback } = this.props;
    const { searchText } = this.state;

    e.preventDefault();

    if (e.keyCode !== 13 && e.type !== 'click') return;

    if (clickCallback) clickCallback(searchText, null, true);
  };

  handleClearHandler = (e) => {
    const { clickCallback } = this.props;

    e.preventDefault();

    this.setState(() => ({
      showTimes: false,
      searchText: '',
    }));
    setTimeout(() => {
      clickCallback('', null, true);
    }, 100);
  };

  render() {
    const { placeholder } = this.props;
    const { showTimes, searchText } = this.state;
    return (
      <div className="search-box">
        <div className="icon-wrapper left">
          <FontAwesomeIcon
            className="search-icon"
            icon={faSearch} />
        </div>
        <input className="input-search-box" type="text" placeholder={placeholder} onKeyUp={this.handleSearchHandler} onChange={this.handleKeyPress} value={searchText} />
        <span className={`${showTimes ? 'unhide' : 'hide'}`}>
          <FontAwesomeIcon icon={faTimes} className="fa-times" onClick={this.handleClearHandler} />
        </span>
      </div>
    );
  }
}

export default SearchBox;
