import { createAction, loading } from 'ducks/generics';
import { genericInterface } from 'utils/http';
import { omit, get, isEmpty } from 'utils/lodash';
import { reset } from 'redux-form';
import { notify, notifyError } from 'ducks/notification';
import { checkActivation } from 'ducks/activation';
import { verifyConfigData } from 'utils/helpers';
import * as loginSelectors from 'ducks/login/selectors';
import * as constants from './constants';
import actionTypes from './action-types';
import * as selectors from './selectors';

export { REDUCER_KEY } from './constants';

const actionTypeExists = Object.keys(actionTypes).reduce((hash, key) => ({
  ...hash,
  [actionTypes[key]]: true,
}), {});

export const reducer = (state = constants.DEFAULT_STATE, action = {}) => {
  if (actionTypeExists[action.type]) {
    return {
      ...state,
      ...action.payload,
    };
  }
  return state;
};
export default reducer;

const boundLoading = loading(actionTypes.DATA_LOADING);
const boundError = createAction(actionTypes.DATA_LOAD_ERROR);
const boundDataLoadSuccess = createAction(actionTypes.DATA_LOAD_SUCCESS);
const boundModalLoading = createAction(actionTypes.MODAL_LOADING);
 
export const loader = () => async (dispatch) => {
  dispatch(
    boundLoading({
      ...constants.DEFAULTS,
      loading: true,
    }),
  );
  // dispatch(reset('dnsInsightsFiltersForm'));
  // Prev Day;
  try {
    const ccAdvancedSettings = await genericInterface(
      constants.CC_ADVANCED_SETTINGS_API_QUERY,
    ).read();
    const { data } = ccAdvancedSettings || [];
    const { accountIdEnabled, subIdEnabled, projectIdEnabled } = data;
    dispatch(boundDataLoadSuccess({
      [constants.DATA_TABLE]: data,
      [constants.ACCOUNT_ID]: accountIdEnabled,
      [constants.SUBSCRIPTION_ID]: subIdEnabled,
      [constants.PROJECT_ID]: projectIdEnabled,
      loading: false,
    }));
  } catch (error) {
    const errorMsg = get(error, 'response.data.message');
    dispatch(boundError({
      [constants.MODAL_LOADING]: false,
      loading: false,
    }));
    dispatch(boundError(errorMsg));
    if (isEmpty(errorMsg)) dispatch(notifyError('ERROR_LOADING_DATA'));
    else dispatch(notifyError(errorMsg));
  }
};

export const handleCancel = () => (dispatch) => {
  dispatch(reset('cloudConfigurationAdvancedSettingsForm'));
};

export const saveCCAdvancedSettings = () => async (dispatch, getState) => {
  dispatch(boundLoading({ loading: true }));
  const state = getState();
  const newValue = selectors.formValuesSelector(state);
  const editApi = genericInterface(constants.CC_ADVANCED_SETTINGS_API_QUERY);
  
  const configData = loginSelectors.configDataSelector(state);
  const enableGcp = verifyConfigData({ configData, key: 'enableGcp' });
  const updatedValue = enableGcp
    ? newValue
    : omit(newValue, 'projectIdEnabled');
    
  try {
    await editApi.update(updatedValue);
  } catch (error) {
    const errorMsg = get(error, 'response.data.message');
    dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
    dispatch(boundError(errorMsg));
    if (isEmpty(errorMsg)) dispatch(notifyError('ERROR_DELETING_API_KEY_MANAGEMENT'));
    else dispatch(notifyError(errorMsg));
    return;
  }
  dispatch(notify('SUCCESSFULLY_SAVED'));
  dispatch(loader());
  dispatch(checkActivation());
};
