@import "scss/colors.scss";

.ec-root-page {
.checkbox-container {
    padding: 5px 0;

    .container {
        display: block;
        position: relative;
        padding-left: 25px;
        cursor: pointer;
        font-size: 13px;
        color: var(--semantic-color-content-base-primary);

        input {
            position: absolute;
            opacity: 0;
            cursor: pointer;
            height: 0;
            width: 0;

            &:checked~.checkmark {
                background-color: var(--semantic-color-content-interactive-primary-default);
                span {
                  display: block;           
                }
            }
        }

        .checkmark {
            border: .0625rem solid var(--semantic-color-content-base-primary);
            border-radius: .125rem;
            position: absolute;
            top: 0;
            left: 0;
            height: 19px;
            width: 19px;
            span {
                position: absolute;
                display: none;
                left: 4px;
                top: 0;
                width: 8px;
                height: 13px;
                border: solid var(--semantic-color-content-base-primary);
                border-width: 0 2.5px 2.5px 0;
                transform: rotate(45deg);
            }
        }

    }

    .disabled {
        background: var(—surface-fields-disabled);
        color: var(--semantic-color-content-interactive-primary-disabled);
        cursor: default;

        .checkmark {
            border: 1px solid var(--semantic-color-border-interactive-primary-disabled);
        }
    }
}
}