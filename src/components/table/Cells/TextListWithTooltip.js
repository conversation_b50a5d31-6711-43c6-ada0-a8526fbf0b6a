// @flow

import React from 'react';
import ReactTooltip from 'react-tooltip';
import PropTypes from 'prop-types';
import './index.scss';

function TextListWithTooltip({ value }) {
  return (
    <div className="cell-table-text-list">
      <ReactTooltip place="bottom" type="light" className="ec-tooltip-container" />
      <div className="icon-cont elipsis" data-tip={value.join(' , ')}>
        <span>{value.join(' , ')}</span>
      </div>
    </div>
  );
}

TextListWithTooltip.propTypes = {
  value: PropTypes.arrayOf(PropTypes.string),
};

TextListWithTooltip.defaultProps = {
  value: [],
};

export default TextListWithTooltip;
