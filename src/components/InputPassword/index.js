import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faAsterisk } from '@fortawesome/pro-regular-svg-icons';
import { faEye, faEyeSlash } from '@fortawesome/pro-solid-svg-icons';

import './index.scss';

function InputPassword(props = {}) {
  const {
    meta,
    input,
    label,
    id,
    placeholder,
    customClass,
    t,
    isDisabled,
    required,
    showPasswordIcon,
    outlineUnSet,
  } = props;
  const hasError = !meta.active && meta.touched && !!meta.error;

  const [showPassword, setShowPassword] = useState(false);

  let outlineStyle = {};
  if (outlineUnSet) {
    outlineStyle = {
      outline: 'none',
    };
  }
  return (
    <div className={`input-password-container ${hasError ? 'error' : ''} ${customClass}`}>
      <div>
        <label htmlFor={id}>
          {label && (
            <p className={`input-password-label ${required ? 'required' : ''}`}>
              {required && <FontAwesomeIcon icon={faAsterisk} />}
              {t(label)}
            </p>
          )}
          <div className="input-password-wrapper">
            <input
              {...input}
              id={id}
              disabled={isDisabled ? 'disabled' : ''}
              placeholder={placeholder ? t(placeholder) : ''}
              type={showPassword ? 'text' : 'password'}
              style={{ ...outlineStyle }} />
            {
              showPasswordIcon
              && (
                <>
                &nbsp;
                  <span>
                    <FontAwesomeIcon
                      icon={showPassword ? faEyeSlash : faEye}
                      onClick={() => setShowPassword(!showPassword)} />
                  </span>
                &nbsp;
                  <span className="show-or-hide-password">
                    {
                      showPassword ? 'Hide Password' : 'Show Password'
                    }
                  </span>
                </>
              )
            }
          </div>
        </label>
      </div>

      <div className="error-container">
        {hasError && <p>{t(meta.error)}</p>}
      </div>
    </div>
  );
}

InputPassword.defaultProps = {
  label: null,
  unit: null,
  placeholder: '',
  isDisabled: false,
  customClass: '',
  t: (str) => str,
  required: false,
  showPasswordIcon: false,
  outlineUnSet: false,
};

InputPassword.propTypes = {
  label: PropTypes.string,
  unit: PropTypes.string,
  isDisabled: PropTypes.bool,
  id: PropTypes.string.isRequired,
  placeholder: PropTypes.string,
  customClass: PropTypes.string,
  t: PropTypes.func,
  meta: PropTypes.shape({
    active: PropTypes.bool,
    touched: PropTypes.bool,
    error: PropTypes.string,
  }).isRequired,
  input: PropTypes.shape({
    onFocus: PropTypes.func.isRequired,
    onBlur: PropTypes.func.isRequired,
    onChange: PropTypes.func.isRequired,
    name: PropTypes.string,
    value: PropTypes.any,
  }).isRequired,
  required: PropTypes.bool,
  showPasswordIcon: PropTypes.bool,
  outlineUnSet: PropTypes.bool,
};

export default withTranslation()(InputPassword);
export { InputPassword };
