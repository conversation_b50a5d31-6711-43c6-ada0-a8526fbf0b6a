// Import library-specific styles
import './styles/library.scss';

// Export main pages
export { default as DashboardPage } from './pages/dashboard';
export { default as AdministrationPage } from './pages/administration';
export { default as AnalyticsPage } from './pages/analytics';
export { default as PolicyPage } from './pages/policy';
export { default as ProfilePage } from './pages/profile';
export { default as HelpPage } from './pages/help';
export { default as LoginPage } from './pages/login';
export { default as OneIdentityLoginPage } from './pages/one-identity-login';

// Export key reusable components
export { default as Header } from './components/header';
export { default as MainNav } from './components/MainNav';
export { default as Footer } from './components/footer';
export { default as Modal } from './components/modal';
export { default as Button } from './components/button/Button';
export { default as ButtonNew } from './components/button/ButtonNew';
export { default as Input } from './components/Input';
export { default as InputPassword } from './components/InputPassword';
export { default as Table } from './components/table';
export { default as TablePro } from './components/tablePro';
export { default as ConfigTable } from './components/configTable';
export { default as Dropdown } from './components/dropDown';
export { default as CommonDropdown } from './components/commonDropdown';
export { default as MultiSelectDropdown } from './components/multiSelectDropdown';
export { default as Spinner } from './components/spinner';
export { default as Loading } from './components/spinner/Loading';
export { default as PageLoader } from './components/spinner/PageLoader';
export { default as Notification } from './components/notification/Notification';
export { default as PieChart } from './components/pieChart';
export { default as BarChart } from './components/barChart';
export { default as LineChart } from './components/lineChartBasic';
export { default as WorldMap } from './components/worldMap';
export { default as NavTabs } from './components/navTabs';
export { default as Toggle } from './components/toggle';
export { default as RadioGroup } from './components/radioGroup';
export { default as CheckboxInput } from './components/CheckboxInput';
export { default as DateTimePicker } from './components/dateTimePicker';
export { default as SearchBox } from './components/searchBox';
export { default as PaginationBar } from './components/PaginationBar';
export { default as Tooltip } from './components/tooltip';
export { default as Drawer } from './components/Drawer';

// Export layout components
export { default as AppLayout } from './layout/AppLayout';

// Export providers
export { default as AppProvider } from './providers/AppProvider';

// Export utilities and hooks
export { http, genericInterface } from './utils/http';
export { initializeHttpClient } from './utils/http/http';
export * as helpers from './utils/helpers';
export * as validations from './utils/validations';
export * as persistentStorage from './utils/persistentStorage';

// Export store configuration
export { configureStore } from './store';

// Export routing
export { default as routes } from './routes';
export { default as AuthRouter } from './AuthRouter';
export { default as OneUIAuthRouter } from './OneUIAuthRouter';

// Export common connected components
export * as commonConnectedComponents from './commonConnectedComponents';

// Export duck modules for state management
export * as ducks from './ducks';

// Export configuration
export * as config from './config';
