import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import { get } from 'utils/lodash';

import DropdownWithSearch from 'components/dropDown/DropdownWithSearch';
import { dropdownActions, selector } from 'ducks/dropdowns/ip-source-group';
import labelHelper from './labelHelper';

export const getUnselectedValues = (state, isZpa) => {
  const { selectedValueMap = {}, data } = selector(state).dropdown;

  const unselectedValues = data.map((item, index) => {
    let checked = !!selectedValueMap[get(item, 'id')];
    let disabled = false;
    let hidden = false;

    // BUG-162491 'App Connector Source IP Group' && ZPA
    const appConnectorGroup = 'App Connector Source IP Group';

    if (isZpa && get(item, 'name') === appConnectorGroup) {
      checked = isZpa;
      disabled = isZpa;
      hidden = false;
    }
    
    if (!isZpa && get(item, 'name') === appConnectorGroup) {
      checked = false;
      disabled = true;
      hidden = true;
    }

    return {
      original: item,
      id: get(item, 'id', index),
      name: get(item, 'name', ''),
      checked,
      hidden,
      disabled,
    };
  });
  return unselectedValues;
};

export const mapStateToProps = (state, ownProps) => {
  const {
    t, meta, label, tooltip, isZpa,
  } = ownProps;
  const dropdownState = selector(state).dropdown;
  const { selectedValues, values } = dropdownState;
  const dropdownLabels = labelHelper(t, selectedValues, values, tooltip || 'TOOLTIP_USED_IN_EXCLUDED_CRITERIA', label || 'ALL_ZSCALER_LOCATIONS');

  return {
    ...dropdownState,
    ...dropdownLabels,
    unselectedValues: getUnselectedValues(state, isZpa),
    meta,
  };
};

export const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    ...dropdownActions,
  }, dispatch);

  return {
    actions,
  };
};

export default withTranslation()(connect(
  mapStateToProps,
  mapDispatchToProps,
)(DropdownWithSearch));
