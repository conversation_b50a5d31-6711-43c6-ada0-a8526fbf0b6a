import { createAction, loading } from 'ducks/generics';
import { genericInterface } from 'utils/http';
import { change } from 'redux-form';
import {
  get, omit, isEmpty, uniqBy,
} from 'utils/lodash';
import { notify, notifyError } from 'ducks/notification';
import { checkActivation } from 'ducks/activation';
import * as constants from './constants';
import actionTypes from './action-types';
import * as selectors from './selectors';

export { REDUCER_KEY } from './constants';

const actionTypeExists = Object.keys(actionTypes).reduce((hash, key) => ({
  ...hash,
  [actionTypes[key]]: true,
}), {});

export const reducer = (state = constants.DEFAULT_STATE, action = {}) => {
  if (actionTypeExists[action.type]) {
    return {
      ...state,
      ...action.payload,
    };
  }
  return state;
};
export default reducer;

const boundLoading = loading(actionTypes.DATA_LOADING);
const boundError = createAction(actionTypes.DATA_LOAD_ERROR);
const dataChanged = createAction(actionTypes.DATA_CHANGED);
const boundDataLoadSuccess = createAction(actionTypes.DATA_LOAD_SUCCESS);
const boundModalLoading = createAction(actionTypes.MODAL_LOADING);

const form = 'appliancesForm';

export const loader = (initializeProps) => async (dispatch, getState) => {
  dispatch(
    boundLoading({
      loading: true,
    }),
  );

  const state = getState();
  if (initializeProps) {
    state.appliances.page = 1;
    state.appliances.appliancesData = [];
    state.appliances.hasNextPage = false;
    state.appliances.search = null;
    state.appliances.moreItemsLoading = false;
    dispatch(boundLoading({
      loading: true,
    }));
  }
  const { page } = state.appliances;
  const pageSize = 100;
  const searchText = document.querySelector('.search-container .input-search-box')?.value;
  // const searchText = document.querySelector('.search-container .input-search-box').value;
  const param = searchText !== '' ? { search: searchText, page, pageSize } : { page, pageSize };

  try {
    const response = await genericInterface(constants.APPLIANCES_QUERY).read(null, {
      params: param,
    });
    const { data = [] } = response || {};
    const combinedData = [...state.appliances.appliancesData, ...data];
    const updatedData = uniqBy(combinedData, 'id');

    if (data.length > 0) {
      dispatch(boundDataLoadSuccess({
        loading: false,
        [constants.DATA_TABLE]: updatedData,
        [constants.PAGE_NUMBER]: state.appliances.page + 1,
        [constants.SEARCH_TEXT]: searchText,
        [constants.HAS_NEXT_PAGE]: !(data.length < pageSize),
        [constants.MORE_ITEMS_LOADING]: false,
      }));
    } else {
      dispatch(boundDataLoadSuccess({
        loading: false,
        [constants.DATA_TABLE]: updatedData,
        [constants.PAGE_NUMBER]: state.appliances.page + 1,
        [constants.SEARCH_TEXT]: searchText,
        [constants.HAS_NEXT_PAGE]: false,
        [constants.MORE_ITEMS_LOADING]: false,
      }));
    }
  } catch (error) {
    const errorMsg = get(error, 'response.data.message');
    dispatch(boundModalLoading({
      [constants.MODAL_LOADING]: false,
      loading: false,
    }));
    dispatch(boundError(errorMsg));
    if (isEmpty(errorMsg)) dispatch(notifyError('ERROR_LOADING_DATA'));
    else dispatch(notifyError(errorMsg));
  }
};

export const updateMenu = (columns) => (dispatch) => {
  dispatch(
    dataChanged({
      [constants.COLUMNS]: [],
    }),
  );
  dispatch(
    dataChanged({
      [constants.COLUMNS]: columns,
    }),
  );
};

export const toggleAddForm = (toggle) => (dispatch) => {
  dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
  dispatch(dataChanged({
    [constants.SHOW_ADD_FORM]: toggle,
    [constants.SELECTED_ROW]: null,
  }));
};

export const toggleDeleteConfirmationForm = (row, toggle) => (dispatch) => {
  dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
  dispatch(dataChanged({
    [constants.SHOW_DELETE_FORM]: toggle,
    [constants.SELECTED_ROW]: row,
  }));
};

export const toggleEditForm = (row, toggle) => (dispatch) => {
  dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
  dispatch(dataChanged({
    [constants.SHOW_ADD_FORM]: false,
    [constants.SHOW_EDIT_FORM]: toggle,
    [constants.SHOW_VIEW_FORM]: false,
    [constants.SELECTED_ROW]: row,
  }));
};

export const toggleViewForm = (row, toggle) => (dispatch) => {
  dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
  dispatch(dataChanged({
    [constants.SHOW_ADD_FORM]: false,
    [constants.SHOW_VIEW_FORM]: toggle,
    [constants.SHOW_EDIT_FORM]: false,
    [constants.SELECTED_ROW]: row,
  }));
};

export const deleteAppliance = () => async (dispatch, getState) => {
  dispatch(boundLoading({
    [constants.MODAL_LOADING]: true,
    loading: true,
  }));
  const state = getState();
  const { selectedRow } = selectors.default(state);
  const { id } = selectedRow || {};

  if (!id) dispatch(notifyError('ERROR_DELETING_APPLIANCE'));
  const deleteApi = genericInterface(constants.APPLIANCES_QUERY + '/' + id);

  try {
    await deleteApi.del();
  } catch (error) {
    const errorMsg = get(error, 'response.data.message');
    dispatch(boundLoading({
      [constants.MODAL_LOADING]: false,
      loading: false,
    }));
    dispatch(boundError(errorMsg));
    if (isEmpty(errorMsg)) dispatch(notifyError('ERROR_DELETING_APPLIANCE'));
    else dispatch(notifyError(errorMsg));
    dispatch(toggleDeleteConfirmationForm(false));
    dispatch(checkActivation());

    return;
  }
  dispatch(toggleDeleteConfirmationForm(false));
  dispatch(notify('SUCCESSFULLY_DELETED'));
  dispatch(loader(true));
  dispatch(checkActivation());
};

export const updateApplianceData = () => async (dispatch, getState) => {
  dispatch(boundLoading({
    [constants.MODAL_LOADING]: true,
    loading: true,
  }));
  const state = getState();
  const { showEditForm, selectedRow } = selectors.default(state);
  const { id } = selectedRow || {};
  const formValuesSelector = selectors.formValuesSelector(state);

  const newValue = {
    ...omit(selectedRow, ['idx', 'isDeletable', 'isEditable', 'isReadOnly', 'provTemplateName', 'locationName']),
    ...formValuesSelector,
    ziaSupportTunAllow: (formValuesSelector.ziaSupportTunAllow === 'ENABLE'),
    startSupportTunnel: (formValuesSelector.startSupportTunnel === 'ENABLE'),
  };

  if (showEditForm && (!selectedRow || !id)) dispatch(notifyError('ERROR_EDITING'));

  const saveApi = genericInterface(constants.APPLIANCES_QUERY);
  const apiEndPoint = showEditForm ? saveApi.update : saveApi.create;

  try {
    await apiEndPoint(newValue);
  } catch (error) {
    const errorMsg = get(error, 'response.data.message');
    dispatch(boundLoading({
      [constants.MODAL_LOADING]: false,
      loading: false,
    }));
    dispatch(boundError(errorMsg));
    if (isEmpty(errorMsg)) dispatch(notifyError('ERROR_EDITING'));
    else dispatch(notifyError(errorMsg));
    dispatch(toggleEditForm(false));
    dispatch(checkActivation());
    return;
  }
  dispatch(toggleEditForm(false));
  dispatch(notify('SUCCESSFULLY_SAVED'));
  dispatch(loader(true));
  dispatch(checkActivation());
};

export const handleOnDemandSupportTunnel = (e) => async (dispatch) => {
  const value = e.target && e.target.value;
  if (value !== 'ENABLE') return null;
  return dispatch(change(form, 'startSupportTunnel', 'DISABLE'));
  // return dispatch(change(form, 'ziaSupportTunAllow', value));
};

// export const handleEstabilishSupportTunnel = value => async (dispatch) => {
//   // return dispatch(change(form, 'startSupportTunnel', value));
// };
