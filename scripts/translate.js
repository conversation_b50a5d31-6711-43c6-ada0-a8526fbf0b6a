// NOTE: THIS SCRIPT IS VERY INEFFICIENT.
// IT WILL TAKE FEW MINUTES TO CONVERT ALL FILES.

const fs = require('fs');
const csv = require('csvtojson');

const folder = '/Users/<USER>/Documents/Dev/verify/02Jul20_6.1/userinterface/edge-connector/edge-domain/generated/localization/map';
const output = '/Users/<USER>/Documents/Dev/BitBucket/EdgeConnector/13May20-EC/src/utils/i18n/gen';

const files = fs.readdirSync(folder);
const csvFiles = files.filter(file => file.indexOf('.csv') > -1);

csvFiles.forEach((file) => {
  const csvFile = folder + '/' + file;
  const fileName = file.split('.')[0];
  const jsonFile = output + '/' + fileName + '.json';
  
  csv({
    noheader: true,
    quote: '"',
  })
    .fromFile(csvFile)
    .then((jsonObj) => {
      console.log('Reading from', csvFile); // eslint-disable-line 
      const json = jsonObj.reduce((obj, row) => {
        return {
          ...obj,
          [row.field1]: row.field3,
        };
      }, {});
      // console.log(json);
      console.log('Writing to', jsonFile);  // eslint-disable-line 
      fs.writeFileSync(jsonFile, JSON.stringify(json, null, 2));
    });
});
