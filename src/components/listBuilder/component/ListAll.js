import React from 'react';
import PropTypes from 'prop-types';
import ListItem from './ListItem';

function ListAll({
  onRemove, validation, list, search, disabled,
}) {
  return (
    <div className="list-builder-list-overflow">
      <div className="list-builder-list">
        {list
          .filter((x) => x.includes(search))
          .map((item) => (
            <ListItem
              key={item}
              item={item}
              search={search}
              disabled={disabled}
              validation={validation}
              handleRemove={onRemove} />
          ))}
      </div>
    </div>
  );
}

ListAll.propTypes = {
  onRemove: PropTypes.func,
  validation: PropTypes.func,
  list: PropTypes.arrayOf(PropTypes.string),
  search: PropTypes.string,
  disabled: PropTypes.bool,
};
  
ListAll.defaultProps = {
  disabled: true,
  onRemove: null,
  validation: null,
  list: [],
  search: '',
};

export default ListAll;
