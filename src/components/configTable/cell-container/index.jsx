import React, { useCallback, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import { noop, isArray, isEmpty } from 'utils/lodash';
import dayjs from 'dayjs';
import i18n from 'utils/i18n';

import { withTranslation } from 'react-i18next';

import RowActions from 'components/tablePro/RowActions';
import RowExpander from 'components/tablePro/RowExpander';

import TableCheckBox from './components/CheckboxCell';
import TableCloudIcon from './components/CloudIcon';
import TableLink from './components/LinkCell';
import TableDefaultItemCell from './components/Default';
import TableDownloadFile from './components/DownloadFile';
import TableCheckBoxLink from './components/CheckboxLinkCell';
import TableCloudName from './components/CloudName';
import TableUpgradeStatus from './components/UpgradeStatus';
import TableHAStatus from './components/HaStatus';
import TableSingleItemCell from './components/SingleItemView';
import TableDescriptionCell from './components/Description';
import TablePolicyActionCell from './components/PolicyAction';
import TablePolicyCriteriasCell from './components/PolicyCriterias';
import TableOperationStatus from './components/OperationalStatusCell';
import TablePartnerPermission from './components/PartnerPermission';
import TablePartnerStatus from './components/PartnerStatus';
import TablePartnerProjectStatus from './components/PartnerProjectStatus';
import TablePartnerTopicCloudConnector from './components/PartnerTopicCloudConnector';
import TablePartnerTopicDiscovery from './components/PartnerTopicDiscovery';
import TableProvInfoStatus from './components/ProvInfoStatus';
import TableZtDeviceStatus from './components/ZtDeviceStatusCell';
// import TableLink from './components/link';
// import ThreatName from './components/threat-name';

import './index.scss';

function CellContainer({
  column,
  t,
  row,
  value,
  onHandleRowEdit,
  onHandleRowDisable,
  onHandleRowRefresh,
  onHandleRowRun,
  onHandleRowEllipsis,
  onHandleDuplicateRow,
  onHandleEnableDisableRow,
  onHandleRowDelete,
  onHandleRowView,
  onHandleRowViewDiff,
  onHandleRowSubEdit,
  onHandleRowDownload,
  onHandleShowHideChilds,
  onHandleCheck,
  linkNameParameter,
  onHandleLink,
  onHandleLinkSelf,
  onHandleRowTestConnectivity,
  onHandleStatusChange,
  pageIndex,
  pageSize,
  setSize,
  renderIndex,
  setTooltipContent,
  tooltipRef,
  tooltipContent,
  updatePosition,
  isTestConnectivityInProgress,
  isTestConnectivityFeedDisabled,
}) {
  const cellRef = useRef(null);
  const { filterValue, meta = {}, id } = column;
  const {
    skipTranslation = false, customCellType = '', showMoreButton = false, getValue = () => null, pageName = '', field = '', generalValue = '',
  } = meta;

  // Updates tooltip position after tooltip content has been updated
  useEffect(() => {
    const { current: cell } = cellRef;
    const { innerText } = '';

    if (cell.innerText === innerText && cell.scrollWidth > cell.offsetWidth) {
      updatePosition(cellRef.current, { top: 50, left: 10 });
    }
  }, [tooltipContent]);

  const showTooltip = (event) => {
    const cell = event.currentTarget;
    const { parentElement } = cell;

    if (parentElement && parentElement.scrollWidth > parentElement.offsetWidth) {
      const content = skipTranslation ? value : t(value);
      const { innerText, style } = tooltipRef.current;

      if (style.display === '' || style.display === 'none') {
        style.display = 'block';
      } else if (innerText === content) {
        style.display = 'none';
      }

      setTooltipContent(content);
    }
  };

  const getReadOnlyProp = () => {
    return row.original.isReadOnly || false;
  };

  const getEditableProp = () => {
    return row.original.isEditable || false;
  };
  const getRefreshableProp = () => {
    return row.original.isRefreshable || false;
  };
  const getRunableProp = () => {
    return row.original.isRunable || false;
  };
  const getEllipsisableProp = () => {
    return row.original.isEllipsisable || false;
  };
  const getDisableProp = () => {
    return row.original.isDisable || false;
  };

  const getCopyableProp = () => {
    return row.original.isCopyable || false;
  };

  const getDeletableProp = () => {
    return row.original.isDeletable || false;
  };

  const getDownloadableProp = () => {
    return row.original.isDownloadable || false;
  };

  const getViewDiffProp = () => {
    return !row.original.isEditable && row.original.isViewDiff;
  };

  const getDownloadDisableProp = () => {
    return row.original.disableDownload || false;
  };

  const getIsChildRowProp = () => {
    return row.original && Boolean(row.original.parentId);
  };
  
  const getShowTestConnectivityIconProp = () => {
    return row.original.cloudNss || false;
  };

  const renderDefaultSection = () => {
    try {
      return (
        <div
          tabIndex="0"
          // eslint-disable-next-line no-nested-ternary
          aria-label={isEmpty(value) ? 'Cell Value' : skipTranslation ? value : t(value)}
          role="button"
          onClick={showTooltip}
          onKeyDown={noop}>
          { skipTranslation ? value : t(value) }
        </div>
      );
    } catch (e) {
      return value;
    }
  };

  const renderMultipleAttributesInCell = (values) => {
    if (values && isArray(values) && values.length) {
      const updatedArray = values.length > 6 ? values.slice(0, 6) : values;
      return updatedArray.map((m) => {
        const headerValue = (m && m.skipAttribureTranslation) ? m.header : t(m.header);
        const cellValue = (m && m.skipAttribureTranslation) ? m.value : t(m.value);
        return (
          <>
            <div>
              <span
                className="multi-attribute-cell-content-header"
                aria-label={isEmpty(headerValue) ? 'Header Value' : headerValue}
                tabIndex="0"
                role="button"
                onClick={showTooltip}
                onKeyDown={noop}>
                {headerValue}
              </span>
            </div>
            <div>
              <span
                className="multi-attribute-cell-content-value"
                aria-label={isEmpty(cellValue) ? 'Cell Value' : cellValue}
                tabIndex="0"
                role="button"
                onClick={showTooltip}
                onKeyDown={noop}>
                {cellValue}
              </span>
            </div>
          </>
        );
      });
    }
    return values;
  };

  const handleEnableTooltip = (event) => {
    const cell = event.currentTarget;

    if (cell && cell.scrollWidth > cell.offsetWidth) {
      cell.classList.add('tooltip-cue', '-js.tooltip-cue');
    }
  };

  const handleDisableTooltip = (event) => {
    const cell = event.currentTarget;

    if (cell) {
      cell.classList.remove('tooltip-cue', '-js.tooltip-cue');
    }
  };
  // passing second argument to open modal for existing react-modal
  const onClickRowEdit = useCallback(() => onHandleRowEdit(row.original, true), [row]);

  const onClickCheckLink = useCallback((e) => onHandleLink(row.original, e), [row]);

  const onClickLinkSelf = useCallback((e) => onHandleLinkSelf(row.original, e), [row]);

  const onClickRowDisable = useCallback(() => onHandleRowDisable(row.original, true), [row]);

  const onClickRowRefresh = useCallback(() => onHandleRowRefresh(row.original, true), [row]);

  const onClickRowRun = useCallback(() => onHandleRowRun(row.original, true), [row]);

  const onClickRowEllipsis = useCallback((e) => onHandleRowEllipsis(row.original, e), [row]);

  const onDuplicateRow = useCallback(() => onHandleDuplicateRow(row.original, true), [row]);

  const onEnableDisableRow = useCallback(() => onHandleEnableDisableRow(row.original, true), [row]);

  const onClickRowSubEdit = useCallback(() => onHandleRowSubEdit(row.original, true), [row]);

  const onClickRowDelete = useCallback(() => onHandleRowDelete(row.original, true), [row]);

  const onClickRowView = useCallback(() => onHandleRowView(row.original, true), [row]);

  const onClickRowViewDiff = useCallback(() => onHandleRowViewDiff(row.original, true), [row]);

  const onClickRowDownload = useCallback(() => onHandleRowDownload(row.original), [row]);

  // eslint-disable-next-line max-len
  const onChangeStatus = useCallback((status) => onHandleStatusChange(row.original, true, status), [row]);

  // Download a Cell Value in a Row.
  const onClickCellInRowDownload = useCallback(() => onHandleRowDownload(row.original.id), [row]);
  const onClickExpand = useCallback(() => {
    if (!row.isExpanded) setSize(renderIndex, row.original.expandendHeight || 120);
    if (row.isExpanded) setSize(renderIndex, row.original.height || 50);
  }, [row]);

  // eslint-disable-next-line max-len
  const onClickRowTestConnectivity = useCallback(() => onHandleRowTestConnectivity(row.original), [row]);

  const getCellClassName = () => `cell-container ${filterValue ? 'filtered' : ''
  } ${customCellType === 'ROW_ACTIONS' ? 'no-ellipsis' : ''
  } ${customCellType === 'NSS_FEEDS' || pageName === 'NSS_FEEDS' ? 'multi-attribute-cell-fixed-height' : ''
  } ${getIsChildRowProp() ? 'table-row-child' : 'table-row-parent'
  }`;

  const renderCellSection = () => {
    if (customCellType === 'RENDER_INDEX') {
      return (
        <div>
          {pageIndex && pageSize ? pageIndex * pageSize + renderIndex + 1
            : renderIndex + 1}
        </div>
      );
    }

    // if (customCellType === 'LINK') {
    //   return <TableLink value={value} skipTranslation={skipTranslation} />;
    // }

    if (customCellType === 'POLICY_ACTION') {
      return <div className={`policy-action ${value.toLowerCase()}`}>{renderDefaultSection()}</div>;
    }

    if (customCellType === 'MULTIPLE_ATTRIBUTE') {
      return (
        <div>
          {
            renderMultipleAttributesInCell(value)
          }
          {showMoreButton && value && isArray(value) && value.length > 6 && (
            <span
              onClick={onClickRowEdit}
              // aria-label="Show more"
              onKeyPress={noop}
              role="button"
              tabIndex="0"
              className="more-button">
              More...
            </span>
          )}
        </div>
      );
    }

    if (customCellType === 'NSS_SERVER_DOWNLOAD') {
      return (
        <div
          className="download-nss-server"
          aria-label={isEmpty(value) ? 'Cell Value' : value}
          onKeyPress={noop}
          role="button"
          tabIndex="0"
          onClick={onClickCellInRowDownload}>
          {value}
        </div>
      );
    }

    // if (customCellType === 'THREAT_NAME') {
    //   return <ThreatName row={row} value={value} skipTranslation={skipTranslation} />;
    // }

    if (customCellType === 'GET_VALUE') {
      return getValue(row, i18n.localizeString);
    }

    if (customCellType === 'DATE_TIME') {
      return <div>{(value === 0 || value === '0') ? '---' : dayjs(value).format('MMMM DD, YYYY - hh:mm:ss A')}</div>;
    }
    
    if (customCellType === 'ROW_ACTIONS') {
      return (
        <RowActions
          {...row}
          isEditable={getEditableProp(row, pageName)}
          isCopyable={getCopyableProp(row, pageName)}
          isRefreshable={getRefreshableProp(row, pageName)}
          isRunable={getRunableProp(row, pageName)}
          isEllipsisable={getEllipsisableProp(row, pageName)}
          isDisable={getDisableProp(row, pageName)}
          isDownloadable={getDownloadableProp(row, pageName)}
          isViewDiff={getViewDiffProp(row)}
          isDeletable={getDeletableProp(row, pageName)}
          isReadOnly={getReadOnlyProp(row, pageName)}
          onDeleteClick={onClickRowDelete}
          onViewClick={onClickRowView}
          onViewDiffClick={onClickRowViewDiff}
          onSubEditClick={onClickRowSubEdit}
          onDownloadClick={onClickRowDownload}
          disableDownload={getDownloadDisableProp(row)}
          onDuplicateClick={onDuplicateRow}
          onEditClick={onClickRowEdit}
          onDisableClick={onClickRowDisable}
          onRefreshClick={onClickRowRefresh}
          onRunClick={onClickRowRun}
          onEllipsisClick={onClickRowEllipsis}
          isTestConnectivityInProgress={isTestConnectivityInProgress}
          isTestConnectivityFeedDisabled={isTestConnectivityFeedDisabled}
          showTestConnectivityIcon={getShowTestConnectivityIconProp(row)}
          onTestConnectivityClick={onClickRowTestConnectivity} />
      );
    }

    if (customCellType === 'ROW_EXPANDER') {
      return <RowExpander {...row} onExpand={onClickExpand} />;
    }

    if (customCellType === 'CHECKBOX' || (customCellType === 'CHECKBOX_VERSION')) {
      return (
        <TableCheckBox
          value={row.original}
          column={column}
          customCellType={customCellType}
          skipTranslation={skipTranslation}
          onHandleCheck={onHandleCheck}
          onHandleShowHideChilds={onHandleShowHideChilds} />
      );
    }
    if (customCellType === 'LINK') {
      return (
        <TableLink
          value={row.original}
          currentRow={row}
          linkNameParameter={linkNameParameter}
          skipTranslation={skipTranslation}
          onHandleLink={onClickCheckLink}
          onHandleCheck={onHandleCheck} />
      );
    }
    if (customCellType === 'LINK_SELF') {
      return (
        <TableLink
          linkSelf
          value={row.original}
          currentRow={row}
          column={column}
          skipTranslation={skipTranslation}
          onHandleLinkSelf={onClickLinkSelf}
          onHandleCheck={onHandleCheck} />
      );
    }
    if (customCellType === 'CHECKBOX_LINK') {
      return (
        <TableCheckBoxLink
          value={value}
          original={row.original}
          skipTranslation={skipTranslation}
          onHandleLink={onClickCheckLink}
          onHandleCheck={onHandleCheck} />
      );
    }
    if (customCellType === 'PARTNER_PERMISSION') {
      return (
        <TablePartnerPermission
          value={row.original}
          cellValue={value}
          skipTranslation={skipTranslation} />
      );
    }
    if (customCellType === 'PARTNER_STATUS') {
      return (
        <TablePartnerStatus
          value={row.original}
          cellValue={value}
          onEnableDisableRow={onEnableDisableRow}
          skipTranslation={skipTranslation} />
      );
    }
    if (customCellType === 'PARTNER_PROJECT_STATUS') {
      return (
        <TablePartnerProjectStatus
          value={row.original}
          cellValue={value}
          onEnableDisableRow={onEnableDisableRow}
          skipTranslation={skipTranslation} />
      );
    }
    if (customCellType === 'PARTNER_TOPIC_DISCOVERY') {
      return (
        <TablePartnerTopicDiscovery
          value={row.original}
          cellValue={value}
          skipTranslation={skipTranslation} />
      );
    }
    if (customCellType === 'PARTNER_TOPIC_CLOUD_CONNECTOR') {
      return (
        <TablePartnerTopicCloudConnector
          value={row.original}
          cellValue={value}
          skipTranslation={skipTranslation} />
      );
    }

    if (customCellType === 'UPGRADE_STATUS') {
      return (
        <TableUpgradeStatus
          value={row.original}
          skipTranslation={skipTranslation} />
      );
    }

    if (customCellType === 'HA_STATUS') {
      return (
        <TableHAStatus
          value={row.original}
          skipTranslation={skipTranslation} />
      );
    }

    if (customCellType === 'CLOUD_NAME') {
      return (
        <TableCloudName
          value={row.original}
          t={t}
          skipTranslation={skipTranslation} />
      );
    }

    if (customCellType === 'CLOUD_ICON') {
      return (
        <TableCloudIcon
          value={row.original}
          t={t}
          skipTranslation={skipTranslation} />
      );
    }

    if (customCellType === 'OPERATIONAL_STATUS') {
      return (
        <TableOperationStatus
          value={row.original}
          skipTranslation={skipTranslation} />
      );
    }

    if (customCellType === 'DNS_POLICY_ACTION') {
      return (
        <TablePolicyActionCell
          value={row.original}
          skipTranslation={skipTranslation} />
      );
    }

    if (customCellType === 'DNS_STATE') {
      return (
        <TablePolicyActionCell
          value={row.original}
          stateOnly
          skipTranslation={skipTranslation} />
      );
    }

    if (customCellType === 'DESCRIPTION') {
      return (
        <TableDescriptionCell
          value={row.original}
          skipTranslation={skipTranslation} />
      );
    }

    if (customCellType === 'POLICY_CRITERIA') {
      return (
        <TablePolicyCriteriasCell
          value={row.original}
          skipTranslation={skipTranslation} />
      );
    }
    
    if (customCellType === 'SINGLE_ITEM') {
      return (
        <TableSingleItemCell
          field={field}
          generalValue={generalValue}
          row={row.original}
          skipTranslation={skipTranslation} />
      );
    }
    if (customCellType === 'DEFAULT') {
      return (
        <TableDefaultItemCell
          value={value}
          generalValue={generalValue}
          skipTranslation={skipTranslation} />
      );
    }

    if (customCellType === 'PROV_INFO_STATUS') {
      return (
        <TableProvInfoStatus
          onChangeHandler={onChangeStatus}
          row={row}
          value={row.original} />
      );
    }

    if (customCellType === 'ZT_DEVICE_STATUS') {
      return (
        <TableZtDeviceStatus
          onChangeHandler={onChangeStatus}
          row={row}
          value={row.original} />
      );
    }
    
    if ((customCellType === 'RELEASE_NOTES')
    || (customCellType === 'DOWNLOAD_FILE_32BITS')
    || (customCellType === 'DOWNLOAD_FILE_64BITS')
    || (customCellType === 'DOWNLOAD_FILE_BC_IMAGES')
    ) {
      return (
        <TableDownloadFile
          row={row}
          customCellType={customCellType}
          value={row.original} />
      );
    }

    return renderDefaultSection();
  };

  return (
    <div
      key={id}
      ref={cellRef}
      className={getCellClassName()}
      onFocus={noop}
      onBlur={noop}
      onMouseOver={handleEnableTooltip}
      onMouseOut={handleDisableTooltip}>
      {renderCellSection()}
    </div>
  );
}

CellContainer.defaultProps = {
  column: {},
  row: {},
  value: '',
  renderIndex: 0,
  t: (str) => str,
  tooltipRef: null,
  setTooltipContent: noop,
  updatePosition: noop,
  onHandleRowDownload: noop,
  onHandleRowEdit: noop,
  onHandleRowDisable: noop,
  onHandleRowRefresh: noop,
  onHandleRowRun: noop,
  onHandleRowEllipsis: noop,
  onHandleDuplicateRow: noop,
  onHandleEnableDisableRow: noop,
  onHandleRowDelete: noop,
  onHandleRowView: noop,
  onHandleRowViewDiff: noop,
  onHandleRowSubEdit: noop,
  onHandleStatusChange: noop,
  linkNameParameter: '',
  onHandleRowTestConnectivity: noop,
  isTestConnectivityInProgress: false,
  isTestConnectivityFeedDisabled: false,
};

CellContainer.propTypes = {
  column: PropTypes.objectOf(Object),
  row: PropTypes.objectOf(Object),
  value: PropTypes.oneOfType([PropTypes.number, PropTypes.string, PropTypes.shape({})]),
  renderIndex: PropTypes.number,
  onHandleRowEdit: PropTypes.func,
  onHandleRowDisable: PropTypes.func,
  onHandleRowRefresh: PropTypes.func,
  onHandleRowRun: PropTypes.func,
  onHandleRowEllipsis: PropTypes.func,
  onHandleDuplicateRow: PropTypes.func,
  onHandleEnableDisableRow: PropTypes.func,
  onHandleRowDelete: PropTypes.func,
  onHandleRowView: PropTypes.func,
  onHandleRowViewDiff: PropTypes.func,
  onHandleRowSubEdit: PropTypes.func,
  onHandleRowDownload: PropTypes.func,
  onHandleShowHideChilds: PropTypes.func,
  onHandleCheck: PropTypes.func,
  onHandleLink: PropTypes.func,
  onHandleLinkSelf: PropTypes.func,
  onHandleStatusChange: PropTypes.func,
  onHandleRowTestConnectivity: PropTypes.func,
  setSize: PropTypes.func,
  t: PropTypes.func,
  tooltipRef: PropTypes.oneOfType([
    PropTypes.func,
    PropTypes.shape({ current: PropTypes.instanceOf(Element) }),
  ]),
  setTooltipContent: PropTypes.func,
  tooltipContent: PropTypes.string,
  linkNameParameter: PropTypes.string,
  updatePosition: PropTypes.func,
  isTestConnectivityInProgress: PropTypes.bool,
  isTestConnectivityFeedDisabled: PropTypes.bool,
  pageIndex: PropTypes.number,
  pageSize: PropTypes.number,
};

export default withTranslation()(CellContainer);
