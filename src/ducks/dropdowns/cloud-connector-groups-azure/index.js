import { genericInterface } from 'utils/http';
import genericDropdown from 'ducks/generics/dropdown';

import actionTypes from './action-types';
import selector, * as selectors from './selectors';
import * as constants from './constants';

export { REDUCER_KEY } from './constants';

const actionTypeExists = Object.keys(actionTypes).reduce((hash, key) => ({
  ...hash,
  [actionTypes[key]]: true,
}), {});

export const reducer = (state = constants.DEFAULT_STATE, action = {}) => {
  // Simplifyeverything: actions provide a payload, and we merge that payload.
  if (actionTypeExists[action.type]) {
    return {
      ...state,
      ...action.payload,
    };
  }
  return state;
};
export default reducer;

const http = genericInterface(constants.API_ENDPOINT);

const dropdownActions = genericDropdown.createActions({ actionTypes, selector, http });

export {
  selector,
  selectors,
  dropdownActions,
};
