// @flow
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { NavLink } from 'react-router-dom';
import { withTranslation } from 'react-i18next';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { getPermission } from 'utils/helpers';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSearch } from '@fortawesome/pro-regular-svg-icons';
import * as ssoDuck from 'ducks/sso';
import { isEmpty, sortBy } from 'utils/lodash';
import Modal from 'components/modal';

import {
  // ANALYTICS_SSO_ROUTES,
  AUTHENTICATION_CONFIGURATION_SSO_ROUTES,
  NSS_SSO_ROUTES,
  FORWARDING_METHODS_SSO_ROUTES,
  FILTERING_SSO_ROUTES,
  FORWARDING_CONTROL_SSO_ROUTES,

  // NAVBAR_ROUTES,
  LOGS_SUB_NAV_ROUTES,
  ANALYTICS_SUB_NAV_ROUTES,
  ADMIN_SUB_NAV_ROUTES,
  HELP_SUB_NAV_ROUTES,
  POLICY_SUB_NAV_ROUTES,

  BASE_LAYOUT,
} from 'config';

import { toggleSearchPanel, handleOnSearchFilter } from 'ducks/searchNav';
import * as searchNavSelector from 'ducks/searchNav/selectors';
import * as loginSelectors from 'ducks/login/selectors';
import * as helpSelectors from 'ducks/helpNav/selectors';
import SimpleSearchInput from 'components/Input/SimpleSearchInput';

import {
  toggleHelpPanel, toggleShowForm, setModalTitle,
} from 'ducks/helpNav';

import {
  RemoteAssistanceForm,
  URLLookupForm,
  BlacklistedIPCheckForm,
} from 'components/navbar/helpNav/components';

export function Toggler(props) {
  const {
    togglePanel,
    actionCb,
    routes,
    handleSearchFilter,
  } = props;
  if (!togglePanel) return null;
  return (
    <div
      className="admin-panel"
      onMouseOver={() => actionCb(true)}
      onFocus={() => actionCb(true)}
      onMouseLeave={() => actionCb(false)}>
      <div className="menu-nav-search-input">
        <SimpleSearchInput
          withButton
          onKeyPressCb={handleSearchFilter} />
      </div>
      <ul id="ulRoutes" className="search-result-routes">
        {routes}
      </ul>
    </div>
  );
}

Toggler.propTypes = {
  togglePanel: PropTypes.bool,
  actionCb: PropTypes.func,
  handleSearchFilter: PropTypes.func,
  routes: PropTypes.arrayOf(PropTypes.shape()),
};

Toggler.defaultProps = {
  togglePanel: false,
  actionCb: (str) => str,
  handleSearchFilter: (str) => str,
  routes: [{}],
};

class SearchNav extends Component {
  static propTypes = {
    actions: PropTypes.shape({}),
    data: PropTypes.shape({}),
    configData: PropTypes.shape(),
    accessPermissions: PropTypes.shape({}),
    toggleSearchPanel: PropTypes.bool,
    location: PropTypes.shape({ pathname: PropTypes.string }),
    searchData: PropTypes.string,
    t: PropTypes.func,
    modalTitle: PropTypes.string,
    showForm: PropTypes.bool,
  };

  static defaultProps = {
    actions: {},
    data: {},
    configData: [{}],
    accessPermissions: {},
    toggleSearchPanel: false,
    location: null,
    searchData: '',
    t: (str) => str,
  };

  // routes will come from top level route
  active = (prop) => {
    const { location } = this.props;
    // debugger;
    if (!location) return '';

    const { pathname } = location;

    // we will make following dynamic based on url
    if (pathname.includes(prop.path)) {
      return 'active';
    }

    return '';
  };

  getRoutes = (pageroutes) => pageroutes.map((prop) => {
    const {
      t,
      actions,
      accessPermissions,
    } = this.props;
        
    return (
      <li className="nav-search-menu-section" key={prop.title}>
        <ul id="ulResults" className="search-result-panel">
          <li className="search-result-panel-item">&nbsp;</li>
          {
            prop.routes.map((route) => {
              if (route.perm && !getPermission(route.perm, accessPermissions)) {
                return null;
              }

              if (route.sso) {
                return (
                  <li
                    key={route.breadcrumbs}
                    className="search-result-panel-item">
                    <FontAwesomeIcon className="search-icon" icon={faSearch} size="lg" />
                    <NavLink
                      exact
                      to={`${BASE_LAYOUT}/administration/loading`}
                      onClick={() => actions.generateToken(route.relayPath)}
                      activeclassname="">
                      <span className="search-options-text">{t(route.breadcrumbs)}</span>
                    </NavLink>
                  </li>
                );
              }

              return (
                route.externalLink
                  ? (
                    <li key={route.breadcrumbs} className="search-result-panel-item">
                      <FontAwesomeIcon className="search-icon" icon={faSearch} size="lg" />
                      <a href={route.redirectTo} target="_blank" rel="noopener noreferrer" className="external-link-button">
                        <span className="search-options-text">{t(route.breadcrumbs)}</span>
                      </a>
                    </li>
                  )
                  : (
                    <li key={route.breadcrumbs} className="search-result-panel-item">
                      <FontAwesomeIcon className="search-icon" icon={faSearch} size="lg" />
                      {['REMOTE_ASSISTANCE', 'BLACKLISTED_IP_CHECK', 'URL_LOOKUP', 'SUBMIT_TICKET'].includes(route.name)
                        ? (
                          <NavLink
                            key={route.name}
                            to="?"
                            onClick={
                              async (e) => {
                                if (route.name === 'SUBMIT_TICKET') {
                                  e.preventDefault();
                                  actions.submitTicket();
                                } else {
                                  await actions.toggleHelpPanel(false);
                                  await actions.setModalTitle(route.name);
                                  await actions.toggleShowForm(true);
                                }
                              }
                            }
                            activeclassname="active-menu-item">
                            <span className="search-options-text">{t(route.breadcrumbs)}</span>
                          </NavLink>
                        )
                        : (
                          <NavLink exact to={route.redirectTo} activeclassname="">
                            <span className="search-options-text">{t(route.breadcrumbs)}</span>
                          </NavLink>
                        )}
                    </li>
                  )
              );
            })
          }
       
        </ul>
      </li>
    );
  });

  render() {
    const {
      modalTitle,
      showForm,
      actions,
      configData,
      actions: { toggleShowForm: onShowForm },
      toggleSearchPanel: togglePanel,
      searchData,
      t,
    } = this.props;

    const searchResult = searchData ? [{
      title: 'SEARCH_RESULT',
      perm: 'EDGE_CONNECTOR_FORWARDING',
      routes: sortBy([
        // eslint-disable-next-line max-len
        // ...ANALYTICS_SSO_ROUTES.map(x => ({ ...x, path: x.relaypath, breadcrumbs: `${t('ANALYTICS')}>${t(x.name)}` })),
        ...AUTHENTICATION_CONFIGURATION_SSO_ROUTES.map((x) => ({
          ...x, sso: true, redirectTo: x.relayPath, breadcrumbs: `${t('ADMINISTRATION')} > ${t('AUTHENTICATION_CONFIGURATION')} > ${t(x.name)}`,
        })),
        ...NSS_SSO_ROUTES.map((x) => ({
          ...x, sso: true, redirectTo: x.relayPath, breadcrumbs: `${t('ADMINISTRATION')} > ${t('AUTHENTICATION_CONFIGURATION')} > ${t(x.name)}`,
        })),
        ...FORWARDING_METHODS_SSO_ROUTES.map((x) => ({
          ...x, sso: true, redirectTo: x.relayPath, breadcrumbs: `${t('FORWARDING')} > ${t('POLICY_MANAGEMENT')} > ${t(x.name)}`,
        })),
        ...FILTERING_SSO_ROUTES.map((x) => ({
          ...x, sso: true, redirectTo: x.relayPath, breadcrumbs: `${t('FORWARDING')} > ${t('POLICY_MANAGEMENT')} > ${t(x.name)}`,
        })),
        ...FORWARDING_CONTROL_SSO_ROUTES.map((x) => ({
          ...x, sso: true, redirectTo: x.relayPath, breadcrumbs: `${t('FORWARDING')} > ${t('POLICY_MANAGEMENT')} > ${t(x.name)}`,
        })),
    
        // ...NAVBAR_ROUTES,
        ...LOGS_SUB_NAV_ROUTES[0].routes.map((x) => ({ ...x, sso: false, breadcrumbs: `${t('ANALYTICS')} >  ${t(x.name)}` })),
        ...ANALYTICS_SUB_NAV_ROUTES[0].routes.map((x) => ({ ...x, sso: false, breadcrumbs: `${t('ANALYTICS')} >  ${t(x.name)}` })),
        ...ADMIN_SUB_NAV_ROUTES(configData).map((menu) => {
          if (!isEmpty(menu.routes)) {
            return menu.routes.map((x) => ({
              ...x, sso: false, perm: menu.perm, breadcrumbs: `${t('ADMINISTRATION')} > ${t(menu.title)} >  ${t(x.name)}`,
            }));
          }
          return [];
        }).flat(),
        ...HELP_SUB_NAV_ROUTES.map((menu) => {
          if (!isEmpty(menu.routes)) {
            return menu.routes.map((x) => ({
              ...x, sso: false, perm: menu.perm, breadcrumbs: `${t('HELP')} > ${t(menu.title)} >  ${t(x.name)}`,
            }));
          }
          return [];
        }).flat(),
        ...POLICY_SUB_NAV_ROUTES.map((menu) => {
          if (!isEmpty(menu.routes)) {
            return menu.routes.map((x) => ({
              ...x, sso: false, perm: menu.perm, breadcrumbs: `${t('FORWARDING')} > ${t(menu.title)} >  ${t(x.name)}`,
            }));
          }
          return [];
        }).flat(),
      ], 'breadcrumbs')
        .filter((x) => x.breadcrumbs.toLowerCase().includes(searchData.toLowerCase())),
    }] : [];

    return (
      <>
        <span
          className="custom-nav search-nav"
          onMouseOver={() => actions.toggleSearchPanel(true)}
          onFocus={() => actions.toggleSearchPanel(true)}
          onMouseLeave={() => actions.toggleSearchPanel(false)}
          onBlur={() => actions.toggleSearchPanel(true)}>
          <span className="fa-cloud">
            <FontAwesomeIcon className="far fa-2xl" icon={faSearch} size="lg" />
          </span>
          <span>{t('SEARCH')}</span>
          <Toggler
            togglePanel={togglePanel}
            actionCb={actions.toggleSearchPanel}
            handleSearchFilter={actions.onSearchFilter}
            routes={this.getRoutes(searchResult)} />
        </span>
        <Modal
          title={t(modalTitle)}
          isOpen={showForm}
          closeModal={() => onShowForm(false)}>
          <div className="remote-assistance-form">
            {modalTitle === 'REMOTE_ASSISTANCE' && <RemoteAssistanceForm />}
            {modalTitle === 'URL_LOOKUP' && <URLLookupForm />}
            {modalTitle === 'BLACKLISTED_IP_CHECK' && <BlacklistedIPCheckForm />}
          </div>
        </Modal>
      </>
    );
  }
}

const mapStateToProps = (state) => ({
  ...state.search_nav,
  ...helpSelectors.baseSelector(state),
  accessPermissions: loginSelectors.accessPermissionsSelector(state),
  configData: loginSelectors.configDataSelector(state),
  searchData: searchNavSelector.searchDataSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    submitTicket: ssoDuck.submitTicket,
    toggleSearchPanel,
    onSearchFilter: handleOnSearchFilter,
    generateToken: ssoDuck.generateToken,
    toggleHelpPanel,
    toggleShowForm,
    setModalTitle,
  }, dispatch);

  return {
    actions,
  };
};

export default withTranslation()(connect(
  mapStateToProps,
  mapDispatchToProps,
)(SearchNav));

export { SearchNav };
