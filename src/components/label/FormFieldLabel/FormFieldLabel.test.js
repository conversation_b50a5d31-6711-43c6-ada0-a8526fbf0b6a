import React from 'react';
import { render } from '@testing-library/react';
import FormFieldLabel from './index';

jest.mock('react-i18next', () => ({
  withTranslation: () => (Component) => Component,
}));

jest.mock('utils/helpers', () => ({
  setToolTipPosition: jest.fn(),
}));

describe('FormFieldLabel component', () => {
  it('renders correctly', () => {
    const { getByText } = render(
      <FormFieldLabel text="Test Label" id="test-label" />,
    );
    expect(getByText('Test Label')).toBeInTheDocument();
  });

  it('renders tooltip correctly', () => {
    const { getByText } = render(
      <FormFieldLabel text="Test Label" id="test-label" tooltip="Test Tooltip" />,
    );
    const tooltip = getByText('Test Tooltip');
    expect(tooltip).toBeInTheDocument();
  });

  it('renders error message correctly', () => {
    const { getByText } = render(
      <FormFieldLabel text="Test Label" id="test-label" error="Test Error" />,
    );
    const error = getByText('Test Error');
    expect(error).toBeInTheDocument();
  });

  it('renders with custom style class', () => {
    const { getByText } = render(
      <FormFieldLabel text="Test Label" id="test-label" styleClass="custom-class" />,
    );
    const label = getByText('Test Label');
    expect(label).toHaveClass('custom-class');
  });

  it('renders with null id', () => {
    const { getByText } = render(
      <FormFieldLabel text="Test Label" id={null} />,
    );
    expect(getByText('Test Label')).toBeInTheDocument();
  });

  it('renders with null text', () => {
    const { queryByText } = render(
      <FormFieldLabel id="test-label" />,
    );
    expect(queryByText('Test Label')).not.toBeInTheDocument();
  });

  it('renders with null error', () => {
    const { queryByText } = render(
      <FormFieldLabel text="Test Label" id="test-label" error={null} />,
    );
    expect(queryByText('Test Error')).not.toBeInTheDocument();
  });
});
