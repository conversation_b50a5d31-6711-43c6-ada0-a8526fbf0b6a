import React from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faMapMarkerAlt } from '@fortawesome/pro-regular-svg-icons';
import i18n from 'utils/i18n';
import { withTranslation } from 'react-i18next';
import { convertZeros } from 'utils/helpers';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import {
  viewCloudConnectorDetails,
  handlePopupOpen,
} from 'ducks/connectorMonitoring';
import Loading from 'components/spinner/Loading';
import ServerError from 'components/errors/ServerError';
import * as dashboardSelector from 'ducks/connectorMonitoring/selectors';
import ReasonDetail from './reasonDetail';

import './index.scss';

const DATA_TIP = 'map-tooltip';

export class ConnectorTooltip extends React.Component {
  static propTypes = {
    isZeroTrustGateway: PropTypes.bool,
    showConnectorToolTip: PropTypes.bool,
    deployAsGateway: PropTypes.bool,
    loadingPopUp: PropTypes.bool,
    actions: PropTypes.shape({
      popupOpen: PropTypes.func,
    }),
    cloudInfo: PropTypes.shape({
      ecId: PropTypes.string,
    }),
    connectorCloudInfo: PropTypes.shape({}),
    cloudConnectorData: PropTypes.shape({
      ecName: PropTypes.string,
      awsRegion: PropTypes.string,
      status: PropTypes.string,
      endpointsCount: PropTypes.string,
      content: PropTypes.shape({}),
      managementIps: PropTypes.shape({}),
      serviceIps: PropTypes.shape({}),
      deploymentType: PropTypes.string,
      group: PropTypes.string,
      location: PropTypes.string,
      managementNw: PropTypes.shape({
        managementIp: PropTypes.shape({
          ipStart: PropTypes.string,
        }),
      }),
      ecInstance: PropTypes.arrayOf(PropTypes.shape({})),
      vmSize: PropTypes.string,
      reason: PropTypes.string,
      availabilityZone: PropTypes.string,
      haStatus: PropTypes.string,
      errorCodes: PropTypes.string,
      lastModifiedTime: PropTypes.string,
      deployAsGateway: PropTypes.bool,
      deviceType: PropTypes.string,
    }),
  };
  
  static defaultProps = {
    isZeroTrustGateway: false,
    showConnectorToolTip: false,
    deployAsGateway: false,
    actions: {},
    cloudInfo: {},
    connectorCloudInfo: {},
    cloudConnectorData: {
      ecName: '',
      awsRegion: '',
      endpointsCount: '',
      status: '',
      content: {},
      managementIps: {},
      managementNw: {},
      ecInstance: [],
      serviceIps: {},
      deploymentType: '',
      group: '',
      location: '',
      vmSize: '',
      reason: '',
      availabilityZone: '',
    },
  };

  async componentDidMount() {
    // const { actions, cloudInfo, connectorCloudInfo } = this.props;
    // const { popupOpen } = actions;
    // const info = (cloudInfo && cloudInfo.ecId) ? cloudInfo : connectorCloudInfo;
    // await popupOpen(info);
  }

  // shouldComponentUpdate(nextProps) {
  //   const { actions, cloudInfo, connectorCloudInfo } = this.props;
  //   const { cloudInfo: nextCloudInfo, connectorCloudInfo: nextConnectorCloudInfo } = nextProps;
  //   const { popupOpen } = actions;
  //   const info = (cloudInfo && cloudInfo.ecId) ? cloudInfo : connectorCloudInfo;
  // const nextInfo = (nextCloudInfo && nextCloudInfo.ecId)
  //     ? nextCloudInfo
  //     : nextConnectorCloudInfo;
  //   console.log('componentDidMount.....nextProps....', { nextInfo, info });
  //   if (nextInfo !== info) popupOpen(nextInfo);
  //   return (nextInfo !== info);
  // }

  render() {
    // const { actions, cloudInfo, connectorCloudInfo } = this.props;
    // console.log('componentDidMount...render......',{cloudInfo, connectorCloudInfo })
    // const { popupOpen } = actions;
    // const info = (cloudInfo && cloudInfo.ecId) ? cloudInfo : '';
    // popupOpen(info);

    const {
      cloudConnectorData, showConnectorToolTip, isZeroTrustGateway, loadingPopUp,
    } = this.props;
    const {
      ecName,
      awsRegion,
      endpointsCount,
      status,
      haStatus,
      availabilityZone,
      managementNw,
      ecInstance,
      deploymentType,
      deployAsGateway,
      deviceType,
      group,
      location,
      vmSize,
      errorCodes,
      lastModifiedTime,
    } = cloudConnectorData;
    if (!showConnectorToolTip || !Object.keys(cloudConnectorData).length) return null;

    if (isZeroTrustGateway) {
      return (
        <ServerError {...this.props}>
          <div className={`${errorCodes ? 'extra-height' : 'auto-height'}`}>
            <div className="padding-0-2em">
              <div className="location-name">
                <span className="tooltip-header-connector">{ecName}</span>
                <span className="tooltip-header-status">|</span>
                <span className="tooltip-header-status">{status}</span>
                <div className="location">
                  <FontAwesomeIcon icon={faMapMarkerAlt} className="fontStyle" />
                  <span className="tooltip-header-loc">{location}</span>
                </div>
              </div>
            </div>
            <hr className="line" />
            <div className="tooltip-content">
              <div className="long-text">
                <div className="tooltip-body-key-min">
                  {i18n.t('DEPLOYMENT_TYPE')}
                </div>
                <div className="tooltip-body-value-min">
                  {deviceType === 'PHYSICAL' ? i18n.t(deviceType) : deploymentType}
                </div>
              </div>
        
              <div className="long-text">
                <div className="tooltip-body-key-min">
                  {i18n.t('NAME')}
                </div>
                <div className="tooltip-body-value-min">
                  {ecName}
                </div>
              </div>

              <div className="long-text">
                <div className="tooltip-body-key-min">
                  {i18n.t('REGION')}
                </div>
                <div className="tooltip-body-value-min">
                  {i18n.t(awsRegion)}
                </div>
              </div>

              <div className="long-text">
                <div className="tooltip-body-key-min">
                  {i18n.t('LOCATION')}
                </div>
                <div className="tooltip-body-value-min">
                  {i18n.t(location)}
                </div>
              </div>

              <div className="long-text">
                <div className="tooltip-body-key-min">
                  {i18n.t('ENDPOINTS')}
                </div>
                <div className="tooltip-body-value-min">
                  {(endpointsCount || 0)}
                </div>
              </div>

              <div className="long-text">
                <div className="tooltip-body-key-min">
                  {i18n.t('SERVICE_STATUS')}
                </div>
                <div className="tooltip-body-value-min">
                  {i18n.t(status)}
                </div>
              </div>

              <div className={`long-text ${errorCodes ? '' : 'hide'}`}>
                <div className="tooltip-body-key-min">{i18n.t('REASON')}</div>
                <div className="tooltip-body-value-reason">
                  <div className="reason-content"><ReasonDetail errorCodes={errorCodes} t={i18n.t} /></div>
                </div>
              </div>
            </div>
          </div>
        </ServerError>
      );
    }

    return (
      <ServerError {...this.props}>
        <div className={`${errorCodes ? 'extra-height' : 'auto-height'}`}>
          <Loading loading={loadingPopUp}>
            <div style={{ padding: '0.2em' }}>
              <div className="location-name">
                <span className="tooltip-header-connector">{ecName}</span>
                <span className="tooltip-header-status">|</span>
                <span className="tooltip-header-status">{status}</span>
                <div className="location">
                  <FontAwesomeIcon icon={faMapMarkerAlt} className="fontStyle" />
                  <span className="tooltip-header-loc">{location}</span>
                </div>
              </div>
            </div>
            <hr className="line" />
            <div className="tooltip-content">
              <div className="long-text">
                <div className="tooltip-body-key-min">
                  {i18n.t('DEPLOYMENT_TYPE')}
                </div>
                <div className="tooltip-body-value-min">
                  {deviceType === 'PHYSICAL' ? i18n.t(deviceType) : deploymentType}
                </div>
              </div>
          
              <div className="long-text">
                <div className="tooltip-body-key-min">
                  {i18n.t('GROUP')}
                </div>
                <div className="tooltip-body-value-min">
                  {group}
                </div>
              </div>
  
              {!(deviceType === 'PHYSICAL' || (deploymentType
              && (deploymentType === 'CENTOS'
                || deploymentType === 'REDHAT_LINUX'
                || deploymentType === 'MICROSOFT_HYPER_V'
                || deploymentType === 'VMWARE_ESXI'))) && (
                <div>
                  <div className="tooltip-body-key-min">
                    {i18n.t('AVAILABILITY_ZONE')}
                  </div>
                  <div className="tooltip-body-value-min">
                    {i18n.t(availabilityZone)}
                  </div>
                </div>
              )}
          
              <div className="long-text">
                <div className="tooltip-body-key-min">
                  {i18n.t('MANAGEMENT_IP')}
                </div>
                <div className="tooltip-body-value-min">
                  {(managementNw && managementNw.managementIp && managementNw.managementIp.ipStart) ? convertZeros(managementNw.managementIp.ipStart) : '-'}
                </div>
              </div>
          
              <div className="long-text">
                <div className="tooltip-body-key-min">
                  {i18n.t('SERVICE_IP')}
                </div>
                <div className="tooltip-body-value-min">
                  {deployAsGateway && 'N/A' }
                  {!deployAsGateway && !ecInstance && (<>-</>)}
                  {!deployAsGateway && ecInstance && ecInstance.map((ec) => (
                    <>
                      {ec.serviceIps && ec.serviceIps.ipStart ? `${convertZeros(ec.serviceIps.ipStart)} ` : '-'}
                    </>
                  ))}
                </div>
              </div>
  
              <div className="long-text">
                <div className="tooltip-body-key-min">
                  {i18n.t('VM_SIZE')}
                </div>
                <div className="tooltip-body-value-min">
                  {deviceType === 'PHYSICAL' ? 'N/A' : i18n.t(vmSize)}
                </div>
              </div>

              {(deviceType === 'PHYSICAL' || (deploymentType
              && (deploymentType === 'CENTOS'
                || deploymentType === 'REDHAT_LINUX'
                || deploymentType === 'VMWARE_ESXI')))
              && (
                <div>
                  <div className="tooltip-body-key-min">
                    {i18n.t('HIGH_AVAILABILITY_STATUS')}
                  </div>
                  <div className="tooltip-body-value-min">
                    {deployAsGateway ? 'N/A' : i18n.t(haStatus)}
                  </div>
                </div>
              )}

              <div className="long-text">
                <div className="tooltip-body-key-min">
                  {i18n.t('LAST_HEARTBEAT_RECEIVED_ON')}
                </div>
                <div className="tooltip-body-value-min">
                  {lastModifiedTime ? moment(lastModifiedTime).format('MMM, DD YYYY hh:mm A') : i18n.t('NA')}
                </div>
              </div>

              <div className={`long-text ${errorCodes ? '' : 'hide'}`}>
                <div className="tooltip-body-key-min">{i18n.t('REASON')}</div>
                <div className="tooltip-body-value-reason">
                  <div className="reason-content"><ReasonDetail errorCodes={errorCodes} t={i18n.t} /></div>
                </div>
              </div>
            </div>
          </Loading>
        </div>
      </ServerError>
    );
  }
}

const mapStateToProps = (state) => ({
  ...dashboardSelector.default(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    viewCloudConnectorDetails,
    popupOpen: handlePopupOpen,
  }, dispatch);
  return { actions };
};

const WrappedConnectorTooltip = withTranslation()(ConnectorTooltip);

export { WrappedConnectorTooltip, DATA_TIP };

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(ConnectorTooltip);
