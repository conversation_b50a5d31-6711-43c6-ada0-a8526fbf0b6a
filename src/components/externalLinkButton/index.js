// @flow

import React from 'react';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faExternalLink } from '@fortawesome/pro-solid-svg-icons';

import './index.scss';

function ExternalLinkButton(props) {
  const { label, targetURL } = props;

  return (
    <a href={targetURL} target="_blank" rel="noopener noreferrer" className="external-link-button">
      <FontAwesomeIcon icon={faExternalLink} />
      {label}
    </a>
  );
}

ExternalLinkButton.propTypes = {
  label: PropTypes.node,
  targetURL: PropTypes.string,
};

ExternalLinkButton.defaultProps = {
  label: null,
  targetURL: null,
};

export default ExternalLinkButton;
