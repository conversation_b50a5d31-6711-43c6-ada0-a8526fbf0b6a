import React from 'react';
import PropTypes from 'prop-types';
import i18n from 'utils/i18n';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faEye } from '@fortawesome/pro-solid-svg-icons';

const noop = () => undefined;
// RowView is responsible to support VIEW feature of the respective row
function RowView(props) {
  const {
    onClickCallBack,
    isReadOnly,
  } = props;

  if (isReadOnly) {
    return (
      <FontAwesomeIcon
        icon={faEye}
        title={i18n.t('VIEW')}
        onClick={onClickCallBack}
        className="view-icon" />
    );
  }
  return '';
}

RowView.propTypes = {
  onClickCallBack: PropTypes.func,
  isReadOnly: PropTypes.bool,
};

RowView.defaultProps = {
  onClickCallBack: noop,
  isReadOnly: false,
};

export default RowView;
