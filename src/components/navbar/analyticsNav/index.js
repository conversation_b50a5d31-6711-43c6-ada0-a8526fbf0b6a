// @flow
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { NavLink } from 'react-router-dom';
import { withTranslation } from 'react-i18next';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChartLine } from '@fortawesome/pro-regular-svg-icons';

import { ANALYTICS_SUB_NAV_ROUTES } from 'config';

import { toggleLogsPanel } from 'ducks/logsNav';
import * as loginSelectors from 'ducks/login/selectors';

export function Toggler({
  togglePanel,
  actionCb,
  routes,
}) {
  if (!togglePanel) return null;
  return (
    <div
      className="admin-panel"
      onMouseOver={() => actionCb(true)}
      onFocus={() => actionCb(true)}
      onMouseLeave={() => actionCb(false)}
      onBlur={() => actionCb(false)}>
      <ul>
        {routes}
      </ul>
    </div>
  );
}

Toggler.propTypes = {
  togglePanel: PropTypes.bool,
  actionCb: PropTypes.func,
  routes: PropTypes.arrayOf(PropTypes.shape()),
};

Toggler.defaultProps = {
  togglePanel: false,
  actionCb: (str) => str,
  routes: [{}],
};

class AnalyticsNav extends Component {
  static propTypes = {
    actions: PropTypes.shape({}),
    data: PropTypes.shape({}),
    accessPermissions: PropTypes.shape({}),
    authType: PropTypes.string,
    toggleLogsPanel: PropTypes.bool,
    location: PropTypes.shape({ pathname: PropTypes.string }),
    t: PropTypes.func,
  };

  static defaultProps = {
    actions: {},
    data: {},
    accessPermissions: {},
    authType: '',
    toggleLogsPanel: false,
    location: null,
    t: (str) => str,
  };

  // routes will come from top level route
  active = (prop) => {
    const { location } = this.props;
    // debugger;
    if (!location) return '';

    const { pathname } = location;

    // we will make following dynamic based on url
    if (pathname.includes(prop.path)) {
      return 'active';
    }

    return '';
  };

  getRoutes = (pageroutes) => pageroutes.map((prop) => {
    const {
      t,
      accessPermissions,
      authType,
    } = this.props;
    
    return (
      <li className={`nav-menu-section ${(prop.perm && accessPermissions[prop.perm] === 'NONE') ? 'hide' : ''}`} key={prop.title}>
        <h3 className="nav-menu-section-header">{prop.title}</h3>
        <ul>
          {
            prop.routes.map((route) => {
              const className = this.active(route);
              if ((route.perm && accessPermissions[route.perm] === 'NONE') || authType === 'SUPPORT_ACCESS_PARTIAL') {
                return null;
              }
              return (
                <li key={route.name} className={`${className} ${route.liclass ? route.liclass : ''}`}>
                  <NavLink to={`${route.redirectTo ? route.redirectTo : (route.layout + route.path)}`} activeclassname="active-menu-item">
                    <span className="nav-menu-list-item-text">{t(route.name)}</span>
                  </NavLink>
                </li>
              );
            })
          }
        </ul>
      </li>
    );
  });
 
  render() {
    const {
      actions: { toggleLogsPanel: toggleActivePanel },
      toggleLogsPanel: togglePanel,
      t,
    } = this.props;

    return (
      <span
        className="custom-nav policy-nav"
        onMouseOver={() => toggleActivePanel(true)}
        onFocus={() => toggleActivePanel(true)}
        onMouseLeave={() => toggleActivePanel(false)}
        onBlur={() => toggleActivePanel(false)}>
        <span className="fa-cloud">
          <FontAwesomeIcon className="far fa-2xl" icon={faChartLine} size="lg" />
        </span>
        <span>{t('ANALYTICS')}</span>
        <Toggler
          togglePanel={togglePanel}
          actionCb={toggleActivePanel}
          routes={this.getRoutes(ANALYTICS_SUB_NAV_ROUTES)} />
      </span>
    );
  }
}

const mapStateToProps = (state) => ({
  ...state.logs_nav,
  accessPermissions: loginSelectors.accessPermissionsSelector(state),
  authType: loginSelectors.authTypeSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    toggleLogsPanel,
  }, dispatch);

  return {
    actions,
  };
};

export default withTranslation()(connect(
  mapStateToProps,
  mapDispatchToProps,
)(AnalyticsNav));

export { AnalyticsNav };
