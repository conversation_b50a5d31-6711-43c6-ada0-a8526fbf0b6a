import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { reduxForm } from 'redux-form';
import { withTranslation } from 'react-i18next';
import Loading from 'components/spinner/Loading';
import * as selectors from 'ducks/helpNav/selectors';

import {
  toggleShowForm, urlLookup,
} from 'ducks/helpNav';

import {
  Url,
} from './GenericFields';

export function BasicCustomAppForm(props) {
  const {
    t, actions, handleSubmit, loading,
  } = props;

  const onSubmit = () => {
    actions.urlLookup();
  };

  const cancelHandle = () => {
    actions.toggleShowForm(false);
  };

  return (
    <Loading loading={loading}>
      <form onSubmit={handleSubmit(onSubmit)} className="adminManagement-form">
        <div className="form-sections-container">
          <Url />
        </div>
        <div className="dialog-footer">
          <div className="dialog-footer-left">
            <button type="button" className="submit" onClick={cancelHandle}>{t('CLOSE')}</button>
          </div>
        </div>
      </form>
    </Loading>
  );
}

BasicCustomAppForm.propTypes = {
  t: PropTypes.func,
  loading: PropTypes.bool,
  handleSubmit: PropTypes.func,
  actions: PropTypes.shape({
    urlLookup: PropTypes.func,
    toggleShowForm: PropTypes.func,
  }),
};
  
BasicCustomAppForm.defaultProps = {
  t: (str) => str,
  loading: false,
  handleSubmit: (str) => str,
  actions: {
    urlLookup: null,
    toggleShowForm: null,
  },
};

const BasicCustomForm = reduxForm({
  form: 'urlLookup',
})(BasicCustomAppForm);

const UrlLookupForm = connect(
  () => ({
    initialValues: {
      urlLookup: '',
    },
  }),
)(BasicCustomForm);

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    urlLookup,
    toggleShowForm,
  }, dispatch);

  return {
    actions,
  };
};

const mapStateToProps = (state) => ({
  ...selectors.baseSelector(state),
  formData: selectors.formValuesSelector(state),
});

// eslint-disable-next-line max-len
export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(UrlLookupForm));
