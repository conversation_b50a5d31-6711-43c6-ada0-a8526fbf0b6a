import React from 'react';
import { render, fireEvent } from '@testing-library/react';
import { I18nextProvider } from 'react-i18next';
import i18n from 'utils/i18n';
import BoxEntity from './index';

describe('BoxEntity component', () => {
  it('renders the text prop', () => {
    const { getByText } = render(
      <I18nextProvider i18n={i18n}>
        <BoxEntity text="Hello World" />
      </I18nextProvider>,
    );

    expect(getByText('Hello World')).toBeInTheDocument();
  });

  it('renders the default tooltip text when no tooltipText prop is provided', () => {
    const { container } = render(
      <I18nextProvider i18n={i18n}>
        <BoxEntity text="Hello World" />
      </I18nextProvider>,
    );

    const tooltip = container.querySelector('[data-tip]');
    fireEvent.mouseOver(tooltip);

    expect(container).toContainHTML('');
  });

  it('renders the default tooltip text when no tooltipText prop is provided', () => {
    const { container } = render(
      <I18nextProvider i18n={i18n}>
        <BoxEntity text="Hello World" tooltip="This is a tooltip" />
      </I18nextProvider>,
    );

    const tooltip = container.querySelector('[data-tip]');
    fireEvent.mouseOver(tooltip);

    expect(container).toContainHTML('This is a tooltip');
  });
});
