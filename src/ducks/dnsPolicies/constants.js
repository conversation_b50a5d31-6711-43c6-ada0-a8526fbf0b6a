// this will be hooked onto the relative state tree.
import { BASE_API_PATH } from 'config';

export const REDUCER_KEY = 'dnsPolicies';

export const DNSPOLICIES_ADV_SETTINGS_API_QUERY = `${BASE_API_PATH}/v1/advancedSettings`;
export const DNSPOLICIES_API = `${BASE_API_PATH}/v1/ecRules/ecDns`;
export const DNSPOLICIES_COUNT = `${BASE_API_PATH}/v1/ecRules/ecDns/count`;

export const DATA_ERROR = 'error';
export const DATA_LOADING = 'loading';
export const MODAL_LOADING = 'modalLoading';
export const DATA_TABLE = 'dnsPoliciesTableData';
export const DATA = 'data';

// this will store data to create and edit form
export const APP_DATA = 'appData';
export const ADD_POLICY = 'addPolicy';
export const DUPLICATE_ROW = 'duplicateRow';
export const VIEW_ONLY = 'viewOnly';
export const MODAL_TITLE = 'modalTitle';
export const SEARCH_DATA = 'searchData';
export const SEARCH_TEXT = 'searchText';
export const CACHED_DATA = 'cachedData';
export const SHOW_FORM = 'showForm';
export const FEATURE_PERMISSIONS = 'featurePermissions';
export const CUSTOM_APPTYPE = 'CUSTOM';
export const IP_ADDRESSES_DATA = 'ipAddressesData';
export const EXPANDED_APPS = 'expandedApps';
export const COLUMNS = 'columns';
export const SHOW_DELETE_FORM = 'showDeleteForm';
export const SELECTED_ROW_ID = 'selectedRowID';
export const SORT_FIELD = 'sortField';
export const SORT_DIRECTION = 'sortDirection';
export const DASHBOARD = 'EDGE_CONNECTOR_DASHBOARD';
export const CLOUD_CONNECTOR_PROVISIONING = 'EDGE_CONNECTOR_CLOUD_PROVISIONING';
export const POLICY_CONFIGURATION = 'EDGE_CONNECTOR_POLICY_CONFIGURATION';
export const ADMIN_MANAGEMENT = 'EDGE_CONNECTOR_ADMIN_MANAGEMENT';
export const LOCATION_MANAGEMENT = 'EDGE_CONNECTOR_LOCATION_MANAGEMENT';
export const TRAFFIC_FORWARDING_DNS = 'EDGE_CONNECTOR_FORWARDING';
export const API_KEY_MANAGEMENT = 'APIKEY_MANAGEMENT';
export const REMOTE_ASSISTANCE_MANAGEMENT = 'REMOTE_ASSISTANCE_MANAGEMENT';
export const SOURCE_IP_ADDRESSES = 'sourceIpAddresses';
export const SOURCE_IP_GROUP_EXCLUSION = 'sourceIpGroupExclusion';
export const DESTINATION_IP_ADDRESSES = 'destinationIpAddresses';
export const NSS_LOGGING = 'EDGE_CONNECTOR_NSS_CONFIGURATION';
export const NUMBER_OF_LINES = 'numberOfLines';
export const NUMBER_OF_ORDERED_LINES = 'numberOfOrderedLines';
export const MORE_ITEMS_LOADING = 'moreItemsLoading';
export const HAS_NEXT_PAGE = 'hasNextPage';
export const PAGES = 'pages';
export const PAGE_NUMBER = 'pageNumber';
export const PAGE_SIZE = 'pageSize';

const pageSize = 10;

const dnsPoliciesColumns = [{
  id: 'order',
  // eslint-disable-next-line no-nested-ternary
  accessor: (row) => ((row.order === -1) ? ('ORDER_DEFAULT') : (row.order <= -2) ? 'PREDEFINED' : row.order),
  Header: 'RULE_ORDER',
  width: 90,
  canSort: true,
  isSorted: true,
  isSortedDesc: false,
  disableReordering: true,
  meta: {
    sortFieldName: 'order',
    skipTranslation: false,
  },
}, {
  id: 'name',
  accessor: (row) => row.name,
  Header: 'RULE_NAME',
  width: 90,
  canSort: true,
  isSorted: true,
  meta: {
    sortFieldName: 'name',
    skipTranslation: false,
  },
}, {
  id: 'criteria',
  accessor: (row) => row.name, // destIpCategories, protocols, locations
  Header: 'RULE_CRITERIA',
  width: 100,
  disableSortBy: true,
  meta: {
    sortFieldName: 'criteria',
    skipTranslation: false,
    customCellType: 'POLICY_CRITERIA',
  },
}, {
  id: 'action',
  accessor: (row) => row.action,
  Header: 'ACTION',
  width: 80,
  disableSortBy: true,
  meta: {
    sortFieldName: 'action',
    skipTranslation: false,
    customCellType: 'DNS_POLICY_ACTION',
  },
}, {
  id: 'state',
  accessor: (row) => row.state, // zpaIpGroup
  Header: 'STATUS',
  width: 40,
  disableSortBy: true,
  meta: {
    sortFieldName: 'action',
    skipTranslation: false,
    customCellType: 'DNS_STATE',
  },
}, {
  id: 'description',
  accessor: (row) => row.description,
  Header: 'DESCRIPTION',
  width: 170,
  disableSortBy: true,
  meta: {
    sortFieldName: 'description',
    skipTranslation: false,
    customCellType: 'DESCRIPTION',
  },
}];

const editColumn = {
  id: 'editor',
  Header: '',
  width: 140,
  disableReordering: true,
  disableResizing: true,
  disableSortBy: true,
  meta: {
    customCellType: 'ROW_ACTIONS',
    pageName: 'CLOUD_CONNECTORS',
  },
};

export const DNS_POLICIES_TABLE_CONFIGS = {
  columns: [...dnsPoliciesColumns, editColumn],
  initialState: {
    expanded: { 0: true },
    // sortBy: [{ id: 'order', desc: false }],
  },
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: true,
  isParentChildTable: true,
};

export const DEFAULT_FORM_DATA = {
  name: '',
  active: true,
  type: CUSTOM_APPTYPE,
};

const sgAppData = [{
  id: '1',
  valid: true,
  value: 'Gateway 1',
}];

export const RESOURCE_DEFAULTS = {
  [DATA_ERROR]: null, // or error object so we can process and show to user
  [DATA_LOADING]: true,
  [APP_DATA]: sgAppData,
  [ADD_POLICY]: false,
  [SOURCE_IP_ADDRESSES]: '',
  [SOURCE_IP_GROUP_EXCLUSION]: 'INCLUDE',
  [DESTINATION_IP_ADDRESSES]: '',
  [SHOW_FORM]: false,
  [DATA]: {},
  [DATA_TABLE]: [],
  [SHOW_DELETE_FORM]: false,
  [SELECTED_ROW_ID]: null,
  [IP_ADDRESSES_DATA]: {},
  [EXPANDED_APPS]: [],
  [COLUMNS]: [...dnsPoliciesColumns, editColumn],
  [MODAL_LOADING]: false,
  [SORT_FIELD]: 'order',
  [SORT_DIRECTION]: 'ruleExecution',
  [MORE_ITEMS_LOADING]: false,
  // [PAGE_NUMBER]: 1,
  [PAGE_SIZE]: pageSize,
  [PAGES]: [],
  [NUMBER_OF_LINES]: 0,
  [NUMBER_OF_ORDERED_LINES]: 0,
};

export const DEFAULTS = {
  [DATA_ERROR]: null, // or error object so we can process and show to user
  [DATA_LOADING]: true,
  [APP_DATA]: sgAppData,
  [ADD_POLICY]: false,
  [SOURCE_IP_ADDRESSES]: '',
  SOURCE_IP_GROUP_EXCLUSION: 'INCLUDE',
  [DESTINATION_IP_ADDRESSES]: '',
  [SHOW_FORM]: false,
  [DATA]: {},
  [DATA_TABLE]: [],
  [SHOW_DELETE_FORM]: false,
  [SELECTED_ROW_ID]: null,
  [IP_ADDRESSES_DATA]: {},
  [EXPANDED_APPS]: [],
  [COLUMNS]: [...dnsPoliciesColumns, editColumn],
  [MODAL_LOADING]: false,
  // [SORT_FIELD]: 'order',
  // [SORT_DIRECTION]: 'ruleExecution',
  [MORE_ITEMS_LOADING]: false,
  // [PAGE_NUMBER]: 1,
  [PAGE_SIZE]: pageSize,
  [PAGES]: [],
  // [NUMBER_OF_LINES]: 0,
};

// default state for this page/widget/component
export const DEFAULT_STATE = {
  ...RESOURCE_DEFAULTS,
};

// For testing...to be used later
export const DUMMY_STORE_DATA = {
  [REDUCER_KEY]: {
    ...DEFAULT_STATE,
  },
};

export const isDisableablePredefinied = (rule) => {
  if (!rule) return false;
  if (rule && ['ZPA Resolver'].includes(rule.name)) return true;
  return false;
};

export const isWanCtrRule = (rule) => {
  if (!rule) return false;
  if (rule && ['Redirect Resolution of Zscaler Domains to WAN CTR'].includes(rule.name)) return true;
  return false;
};

export const isDisableablePredefiniedZTW3626 = (rule) => {
  if (!rule) return false;
  if (rule && ['Redirect Resolution of Zscaler Domains to WAN CTR'].includes(rule.name)) return true;
  return false;
};
