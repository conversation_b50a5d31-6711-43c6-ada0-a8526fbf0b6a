import {
  createAction,
} from 'ducks/generics';
import * as constants from './constants';
import actionTypes from './action-types';

export { REDUCER_KEY } from './constants';

const actionTypeExists = Object.keys(actionTypes).reduce((hash, key) => ({
  ...hash,
  [actionTypes[key]]: true,
}), {});

export const reducer = (state = constants.DEFAULT_STATE, action = {}) => {
  if (actionTypeExists[action.type]) {
    return {
      ...state,
      ...action.payload,
    };
  }
  return state;
};
export default reducer;

const boundDataLoadSuccess = createAction(actionTypes.DATA_LOAD_SUCCESS);

export const getDeviceLogsData = () => (dispatch) => {
  dispatch(boundDataLoadSuccess({
    [constants.DATA]: constants.DATA,
    loading: false,
  }));
};
