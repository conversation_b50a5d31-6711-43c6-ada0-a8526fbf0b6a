import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import Tooltip from 'components/tooltip/ToolTipPane';
import PropTypes from 'prop-types';
import React, { useEffect } from 'react';
import { noop } from 'utils/lodash';

let setPosition;
let tooltipTimeout;
function PrimaryButton(props) {
  const [isActive, setIsActive] = React.useState(false);
  const buttonRef = React.useRef();
  const {
    label, onActionCb, cclass, enable, icon, preIcon, preIconComponent, tooltip, getRef,
  } = props;
  const keepTooltipVisible = () => {
    clearTimeout(tooltipTimeout);
  };

  useEffect(() => {
    if (getRef) {
      getRef(buttonRef);
    }
  }, []);

  useEffect(() => {
    if (isActive) {
      if (setPosition) {
        setPosition(buttonRef.current, { top: -20, left: 30 });
      }
    }
  }, [isActive]);

  const handleOnMouseOver = () => {
    if (!isActive) {
      setIsActive(true);
    } else {
      keepTooltipVisible();
    }
  };

  const handleOnMouseOut = () => {
    tooltipTimeout = setTimeout(() => {
      setIsActive(false);
    }, 300);
  };
  return (
    <>
      <button
        type="submit"
        ref={buttonRef}
        onMouseOver={handleOnMouseOver}
        onMouseOut={handleOnMouseOut}
        onBlur={noop}
        onFocus={noop}
        className={`button primary -js-password ${cclass} ${enable ? '' : 'disabled'}`}
        onClick={() => onActionCb()}
        disabled={!enable && !tooltip}>
        {preIcon && <i className={`far ${preIcon}`}></i>}
        {!!preIconComponent && <FontAwesomeIcon icon={preIconComponent} />}
        {label}
        {icon && <i className={`far ${icon}`}></i>}
      </button>
      {(tooltip) ? (
        <Tooltip
          tooltip={tooltip}
          isActive={isActive}
          tooltipOptions={{
            isValidation: true,
          }}
          setPosition={(hover) => { setPosition = hover; }}
          openTooltip={keepTooltipVisible}
          closeTooltip={handleOnMouseOut} />
      ) : (
        ''
      )}
    </>
  );
}

PrimaryButton.propTypes = {
  label: PropTypes.string,
  cclass: PropTypes.string,
  onActionCb: PropTypes.func,
  getRef: PropTypes.func,
  enable: PropTypes.bool,
  tooltip: PropTypes.string,
  icon: PropTypes.string,
  preIcon: PropTypes.string,
  preIconComponent: PropTypes.shape({}),
};

PrimaryButton.defaultProps = {
  label: '',
  cclass: '',
  onActionCb: noop,
  enable: true,
  icon: '',
  preIcon: '',
};

export default PrimaryButton;
