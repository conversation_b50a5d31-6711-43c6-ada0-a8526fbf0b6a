/* eslint-disable no-console */

const childProcess = require('child_process');

const { exec } = childProcess;

const MAC_OS = 'darwin';

let command;

if (process.platform === MAC_OS) {
  command = 'TIMING=1 node ./node_modules/eslint/bin/eslint.js --fix .';
} else {
  command = 'set TIMING=1 && node ./node_modules/eslint/bin/eslint.js --fix .';
}

console.log(`RUNNING LINTER FOR PLATFORM: ${process.platform}`);
console.log(`LINT COMMAND: ${command}`);

exec(command, (error, stdout, stderr) => {
  if (error || stderr) {
    console.log('LINTER STDERR:\n', stderr);
    console.log('LINTER STDOUT:\n', stdout);
    console.log('LINTER ERROR:\n', error);
    process.exit(1);
  } else {
    console.log('LINTER STDOUT:\n', stdout);
  }
});
