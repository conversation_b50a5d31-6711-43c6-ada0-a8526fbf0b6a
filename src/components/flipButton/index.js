// @flow
import React from 'react';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlusCircle, faAngleDown, faAngleUp } from '@fortawesome/pro-solid-svg-icons';
import FlipMenu from './FlipMenu';
import './index.scss';

function FlipButton(props) {
  const {
    label, showAddIcon, clickCallback, show,
  } = props;

  return (
    <>
      <button onClick={clickCallback} type="button" className="flip-button">
        <FontAwesomeIcon icon={faPlusCircle} className={`${showAddIcon ? '' : 'hide'}`} />
        {label}
        <FontAwesomeIcon icon={faAngleUp} className={`${!show ? 'fal fa-1x' : 'hide'}`} />
        <FontAwesomeIcon icon={faAngleDown} className={`${show ? 'fal fa-1x' : 'hide'}`} />
      </button>
      <FlipMenu {...props} />
    </>
  );
}

FlipButton.propTypes = {
  label: PropTypes.node,
  clickCallback: PropTypes.func,
  show: PropTypes.bool,
  showAddIcon: PropTypes.bool,
};

FlipButton.defaultProps = {
  label: null,
  clickCallback: null,
  show: true,
  showAddIcon: false,
};

export default FlipButton;
