import PropTypes from 'prop-types';
import React from 'react';
import { noop } from 'utils/lodash';

import { withTranslation } from 'react-i18next';

import SectionLabel from '../label/SectionLabel';

export function RadioButton(props) {
  const {
    propertyName, list, actionCallback, customAttributeClass, t, isReadOnly,
    defaultValue, label, tooltip, disabled, throwError, errorTooltip,
  } = props;

  if (isReadOnly) {
    const selectedItem = list.filter((item) => (
      item.classNames && item.classNames.includes('active')
    )
      || item.value === defaultValue)
      .map((item) => t(item.displayText)).join(', ');
    return (
      <span className="radio-button-container form-error">
        {selectedItem}
      </span>
    );
  }

  const callback = (propName, item) => {
    if (disabled) return;
    if (item.disabled) return;

    actionCallback(propName, item);
  };

  return (
    <>
      {label && (
        <SectionLabel
          label={label}
          tooltip={tooltip}
          isInvalid={throwError}
          infoText={throwError ? errorTooltip : ''}
          addInfo />
      )}
      <span className={`radio-button-container ${customAttributeClass} ${disabled ? 'disabled' : ''} form-error ${throwError ? 'invalid' : ''}`}>
        {list.map((item) => (
          <button
            key={item.id}
            className={`radio-button ${item?.classNames?.join(' ')} ${item.disabled ? 'disabled' : ''} ${item.value === defaultValue ? 'active' : ''}`}
            type="button"
            onClick={() => callback(propertyName, item)}>
            <i className="far fa-check-circle"></i>
            <span className="radio-button-text">{t(item.displayText)}</span>
          </button>
        ))}
      </span>
    </>
  );
}

RadioButton.propTypes = {
  propertyName: PropTypes.string,
  defaultValue: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
  actionCallback: PropTypes.func,
  isReadOnly: PropTypes.bool,
  disabled: PropTypes.bool,
  list: PropTypes.arrayOf(PropTypes.shape()),
  t: PropTypes.func,
  label: PropTypes.string,
  customAttributeClass: PropTypes.string,
  tooltip: PropTypes.string,
  throwError: PropTypes.bool,
  errorTooltip: PropTypes.string,
};

RadioButton.defaultProps = {
  propertyName: 'radio-button',
  actionCallback: noop,
  isReadOnly: false,
  customAttributeClass: '',
  list: [],
  disabled: false,
  t: (str) => str,
  label: '',
  tooltip: '',
};

export default withTranslation()(RadioButton);
