import React from 'react';
import { EntityDropdown } from 'components/entityDropdown';
import i18n from 'utils/i18n';

export const WAN_SELECTION_DATA = [
  { id: 'NONE', name: i18n.t('NONE') },
  { id: 'BALANCED_RULE', name: i18n.t('BALANCED_RULE') },
  { id: 'BESTLINK_RULE', name: i18n.t('BESTLINK_RULE') },
];

export function WanSelectionDropdownList(props) {
  return <EntityDropdown data={WAN_SELECTION_DATA} noTranslation {...props} />;
}

export default WanSelectionDropdownList;
