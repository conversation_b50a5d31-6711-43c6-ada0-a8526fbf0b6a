import React from 'react';
import PropTypes from 'prop-types';

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCloudUpload, faSpinner } from '@fortawesome/free-solid-svg-icons';

const noop = () => undefined;
// RowTestConnectivity is responsible to support Connectivity for the Feed
// to the Cloud NSS -> (Only Applicable in Cloud Nanolog Streaming Services)
function RowTestConnectivity(props) {
  const {
    onClickCallBack,
    isTestConnectivityInProgress,
    isTestConnectivityFeedDisabled,
    showTestConnectivityIcon,
  } = props;

  if (showTestConnectivityIcon) {
    if (isTestConnectivityInProgress) {
      return (
        <FontAwesomeIcon
          icon={faSpinner}
          title="TestConnectivity"
          onClick={onClickCallBack}
          className="download-icon" />
      );
    } if (isTestConnectivityFeedDisabled) {
      return (
        <FontAwesomeIcon
          icon={faCloudUpload}
          title="TestConnectivity"
          className="disabled-test-connectivity-icon" />
      );
    }
    return (
      <FontAwesomeIcon
        icon={faCloudUpload}
        title="TestConnectivity"
        onClick={onClickCallBack}
        className="download-icon" />
    );
  }
  return '';
}

RowTestConnectivity.propTypes = {
  onClickCallBack: PropTypes.func,
  isTestConnectivityInProgress: PropTypes.bool,
  isTestConnectivityFeedDisabled: PropTypes.bool,
  showTestConnectivityIcon: PropTypes.bool,
};

RowTestConnectivity.defaultProps = {
  onClickCallBack: noop,
  isTestConnectivityInProgress: false,
  showTestConnectivityIcon: false,
};

export default RowTestConnectivity;
