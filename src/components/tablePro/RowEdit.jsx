import React from 'react';
import PropTypes from 'prop-types';
import i18n from 'utils/i18n';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPencilAlt } from '@fortawesome/pro-regular-svg-icons';

const noop = () => undefined;
// RowEdit is responsible to support EDIT feature of the respective row
function RowEdit(props) {
  const {
    onClickCallBack,
    isEditable,
  } = props;

  if (isEditable) {
    return (
      <FontAwesomeIcon
        icon={faPencilAlt}
        title={i18n.t('EDIT')}
        onClick={onClickCallBack}
        className="pencil-icon" />
    );
  }
  return '';
}

RowEdit.propTypes = {
  onClickCallBack: PropTypes.func,
  isEditable: PropTypes.bool,
};

RowEdit.defaultProps = {
  onClickCallBack: noop,
  isEditable: false,
};

export default RowEdit;
