import { createSelector } from 'reselect';
import { getFormValues, getFormSyncErrors, getFormMeta } from 'redux-form';

import * as constants from './constants';

// get login state
export const baseSelector = (state) => state[constants.REDUCER_KEY];

export const loadingSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA_LOADING],
);

export const errorSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA_ERROR],
);

export const dataSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA] || {},
);

export const dataTableSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA_TABLE] || [],
);

export const checkAllSelector = createSelector(
  baseSelector,
  (state) => state[constants.CHECK_ALL] || false,
);

export const sortFieldSelector = createSelector(
  baseSelector,
  (state) => state[constants.SORT_FIELD] || '',
);
export const sortDirectionSelector = createSelector(
  baseSelector,
  (state) => state[constants.SORT_DIRECTION] || 'desc',
);

export const appDataSelector = createSelector(
  baseSelector,
  (state) => state[constants.APP_DATA],
);

export const nextBuildSelector = createSelector(
  baseSelector,
  (state) => state[constants.NEXT_BUILD],
);

export const modalLoadingSelector = createSelector(
  baseSelector,
  (state) => state[constants.MODAL_LOADING],
);

export const selectedRowIDSelector = createSelector(
  baseSelector,
  (state) => state[constants.SELECTED_ROW_ID],
);

export const showFormSelector = createSelector(
  baseSelector,
  (state) => state[constants.SHOW_FORM],
);

export const showDeleteFormSelector = createSelector(
  baseSelector,
  (state) => state[constants.SHOW_DELETE_FORM],
);

export const showGroupDeleteFormSelector = createSelector(
  baseSelector,
  (state) => state[constants.SHOW_GROUP_DELETE_FORM],
);

export const showDisableFormSelector = createSelector(
  baseSelector,
  (state) => state[constants.SHOW_DISABLE_FORM],
);

export const showEnableFormFormSelector = createSelector(
  baseSelector,
  (state) => state[constants.SHOW_ENABLE_FORM],
);

export const disableTypeSelector = createSelector(
  baseSelector,
  (state) => state[constants.DISABLE_TYPE],
);

export const enableTypeSelector = createSelector(
  baseSelector,
  (state) => state[constants.ENABLE_TYPE],
);

export const selectedBCDataSelector = createSelector(
  baseSelector,
  (state) => state[constants.SELECTED_BC_DATA],
);

export const vmHeathSelector = createSelector(
  baseSelector,
  (state) => state[constants.VM_HEALTH],
);

export const formTypeSelector = createSelector(
  baseSelector,
  (state) => state[constants.FORM_TYPE] || {},
);

export const bulkUpdateSelector = createSelector(
  baseSelector,
  (state) => state[constants.BULK_UPDATE],
);

export const bulkUpdateHasZTGSelector = createSelector(
  baseSelector,
  (state) => state[constants.BULK_UPDATE_HAS_ZTG],
);

export const pageTabsSelector = (state) => {
  const form = getFormValues('cloudConnectorsPage')(state);
  const { pageTabs } = form || {};

  return pageTabs;
};

export const formDataValuesSelector = (state) => getFormValues('CloudConnectorViewForm')(state);
export const formMetaSelector = (state) => getFormMeta('CloudConnectorViewForm')(state);
export const formSyncErrorsSelector = (state) => getFormSyncErrors('CloudConnectorViewForm')(state);

// alway return top level selector
export default baseSelector;
