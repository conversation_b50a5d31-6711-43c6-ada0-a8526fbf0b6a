import React from 'react';
import PropTypes from 'prop-types';

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCode } from '@fortawesome/pro-solid-svg-icons';

const noop = () => undefined;
// RowDiffView is responsible to support VIEW DIFFERENCE feature of the respective row
function RowDiffView(props) {
  const {
    onClickCallBack,
    isViewDiff,
  } = props;

  if (isViewDiff) {
    return (
      <FontAwesomeIcon
        icon={faCode}
        title="View Difference"
        onClick={onClickCallBack}
        className="diff-icon" />
    );
  }
  return '';
}

RowDiffView.propTypes = {
  onClickCallBack: PropTypes.func,
  isViewDiff: PropTypes.bool,
};

RowDiffView.defaultProps = {
  onClickCallBack: noop,
  isViewDiff: false,
};

export default RowDiffView;
