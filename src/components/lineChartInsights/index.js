/* eslint-disable max-len */
/* eslint-disable no-return-assign */
/* eslint-disable react/prop-types */
// @flow
import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import GenericErrorMessage from 'components/errors/ServerError/GenericErrorMessage';

import {
  <PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
} from 'recharts';
import './index.scss';

const RGB_COLOR_CYAN = '#00B5BF';
const RGB_COLOR_BLUE_MAGENTA = '#8884D8';
const RGB_COLOR_GREEN_CYAN = '#82CA9d';
const RGB_COLOR_LIGHT_CYAN = '#8DD1E1';
const RGB_COLOR_LIGHT_GREEN = '#A4DE6C';
const RGB_COLOR_BLACK = '#010101';
const RGB_COLOR_RED = '#FF0000';
const RGB_COLOR_BRIGHTGREEN = '#00FF00';
const RGB_COLOR_BLUE = '#0000FF';
const RGB_COLOR_YELLOW = '#FFFF00';
const RGB_COLOR_PINK = '#FF00FF';
const RGB_COLOR_TURQUOISE = '#00FFFF';
const RGB_COLOR_DARKRED = '#800000';
const RGB_COLOR_GREEN = '#008000';
const RGB_COLOR_DARKBLUE = '#000080';
const RGB_COLOR_DARKYELLOW = '#808000';
const RGB_COLOR_VIOLET = '#800080';
const RGB_COLOR_TEAL = '#008080';
const RGB_COLOR_GRAY25 = '#C0C0C0';
const RGB_COLOR_GRAY50 = '#808080';
const RGB_COLOR_PERIWINKLE_CF = '#9999FF';
const RGB_COLOR_PLUM_CF = '#993366';
const RGB_COLOR_IVORY_CF = '#FFFFCC';
const RGB_COLOR_LIGHTTURQUOISE_CF = '#CCFFFF';
const RGB_COLOR_BLUEGRAY = '#996699';

const strokeColor = [RGB_COLOR_CYAN,
  RGB_COLOR_BLUE_MAGENTA,
  RGB_COLOR_GREEN_CYAN,
  RGB_COLOR_LIGHT_CYAN,
  RGB_COLOR_LIGHT_GREEN,
  RGB_COLOR_BLACK,
  RGB_COLOR_RED,
  RGB_COLOR_BRIGHTGREEN,
  RGB_COLOR_BLUE,
  RGB_COLOR_PINK,
  RGB_COLOR_TURQUOISE,
  RGB_COLOR_DARKRED,
  RGB_COLOR_GREEN,
  RGB_COLOR_DARKBLUE,
  RGB_COLOR_DARKYELLOW,
  RGB_COLOR_VIOLET,
  RGB_COLOR_TEAL,
  RGB_COLOR_GRAY25,
  RGB_COLOR_GRAY50,
  RGB_COLOR_PERIWINKLE_CF,
  RGB_COLOR_PLUM_CF,
  RGB_COLOR_IVORY_CF,
  RGB_COLOR_LIGHTTURQUOISE_CF,
  RGB_COLOR_BLUEGRAY,
  RGB_COLOR_YELLOW,
];

const getSerializedData = (data) => {
  const graphData = {};

  const dataKeys = Object.keys(data);

  dataKeys.forEach((key) => {
    const list = data[key];

    if (Array.isArray(list)) {
      list.forEach((item) => {
        if (item.orgTime) {
          graphData[item.orgTime] = { ...graphData[item.orgTime], ...item, [`${key}_META`]: { ...item } };
        }
      });
    }
  });

  const serializedData = [];

  Object.keys(graphData).forEach((key) => {
    serializedData.push({ ...graphData[key] });
  });

  return serializedData;
};

const initializeStyles = (data) => {
  const styles = [];
  const dataKeys = Object.keys(data);

  dataKeys.forEach((key) => {
    styles.push({ id: key, opacity: 1, strokeWidth: 1 });
  });

  return styles;
};

function CustomizedDot(props) {
  const { cx, cy } = props;
  return (
    <svg
      x={cx - 20}
      y={cy - 15}
      width={40}
      height={40}
      viewBox="0 0 200 200">
    </svg>
  );
}

function ToolTipMessageAction({ selectedDataKey, actionToolTip, DrillDownAction }) {
  return (selectedDataKey && actionToolTip)
    ? <DrillDownAction filter={selectedDataKey} />
    : <></>;
}

function ToolTipMessage({ selectedDataKey, actionToolTip }) {
  return selectedDataKey && !actionToolTip
    ? (
      <div id="tooltip" className="tooltip">
        <div className="ec-tooltip-container">
          <div id="tooltip-top" className="tooltip-top tooltip-top-text">{selectedDataKey}</div>
          <div id="tooltip-bottom" className="tooltip-bottom tooltip-bottom-text">Click for more info</div>
        </div>
      </div>
    )
    : <></>;
}

export function LineChartBasic(props) {
  const {
    data, DrillDownAction, height,
  } = props;

  const [styles, setStyles] = useState([]);
  const [selectedDataKey, setSelectedDataKey] = useState('');
  const [actionToolTip, setActionToolTip] = useState(false);
  const [serializedData, setSerializedData] = useState([]);
 
  useEffect(() => {
    setStyles(initializeStyles(data));
    setSerializedData(getSerializedData(data));
  }, [data]);

  const customClick = (e) => {
    const { dataKey } = e;
    
    setActionToolTip(!actionToolTip);
    setSelectedDataKey(dataKey);
  };

  const handleOnClick = (e, key) => {
    setActionToolTip(!actionToolTip);
    setSelectedDataKey(key);
  };

  const handleMouseEnter = ({ dataKey }) => {
    if (!actionToolTip) {
      setSelectedDataKey(dataKey);
      const newStyle = initializeStyles(data);
      if (newStyle && Array.isArray(newStyle)) {
        setStyles(() => newStyle.map((x) => (x.id === dataKey ? { ...x, strokeWidth: 3 } : { ...x, opacity: 0.3 })));
      }
    }
  };

  const handleLineMouseEnter = (e, dataKey) => {
    if (!actionToolTip) {
      setSelectedDataKey(dataKey);
      const newStyle = initializeStyles(data);
      if (newStyle && Array.isArray(newStyle)) {
        setStyles(() => newStyle.map((x) => (x.id === dataKey ? { ...x, strokeWidth: 3 } : { ...x, opacity: 0.3 })));
      }
    }
  };

  const handleMouseLeave = () => {
    if (!actionToolTip) {
      setSelectedDataKey('');
      setStyles(() => initializeStyles(data));
    }
  };

  const handleLineCharClick = () => {
    if (actionToolTip) {
      setSelectedDataKey('');
      setStyles(() => initializeStyles(data));
    }
    setActionToolTip(!actionToolTip);
  };

  const getOpacity = (key) => (styles && Array.isArray(styles) && styles.find((x) => x.id === key) ? styles.find((x) => x.id === key).opacity : 1);

  const getStrokeWidth = (key) => (styles && Array.isArray(styles) && styles.find((x) => x.id === key) ? styles.find((x) => x.id === key).strokeWidth : 1);

  const handleLegendText = (value, entry) => {
    const { color } = entry;
    return (
      <>
        <span
          style={{
            color, fontSize: 13, opacity: getOpacity(value),
          }}>
          {'\u25A0'}
        </span>
        <span style={{ color, opacity: getOpacity(value) }}>{value}</span>
      </>
    );
  };

  const yAxisProps = {
    type: 'number',
    domain: [0, 'dataMax + 25'],
    tickCount: 25,
  };

  const custHeight = height && height.length ? Number.parseInt(height, 10) : 285;

  if (Array.isArray(serializedData) && !serializedData.length) {
    return <GenericErrorMessage />;
  }

  if (serializedData[0].unit) {
    yAxisProps.label = {
      value: serializedData[0].unitLabel,
      angle: -90,
      position: 'insideLeft',
    };
  }

  return (
    <div
      className="line-chart-container"
      style={{ height: custHeight + 'px' }}>
      <div className="line-chart-field-tooltip-container">
        <ToolTipMessageAction
          DrillDownAction={DrillDownAction}
          selectedDataKey={selectedDataKey}
          actionToolTip={actionToolTip} />
      </div>
      <ResponsiveContainer debounce={1}>
        <LineChart
          width={600}
          height={custHeight}
          data={serializedData}
          onClick={handleLineCharClick}
          margin={{
            top: 5,
            right: 30,
            left: 20,
            bottom: 5,
          }}>
          <XAxis dataKey="name" />
          <YAxis {...yAxisProps} />
          <CartesianGrid strokeDasharray="3 3" />
          <Tooltip
            animationDuration={0}
            content={(
              <ToolTipMessage
                selectedDataKey={selectedDataKey}
                actionToolTip={actionToolTip} />
            )} />

          <Legend
            data-tip
            data-for="ReactToolTipLegend"
            // // content={renderLegend}
            // iconType="square"
            iconSize="0"
            formatter={handleLegendText}
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
            onClick={customClick} />
          {Object.keys(data).map((key, idx) => (
            <Line
              data-tip
              data-for="ReactToolTipLine "
              onClick={(e) => handleOnClick(e, key)}
              onMouseEnter={(e) => handleLineMouseEnter(e, key)}
              onMouseLeave={() => handleMouseLeave()}
              isAnimationActive={false}
              dataKey={key}
              dot={<CustomizedDot />}
              stroke={strokeColor[idx]}
              strokeOpacity={getOpacity(key)}
              strokeWidth={getStrokeWidth(key)}
              key={'chart' + key} />
          ))}

        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}

LineChartBasic.propTypes = {
  clickMoreInfo: PropTypes.func,
  data: PropTypes.shape({}),
  dataKeys: PropTypes.arrayOf(PropTypes.shape({})),
};

LineChartBasic.defaultProps = {
  clickMoreInfo: null,
  data: [],
  dataKeys: [],
};

export default LineChartBasic;
