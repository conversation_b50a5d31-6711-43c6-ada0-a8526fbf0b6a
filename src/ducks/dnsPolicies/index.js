/* eslint-disable max-len */
import { createAction, loading } from 'ducks/generics';
import { genericInterface } from 'utils/http';
import { change, getFormValues } from 'redux-form';
import {
  get, orderBy, isEmpty,
} from 'utils/lodash';
import i18n from 'utils/i18n';
import { notify, notifyError } from 'ducks/notification';
import { checkActivation } from 'ducks/activation';
import { verifyConfigData } from 'utils/helpers';

import * as constants from './constants';

import * as selectors from './selectors';
import actionTypes from './action-types';

export { REDUCER_KEY } from './constants';

const actionTypeExists = Object.keys(actionTypes).reduce((hash, key) => ({
  ...hash,
  [actionTypes[key]]: true,
}), {});

export const reducer = (state = constants.DEFAULT_STATE, action = {}) => {
  if (actionTypeExists[action.type]) {
    return {
      ...state,
      ...action.payload,
    };
  }
  return state;
};
export default reducer;

const boundLoading = loading(actionTypes.DATA_LOADING);
const boundError = createAction(actionTypes.DATA_LOAD_ERROR);
const dataChanged = createAction(actionTypes.DATA_CHANGED);
const boundDataLoadSuccess = createAction(actionTypes.DATA_LOAD_SUCCESS);
const boundModalLoading = createAction(actionTypes.MODAL_LOADING);

export const loader = (initializeProps, newPage = 1) => async (dispatch, getState) => {
  const state = getState();
  const baseSelector = await selectors.baseSelector(state);
  const {
    sortField, sortDirection, searchData, pageSize,
  } = baseSelector;
  let {
    pages, numberOfLines, numberOfOrderedLines,
  } = baseSelector;
  await dispatch(
    dataChanged({
      [constants.DATA_TABLE]: [],
      ...constants.DEFAULTS,
      loading: true,
    }),
  );
  if (initializeProps) {
    state.dnsPolicies.cloudConnectorsData = [];
    state.dnsPolicies.pages = [];
    pages = [];
  }

  const { searchParameter } = getFormValues('searchParameterForm')(state) || {};

  const param = searchData !== '' ? {
    [searchParameter?.id]: searchData,
    page: newPage,
    pageSize,
    sortBy: sortField,
    sortOrder: sortDirection,
  } : {
    page: newPage,
    pageSize,
    sortBy: sortField,
    sortOrder: sortDirection,
  };

  const searchParam = searchData !== '' ? { [searchParameter?.id]: searchData } : { };
  let totalNumberOfLines;
  let predefinedLines;

  try {
    const dnsPoliciesCountApi = genericInterface(constants.DNSPOLICIES_COUNT);
    if (!numberOfLines || !numberOfOrderedLines || initializeProps) {
      try {
        const { data } = await dnsPoliciesCountApi.read(null, {
          params: searchParam,
        });
        numberOfLines = data || 1;
      } catch (error) {
        const errorMsg = get(error, 'response.data.message', i18n.t('ERROR_BRANCH_CLOUD_CONNECTORS_GROUP_ERROR'));
        dispatch(notifyError(errorMsg));
      }
      if (searchParam && searchParam.name) {
        try {
          const { data } = await dnsPoliciesCountApi.read();
          totalNumberOfLines = data || 1;
        } catch (error) {
          const errorMsg = get(error, 'response.data.message', i18n.t('ERROR_BRANCH_CLOUD_CONNECTORS_GROUP_ERROR'));
          dispatch(notifyError(errorMsg));
        }
      } else {
        totalNumberOfLines = numberOfLines;
      }

      try {
        const { data } = await dnsPoliciesCountApi.read(null, {
          params: { predefinedRuleCount: true },
        });
        predefinedLines = data || 1;
      } catch (error) {
        const errorMsg = get(error, 'response.data.message', i18n.t('ERROR_BRANCH_CLOUD_CONNECTORS_GROUP_ERROR'));
        dispatch(notifyError(errorMsg));
      }

      numberOfOrderedLines = totalNumberOfLines - predefinedLines;
    }

    const numberOfPages = Math.ceil(numberOfLines / pageSize);

    dispatch(boundLoading({ loading: true }));

    if (numberOfLines > 0 && pageSize > 0
          && numberOfPages === pages.length
          && !isEmpty(pages[newPage - 1])) {
      dispatch(dataChanged({
        loading: false,
        [constants.DATA_TABLE]: pages[newPage - 1],
        [constants.PAGE_NUMBER]: newPage,
      }));
      return null;
    }
    const newPages = isEmpty(pages) ? Array(numberOfPages) : pages;

    const dnsPoliciesApi = await genericInterface(constants.DNSPOLICIES_API)
      .read(null, {
        params: param,
      });
    const { data } = dnsPoliciesApi || [];
    const finalData = data ? (data.map((x, idx) => {
      const isDisablablePredefinied = constants.isDisableablePredefinied(x);
      const isDisableablePredefiniedZTW3626 = constants.isDisableablePredefiniedZTW3626(x);
      const isWanCtrRule = constants.isWanCtrRule(x);

      if (x.order < 0) {
        return ({
          // ...x, defaultRule: true, showOrder: data.length + 10 + x.order, idx: idx + 1,
          ...x, defaultRule: true, showOrder: x.order, idx: idx + 1,
        });
      }
      return ({
        ...x, showOrder: x.order, idx: idx + 1, isDisablablePredefinied, isWanCtrRule, isDisableablePredefiniedZTW3626,
      });
    })) : [];
    await dispatch(dataChanged({
      [constants.DATA_TABLE]: finalData,
      [constants.CACHED_DATA]: finalData,
      [constants.PAGES]: newPages,
      [constants.PAGE_NUMBER]: newPage,
      [constants.PAGE_SIZE]: pageSize,
      [constants.NUMBER_OF_LINES]: numberOfLines,
      [constants.NUMBER_OF_ORDERED_LINES]: numberOfOrderedLines,
    }));
  } catch (error) {
    const errorMsg = get(error, 'response.data.message');
    dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
    dispatch(boundError(errorMsg));
    dispatch(boundLoading({ loading: false }));
    if (isEmpty(errorMsg)) dispatch(notifyError(i18n.t('ERROR_LOADING_FORWARDING_POLICIES')));
    else dispatch(notifyError(errorMsg));
  }
  return dispatch(boundDataLoadSuccess({
    loading: false,
  }));
};

export const handlePageSize = (pgSize) => async (dispatch) => {
  await dispatch(dataChanged({
    loading: false,
    [constants.PAGE_SIZE]: pgSize,
  }));
  return dispatch(loader(true, 1));
};

export const handleSearchText = (searchText) => async (dispatch) => {
  return dispatch(dataChanged({
    loading: false,
    [constants.SEARCH_TEXT]: searchText,
  }));
};

export const handlePageNumber = (pg = 1) => async (dispatch, getState) => {
  const currentState = await getState();
  const currentPage = await selectors.pageNumberSelector(currentState);
  if (currentPage === pg) return null;

  return dispatch(loader(false, pg));
};

export const toggleAddForm = (toggle, appData = constants.DEFAULT_FORM_DATA, modalTitle) => (dispatch) => {
  dispatch(dataChanged({
    [constants.SHOW_FORM]: toggle,
    [constants.APP_DATA]: appData,
    [constants.MODAL_TITLE]: modalTitle,
    [constants.ADD_POLICY]: true,
    [constants.VIEW_ONLY]: false,
    [constants.DUPLICATE_ROW]: false,
  }));
};

export const toggleEditForm = (toggle, appData = constants.DEFAULT_FORM_DATA, modalTitle) => (dispatch) => {
  dispatch(dataChanged({
    [constants.SHOW_FORM]: toggle,
    [constants.APP_DATA]: appData,
    [constants.MODAL_TITLE]: modalTitle,
    [constants.ADD_POLICY]: false,
    [constants.VIEW_ONLY]: false,
    [constants.DUPLICATE_ROW]: false,
  }));
};

export const toggleDuplicateForm = (toggle, appData = constants.DEFAULT_FORM_DATA, modalTitle) => (dispatch) => {
  dispatch(dataChanged({
    [constants.SHOW_FORM]: toggle,
    [constants.APP_DATA]: appData,
    [constants.MODAL_TITLE]: modalTitle,
    [constants.ADD_POLICY]: true,
    [constants.VIEW_ONLY]: false,
    [constants.DUPLICATE_ROW]: true,
  }));
};

export const toggleViewForm = (toggle, appData = constants.DEFAULT_FORM_DATA, modalTitle) => (dispatch) => {
  dispatch(dataChanged({
    [constants.SHOW_FORM]: toggle,
    [constants.APP_DATA]: appData,
    [constants.MODAL_TITLE]: modalTitle,
    [constants.ADD_POLICY]: false,
    [constants.VIEW_ONLY]: true,
    [constants.DUPLICATE_ROW]: false,
  }));
};

export const toggleClose = () => async (dispatch, getState) => {
  const state = getState();
  await dispatch(dataChanged({
    [constants.DATA_TABLE]: [],
    ...constants.DEFAULTS,
    loading: true,
  }));
  const finalData = selectors.dataTableSelector(state);
  dispatch(dataChanged({
    [constants.SHOW_FORM]: false,
    [constants.DATA_TABLE]: finalData,
    loading: false,
  }));
};

export const toggleDeleteForm = (toggle, data) => async (dispatch, getState) => {
  const state = getState();
  await dispatch(dataChanged({
    [constants.DATA_TABLE]: [],
    ...constants.DEFAULTS,
    loading: true,
  }));
  const finalData = selectors.dataTableSelector(state);
  dispatch(dataChanged({
    [constants.SHOW_DELETE_FORM]: toggle,
    [constants.SELECTED_ROW_ID]: data ? data.id.toString() : null,
    [constants.DATA_TABLE]: finalData,
    loading: false,
  }));
};

const beforeSave = (newRole, appData, formData) => {
  const local = localStorage.getItem('configData');
  const configData = (local && local.length) ? JSON.parse(local) : {};
  const disableExcludeSourceIp = verifyConfigData({ configData, key: 'disableExcludeSourceIp' });

  return ({
    ...(!newRole && { id: appData.id }),
    accessControl: appData.accessControl,
    action: formData.networkTraffic.id, // "ALLOW",
    // applications: [],
    defaultRule: false,
    description: formData.description,
    // dnsRuleRequestTypes: [],
    order: formData.ruleOrder.id,
    rank: newRole ? 7 : appData.rank,
    name: formData.ruleName && formData.ruleName.trim(),
    ecGroups: formData.bcGroups,
    locationGroups: formData.locationGroup || [],
    locations: formData.locationName,
    predefined: false,
    // protocols: ['ANY_RULE'],
    state: formData.ruleStatus.name,
    type: 'EC_DNS',
    ...(formData.networkTraffic.id === 'REDIR_ZPA' && { zpaIpGroup: { id: formData.ipPool.id, name: formData.ipPool.name } }),
    ...(!disableExcludeSourceIp && { sourceIpGroupExclusion: formData.sourceIpGroupExclusion === 'EXCLUDE' }),
    ...(!isEmpty(formData.sourceIpAddresses) && { srcIps: formData.sourceIpAddresses }),
    ...(!isEmpty(formData.srcIpGroups) && { srcIpGroups: formData.srcIpGroups }),
    ...(!isEmpty(formData.ipDestinationGroup) && formData.networkTraffic.id !== 'REDIR_ZPA' && { destIpGroups: formData.ipDestinationGroup.filter((x) => x.parent).map((x) => ({ id: x.id, name: x.name })) }),
    ...(!isEmpty(formData.destinationIpAddresses) && formData.networkTraffic.id !== 'REDIR_ZPA' && { destAddresses: formData.destinationIpAddresses }),
    ...(formData.networkTraffic.id === 'REDIR_REQ' && { dnsGateway: formData.dnsGateway }),
  });
};

export const saveForm = () => async (dispatch, getState) => {
  const state = getState();
  await dispatch(boundLoading({
    loading: true,
    [constants.SHOW_FORM]: false,
  }));
  const appData = selectors.appDataSelector(state);
  const formData = selectors.formValuesSelector(state);
  const baseSelector = selectors.baseSelector(state);
  const { duplicateRow, addPolicy } = baseSelector || {};

  const dnsPoliciesValues = beforeSave(duplicateRow || addPolicy, appData, formData);
  const saveDnsPolicies = genericInterface(constants.DNSPOLICIES_API);
  const apiEndPoint = duplicateRow || addPolicy ? saveDnsPolicies.create : saveDnsPolicies.update;

  try {
    await apiEndPoint(dnsPoliciesValues, {});
    dispatch(notify(i18n.t('SUCCESSFULLY_SAVED')));
  } catch (error) {
    const errorMsg = get(error, 'response.data.message');
    dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
    dispatch(boundError(errorMsg));
    dispatch(boundLoading({
      loading: false,
      [constants.SHOW_FORM]: true,
    }));
    if (isEmpty(errorMsg)) dispatch(notifyError(i18n.t('ERROR_SAVING_ROLE_MANAGEMENT')));
    else dispatch(notifyError(errorMsg));
    return;
  }
  await dispatch(loader(true, 1));
  await dispatch(toggleClose());
  await dispatch(checkActivation());
};

export const deletePolicy = () => async (dispatch, getState) => {
  await dispatch(dataChanged({
    [constants.SHOW_DELETE_FORM]: false,
  }));
  await dispatch(boundLoading({
    loading: true,
    modal_loading: true,
  }));
  const state = getState();
  const rowId = selectors.selectedRowIDSelector(state);

  const apiDnsPolicies = genericInterface(`${constants.DNSPOLICIES_API}/${rowId}`);
  const apiEndPoint = apiDnsPolicies.del;

  try {
    await apiEndPoint(rowId, {});
    dispatch(notify(i18n.t('SUCCESSFULLY_DELETED')));
  } catch (error) {
    const errorMsg = get(error, 'response.data.message');
    dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
    dispatch(boundError(errorMsg));
    dispatch(boundLoading({ loading: false }));
    if (isEmpty(errorMsg)) dispatch(notifyError(i18n.t('ERROR_DELETING_ROLE_MANAGEMENT')));
    else dispatch(notifyError(errorMsg));
  }
  await dispatch(loader(true, 1));
  await dispatch(toggleClose());
  await dispatch(toggleDeleteForm(false));
  await dispatch(checkActivation());
};

export const updateMenu = (columns) => (dispatch) => {
  dispatch(
    dataChanged({
      [constants.COLUMNS]: [],
    }),
  );
  dispatch(
    dataChanged({
      [constants.COLUMNS]: columns,
    }),
  );
};

/* SEARCH */
export const handleOnSearchFilter = (event, searchText) => async (dispatch) => {
  if (searchText.length === 0) {
    await dispatch(dataChanged({
      [constants.SEARCH_DATA]: '',
      [constants.NUMBER_OF_LINES]: null,
    }));
    return dispatch(loader(true, 1));
  }

  if (event.keyCode && event.keyCode === 13 && searchText.length > 0) {
    // eslint-disable-next-line no-unused-vars
    const response = await dispatch(dataChanged({
      [constants.SEARCH_DATA]: searchText,
      [constants.NUMBER_OF_LINES]: null,
    }));
    return dispatch(loader(true, 1));
  }

  return null;
};

export const tabConfiguration = [{
  value: 'GENERAL',
  title: 'GENERAL',
  visible: true,
  to: '?',
  handleClick: (str) => str,
}, {
  value: 'SOURCE',
  title: 'SOURCE',
  visible: true,
  to: '?',
  handleClick: (str) => str,
}, {
  value: 'DNS_APPLICATION',
  title: 'DNS_APPLICATION',
  visible: true,
  to: '?',
  handleClick: (str) => str,
}];

export const toggleSortBy = (sortBy) => async (dispatch, getState) => {
  await dispatch(boundLoading({ loading: true }));
  const currentState = await getState();
  const originalData = await selectors.dataTableSelector(currentState);
  const sortField = await selectors.sortFieldSelector(currentState);
  const sortDirection = await selectors.sortDirectionSelector(currentState);

  const direction = () => {
    if (sortField !== sortBy) return 'desc';
    return sortDirection === 'desc' ? 'asc' : 'desc';
  };

  const sortData = orderBy(originalData, sortBy, direction());
  return dispatch(dataChanged({
    loading: false,
    [constants.DATA_TABLE]: sortData,
    [constants.SORT_FIELD]: sortBy,
    [constants.SORT_DIRECTION]: direction(),
  }));
};

export const handleSourceIpAddresses = (ipAddressList) => (dispatch) => {
  dispatch(dataChanged({
    [constants.SOURCE_IP_ADDRESSES]: ipAddressList,
  }));
  dispatch(change('addEditDnsFilteringRules', 'sourceIpAddresses', ipAddressList));
};

export const handleDestinationIpAddresses = (ipAddressList) => (dispatch) => {
  dispatch(dataChanged({
    [constants.DESTINATION_IP_ADDRESSES]: ipAddressList,
  }));
  dispatch(change('addEditDnsFilteringRules', 'destinationIpAddresses', ipAddressList));
};

export const handleSourceIpGroupExclusionChange = (event) => (dispatch) => {
  dispatch(dataChanged({
    [constants.SOURCE_IP_GROUP_EXCLUSION]: event && event.target && event.target.value,
  }));
  dispatch(change('addEditDnsFilteringRules', 'sourceIpGroupExclusion', event && event.target && event.target.value));
};
