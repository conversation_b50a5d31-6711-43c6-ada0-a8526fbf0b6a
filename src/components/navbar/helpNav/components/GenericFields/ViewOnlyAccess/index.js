import React from 'react';
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';
import { FormFieldLabel, FormSectionLabel } from 'components/label';
import { withTranslation } from 'react-i18next';
import { Field, getFormValues } from 'redux-form';
import { ToggleCheckBox } from 'components/ecToggle';
import SingleDate from 'components/dateTimePicker/singleDate';
import {
  required,
} from 'utils/validations';
import moment from 'moment-timezone';

function ViewOnlyAccess({ viewOnly, form, t }) {
  const data = useSelector((state) => getFormValues(form)(state)) || {};
  const { enableViewOnlyAccess, viewOnlyUntil } = data;

  return (
    <>
      <FormSectionLabel text={t('VIEW_ONLY_ACCESS')} />
      <div className="form-section">
        <div className="g-row">
          <div className="g-left">
            <FormFieldLabel text={t('ENABLE_VIEW_ONLY_ACCESS')} tooltip={t('TOOLTIP_HELP_ENABLE_VIEW_ONLY_REMOTE_ASSISTANCE')} />
            {!viewOnly
              ? (
                <Field
                  id="enableViewOnlyAccess"
                  name="enableViewOnlyAccess"
                  styleClass="ec-toggle-checkbox"
                  component={ToggleCheckBox} />
              ) : (
                <p className="disabled-input">
                  {enableViewOnlyAccess ? t('ENABLED') : t('DISABLED')}
                </p>
              )}
          </div>
        </div>
        {enableViewOnlyAccess && (
          <div className="g-row">
            <div className="g-left">
              <FormFieldLabel text={t('VIEW_ONLY_ENABLED_UNTIL')} tooltip={t('TOOLTIP_HELP_REMOTE_VIEW_ONLY_ACCESS_ENABLED_UNTIL')} />
              {!viewOnly ? (
                <Field
                  id="viewOnlyUntil"
                  name="viewOnlyUntil"
                  component={SingleDate}
                  styleClass="zia"
                  placeholder={t('ENTER_TEXT')}
                  props={{
                    label: t('VIEW_ONLY_ENABLED_UNTIL'),
                  }}
                  validate={[
                    required,
                  ]} />
              ) : (
                <p className="disabled-input">
                  {moment(viewOnlyUntil).format('MM/DD/YYYY')}
                </p>
              )}
            </div>
          </div>
        )}
      </div>
    </>
  );
}

ViewOnlyAccess.propTypes = {
  viewOnly: PropTypes.bool,
  form: PropTypes.string,
  t: PropTypes.func,
};
  
ViewOnlyAccess.defaultProps = {
  viewOnly: true,
  form: '',
  t: (str) => str,
};

export default withTranslation()(ViewOnlyAccess);
