/*! For license information please see vendors601.js.LICENSE.txt */
(self.webpackChunkec_admin_ui=self.webpackChunkec_admin_ui||[]).push([[601],{42601:function(e,t,n){var r=n(66958);e.exports=function(){"use strict";function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,n){return t=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},t(e,n)}function n(e,r,o){return n=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(<PERSON>olean,[],(function(){}))),!0}catch(e){return!1}}()?Reflect.construct:function(e,n,r){var o=[null];o.push.apply(o,n);var a=new(Function.bind.apply(e,o));return r&&t(a,r.prototype),a},n.apply(null,arguments)}function o(e){return function(e){if(Array.isArray(e))return a(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return a(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?a(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function a(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var i=Object.hasOwnProperty,l=Object.setPrototypeOf,c=Object.isFrozen,u=Object.getPrototypeOf,s=Object.getOwnPropertyDescriptor,m=Object.freeze,f=Object.seal,p=Object.create,d="undefined"!=typeof Reflect&&Reflect,h=d.apply,g=d.construct;h||(h=function(e,t,n){return e.apply(t,n)}),m||(m=function(e){return e}),f||(f=function(e){return e}),g||(g=function(e,t){return n(e,o(t))});var y,b=O(Array.prototype.forEach),v=O(Array.prototype.pop),T=O(Array.prototype.push),N=O(String.prototype.toLowerCase),E=O(String.prototype.toString),A=O(String.prototype.match),_=O(String.prototype.replace),S=O(String.prototype.indexOf),w=O(String.prototype.trim),k=O(RegExp.prototype.test),x=(y=TypeError,function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return g(y,t)});function O(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return h(e,t,r)}}function C(e,t,n){var r;n=null!==(r=n)&&void 0!==r?r:N,l&&l(e,null);for(var o=t.length;o--;){var a=t[o];if("string"==typeof a){var i=n(a);i!==a&&(c(t)||(t[o]=i),a=i)}e[a]=!0}return e}function L(e){var t,n=p(null);for(t in e)!0===h(i,e,[t])&&(n[t]=e[t]);return n}function R(e,t){for(;null!==e;){var n=s(e,t);if(n){if(n.get)return O(n.get);if("function"==typeof n.value)return O(n.value)}e=u(e)}return function(e){return r.warn("fallback value for",e),null}}var D=m(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),M=m(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),I=m(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),F=m(["animate","color-profile","cursor","discard","fedropshadow","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),U=m(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover"]),H=m(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),z=m(["#text"]),P=m(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),B=m(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),j=m(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),G=m(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),W=f(/\{\{[\w\W]*|[\w\W]*\}\}/gm),q=f(/<%[\w\W]*|[\w\W]*%>/gm),Y=f(/\${[\w\W]*}/gm),$=f(/^data-[\-\w.\u00B7-\uFFFF]/),K=f(/^aria-[\-\w]+$/),V=f(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),X=f(/^(?:\w+script|data):/i),Z=f(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),J=f(/^html$/i),Q=f(/^[a-z][.\w]*(-[.\w]+)+$/i),ee=function(){return"undefined"==typeof window?null:window};return function t(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:ee(),a=function(e){return t(e)};if(a.version="2.5.6",a.removed=[],!n||!n.document||9!==n.document.nodeType)return a.isSupported=!1,a;var i=n.document,l=n.document,c=n.DocumentFragment,u=n.HTMLTemplateElement,s=n.Node,f=n.Element,p=n.NodeFilter,d=n.NamedNodeMap,h=void 0===d?n.NamedNodeMap||n.MozNamedAttrMap:d,g=n.HTMLFormElement,y=n.DOMParser,O=n.trustedTypes,te=f.prototype,ne=R(te,"cloneNode"),re=R(te,"nextSibling"),oe=R(te,"childNodes"),ae=R(te,"parentNode");if("function"==typeof u){var ie=l.createElement("template");ie.content&&ie.content.ownerDocument&&(l=ie.content.ownerDocument)}var le=function(t,n){if("object"!==e(t)||"function"!=typeof t.createPolicy)return null;var o=null,a="data-tt-policy-suffix";n.currentScript&&n.currentScript.hasAttribute(a)&&(o=n.currentScript.getAttribute(a));var i="dompurify"+(o?"#"+o:"");try{return t.createPolicy(i,{createHTML:function(e){return e},createScriptURL:function(e){return e}})}catch(e){return r.warn("TrustedTypes policy "+i+" could not be created."),null}}(O,i),ce=le?le.createHTML(""):"",ue=l,se=ue.implementation,me=ue.createNodeIterator,fe=ue.createDocumentFragment,pe=ue.getElementsByTagName,de=i.importNode,he={};try{he=L(l).documentMode?l.documentMode:{}}catch(e){}var ge={};a.isSupported="function"==typeof ae&&se&&void 0!==se.createHTMLDocument&&9!==he;var ye,be,ve=W,Te=q,Ne=Y,Ee=$,Ae=K,_e=X,Se=Z,we=Q,ke=V,xe=null,Oe=C({},[].concat(o(D),o(M),o(I),o(U),o(z))),Ce=null,Le=C({},[].concat(o(P),o(B),o(j),o(G))),Re=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),De=null,Me=null,Ie=!0,Fe=!0,Ue=!1,He=!0,ze=!1,Pe=!0,Be=!1,je=!1,Ge=!1,We=!1,qe=!1,Ye=!1,$e=!0,Ke=!1,Ve=!0,Xe=!1,Ze={},Je=null,Qe=C({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]),et=null,tt=C({},["audio","video","img","source","image","track"]),nt=null,rt=C({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),ot="http://www.w3.org/1998/Math/MathML",at="http://www.w3.org/2000/svg",it="http://www.w3.org/1999/xhtml",lt=it,ct=!1,ut=null,st=C({},[ot,at,it],E),mt=["application/xhtml+xml","text/html"],ft=null,pt=l.createElement("form"),dt=function(e){return e instanceof RegExp||e instanceof Function},ht=function(t){ft&&ft===t||(t&&"object"===e(t)||(t={}),t=L(t),ye=ye=-1===mt.indexOf(t.PARSER_MEDIA_TYPE)?"text/html":t.PARSER_MEDIA_TYPE,be="application/xhtml+xml"===ye?E:N,xe="ALLOWED_TAGS"in t?C({},t.ALLOWED_TAGS,be):Oe,Ce="ALLOWED_ATTR"in t?C({},t.ALLOWED_ATTR,be):Le,ut="ALLOWED_NAMESPACES"in t?C({},t.ALLOWED_NAMESPACES,E):st,nt="ADD_URI_SAFE_ATTR"in t?C(L(rt),t.ADD_URI_SAFE_ATTR,be):rt,et="ADD_DATA_URI_TAGS"in t?C(L(tt),t.ADD_DATA_URI_TAGS,be):tt,Je="FORBID_CONTENTS"in t?C({},t.FORBID_CONTENTS,be):Qe,De="FORBID_TAGS"in t?C({},t.FORBID_TAGS,be):{},Me="FORBID_ATTR"in t?C({},t.FORBID_ATTR,be):{},Ze="USE_PROFILES"in t&&t.USE_PROFILES,Ie=!1!==t.ALLOW_ARIA_ATTR,Fe=!1!==t.ALLOW_DATA_ATTR,Ue=t.ALLOW_UNKNOWN_PROTOCOLS||!1,He=!1!==t.ALLOW_SELF_CLOSE_IN_ATTR,ze=t.SAFE_FOR_TEMPLATES||!1,Pe=!1!==t.SAFE_FOR_XML,Be=t.WHOLE_DOCUMENT||!1,We=t.RETURN_DOM||!1,qe=t.RETURN_DOM_FRAGMENT||!1,Ye=t.RETURN_TRUSTED_TYPE||!1,Ge=t.FORCE_BODY||!1,$e=!1!==t.SANITIZE_DOM,Ke=t.SANITIZE_NAMED_PROPS||!1,Ve=!1!==t.KEEP_CONTENT,Xe=t.IN_PLACE||!1,ke=t.ALLOWED_URI_REGEXP||ke,lt=t.NAMESPACE||it,Re=t.CUSTOM_ELEMENT_HANDLING||{},t.CUSTOM_ELEMENT_HANDLING&&dt(t.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(Re.tagNameCheck=t.CUSTOM_ELEMENT_HANDLING.tagNameCheck),t.CUSTOM_ELEMENT_HANDLING&&dt(t.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(Re.attributeNameCheck=t.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),t.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof t.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(Re.allowCustomizedBuiltInElements=t.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),ze&&(Fe=!1),qe&&(We=!0),Ze&&(xe=C({},o(z)),Ce=[],!0===Ze.html&&(C(xe,D),C(Ce,P)),!0===Ze.svg&&(C(xe,M),C(Ce,B),C(Ce,G)),!0===Ze.svgFilters&&(C(xe,I),C(Ce,B),C(Ce,G)),!0===Ze.mathMl&&(C(xe,U),C(Ce,j),C(Ce,G))),t.ADD_TAGS&&(xe===Oe&&(xe=L(xe)),C(xe,t.ADD_TAGS,be)),t.ADD_ATTR&&(Ce===Le&&(Ce=L(Ce)),C(Ce,t.ADD_ATTR,be)),t.ADD_URI_SAFE_ATTR&&C(nt,t.ADD_URI_SAFE_ATTR,be),t.FORBID_CONTENTS&&(Je===Qe&&(Je=L(Je)),C(Je,t.FORBID_CONTENTS,be)),Ve&&(xe["#text"]=!0),Be&&C(xe,["html","head","body"]),xe.table&&(C(xe,["tbody"]),delete De.tbody),m&&m(t),ft=t)},gt=C({},["mi","mo","mn","ms","mtext"]),yt=C({},["foreignobject","annotation-xml"]),bt=C({},["title","style","font","a","script"]),vt=C({},M);C(vt,I),C(vt,F);var Tt=C({},U);C(Tt,H);var Nt=function(e){T(a.removed,{element:e});try{e.parentNode.removeChild(e)}catch(t){try{e.outerHTML=ce}catch(t){e.remove()}}},Et=function(e,t){try{T(a.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){T(a.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e&&!Ce[e])if(We||qe)try{Nt(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},At=function(e){var t,n;if(Ge)e="<remove></remove>"+e;else{var r=A(e,/^[\r\n\t ]+/);n=r&&r[0]}"application/xhtml+xml"===ye&&lt===it&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");var o=le?le.createHTML(e):e;if(lt===it)try{t=(new y).parseFromString(o,ye)}catch(e){}if(!t||!t.documentElement){t=se.createDocument(lt,"template",null);try{t.documentElement.innerHTML=ct?ce:o}catch(e){}}var a=t.body||t.documentElement;return e&&n&&a.insertBefore(l.createTextNode(n),a.childNodes[0]||null),lt===it?pe.call(t,Be?"html":"body")[0]:Be?t.documentElement:a},_t=function(e){return me.call(e.ownerDocument||e,e,p.SHOW_ELEMENT|p.SHOW_COMMENT|p.SHOW_TEXT|p.SHOW_PROCESSING_INSTRUCTION|p.SHOW_CDATA_SECTION,null,!1)},St=function(e){return e instanceof g&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof h)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},wt=function(t){return"object"===e(s)?t instanceof s:t&&"object"===e(t)&&"number"==typeof t.nodeType&&"string"==typeof t.nodeName},kt=function(e,t,n){ge[e]&&b(ge[e],(function(e){e.call(a,t,n,ft)}))},xt=function(e){var t;if(kt("beforeSanitizeElements",e,null),St(e))return Nt(e),!0;if(k(/[\u0080-\uFFFF]/,e.nodeName))return Nt(e),!0;var n=be(e.nodeName);if(kt("uponSanitizeElement",e,{tagName:n,allowedTags:xe}),e.hasChildNodes()&&!wt(e.firstElementChild)&&(!wt(e.content)||!wt(e.content.firstElementChild))&&k(/<[/\w]/g,e.innerHTML)&&k(/<[/\w]/g,e.textContent))return Nt(e),!0;if("select"===n&&k(/<template/i,e.innerHTML))return Nt(e),!0;if(7===e.nodeType)return Nt(e),!0;if(Pe&&8===e.nodeType&&k(/<[/\w]/g,e.data))return Nt(e),!0;if(!xe[n]||De[n]){if(!De[n]&&Ct(n)){if(Re.tagNameCheck instanceof RegExp&&k(Re.tagNameCheck,n))return!1;if(Re.tagNameCheck instanceof Function&&Re.tagNameCheck(n))return!1}if(Ve&&!Je[n]){var r=ae(e)||e.parentNode,o=oe(e)||e.childNodes;if(o&&r)for(var i=o.length-1;i>=0;--i){var l=ne(o[i],!0);l.__removalCount=(e.__removalCount||0)+1,r.insertBefore(l,re(e))}}return Nt(e),!0}return e instanceof f&&!function(e){var t=ae(e);t&&t.tagName||(t={namespaceURI:lt,tagName:"template"});var n=N(e.tagName),r=N(t.tagName);return!!ut[e.namespaceURI]&&(e.namespaceURI===at?t.namespaceURI===it?"svg"===n:t.namespaceURI===ot?"svg"===n&&("annotation-xml"===r||gt[r]):Boolean(vt[n]):e.namespaceURI===ot?t.namespaceURI===it?"math"===n:t.namespaceURI===at?"math"===n&&yt[r]:Boolean(Tt[n]):e.namespaceURI===it?!(t.namespaceURI===at&&!yt[r])&&!(t.namespaceURI===ot&&!gt[r])&&!Tt[n]&&(bt[n]||!vt[n]):!("application/xhtml+xml"!==ye||!ut[e.namespaceURI]))}(e)?(Nt(e),!0):"noscript"!==n&&"noembed"!==n&&"noframes"!==n||!k(/<\/no(script|embed|frames)/i,e.innerHTML)?(ze&&3===e.nodeType&&(t=e.textContent,t=_(t,ve," "),t=_(t,Te," "),t=_(t,Ne," "),e.textContent!==t&&(T(a.removed,{element:e.cloneNode()}),e.textContent=t)),kt("afterSanitizeElements",e,null),!1):(Nt(e),!0)},Ot=function(e,t,n){if($e&&("id"===t||"name"===t)&&(n in l||n in pt))return!1;if(Fe&&!Me[t]&&k(Ee,t));else if(Ie&&k(Ae,t));else if(!Ce[t]||Me[t]){if(!(Ct(e)&&(Re.tagNameCheck instanceof RegExp&&k(Re.tagNameCheck,e)||Re.tagNameCheck instanceof Function&&Re.tagNameCheck(e))&&(Re.attributeNameCheck instanceof RegExp&&k(Re.attributeNameCheck,t)||Re.attributeNameCheck instanceof Function&&Re.attributeNameCheck(t))||"is"===t&&Re.allowCustomizedBuiltInElements&&(Re.tagNameCheck instanceof RegExp&&k(Re.tagNameCheck,n)||Re.tagNameCheck instanceof Function&&Re.tagNameCheck(n))))return!1}else if(nt[t]);else if(k(ke,_(n,Se,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==S(n,"data:")||!et[e])if(Ue&&!k(_e,_(n,Se,"")));else if(n)return!1;return!0},Ct=function(e){return"annotation-xml"!==e&&A(e,we)},Lt=function(t){var n,r,o,i;kt("beforeSanitizeAttributes",t,null);var l=t.attributes;if(l){var c={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:Ce};for(i=l.length;i--;){var u=n=l[i],s=u.name,m=u.namespaceURI;if(r="value"===s?n.value:w(n.value),o=be(s),c.attrName=o,c.attrValue=r,c.keepAttr=!0,c.forceKeepAttr=void 0,kt("uponSanitizeAttribute",t,c),r=c.attrValue,Pe&&k(/((--!?|])>)|<\/(style|title)/i,r))Et(s,t);else if(!c.forceKeepAttr&&(Et(s,t),c.keepAttr))if(He||!k(/\/>/i,r)){ze&&(r=_(r,ve," "),r=_(r,Te," "),r=_(r,Ne," "));var f=be(t.nodeName);if(Ot(f,o,r)){if(!Ke||"id"!==o&&"name"!==o||(Et(s,t),r="user-content-"+r),le&&"object"===e(O)&&"function"==typeof O.getAttributeType)if(m);else switch(O.getAttributeType(f,o)){case"TrustedHTML":r=le.createHTML(r);break;case"TrustedScriptURL":r=le.createScriptURL(r)}try{m?t.setAttributeNS(m,s,r):t.setAttribute(s,r),St(t)?Nt(t):v(a.removed)}catch(e){}}}else Et(s,t)}kt("afterSanitizeAttributes",t,null)}},Rt=function e(t){var n,r=_t(t);for(kt("beforeSanitizeShadowDOM",t,null);n=r.nextNode();)kt("uponSanitizeShadowNode",n,null),xt(n)||(n.content instanceof c&&e(n.content),Lt(n));kt("afterSanitizeShadowDOM",t,null)};return a.sanitize=function(t){var r,o,l,u,m,f=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if((ct=!t)&&(t="\x3c!--\x3e"),"string"!=typeof t&&!wt(t)){if("function"!=typeof t.toString)throw x("toString is not a function");if("string"!=typeof(t=t.toString()))throw x("dirty is not a string, aborting")}if(!a.isSupported){if("object"===e(n.toStaticHTML)||"function"==typeof n.toStaticHTML){if("string"==typeof t)return n.toStaticHTML(t);if(wt(t))return n.toStaticHTML(t.outerHTML)}return t}if(je||ht(f),a.removed=[],"string"==typeof t&&(Xe=!1),Xe){if(t.nodeName){var p=be(t.nodeName);if(!xe[p]||De[p])throw x("root node is forbidden and cannot be sanitized in-place")}}else if(t instanceof s)1===(o=(r=At("\x3c!----\x3e")).ownerDocument.importNode(t,!0)).nodeType&&"BODY"===o.nodeName||"HTML"===o.nodeName?r=o:r.appendChild(o);else{if(!We&&!ze&&!Be&&-1===t.indexOf("<"))return le&&Ye?le.createHTML(t):t;if(!(r=At(t)))return We?null:Ye?ce:""}r&&Ge&&Nt(r.firstChild);for(var d=_t(Xe?t:r);l=d.nextNode();)3===l.nodeType&&l===u||xt(l)||(l.content instanceof c&&Rt(l.content),Lt(l),u=l);if(u=null,Xe)return t;if(We){if(qe)for(m=fe.call(r.ownerDocument);r.firstChild;)m.appendChild(r.firstChild);else m=r;return(Ce.shadowroot||Ce.shadowrootmod)&&(m=de.call(i,m,!0)),m}var h=Be?r.outerHTML:r.innerHTML;return Be&&xe["!doctype"]&&r.ownerDocument&&r.ownerDocument.doctype&&r.ownerDocument.doctype.name&&k(J,r.ownerDocument.doctype.name)&&(h="<!DOCTYPE "+r.ownerDocument.doctype.name+">\n"+h),ze&&(h=_(h,ve," "),h=_(h,Te," "),h=_(h,Ne," ")),le&&Ye?le.createHTML(h):h},a.setConfig=function(e){ht(e),je=!0},a.clearConfig=function(){ft=null,je=!1},a.isValidAttribute=function(e,t,n){ft||ht({});var r=be(e),o=be(t);return Ot(r,o,n)},a.addHook=function(e,t){"function"==typeof t&&(ge[e]=ge[e]||[],T(ge[e],t))},a.removeHook=function(e){if(ge[e])return v(ge[e])},a.removeHooks=function(e){ge[e]&&(ge[e]=[])},a.removeAllHooks=function(){ge={}},a}()}()}}]);