import {
  createStructuredSelector,
  createSelector,
} from 'reselect';
// import * as auditConstant from './constants';

import dropdown from 'ducks/generics/dropdown';

import { REDUCER_KEY } from './constants';

export const auditLogSelector = (state) => state.auditLogs;

export const dropdownSelector = dropdown.createSelector({ REDUCER_KEY });

export const categorySelector = createSelector(
  auditLogSelector,
  (state) => state.selectedCategory,
);
// customQueryParams
export const selector = createStructuredSelector({
  dropdown: dropdownSelector,
  customQuery: categorySelector,
});
export default selector;
