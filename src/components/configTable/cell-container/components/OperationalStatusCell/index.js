/* eslint-disable jsx-a11y/label-has-for */
import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import i18n from 'utils/i18n';
import { getRealOperationalStatus } from 'utils/helpers';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faCheckCircle,
  faCircleExclamation,
  faSpinner,
  faDoNotEnter,
} from '@fortawesome/pro-solid-svg-icons';
  
const defaultProps = {
  value: {},
};
  
function OperationalStatusCell(props) {
  const { value } = props;
  const { isZTG } = value || {};
  const renderGroupStatus = (item) => {
    const status = item.operationalStatus;
    const activeContent = status.ACTIVE || null;
    const inActiveContent = status.INACTIVE || null;
    const enableContent = status.ENABLE || null;
    const enablingContent = status.ENABLING || null;
    const disabledContent = status.DISABLED || null;
    const disablingContent = status.DISABLING || null;

    return (
      <>
        { (activeContent || enableContent) && (
          <span className="os-green-circle">{activeContent + enableContent}</span>
        )}
        { (inActiveContent) && (
          <span className="os-red-circle">{inActiveContent}</span>
        )}
        { (disabledContent || disablingContent || enablingContent) && (
          <span className="os-gray-circle">{disabledContent + disablingContent + enablingContent}</span>
        )}
      </>
    );
  };
  
  const renderVmStatus = (item) => {
    const status = getRealOperationalStatus(item.operationalStatus, item.status);
    if (status === 0) {
      return '';
    }
    return (
      <label htmlFor="status" className="child-checkbox-label">
        { (status === 'ENABLE' || status === 'ACTIVE') && (
          <FontAwesomeIcon
            icon={faCheckCircle}
            className="success-icon" />
        )}
        {(status === 'DISABLED') && (
          <FontAwesomeIcon
            icon={faDoNotEnter}
            className="disabled-icon" />
        )}
        {(status === 'ENABLING' || status === 'DISABLING' || status === 'DELETING') && (
          <FontAwesomeIcon
            icon={faSpinner}
            className="disabling-icon spin" />
        )}
        {(status === 'INACTIVE') && (
          <FontAwesomeIcon
            icon={faCircleExclamation}
            className="inactive-icon" />
        )}
        {i18n.t(status)}
      </label>
    );
  };

  if (isZTG) {
    return (
      <div
        onKeyPress={null}
        // aria-label="Operational Status"
        role="button"
        tabIndex="0">
        {isZTG && (
          <label htmlFor="status" className="child-checkbox-label">
            {i18n.t('NA')}
          </label>
        )}
      </div>
    );
  }
  
  return (
    <div
      onKeyPress={null}
      // aria-label="Operational Status"
      role="button"
      tabIndex="0">
      {
        (typeof value.operationalStatus === 'object')
          ? renderGroupStatus(value)
          : renderVmStatus(value)
      }
    </div>
  );
}
  
OperationalStatusCell.defaultProps = defaultProps;
  
OperationalStatusCell.propTypes = {
  value: PropTypes.shape({}),
};
  
export default withTranslation()(OperationalStatusCell);
