import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import i18n from 'utils/i18n';
import { isEmpty } from 'utils/lodash';
import { getRuleApplicableFor, isRuleOrder5g } from 'ducks/trafficFwdPolicies/constants';
import PolicyItemView from '../PolicyItemView';
import LocationItemView from '../LocationItemView';
import SingleItemView from '../SingleItemView';

function PartnerTopicCloudConnector({ value }) {
  const { status } = value || {};
  const { ccTopicName, msgSent } = status || {};
  
  const renderContent = () => (
    <>
      <PolicyItemView
        label={i18n.t('NAME')}
        value={i18n.t(ccTopicName || '---')} />

      <PolicyItemView
        label={i18n.t('MESSAGE_SENT')}
        value={i18n.t(msgSent)} />
    </>
  );
  
  return (
    <div
      onKeyPress={null}
      // aria-label="Policy Criterias"
      role="button"
      tabIndex="0">
      {renderContent()}
    </div>
  );
}
  
PartnerTopicCloudConnector.propTypes = {
  value: PropTypes.shape({}),
};

PartnerTopicCloudConnector.defaultProps = {
  value: {},
};
  
export default withTranslation()(PartnerTopicCloudConnector);
