const fs = require('fs');
const path = require('path');

console.log('🔍 Validating Edge UX library build...');

// Check if dist files exist
const requiredFiles = ['dist/index.js', 'dist/index.css', 'dist/index.js.map'];
let allFilesExist = true;

requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ Found: ${file}`);
  } else {
    console.error(`❌ Missing required file: ${file}`);
    allFilesExist = false;
  }
});

if (!allFilesExist) {
  process.exit(1);
}

// Check bundle sizes
const jsStats = fs.statSync('dist/index.js');
const cssStats = fs.statSync('dist/index.css');

console.log(`📦 Bundle sizes:`);
console.log(`   JS: ${(jsStats.size / 1024 / 1024).toFixed(2)} MB`);
console.log(`   CSS: ${(cssStats.size / 1024).toFixed(2)} KB`);

// Warn if bundles are too large
if (jsStats.size > 5 * 1024 * 1024) { // 5MB for this complex app
  console.warn(`⚠️  JS bundle is large (${(jsStats.size / 1024 / 1024).toFixed(2)} MB). Consider optimization.`);
}

if (cssStats.size > 1024 * 1024) { // 1MB
  console.warn(`⚠️  CSS bundle is large (${(cssStats.size / 1024).toFixed(2)} KB). Consider optimization.`);
}

// Test imports
try {
  const lib = require('./dist/index.js');
  console.log('✅ Library imports successfully');

  const exports = Object.keys(lib);
  console.log(`📋 Available exports (${exports.length}):`, exports.slice(0, 10).join(', ') + (exports.length > 10 ? '...' : ''));

  // Check for required exports
  const requiredExports = ['AppProvider', 'DashboardPage', 'configureStore', 'http'];
  const missingExports = requiredExports.filter(exp => !exports.includes(exp));

  if (missingExports.length > 0) {
    console.error('❌ Missing required exports:', missingExports);
    process.exit(1);
  }

  console.log('✅ All required exports found');

} catch (error) {
  console.error('❌ Library import failed:', error.message);
  process.exit(1);
}

console.log('🎉 Edge UX Library validation complete!');
