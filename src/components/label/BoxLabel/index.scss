@import 'scss/colors.scss';

.box-label {
  text-transform: uppercase;
  color: $grey1;
  font-size: 16px;	
  font-weight: 500;	
  letter-spacing: 1px;	
  line-height: 19px;
  padding: 5px 10px 0 10px;
  .form-field-tooltip-container {

    color:  red;
    border: 1px solid red;
    // background: var(--semantic-color-background-primary);
    // color:  var(--semantic-color-content-base-primary);
    // border: 1px solid var(--semantic-color-border-base-primary);
		max-width: 550px;
		min-width: 260px;
		overflow: hidden;
		padding: 12px 16px;
		word-wrap: break-word;
		white-space: break-spaces;
		text-transform: none;
	}
}