import React from 'react';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';

function Footer({
  valueLength, totalItemText, removeItemsText, onRemoveAll, disabled,
}) {
  return (
    <div className="list-builder-footer-container">
      <div className="list-builder-footer-left">
        {`${totalItemText} [${valueLength}]`}
      </div>
      <div className="list-builder-footer-right">
        {!disabled && (
          <span
            className="list-builder-remove-all"
            onKeyPress={null}
            role="button"
            tabIndex="0"
            onClick={onRemoveAll}>
            <FontAwesomeIcon icon={faTimes} />
            {' '}
            {removeItemsText}
          </span>
        )}
      </div>
    </div>
  );
}

Footer.propTypes = {
  valueLength: PropTypes.number,
  totalItemText: PropTypes.string,
  removeItemsText: PropTypes.string,
  onRemoveAll: PropTypes.func,
  disabled: PropTypes.bool,
};

export default Footer;
