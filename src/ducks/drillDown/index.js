import * as constants from './constants';
import actionTypes from './action-types';

export { REDUCER_KEY } from './constants';

const actionTypeExists = Object.keys(actionTypes).reduce((hash, key) => ({
  ...hash,
  [actionTypes[key]]: true,
}), {});

export const reducer = (state = constants.DEFAULT_STATE, action = {}) => {
  if (actionTypeExists[action.type]) {
    switch (action.type) {
    case actionTypes.DRILLDOWN_USER_SELECT: {
      return {
        ...state,
        [constants.SELECTED_USER_ID]: action.payload,
      };
    }
    case actionTypes.DRILLDOWN_LOCATION_SELECT: {
      return {
        ...state,
        [constants.SELECTED_LOCATION_ID]: action.payload,
      };
    }
    default:
      return state;
    }
  }
  return state;
};
export default reducer;

export const setSelectedUserId = (id) => ({
  type: actionTypes.DRILLDOWN_USER_SELECT,
  payload: id,
});

export const setSelectedLocationId = (id) => ({
  type: actionTypes.DRILLDOWN_LOCATION_SELECT,
  payload: id,
});
