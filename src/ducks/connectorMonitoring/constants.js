import {
  BASE_API_PATH,
  // TOTAL_EC_DATA, ACTIVE_HEALTH_DATA,
  GEO_DATA,
} from 'config';
import { verifyConfigData } from 'utils/helpers';
import i18n from 'utils/i18n';

// this will be hooked onto the relative state tree.
export const REDUCER_KEY = 'connectormonitoring';
export const EC_HARDWARE_NOTIFICATION = `${BASE_API_PATH}/v1/ecReportData/newAppliances`;
export const EC_REPORT_DATA_STATUS_API_ENDPOINT = `${BASE_API_PATH}/v1/ecReportData/vm/status`;
export const EC_REPORT_DATA_DEVICE_API_ENDPOINT = `${BASE_API_PATH}/v1/ecReportData/device`;
export const EC_REPORT_DATA_DEVICE_TUNNEL_API_ENDPOINT = (ecId) => `${BASE_API_PATH}/v1/ecReportData/tunnel/${ecId}`;
export const EC_REPORT_DATA_DEVICE_INTERFACES_API_ENDPOINT = (groupId, ecId) => `${BASE_API_PATH}/v1/ecgroup/${groupId}/vm/${ecId}/intf`;
export const EC_REPORT_DATA_DEVICE_ROUTES_API_ENDPOINT = (groupId, ecId) => `${BASE_API_PATH}/v1/ecgroup/${groupId}/vm/${ecId}/route`;
export const EC_REPORT_DATA_DEVICE_LITE_API_ENDPOINT = `${BASE_API_PATH}/v1/ecReportData/device/lite`;

const DATA_ERROR = 'error';
export const DATA_LOADING = 'loading';
const LIST = 'list';
export const EC_DATA = 'totalEcData';
export const VM_STATUS_DETAIL_DATA = 'vmStatusDetailsData';
export const ACT_HEALTH_DATA = 'activeHealthData';
export const EC_GEO_DATA = 'geoData';

export const SEARCH_TEXT = 'searchText';
export const HIDDEN_COLUMNS = 'hiddenColumns';
export const SORT_FIELD = 'sortField';
export const SORT_DIRECTION = 'sortDirection';
export const CONNECTOR_DATA_TABLE = 'connectortabledata';
export const CLOUD_CONNECTOR_DATA = 'cloudConnectorData';
export const SHOW_CLOUD_CONNECTOR = 'showCloudConnector';
export const WEBLOG_TIME = 'weblogTime';
export const DEVICE_STATUS_TIME = 'deviceStatuslogTime';
export const STATE_FORM = 'form';
export const HARDWARE_NOTIFICATIONS = 'hardwareNotifications';
export const AWS_REGIONS_DROPDOWN = 'awsRegionsDropdown';
export const AZURE_REGIONS_DROPDOWN = 'azureRegionsDropdown';
export const GCP_REGIONS_DROPDOWN = 'gcpRegionsDropdown';
export const REGIONS_DROPDOWN = 'regionsDropdown';
export const EC_INFO = 'ecInfo';
export const CLOUD_INFO = 'cloudInfo';
export const LOADING_POPUP = 'loadingPopUp';
export const LAST_URL = 'lastUrl';
export const CURRENT_PAGE = 'currentPage';
export const OPEN_DRAWER = 'openDrawer';
export const DRAWER_DATA = 'drawerData';

const pageSize = 50;
const local = localStorage.getItem('configData');
const configData = (local && local.length) ? JSON.parse(local) : {};
const enableAWSASG = verifyConfigData({ configData, key: 'enableASG' });
const enableAzureASG = verifyConfigData({ configData, key: 'enableAzureASG' });
const enableASG = enableAWSASG || enableAzureASG;

export const connectMonitoringColumns = [
  {
    id: 'dataIdx',
    accessor: (row, i) => i + 1,
    Header: 'NUMBER_ABBR',
    width: 60,
    disableReordering: true,
    disableResizing: true,
    disableSortBy: true,
    meta: {
      skipTranslation: false,
      customCellType: 'RENDER_INDEX',
    },
  },
  {
    id: 'ecName',
    accessor: (row) => row.ecName,
    Header: 'NAME',
    width: 180,
    defaultCanSort: true,
    defaultCanFilter: true,
    meta: {
      skipTranslation: false,
    },
  },
  {
    id: 'cloudIcon',
    accessor: (row) => (row.provUrlData && row.provUrlData.cloudProviderType) || '',
    Header: 'TYPE',
    width: 40,
    disableSortBy: true,
    meta: {
      skipTranslation: false,
      customCellType: 'CLOUD_ICON',
    },
  },
  {
    id: 'group',
    accessor: (row) => row.group,
    Header: 'GROUP_ONLY',
    width: 120,
    defaultCanSort: true,
    defaultCanFilter: true,
    meta: {
      skipTranslation: false,
    },
  },
  {
    id: 'location',
    accessor: (row) => row.location || '---',
    Header: 'LOCATION',
    width: 120,
    defaultCanSort: true,
    defaultCanFilter: true,
    meta: {
      skipTranslation: false,
    },
  },
  {
    id: 'geoLocation',
    accessor: (row) => row.geo || '---',
    Header: 'GEO_LOCATION',
    width: 120,
    defaultCanSort: true,
    defaultCanFilter: true,
    meta: {
      skipTranslation: false,
    },
  },
  ...(enableASG ? [{
    id: 'autoScaling',
    accessor: (row) => row.autoScale,
    Header: 'AUTO_SCALING',
    width: 120,
    defaultCanSort: false,
    defaultCanFilter: false,
    meta: {
      skipTranslation: false,
    },
  }] : []),
  {
    id: 'status',
    accessor: (row) => i18n.t(row.status && row.status.toUpperCase()),
    Header: 'STATUS',
    width: 80,
    defaultCanSort: true,
    defaultCanFilter: true,
    meta: {
      skipTranslation: false,
      filterActiveInactive: true,
      filterActiveDisabled: false,
    },
    filter: 'equals',
  },
  {
    id: 'haStatus',
    accessor: (row) => i18n.t(row.haStatus && row.haStatus.toUpperCase()),
    Header: 'HA_STATUS',
    width: 80,
    defaultCanSort: true,
    defaultCanFilter: true,
    meta: {
      skipTranslation: false,
      filterActiveInactive: false,
      filterActiveDisabled: true,
    },
    filter: 'equals',
  },
  {
    id: 'vmSize',
    accessor: (row) => i18n.t(row.vmSize),
    Header: 'VM_SIZE',
    width: 80,
    defaultCanSort: true,
    defaultCanFilter: true,
    meta: {
      skipTranslation: false,
    },
  },
];

const editColumn = {
  id: 'editor',
  Header: '',
  width: 90,
  disableReordering: true,
  disableResizing: true,
  disableSortBy: true,
  meta: {
    customCellType: 'ROW_ACTIONS',
    pageName: 'CLOUD_MONITORING',
  },
};

export const CONNECTOR_MONITORING_TABLE_CONFIGS = {
  columns: [...connectMonitoringColumns, editColumn],
  notMemoed: true,
  initialState: {
    sortBy: [{ id: 'name' }],
    pageSize,
    // hiddenColumns: ['haStatus'],
  },
  disableTableHeaderDrag: true, // disable table column drag and drop
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: true,
};

export const drawerColumns = (t) => [
  {
    id: 'macAddress',
    accessor: (row) => (row?.macAddress) || '---',
    Header: t('MAC_ADDRESS'),
    width: 200,
    defaultCanSort: false,
    disableSortBy: true,
    meta: {
      skipTranslation: true,
    },
  },
  {
    id: 'fixedAddress',
    accessor: (row) => (row?.fixedAddress) || '---',
    Header: t('IP_ADDRESS'),
    width: 200,
    defaultCanSort: false,
    disableSortBy: true,
    meta: {
      skipTranslation: true,
    },
  },
];

export const DRAWER_TABLE_CONFIGS = (t) => ({
  columns: [...(drawerColumns(t))],
  initialState: {
    sortBy: [{ id: 'name' }],
    expanded: { 0: true },
    pageSize,
  },
  disableTableHeaderDrag: true, // disable table column drag and drop
  onFiltersApply: () => null,
  showColumnLayoutConfigurer: true,
  isParentChildTable: true,
});

export const RESET_PIE = {
  [EC_DATA]: {}, // tstEcDeploymentData,
  [ACT_HEALTH_DATA]: {}, // tstEcActiveHealthData,
};

export const RESOURCE_DEFAULTS = {
  [CURRENT_PAGE]: [],
  [DATA_ERROR]: '', // or error object so we can process and show to user
  [DATA_LOADING]: true,
  [HIDDEN_COLUMNS]: ['haStatus'],
  [LIST]: [], // or {}
  [EC_DATA]: {}, // tstEcDeploymentData,
  [ACT_HEALTH_DATA]: {}, // tstEcActiveHealthData,
  [EC_GEO_DATA]: GEO_DATA,
  [CONNECTOR_DATA_TABLE]: [], // tstConnectorTableData,
  [SHOW_CLOUD_CONNECTOR]: false, // True 'll show Cloud Connector Page. Default should be FALSE
  [CLOUD_CONNECTOR_DATA]: {},
  [WEBLOG_TIME]: '',
  [DEVICE_STATUS_TIME]: '',
  [AWS_REGIONS_DROPDOWN]: {},
  [AZURE_REGIONS_DROPDOWN]: {},
  [REGIONS_DROPDOWN]: {},
  [EC_INFO]: {}, // will fetch the EC details on GEO popup / CloudIno
  [CLOUD_INFO]: {}, // cloudinfo to support Refresh
  [HARDWARE_NOTIFICATIONS]: [],
  [LAST_URL]: '',
  [OPEN_DRAWER]: false,
  [DRAWER_DATA]: [],
};

export const DEFAULTS = {
  ...RESOURCE_DEFAULTS,
};

// default state for this page/widget/component
export const DEFAULT_STATE = {
  ...DEFAULTS,
};

// For testing...to be used later
export const DUMMY_STORE_DATA = {
  [REDUCER_KEY]: {
    ...DEFAULT_STATE,
  },
};
