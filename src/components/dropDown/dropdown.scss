@import 'scss/mixins.scss';
@import "scss/colors.scss";

.ec-root-page {
.dropdown {
  position: relative;
  width: 100%;
  height: 100%;
  @include DisplayFlex;
  justify-content: center;
  align-items: center;
  min-height: 40px;
  min-width: 200px;
  border-style: hidden;
  border-bottom: 1px solid var(--semantic-color-content-interactive-primary-default);
  border-radius: unset;

  &.error {
    .dropdown-display {
      border-color: var(--semantic-color-border-status-danger-active);
    }
  }

  .dropdown-display {
    @include DisplayFlex;
    padding: 0;
    width: 260px;
    justify-content: space-between;
    color: var(--semantic-color-content-base-primary);

    .dropdown-value{
      cursor: pointer;
      width: 100%;
      &.disabled {        
        background: var(—surface-fields-disabled);
        color: var(--semantic-color-content-interactive-primary-disabled);
        cursor: not-allowed;
      }
    }
    .dropdown-icon {
      color: var(--semantic-color-content-interactive-primary-default);
      border: none;
      background: transparent;
      &.disabled {        
        background: var(—surface-fields-disabled);
        color: var(--semantic-color-content-interactive-primary-disabled);
        cursor: not-allowed;
      }
    }
  }
  .error-container {
    color: $red4;
    padding-top: 2px;
  }

  .dropdown-no-data-available {
    border: none!important; 
    border-radius: 4px;
    background: var(--DeepBlue-Surface-Default-Surface-00, var(--semantic-color-surface-base-primary));

    /* DropShadow/Bottom/Strong/5 | 16-2-12 */
    box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.16);

    font-size: 13px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;

    padding: 8px;
    min-height: 120px;
    min-width: 300px;
    text-align: center;
    white-space: break-spaces;
    word-wrap: break-word;
    word-break: break-all;
    color: #69696A
  }

  .dropdown-selector {
    position: absolute;
    border: 1px solid var(--semantic-color-border-base-primary);
    top:  40px;
    left: 0;
    background: var(--semantic-color-surface-base-primary);
    z-index: 1;

    .dropdown-lists{
      @include DisplayFlex;

      .dropdown-selected,
      .dropdown-unselected {
        width: 280px;
        height: 396px;
        .__react_component_tooltip {
          position: absolute;
        }

        .dropdown-header {
          height: 45px;
          width: 100%;
          background: var(--semantic-color-content-interactive-primary-default);
          color: var(--semantic-color-content-inverted-base-primary);
          @include DisplayFlex;
          align-items: center;
          padding-left: 16px;
        }
      }

      .dropdown-selected {
        .selected-items {
          height: 350px;
          overflow-x: auto;
          &.no-overflow {
            overflow: hidden;
          }
        }
        .selected {
          padding:  10px 18px  10px 16px;
          @include DisplayFlex;
          justify-content: flex-start;
          color: $grey1;
          min-width: max-content;

          button {
            border: none;
            background: none;
            font-size: 16px;
            color: $grey8;
            margin-right: 4px;
          }
        }
      }

      .dropdown-unselected {
        border-right: 1px solid var(--semantic-color-border-base-primary);
        .unselected-items{
          overflow-x: auto;
          height: 310px;
          .multiselect-list-item.child {
            // padding-left: 0;
            // margin-left: 12px;
          }
          .multiselect-list-item.child.active {
            background: var(--semantic-color-surface-base-primary);
          }
          &.no-overflow {
            overflow: hidden;
          }

          .load-more{
            width: 240px;
            font-size: 13px;
          }

          .unselected {
            padding:  10px 18px  10px 16px;
            &:hover {
              background-color: var(--semantic-color-background-pale);
            }
            .container {
              @include DisplayFlex;

              .label-text{
                padding-top: 1px;
                min-width: max-content;
              }
            }
          }
        }

        .dropdown-search {
            align-items: center;
            background-color: var(--semantic-color-surface-base-primary);
            border-bottom: 1px solid var(--semantic-color-border-base-primary);
            display: flex;
            height: 2.5rem;
            justify-content: space-between;
            padding: 0 1rem;


          input {
            background-color: var(--semantic-color-background-pale);
            border: none;
            border-radius: .25rem;
            color: var(--semantic-color-content-base-primary);
            font-size: .8125rem;
            height: 1.625rem;
            outline: none;
            padding-left: .375rem;
            width: 14.125rem;
          }
          span {
            @include DisplayFlex;

            button {
              border: none;
              background: none;
              font-size: 13px;
              color: var(--semantic-color-content-base-primary);
              padding: 0 5px;
            }
          }
        }
      }
    }

    .dropdown-footer {
      @include DisplayFlex;
      justify-content: space-between;
      align-items: center;
      height:  40px;
      width: 100%;
      background-color: var(--semantic-color-background-pale);
      padding: 0 24px 0 16px;

      button {
        font-size: 13px;
        font-weight: 500;
        width: fit-content;
      }
      .done {
        background-color: var(--semantic-color-content-interactive-primary-default);
        border: none;
        border-radius: .3125rem;
        color: var(--semantic-color-content-immutable-white);
        height: 1.5rem;
        min-width: 3.875rem;
        padding: 0 .25rem;
      }

      .cancel {
        margin-left: 20px;
        padding: 0;
      }
      .select-all, .clear-all {
        width: fit-content;
        margin-right:  10px;
      }
    }
    .flat-button {
      background: none;
      border: none;
      color: var(--semantic-color-content-interactive-primary-default);
      height: 2.1875rem;
      line-height: .875rem;
      min-width: 2.1875rem;
    }
  }
  .dropdown-selector.right {
    right: 0 !important;
    left: inherit;
  }
}
}