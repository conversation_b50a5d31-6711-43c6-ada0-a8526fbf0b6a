import React from 'react';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faToggleOn, faToggleOff } from '@fortawesome/pro-solid-svg-icons';

function ToggleCheckBox(props = {}) {
  const {
    input,
    label,
    disabled,
    styleClass,
  } = props;
  return (
    <div className="check-box-container">
      <label htmlFor="e">
        <input
          id="e"
          type="checkbox"
          disabled={disabled}
          className="hide"
          {...input} />
        <div className="toggle-switch">
          {
            input.value
              ? <FontAwesomeIcon icon={faToggleOn} className={styleClass} />
              : <FontAwesomeIcon icon={faToggleOff} className={styleClass} />
          }
          <span>{label}</span>
        </div>
      </label>
    </div>
  );
}

ToggleCheckBox.defaultProps = {
  label: null,
  disabled: false,
  styleClass: '',
};

ToggleCheckBox.propTypes = {
  label: PropTypes.string,
  disabled: PropTypes.bool,
  input: PropTypes.shape({
    name: PropTypes.string,
    value: PropTypes.any,
    checked: PropTypes.bool,
  }).isRequired,
  styleClass: PropTypes.string,
};

export default ToggleCheckBox;
