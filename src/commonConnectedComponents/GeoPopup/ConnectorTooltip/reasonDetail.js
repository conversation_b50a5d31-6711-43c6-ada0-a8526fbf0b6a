import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';

function ReasonDetail({ errorCodes, t }) {
  // Remove duplicates
  const cleanArray = [...new Set(errorCodes)];
  
  if (cleanArray.length === 0) return <></>;
  
  const result = cleanArray.reduce((acc, x) => `${acc}${t(x)} `, '');
  
  return <>{result}</>;
}

ReasonDetail.propTypes = {
  errorCodes: PropTypes.arrayOf(PropTypes.string),
  t: PropTypes.func,
};
      
export default withTranslation()(ReasonDetail);
