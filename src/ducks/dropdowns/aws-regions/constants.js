import dropdown from 'ducks/generics/dropdown';
import { BASE_API_PATH } from 'config';
import AwsRegion from '@zscaler/ec-domain/json/AwsRegion.json';

export const REDUCER_KEY = 'awsRegionsDropdown';
export const DYN_API_ENDPOINT = `${BASE_API_PATH}/v1/ecReportData/awsRegion`;

export const STATIC_DATA = 'staticData';
export const DROPDOWN = dropdown.createStateConstants();
export const DROPDOWN_DEFAULT_STATE = dropdown.createDefaultState();

const formattedStaticData = Object.values(AwsRegion).map((e) => {
  e.id = e.val;
  return e;
});

export const DEFAULT_STATE = {
  ...DROPDOWN_DEFAULT_STATE,
  [STATIC_DATA]: formattedStaticData,
};

export const DUMMY_STORE_DATA = {
  [REDUCER_KEY]: {
    ...DEFAULT_STATE,
  },
};
