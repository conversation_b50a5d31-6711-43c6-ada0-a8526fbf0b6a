import React from 'react';
import PropTypes from 'prop-types';
import i18n from 'utils/i18n';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faDownload } from '@fortawesome/pro-solid-svg-icons';

const noop = () => undefined;
// RowDownload is responsible to support DOWNLOAD feature of the respective row
function RowDownload(props) {
  const {
    onClickCallBack,
    isDownloadable,
    disableDownload,
  } = props;

  if (isDownloadable) {
    return (
      <FontAwesomeIcon
        icon={faDownload}
        title={i18n.t('DOWNLOAD')}
        onClick={onClickCallBack}
        className={`download-icon ${disableDownload ? 'disabled disable-icon' : ''}`} />
    );
  }
  return '';
}

RowDownload.propTypes = {
  onClickCallBack: PropTypes.func,
  isDownloadable: PropTypes.bool,
  disableDownload: PropTypes.bool,
};

RowDownload.defaultProps = {
  onClickCallBack: noop,
  isDownloadable: false,
  disableDownload: false,
};

export default RowDownload;
