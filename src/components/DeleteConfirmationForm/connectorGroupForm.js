// @flow
import React from 'react';
import { reduxForm } from 'redux-form';
import Loading from 'components/spinner/Loading';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faInfoCircle } from '@fortawesome/pro-light-svg-icons';

import './index.scss';
 
export function BasicCustomAppForm({
  isBCPhysical,
  isCCPage,
  valid,
  t,
  modalLoading,
  selectedRowID,
  handleCancel,
  handleDelete,
  subItems,
}) {
  const onSubmitHandler = (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    handleDelete(selectedRowID, subItems);
  };

  return (
    <form onSubmit={onSubmitHandler} className="add-custom-app-form delete-confirm-form cc-group">
      <Loading loading={modalLoading}>
        <div className="form-sections-container">
          <div className="form-section">
            <p className="are-you-sure-you-want">
              {isCCPage && (subItems.length === 1
                ? t('DELETE_CC_CONFIRMATION')
                : t('DELETE_CC_GROUP_CONFIRMATION'))}
              {!isCCPage && (subItems.length === 1
                ? t('DELETE_BC_CONFIRMATION')
                : t('DELETE_BC_GROUP_CONFIRMATION'))}
            </p>
            <div>
              {subItems.filter((x) => !x.parentId).map((item) => (
                <div key={item.id} className="cc-group-items">
                  <span className="cc-group-title">
                    {isCCPage && t('CLOUD_CONNECTOR_GROUP')}
                    {!isCCPage && t('CLOUD_CONNECTOR_GROUP')}
                  </span>
                  <span className="cc-group-text">
                    {item.name}
                  </span>
                </div>
              ))}

              <div className="cc-group-items">
                {subItems.filter((x) => x.parentId).map((item, index) => (
                  <div key={item.id}>
                    {index === 0 && item.parentId && (
                      <div className="cc-group-title">
                        {isCCPage && (subItems.length === 1
                          ? t('CLOUD_CONNECTOR')
                          : t('CLOUD_CONNECTORS'))}
                        {!isCCPage && (subItems.length === 1
                          ? t('BRANCH_CONNECTOR')
                          : t('BRANCH_CONNECTORS'))}
                      </div>
                    )}
                    <span className="cc-group-text">
                      {item.name}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            <div className="cc-group-delete-alert">
              {(isCCPage || !isBCPhysical) && <FontAwesomeIcon icon={faInfoCircle} className="fa-info-circle-icon" />}
              {' '}
              {isCCPage && t('DELETE_CC_GROUP_CONFIRMATION_ALERT')}
              {!isCCPage && !isBCPhysical && t('DELETE_BC_GROUP_CONFIRMATION_ALERT')}
            </div>
          </div>
        </div>
        <div className="dialog-footer">
          <div className="dialog-footer-left">
            <button type="submit" className={`submit ${!valid ? 'disabled' : ''}`}>{t('DELETE')}</button>
            <button type="button" className="cancel" onClick={() => handleCancel(false)}>{t('CANCEL')}</button>
          </div>
        </div>
      </Loading>
    </form>
  );
}

BasicCustomAppForm.propTypes = {
  isBCPhysical: PropTypes.bool,
  isCCPage: PropTypes.bool,
  handleCancel: PropTypes.func,
  handleDelete: PropTypes.func,
  t: PropTypes.func,
  modalLoading: PropTypes.bool,
  selectedRowID: PropTypes.string,
  valid: PropTypes.bool,
  subItems: PropTypes.arrayOf(),
};

BasicCustomAppForm.defaultProps = {
  isBCPhysical: false,
  isCCPage: false,
  handleCancel: noop,
  handleDelete: noop,
  t: (str) => str,
  modalLoading: false,
  selectedRowID: null,
  valid: true,
  subItems: [],
};

const DeleteConfirmationForm = reduxForm({
  form: 'deleteConfirmationForm',
})(BasicCustomAppForm);

export default (withTranslation()(DeleteConfirmationForm));
