import React from 'react';
import PropTypes from 'prop-types';

import Spinner from '../index';

function Loading(props) {
  const { loading, children } = props;

  return !loading ? children : <Spinner />;
}

Loading.propTypes = {
  loading: PropTypes.bool,
  children: PropTypes.oneOfType([
    PropTypes.arrayOf(PropTypes.node),
    PropTypes.node,
  ]),
};

Loading.defaultProps = {
  loading: true,
  children: null,
};

export default Loading;
