import React from 'react';
import PropTypes from 'prop-types';
import { BASE_ROUTE_PATH } from 'config';
import { useTranslation } from 'react-i18next';
import moment from 'moment-timezone';
import LoadingGIF from 'images/throbber.gif';

function PageLoader(props) {
  const { t } = useTranslation();
  const {
    loading, children, progress, onCancel,
  } = props;

  const myProgress = `${t('NUMBER_OF_RECORDS_FETCHED_SO_FAR')} ${progress.progressItemsComplete || ''} \n
  ${t('RETRIEVING_FOR')} ${progress.progressEndTime ? moment.unix(progress.progressEndTime / 1000).format('LLLL') : ''}
`;
  
  const pageLoading = () => (
    <div className="progress-bar-holder-child-element">
      <div className="progress-bar-child-container">
        <div className="progress-bar-top-row">
          <div className="progress-bar-container-outer progress-bar-child-element">
            <div className="progress-bar-container-inner">
              <img className="progress-bar-throbber" src={LoadingGIF} alt="Loading" />
            </div>
          </div>
          <span id="stop-progress-button" className="progress-bar-cancel-button">
            <span
              id="progress-bar-stop-button-text"
              onKeyPress={null}
              role="button"
              aria-label={t('CANCEL')}
              tabIndex="0"
              onClick={onCancel}>
              { t('CANCEL')}
            </span>
          </span>
        </div>
        <div className="progress-bar-bottom-row">
          <span className="progress-bar-timestamp"></span>
          <div className="progress-bar-recordCount">
            {myProgress}
          </div>
        </div>
      </div>
    </div>
  );

  return !loading ? children : (pageLoading());
}

PageLoader.propTypes = {
  loading: PropTypes.bool,
  children: PropTypes.shape(),
  onCancel: PropTypes.func,
};

PageLoader.defaultProps = {
  loading: true,
  children: null,
  onCancel: null,
};

export default PageLoader;
