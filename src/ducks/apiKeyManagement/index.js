import { createAction, loading } from 'ducks/generics';
import { genericInterface } from 'utils/http';
import { get, omit, isEmpty } from 'utils/lodash';
import { notify, notifyError } from 'ducks/notification';
import { checkActivation } from 'ducks/activation';
import moment from 'moment-timezone';
import * as constants from './constants';
import actionTypes from './action-types';
import * as selectors from './selectors';

export { REDUCER_KEY } from './constants';

const actionTypeExists = Object.keys(actionTypes).reduce((hash, key) => ({
  ...hash,
  [actionTypes[key]]: true,
}), {});

export const reducer = (state = constants.DEFAULT_STATE, action = {}) => {
  if (actionTypeExists[action.type]) {
    return {
      ...state,
      ...action.payload,
    };
  }
  return state;
};
export default reducer;

const boundLoading = loading(actionTypes.DATA_LOADING);
const boundError = createAction(actionTypes.DATA_LOAD_ERROR);
const dataChanged = createAction(actionTypes.DATA_CHANGED);
const boundDataLoadSuccess = createAction(actionTypes.DATA_LOAD_SUCCESS);
const boundModalLoading = createAction(actionTypes.MODAL_LOADING);
 
export const loader = () => async (dispatch) => {
  dispatch(
    boundLoading({
      ...constants.DEFAULTS,
      loading: true,
    }),
  );
  // dispatch(reset('dnsInsightsFiltersForm'));
  // Prev Day;
  try {
    const apiKeyManagement = await genericInterface(constants.APIKEYMANAGEMENT_API_QUERY).read();
    const orgProvisioning = await genericInterface(constants.APIKEYMANAGEMENT_API_QUERY_ORG).read();
    const { data } = apiKeyManagement || [];
    const { data: { cloudName } } = orgProvisioning || [];
    dispatch(boundDataLoadSuccess({
      [constants.CLOUD_NAME]: `connector.${cloudName}`,
      [constants.DATA_TABLE]: data ? data.map((x, idx) => ({ ...x, idx: idx + 1 })) : [],
      loading: false,
    }));
  } catch (error) {
    const errorMsg = get(error, 'response.data.message');
    dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
    dispatch(boundError(errorMsg));
    if (isEmpty(errorMsg)) dispatch(notifyError('ERROR_LOADING_API_KEY_MANAGEMENT'));
    else dispatch(notifyError('errorMsg'));
  }
};

// export const getRoleManagementData = () => (dispatch) => {
//   dispatch(boundDataLoadSuccess({
//     [constants.DATA]: constants.DATA,
//     loading: false,
//   }));
// };

export const updateMenu = (columns) => (dispatch) => {
  dispatch(
    dataChanged({
      [constants.COLUMNS]: [],
    }),
  );
  dispatch(
    dataChanged({
      [constants.COLUMNS]: columns,
    }),
  );
};

export const toggleDeleteConfirmationForm = (toggle, row) => (dispatch) => {
  dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
  dispatch(dataChanged({
    [constants.SHOW_DELETE_FORM]: toggle,
    [constants.SELECTED_ROW]: row,
    loading: false,
  }));
};

export const toggleRegenerateForm = (toggle, row) => (dispatch) => {
  dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
  dispatch(dataChanged({
    [constants.SHOW_REGENERATE_FORM]: toggle,
    [constants.SELECTED_ROW]: row,
    loading: false,
  }));
};

export const toggleEditForm = (toggle, row) => (dispatch) => {
  dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
  dispatch(dataChanged({
    [constants.SHOW_EDIT_FORM]: toggle,
    [constants.SELECTED_ROW]: row,
    loading: false,
  }));
};

export const deleteApiKey = () => async (dispatch, getState) => {
  dispatch(boundLoading({
    [constants.MODAL_LOADING]: true,
    loading: true,
  }));
  const state = getState();
  const row = selectors.selectedRowSelector(state);
  const { id } = row;
  if (!id) dispatch(notifyError('ERROR_DELETING_API_KEY_MANAGEMENT'));
  const deleteApi = genericInterface(constants.APIKEYMANAGEMENT_API_QUERY + '/' + id);

  try {
    await deleteApi.del();
  } catch (error) {
    const errorMsg = get(error, 'response.data.message');
    dispatch(boundLoading({
      [constants.MODAL_LOADING]: false,
      loading: false,
    }));
    dispatch(boundError(errorMsg));
    if (isEmpty(errorMsg)) dispatch(notifyError('ERROR_DELETING_API_KEY_MANAGEMENT'));
    else dispatch(notifyError(errorMsg));
    dispatch(toggleDeleteConfirmationForm(false));
    dispatch(checkActivation());
    
    return;
  }
  dispatch(toggleDeleteConfirmationForm(false));
  dispatch(notify('SUCCESSFULLY_DELETED'));
  dispatch(loader());
  dispatch(checkActivation());
};

export const editApiKey = () => async (dispatch, getState) => {
  dispatch(boundLoading({
    [constants.MODAL_LOADING]: true,
    loading: true,
  }));
  const state = getState();
  const row = selectors.selectedRowSelector(state);
  const data = selectors.dataTableSelector(state);
  const { newApiKey: keyValue } = selectors.formValuesSelector(state);
  const { id } = row;
  const selectedRow = data.find((x) => x.id === id);
  if (!id) dispatch(notifyError('ERROR_EDITING_ADMIN_MANAGEMENT'));

  const editApi = genericInterface(constants.APIKEYMANAGEMENT_API_QUERY);
  const newValue = { ...omit(selectedRow, ['lastModifiedBy', 'idx', 'enabled']), lastModifiedTime: moment().unix(), keyValue };

  try {
    await editApi.update(newValue);
  } catch (error) {
    const errorMsg = get(error, 'response.data.message');
    dispatch(boundLoading({
      [constants.MODAL_LOADING]: false,
      loading: false,
    }));
    dispatch(boundError(errorMsg));
    if (isEmpty(errorMsg)) dispatch(notifyError('ERROR_EDITING_API_KEY_MANAGEMENT'));
    else dispatch(notifyError(errorMsg));
    dispatch(toggleEditForm(false));
    dispatch(checkActivation());
    return;
  }
  dispatch(toggleEditForm(false));
  dispatch(notify('SUCCESSFULLY_SAVED'));
  dispatch(loader());
  dispatch(checkActivation());
};

export const regenerateApiKey = () => async (dispatch, getState) => {
  dispatch(boundLoading({
    [constants.MODAL_LOADING]: true,
    loading: true,
  }));
  const state = getState();
  const row = selectors.selectedRowSelector(state);
  const { id } = row;
  if (!id) dispatch(notifyError('ERROR_REGENERATE_API_KEY_MANAGEMENT'));

  const regenerateApi = genericInterface(constants.APIKEYMANAGEMENT_API_QUERY + '/' + id + '/regenerate');

  try {
    await regenerateApi.create();
  } catch (error) {
    const errorMsg = get(error, 'response.data.message');
    dispatch(boundLoading({
      [constants.MODAL_LOADING]: false,
      loading: false,
    }));
    dispatch(boundError(errorMsg));
    if (isEmpty(errorMsg)) dispatch(notifyError('ERROR_REGENERATE_API_KEY_MANAGEMENT'));
    else dispatch(notifyError(errorMsg));
    dispatch(toggleRegenerateForm(false));
    dispatch(checkActivation());
    return;
  }
  dispatch(toggleRegenerateForm(false));
  dispatch(notify('SUCCESSFULLY_REGENERATED'));
  dispatch(loader());
  dispatch(checkActivation());
};

export const createApiKey = () => async (dispatch) => {
  dispatch(boundLoading({
    [constants.MODAL_LOADING]: true,
    loading: true,
  }));
  const createApi = genericInterface(constants.APIKEYMANAGEMENT_GENERATE);

  try {
    await createApi.create();
  } catch (error) {
    const errorMsg = get(error, 'response.data.message');
    dispatch(boundLoading({
      [constants.MODAL_LOADING]: false,
      loading: false,
    }));
    dispatch(boundError(errorMsg));
    if (isEmpty(errorMsg)) dispatch(notifyError('ERROR_REGENERATE_API_KEY_MANAGEMENT'));
    else dispatch(notifyError(errorMsg));
    dispatch(toggleRegenerateForm(false));
    dispatch(checkActivation());
    
    return;
  }
  dispatch(toggleRegenerateForm(false));
  dispatch(notify('SUCCESSFULLY_REGENERATED'));
  dispatch(loader());
  dispatch(checkActivation());
};
