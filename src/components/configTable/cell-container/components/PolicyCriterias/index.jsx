import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import i18n from 'utils/i18n';
import { isEmpty } from 'utils/lodash';
import { getRuleApplicableFor, isRuleOrder5g } from 'ducks/trafficFwdPolicies/constants';
import PolicyItemView from '../PolicyItemView';
import LocationItemView from '../LocationItemView';
import SingleItemView from '../SingleItemView';

function PolicyCriterias({ value }) {
  const {
    type, ecGroups, zpaIpGroup, locations, locationGroups, applications,
    zpaAppSegments, order, destAddresses, destCountries, destIpGroups, nwServiceGroups, nwServices,
    proxyGateway, srcIps, srcIpGroups, zpaApplicationSegments, zpaApplicationSegmentGroups,
    appServiceGroups, destWorkloadGroups, srcWorkloadGroups,
    // tooltip, protocols,
  } = value || {};
  
  const renderContent = () => (
    <>
      {(type !== 'EC_RDR' && type !== 'EC_SELF') && (
        <PolicyItemView
          label={i18n.t('APPLICATION_SEGMENT')}
          value={!isEmpty(applications) ? applications.map((item) => (i18n.t(item.name))).join() : i18n.t('ANY')} />
      )}
      {type === 'EC_RDR' && !isEmpty(zpaAppSegments) && (
        <PolicyItemView
          label={i18n.t('APPLICATION_SEGMENT')}
          value={zpaAppSegments.map((item) => (i18n.t(item.name))).join(', ')} />
      )}

      {type === 'EC_RDR' && !isEmpty(appServiceGroups) && (
        <PolicyItemView
          label={i18n.t('APPLICATION_SERVICE_GROUPS')}
          tooltip={appServiceGroups.map((item) => (i18n.t(item.name))).join(', ')}
          value=""
          valueObj={appServiceGroups.map((item, idx) => (
            <span key={item.id}>
              <span className={item.deleted ? 'strike-through' : ''}>
                {i18n.t(item.name) }
              </span>
              {(idx === appServiceGroups?.length - 1) ? '' : ', '}
            </span>
          ))} />
      )}

      {type === 'EC_RDR' && !isEmpty(zpaApplicationSegments) && (
        <PolicyItemView
          label={i18n.t('APPLICATION_SEGMENT')}
          tooltip={zpaApplicationSegments.map((item) => (i18n.t(item.name))).join(', ')}
          value=""
          valueObj={zpaApplicationSegments.map((item, idx) => (
            <span key={item.id}>
              <span className={item.deleted ? 'strike-through' : ''}>
                {i18n.t(item.name) }
              </span>
              {(idx === zpaApplicationSegments?.length - 1) ? '' : ', '}
            </span>
          ))} />
      )}

      {type === 'EC_RDR' && !isEmpty(zpaApplicationSegmentGroups) && (
        <PolicyItemView
          label={i18n.t('SEGMENT_GROUPS')}
          value=""
          valueObj={zpaApplicationSegmentGroups.map((item, idx) => (
            <span key={item.id}>
              <span className={item.deleted ? 'strike-through' : ''}>
                {i18n.t(item.name) }
              </span>
              {(idx === zpaApplicationSegmentGroups && zpaApplicationSegmentGroups.length - 1) ? '' : ', '}
            </span>
          ))} />
      )}

      {!isEmpty(ecGroups) && (
        <PolicyItemView
          label={i18n.t('BRANCH_CLOUD_CONNECTOR_GROUP')}
          value={ecGroups.map((item) => (i18n.t(item.name))).join(', ')} />
      )}

      {!isEmpty(destAddresses) && (
        <PolicyItemView
          label={i18n.t('DESTINATION_ADDRESSES')}
          value={destAddresses.map((item) => (i18n.t(item))).join(', ')} />
      )}

      {!isEmpty(destCountries) && (
        <PolicyItemView
          label={i18n.t('DESTINATION_COUNTRIES')}
          value={destCountries.map((item) => (i18n.t(item))).join(', ')} />
      )}

      {!isEmpty(destIpGroups) && (
        <PolicyItemView
          label={i18n.t('DESTINATION_GROUPS')}
          value={destIpGroups.map((item) => (i18n.t(item.name))).join(', ')} />
      )}

      {!isEmpty(destWorkloadGroups) && (
        <PolicyItemView
          label={i18n.t('DESTINATION_WORKLOAD_GROUPS')}
          value={destWorkloadGroups
            .map((item) => (i18n.t(item.name))).join(', ')} />
      )}

      {!isEmpty(zpaIpGroup) && (
        <PolicyItemView
          label={i18n.t('ZPA_IP_POOL')}
          value={i18n.t(zpaIpGroup.name)} />
      )}

      {!isEmpty(locationGroups) && (
        <PolicyItemView
          label={i18n.t('LOCATION_GROUPS')}
          value={locationGroups.map((item) => (i18n.t(item.name))).join(', ')} />
      )}

      {!isEmpty(locations) && (
        <LocationItemView
          label={i18n.t('LOCATIONS')}
          locations={locations} />
      )}

      {!isEmpty(nwServices) && (
        <PolicyItemView
          label={i18n.t('NETWORK_SERVICE')}
          value={nwServices.map((item) => (i18n.t(item.name))).join(', ')} />
      )}

      {!isEmpty(nwServiceGroups) && (
        <PolicyItemView
          label={i18n.t('NETWORK_SERVICE_GROUP')}
          value={nwServiceGroups.map((item) => (i18n.t(item.name))).join(', ')} />
      )}

      {!isEmpty(srcIps) && (
        <PolicyItemView
          label={i18n.t('SOURCE_IP_ADDRESSES')}
          value={srcIps.map((item) => (i18n.t(item))).join(', ')} />
      )}

      {!isEmpty(srcIpGroups) && (
        <PolicyItemView
          label={i18n.t('SOURCE_IP_GROUPS')}
          value={srcIpGroups.map((item) => (i18n.t(item.name))).join(', ')} />
      )}
      
      {!isEmpty(srcWorkloadGroups) && (
        <PolicyItemView
          label={i18n.t('SOURCE_WORKLOAD_GROUPS')}
          value={srcWorkloadGroups.map((item) => (i18n.t(item.name))).join(', ')} />
      )}
      
      {type === 'EC_SELF' && !isEmpty(proxyGateway) && (
        <PolicyItemView
          label=""
          value={(i18n.t(proxyGateway.name))} />
      )}
      {type === 'EC_RDR' && order === -2 && (
        <SingleItemView
          row={{ name: i18n.t('ANY_NON_MATCHED_IP_FROM_ZPA_IP_POOLS') }}
          field="name" />
      )}

      {type === 'EC_RDR' && isRuleOrder5g(order) && (
        <PolicyItemView
          label={i18n.t('APPLICABLE_FOR')}
          value={i18n.t(getRuleApplicableFor(order))} />
      )}

      {type === 'EC_RDR' && !isRuleOrder5g(order) && order !== -2 && (order === -1 || (isEmpty(ecGroups) && isEmpty(destAddresses)
          && isEmpty(destCountries) && isEmpty(destIpGroups) && isEmpty(locationGroups)
          && isEmpty(locations) && isEmpty(nwServices) && isEmpty(nwServiceGroups)
          && isEmpty(srcIps) && isEmpty(srcIpGroups)
          && isEmpty(zpaApplicationSegments) && isEmpty(zpaApplicationSegmentGroups)
      )) && (
        <>{i18n.t('ANY')}</>)}
    </>
  );
  
  return (
    <div
      onKeyPress={null}
      // aria-label="Policy Criterias"
      role="button"
      tabIndex="0">
      {renderContent()}
    </div>
  );
}
  
PolicyCriterias.propTypes = {
  value: PropTypes.shape({}),
};

PolicyCriterias.defaultProps = {
  value: {},
};
  
export default withTranslation()(PolicyCriterias);
