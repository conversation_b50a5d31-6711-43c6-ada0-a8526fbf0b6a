// this will be hooked onto the relative state tree.
import { BASE_API_PATH } from 'config';

export const REDUCER_KEY = 'deviceLogs';

export const API_ENDPOINT = `${BASE_API_PATH}/v1/uem/deviceLogs`;

export const DATA_ERROR = 'error';
export const DATA_LOADING = 'loading';
export const DATA_TABLE = 'devicelogstabledata';
export const DATA = 'data';

// this will store data to create and edit form
export const APP_DATA = 'appData';

const sgAppData = [{
  id: '1',
  valid: true,
}];

const dlData = [];

export const RESOURCE_DEFAULTS = {
  [DATA_ERROR]: null, // or error object so we can process and show to user
  [DATA_LOADING]: true,
  [APP_DATA]: sgAppData,
  [DATA]: {},
  [DATA_TABLE]: dlData,
};

export const DEFAULTS = {
  ...RESOURCE_DEFAULTS,
};

// default state for this page/widget/component
export const DEFAULT_STATE = {
  ...DEFAULTS,
};

// For testing...to be used later
export const DUMMY_STORE_DATA = {
  [REDUCER_KEY]: {
    ...DEFAULT_STATE,
  },
};
