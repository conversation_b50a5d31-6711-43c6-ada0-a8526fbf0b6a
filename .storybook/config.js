import React from 'react';
import { configure, addDecorator } from '@storybook/react';
import { jsxDecorator } from 'storybook-addon-jsx';
import '../src/css/rapp.css';

addDecorator(jsxDecorator);
// To add custom viewport: 
//  1) import addParameters from @storybook/react 
//  2) configure th below variable
// addParameters({ viewport: options });

const styles = {
  paddingLeft: '10px',
  paddingTop: '10px',
};

const paddingDecorator = storyFn => <div style={styles}>{storyFn()}</div>;
addDecorator(paddingDecorator);

// automatically import all files ending in *.stories.js
const req = require.context('../stories', true, /\.stories\.js$/);

function loadStories() {
  req.keys().forEach(filename => req(filename));
}

configure(loadStories, module);


