import React from 'react';
import PropTypes from 'prop-types';
import { noop } from 'utils/lodash';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';
import { withTranslation } from 'react-i18next';
import { FixedSizeList as List } from 'react-window';
import DropdownHeader from './DropdownHeader';

export function SelectedBigData(props) {
  const {
    actions: { remove },
    isViewOnly,
    selectedHeader,
    selectedValues,
    t,
  } = props;

  const itemRenderer = (item, style) => {
    return (
      <div
        style={style}
        key={`selected-${item.id}`}
        id={item.id}
        label={item.label}
        className={`selected ${item.deleted ? 'strike-through' : ''}`}>
        { !isViewOnly && (
          <span>
            <button
              onClick={() => remove(item)}
              type="button"
              className="remove-button">
              <FontAwesomeIcon icon={faTimes} />
            </button>
          </span>
        )}
        <span>{t(item.name)}</span>
      </div>
    );
  };

  const renderContent = ({ index, style }) => {
    const items = [selectedValues[index]].map((item) => itemRenderer(item, style));

    return items;
  };

  return (
    <div className="dropdown-selected">
      <DropdownHeader text={selectedHeader} />
      <div className="selected-items  no-overflow">
        <List
          className="List"
          height={350}
          // height={hasMorePages ? 280 : 310}
          itemCount={selectedValues.length}
          itemSize={40}
          width={280}>
          {renderContent}
        </List>
      </div>
    </div>
  );
}

SelectedBigData.propTypes = {
  isViewOnly: PropTypes.bool,
  actions: PropTypes.shape({
    remove: PropTypes.func,
  }),
  selectedHeader: PropTypes.string,
  selectedValues: PropTypes.arrayOf(PropTypes.shape({})),
  t: PropTypes.func,
};

SelectedBigData.defaultProps = {
  isViewOnly: false,
  actions: {
    remove: noop,
  },
  selectedHeader: '',
  selectedValues: [],
  t: (str) => str,
};

export default withTranslation()(SelectedBigData);
