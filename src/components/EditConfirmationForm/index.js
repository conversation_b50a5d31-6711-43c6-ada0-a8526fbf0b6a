// @flow
import React, { useState } from 'react';
import { reduxForm } from 'redux-form';
import Loading from 'components/spinner/Loading';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';

import './index.scss';
 
export function BasicCustomAppForm({
  valid,
  t,
  message,
  modalLoading,
  selectedRowID,
  handleCancel,
  handleProceed,
  subItems,
}) {
  const [submitting, setSubmitting] = useState(false);

  const onSubmitHandler = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setSubmitting(true);
    handleProceed(selectedRowID, subItems);
  };

  return (
    <form onSubmit={onSubmitHandler} className="add-custom-app-form edit-confirm-form">
      <Loading loading={modalLoading}>
        <div className="form-sections-container">
          <div className="form-section">
            {message || (
              <p className="modal-text">
                {t('ACTION_CAN_NOT_BE_UNDONE')}
              </p>
            )}
          </div>
        </div>
        <div className="dialog-footer">
          <div className="dialog-footer-left">
            <button type="submit" disabled={submitting} className={`submit ${!valid ? 'disabled' : ''}`}>{t('PROCEED')}</button>
            <button type="button" className="cancel" onClick={() => handleCancel(false)}>{t('CANCEL')}</button>
          </div>
        </div>
      </Loading>
    </form>
  );
}

BasicCustomAppForm.propTypes = {
  handleCancel: PropTypes.func,
  handleProceed: PropTypes.func,
  t: PropTypes.func,
  message: PropTypes.string,
  modalLoading: PropTypes.bool,
  selectedRowID: PropTypes.string,
  valid: PropTypes.bool,
  subItems: PropTypes.arrayOf(),
};

BasicCustomAppForm.defaultProps = {
  handleCancel: noop,
  handleProceed: noop,
  t: (str) => str,
  message: null,
  modalLoading: false,
  selectedRowID: null,
  valid: true,
  subItems: [],
};

const editConfirmationForm = reduxForm({
  form: 'editConfirmationForm',
})(BasicCustomAppForm);

export default (withTranslation()(editConfirmationForm));
