/* no-param-reassign */
import React, { useEffect, useState, Fragment } from 'react';
import PropTypes from 'prop-types';
import { noop, isEqual } from 'utils/lodash';
import { withTranslation } from 'react-i18next';
import DateTimePicker from 'components/dateTimePicker';
import './index.scss';

function DateDropdown(props) {
  const [openList, setOpenList] = useState(false);
  const [isDpOpened, setIsDpOpened] = useState(false);
  const [wrapperRef, setWrapperRef] = useState(null);
  const {
    currentValue, setValue,
    items, t, dateRange, maxDateSelectable,
  } = props;

  const handleChangeValue = (item) => {
    if (item?.id !== 'custom') {
      setValue(item);
      setIsDpOpened(false);
      setOpenList(false);
    } else {
      setIsDpOpened(true);
    }
  };

  const handleMousedown = (e) => {
    const containRef = wrapperRef && wrapperRef.contains(e.target);
    if (!containRef) {
      setOpenList(false);
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleMousedown);
    setOpenList(true);
    document.removeEventListener('mousedown', handleMousedown);
  }, []);

  const onSetWrapperRef = (node) => {
    setWrapperRef(node);
  };

  const applyRange = (date) => {
    const {
      timeShift, hours, startDate, endDate,
    } = date;
    const value = {
      value: 'custom',
      label: 'CUSTOM',
      timeshift: timeShift,
      hour: hours,
      startDate,
      endDate,
    };
    setValue(value);
    setIsDpOpened(true);
  };

  const handleCustomPickerClose = () => {
    setIsDpOpened(false);
  };

  return (
    <div className="drop-down-container" ref={onSetWrapperRef}>
      <div className="dropdown-box">
        <div className={`drop-down-list ${openList ? 'open' : ''} ${isDpOpened ? 'open-custom' : ''}`}>
          <div className="dropdown-list-content">
            {items.map((item) => (
              <div
                key={item.id}
                className={`dropdown-list-item ${
                  (isEqual(item.id, currentValue.value)
                        && !isDpOpened)
                      || (isEqual(item.id, 'custom') && isDpOpened)
                    ? 'dropdown-selected-value'
                    : ''
                }`}>
                <button
                  onClick={() => {
                    handleChangeValue(item);
                  }}
                  type="button">
                  <span>{t(item.name)}</span>
                </button>
              </div>
            ))}
          </div>
        </div>
      </div>
      {isDpOpened && (
        <DateTimePicker
          isOpened={isDpOpened}
          showMinute={false}
          showSecond={false}
          dateRange={dateRange}
          maxDateSelectable={maxDateSelectable}
          applyRage={applyRange}
          currentValue={currentValue}
          onClose={handleCustomPickerClose} />
      )}
    </div>
  );
}

DateDropdown.propTypes = {
  items: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.string,
      label: PropTypes.string,
    }),
  ),
  t: PropTypes.func,
  setValue: PropTypes.func,
  defaultValue: PropTypes.shape({
    value: PropTypes.string,
    label: PropTypes.string,
  }),
  currentValue: PropTypes.shape({
    value: PropTypes.string,
    label: PropTypes.string,
  }),
  dateRange: PropTypes.number,
  maxDateSelectable: PropTypes.number,
};

DateDropdown.defaultProps = {
  items: [],
  t: (str) => str,
  setValue: noop,
  defaultValue: {},
  dateRange: 90,
  maxDateSelectable: 90,
};

export default withTranslation()(DateDropdown);
