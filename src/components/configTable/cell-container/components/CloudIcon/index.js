// @flow

import React from 'react';
import PropTypes from 'prop-types';

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faBuilding } from '@fortawesome/pro-regular-svg-icons';
import AWS from 'images/aws.png';
import awsActive from 'images/awsActive.png';
import awsInactive from 'images/awsInactive.png';
import azure from 'images/azure.png';
import azureActive from 'images/azureActive.png';
import azureInactive from 'images/azureInactive.png';
import gcp from 'images/gcp.png';
import gcpActive from 'images/gcpActive.png';
import gcpInactive from 'images/gcpInactive.png';

// DEPLOYED || DEFAULT
const awsIcon = <img src={AWS} className="cluster-child-icon aws" alt="AWS" />;
// AWS ACTIVE
const awsIconActive = <img src={awsActive} className="cluster-child-icon-active aws" alt="AWS active" />;
// AWS INACTIVE
const awsIconInactive = <img src={awsInactive} className="cluster-child-icon-inactive aws" alt="AWS Inactive" />;
  
// Azure Default
const azureDefImg = <img src={azure} className="cluster-child-icon-active azure" alt="Azure" />;
// Azure ACTIVE
const azureImgActive = <img src={azureActive} className="cluster-child-icon-active azure" alt="Azure Active" />;
// Azure INACTIVE
const azureImgInactive = <img src={azureInactive} className="cluster-child-icon-inactive azure" alt="Azure Inactive" />;

// GCP Default
const gcpDefImg = <img src={gcp} className="cluster-child-icon-active gcp" alt="GCP" />;
// GCP ACTIVE
const gcpImgActive = <img src={gcpActive} className="cluster-child-icon-active gcp" alt="GCP Active" />;
// GCP INACTIVE
const gcpImgInactive = <img src={gcpInactive} className="cluster-child-icon-inactive gcp" alt="GCP Inactive" />;
  
// DEPLOYED || DEFAULT Branch
const branchDefImg = <FontAwesomeIcon icon={faBuilding} className="fontStyle fas map-icon-branch branch" alt="Branch" />;
// Branch ACTIVE
const branchImgActive = <FontAwesomeIcon icon={faBuilding} className="fontStyle fas map-icon-branch-active branch" alt="Branch Active" />;
// Branch INACTIVE
const branchImgInactive = <FontAwesomeIcon icon={faBuilding} className="fontStyle fas map-icon-branch-inactive branch" alt="Branch Inactive logo" />;

function CloudIcon(props) {
  const { value } = props;
  const {
    deploymentType, status, deviceType,
  } = value;
  let icon = awsIcon;
      
  if (deploymentType && deploymentType === 'AWS') {
    if (status && status === 'Active') {
      icon = awsIconActive;
    } else if (status && status === 'Inactive') {
      icon = awsIconInactive;
    } else {
      icon = awsIcon;
    }
  } else if (deploymentType && deploymentType === 'AZURE') {
    if (status && status === 'Active') {
      icon = azureImgActive;
    } else if (status && status === 'Inactive') {
      icon = azureImgInactive;
    } else {
      icon = azureDefImg;
    }
  } else if (deploymentType && deploymentType === 'GCP') {
    if (status && status === 'Active') {
      icon = gcpImgActive;
    } else if (status && status === 'Inactive') {
      icon = gcpImgInactive;
    } else {
      icon = gcpDefImg;
    }
  } else if (deviceType === 'PHYSICAL' || (deploymentType
                && (deploymentType === 'CENTOS'
                  || deploymentType === 'REDHAT_LINUX'
                  || deploymentType === 'MICROSOFT_HYPER_V'
                  || deploymentType === 'VMWARE_ESXI'
                ))) {
    if (status && status === 'Active') {
      icon = branchImgActive;
    } else if (status && status === 'Inactive') {
      icon = branchImgInactive;
    } else {
      icon = branchDefImg;
    }
  } // CENTOS REDHAT_LINUX VMWARE_ESXI
      
  return (
    <div className="account-column">
      {icon}
    </div>
  );
}

CloudIcon.propTypes = {
  value: PropTypes.shape(),
};

CloudIcon.defaultProps = {
  value: {},
};

export default CloudIcon;
