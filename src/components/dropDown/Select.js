import { faAngleUp, faAngleDown } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';
import React from 'react';

import './index.scss';

class Select extends React.Component {
  static propTypes = {
    items: PropTypes.arrayOf(PropTypes.shape({
      value: PropTypes.string,
      label: PropTypes.string,
    })),
    setValue: PropTypes.func,
    selectedValue: PropTypes.shape({
      value: PropTypes.string,
      label: PropTypes.string,
    }),
    input: PropTypes.shape({
      value: PropTypes.shape({
        value: PropTypes.string,
        label: PropTypes.string,
      }),
    }),
    onCallBack: PropTypes.func,
    name: PropTypes.string,
    t: PropTypes.func,
  };

  static defaultProps = {
    items: [],
    selectedValue: {},
    setValue: noop,
    input: { value: '', label: '' },
    onCallBack: (str) => str,
    name: '',
    t: (str) => str,
  };

  constructor(props) {
    super(props);
    this.state = {
      openList: false,
    };
    this.wrapperRef = null;
  }

  componentDidMount() {
    document.addEventListener('mousedown', this.handleMousedown);
  }

  componentWillUnmount() {
    document.removeEventListener('mousedown', this.handleMousedown);
  }

  setWrapperRef = (node) => {
    this.wrapperRef = node;
  };

  handleChangeValue = (item) => {
    const {
      setValue, onCallBack, name, input,
    } = this.props;
    input.onChange(item);
    setValue(item);
    this.setState({
      openList: false,
    });
    onCallBack(name, item);
  };

  handleMousedown = (e) => {
    const containRef = this.wrapperRef && this.wrapperRef.contains(e.target);
    if (!containRef) {
      this.setState({ openList: false });
    }
  };

  handleOpenList = () => {
    const { openList } = this.state;
    this.setState({ openList: !openList });
  };

  render() {
    const { items, t, input: { value: { value, label } } } = this.props;
    const { openList } = this.state;

    return (
      <div className="select-container" ref={this.setWrapperRef}>
        <button
          className="select-selected-value"
          onClick={this.handleOpenList}
          type="button">
          <span>{t(label)}</span>
          <FontAwesomeIcon icon={openList ? faAngleUp : faAngleDown} pull="right" />

        </button>
        {openList && (
          <ul className="select-list">
            {items.map((item) => (
              <li key={item.value} className={`select-list-item ${item.value === value ? 'active' : ''}`}>
                <button onClick={() => { this.handleChangeValue(item); }} type="button"><span>{item.label}</span></button>
              </li>
            ))}
          </ul>
        )}
      </div>
    );
  }
}

export default withTranslation()(Select);
