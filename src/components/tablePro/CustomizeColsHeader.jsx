import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { noop } from 'utils/lodash';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSlidersUp } from '@fortawesome/pro-solid-svg-icons';

function CustomizeColsHeader(props) {
  const { onMouseOverCb, handleLeave, showComponent } = props;
  // const [isVisible, setIsVisible] = useState(showComponent);
  const callback = () => {
    // await setIsVisible(() => !isVisible);
    // if (!isVisible) {
    //   onMouseOverCb();
    // } else {
    //   handleLeave();
    // }
    onMouseOverCb();
  };

  const handleClick = (e) => {
    const classList = (e.target && e.target.classList.toString() || e.target.parentElement.classList.toString()) || '';
    if (e.target && (e.target.className !== 'r-check-box'
      && !classList.includes('fa-sliders-up')
      && !classList.includes('column-reset')
      && !classList.includes('table-column-selector')
      && !classList.includes('table-column-selector-button'))) {
      handleLeave();
    }
  };

  useEffect(() => {
    document.addEventListener('click', handleClick);
  }, []);

  return (
    <div
      className="table-column-selector"
      tabIndex={-1}
      onKeyUp={() => noop}
      role="button"
      onClick={() => callback()}>
      <div className="table-column-selector-button">
        <FontAwesomeIcon icon={faSlidersUp} />
      </div>
    </div>
  );
}

CustomizeColsHeader.propTypes = {
  i18n: PropTypes.shape({}),
  onMouseOverCb: PropTypes.func,
};

CustomizeColsHeader.defaultProps = {
  i18n: {
    localizeString: (str) => str,
  },
  onMouseOverCb: noop,
};

export default CustomizeColsHeader;
