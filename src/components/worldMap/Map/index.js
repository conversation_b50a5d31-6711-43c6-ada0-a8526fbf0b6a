/* eslint-disable react/jsx-handler-names */
// @flow
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import colors from 'scss/colors.scss';
import {
  ComposableMap,
  ZoomableGroup,
  Geographies,
  Geography,
} from 'react-simple-maps';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faMinus, faPlus } from '@fortawesome/pro-solid-svg-icons';
import URL_WORLD_MAP_DATA from '../110m.json';

import './index.scss';

const MAP_COLOR = '#F3F3F4'; // colors.grey13;
const MAP_HOVER_COLOR = colors.grey8;

class Map extends Component {
  static propTypes = {
    zooming: PropTypes.bool,
    children: PropTypes.node,
  };

  static defaultProps = {
    zooming: null,
    children: null,
  };

  state = {
    zoom: 1,
  };

  handleZoomIn = () => {
    const { zoom } = this.state;
    this.setState({ zoom: zoom * 2 });
  };

  handleZoomOut = () => {
    const { zoom } = this.state;
    this.setState({ zoom: zoom / 2 });
  };

  mapOnDoubleClick = () => {
    const { zooming } = this.props;
    if (zooming) {
      this.handleZoomIn();
    }
  };

  getPattern = (id, color, zoom) => {
    const patterWidth = 0.5 * (1 / zoom);
    const patterHeight = 0.5 * (1 / zoom);

    const rectWidth = 0.25 * (1 / zoom);
    const rectHeight = 0.25 * (1 / zoom);

    return (
      <svg width="0" height="0">
        <pattern id={id} x="0" y="0" width={patterWidth} height={patterHeight} patternUnits="userSpaceOnUse">
          <rect x="0" width={rectWidth} height={rectHeight} y="0" fill={color} />
        </pattern>
      </svg>
    );
  };

  render() {
    const { zoom } = this.state;
    const { zooming, children } = this.props;
    const MAP_PATTER_ID = 'pattern-checker';
    const MAP_HOVER_PATTER_ID = 'pattern-checker-hover';

    return (
      <>
        {this.getPattern(MAP_PATTER_ID, MAP_COLOR, zoom)}
        {this.getPattern(MAP_HOVER_PATTER_ID, MAP_HOVER_COLOR, zoom)}

        <div className="map-container">
          <ComposableMap
            projectionConfig={{
              scale: 190,
              yOffset: 30,
            }}>
            <ZoomableGroup zoom={zoom}>
              <Geographies geography={URL_WORLD_MAP_DATA} tabable={false}>
                {(geographies, projection) => geographies.map((geography) => (
                  <Geography
                    key={`${geography.id}`}
                    geography={geography}
                    projection={projection}
                    style={{
                      default: {
                        fill: MAP_COLOR,
                        outline: 0,
                      },
                      hover: { fill: `url(#${MAP_HOVER_PATTER_ID})`, outline: 0 },
                      pressed: { fill: `url(#${MAP_HOVER_PATTER_ID})`, outline: 0 },
                    }}
                    onDoubleClick={this.mapOnDoubleClick} />
                ))}
              </Geographies>
              {children}
            </ZoomableGroup>
          </ComposableMap>
        </div>
        {zooming && (
          <div className="zoom-btn-container">
            <button type="button" onClick={this.handleZoomIn}><FontAwesomeIcon icon={faPlus} /></button>
            <button type="button" onClick={this.handleZoomOut}><FontAwesomeIcon icon={faMinus} /></button>
          </div>
        )}
      </>
    );
  }
}

export default Map;
