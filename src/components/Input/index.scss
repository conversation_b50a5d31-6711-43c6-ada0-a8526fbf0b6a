@import "scss/colors.scss";
@import "scss/mixins.scss";

.ec-root-page {
.search-container {
  background-color: var(--semantic-color-surface-base-secondary	);
  // border: 1px solid var(--semantic-color-border-base-primary);
  border-radius: .25rem;
  display: flex;
  width: 13.75rem;
  height: 2rem;
  align-items: center;
  padding: 0;
  .icon-wrapper {
    width: 2.1875rem;
    height: 1.875rem;
    line-height: 1.875rem;
    text-align: center;
    justify-content: center;
    background-color: transparent;
    &.left {
      border-top-left-radius: 0.3125rem;
      border-bottom-left-radius: 0.3125rem;
    }

    &.right {
      border-top-right-radius: 0.3125rem;
      border-bottom-right-radius: 0.3125rem;
    }

    .search-icon {
      color: var(--semantic-color-content-base-secondary);
    }
    .remove-icon {
      color: var(--semantic-color-content-base-primary);

      &.disabled {
        color: var(--semantic-color-content-interactive-primary-disabled );
      }
    }
  }

  .search-input {
    flex-grow: 1;
    height: 1.875rem;
    border-style: hidden;
    background-color: var(--semantic-color-surface-base-primary);
    font-size: 0.8125rem;
    color: var(--semantic-color-content-base-primary);
    border-radius: 0 0.3125rem 0.3125rem 0rem;
    &:focus {
      outline: none;
    }

    &::-webkit-input-placeholder {
      color: var(--semantic-color-content-base-primary);
    }
  }
}
.input-container {
  width: 100%;
  padding: 0 15px;

  textarea:disabled {
    border-radius: 6px;
    color: var(--semantic-color-content-base-secondary);
    background-color: var(--semantic-color-surface-base-secondary);
  }
  input:disabled {
    border-radius: 6px;
    color: var(--semantic-color-content-base-secondary);
    background-color: var(--semantic-color-surface-base-secondary);
  }

  .input-label {
    color: var(--semantic-color-content-base-primary);
    font-weight: 500;
    margin: 15px 0 5px;
    position: relative;
    display: inline-block;
  }
  label {
    .input-label:hover + .rTooltip{
      display: block;
    }
  }

  .input-wrapper {
    @include DisplayFlex;
    align-items: center;
    width: fit-content;
    border-radius: 6px;

    input {
      width: 100%;
      height: 30px;
      border: none;
      border-bottom: 1px solid var(--semantic-color-content-interactive-primary-default);
      color: $grey1;
      font-size: 13px;
      outline-offset: 1px;
    }
    .unit {
      color: var(--semantic-color-content-base-primary);
      margin-left: 8px;
    }
    .has-copy-button {
      position: absolute;
      right: 20px;
      border: none;
      background-color: transparent;
    }
  }

  .error-container {
    color: var(--semantic-color-content-status-danger-primary);
    padding-top: 2px;
  }

  &.error {
    input {
      border-color: var(--semantic-color-content-status-danger-primary);
    }
  }

  &.no-margin-top {
    .input-label {
      margin-top: 0;
    }
  }
}
}