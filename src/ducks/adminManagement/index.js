import { createAction, loading } from 'ducks/generics';
import { genericInterface } from 'utils/http';
import { destroy } from 'redux-form';
import {
  omit, get, isNull, isEmpty, uniqBy,
} from 'utils/lodash';
import { notify, notifyError } from 'ducks/notification';
import { checkActivation } from 'ducks/activation';
import * as constants from './constants';
import * as selectors from './selectors';
import * as loginSelectors from '../login/selectors';
import actionTypes from './action-types';

export { REDUCER_KEY } from './constants';

const actionTypeExists = Object.keys(actionTypes).reduce((hash, key) => ({
  ...hash,
  [actionTypes[key]]: true,
}), {});

export const reducer = (state = constants.DEFAULT_STATE, action = {}) => {
  if (actionTypeExists[action.type]) {
    return {
      ...state,
      ...action.payload,
    };
  }
  return state;
};

export default reducer;

const boundLoading = loading(actionTypes.DATA_LOADING);
const boundError = createAction(actionTypes.DATA_LOAD_ERROR);
const dataChanged = createAction(actionTypes.DATA_CHANGED);
const boundDataLoadSuccess = createAction(actionTypes.DATA_LOAD_SUCCESS);
const boundModalLoading = createAction(actionTypes.MODAL_LOADING);

export const loaderPasswordExpiration = () => async (dispatch) => {
  try {
    const response = await genericInterface(
      constants.ADMINMANAGEMENT_API_PASSWORD_EXPIRE_ENDPOINT,
    ).read();
    const { data } = response || {};
    const { passwordExpiryDays } = data || {};
    dispatch(boundDataLoadSuccess({
      [constants.PASSWORD_EXPIRATION]: {
        ...data,
        passwordExpiryDays: passwordExpiryDays === -1 ? 180 : passwordExpiryDays,
      },
    }));
  } catch (error) {
    const errorMsg = get(error, 'response.data.message');
    dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
    dispatch(boundError(errorMsg));
    if (isEmpty(errorMsg)) dispatch(notifyError('ERROR_LOADING_ADMIN_MANAGEMENT'));
    else dispatch(notifyError('errorMsg'));
  }
};

export const loaderSAML = () => async (dispatch) => {
  try {
    const response = await genericInterface(
      constants.ADMINMANAGEMENT_SAML_ENDPOINT,
    ).read();
    const { data } = response || {};
    const {
      samlEnabled, productId, issuers, certFilename,
    } = data;
    dispatch(boundDataLoadSuccess({
      [constants.SAML_ENABLED]: samlEnabled,
      [constants.SAML_PRODUCTID]: productId,
      [constants.SAML_INITIAL_ISSUERS]: issuers,
      [constants.SAML_ISSUERS]: issuers,
      [constants.SAML_CERT_FILENAME]: certFilename,
    }));
  } catch (error) {
    const errorMsg = get(error, 'response.data.message');
    dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
    dispatch(boundError(errorMsg));
    if (isEmpty(errorMsg)) dispatch(notifyError('ERROR_LOADING_ADMIN_MANAGEMENT'));
    else dispatch(notifyError('errorMsg'));
  }
};

export const getOrgProvisioningInfo = () => async (dispatch) => {
  dispatch(dataChanged({
    loading: true,
  }));
  try {
    const response = await genericInterface(
      constants.ORG_PROV_API,
    ).read();
    const { data } = response || {};
    const {
      oneIdentityUiUrl, oneIdentityAdminLoginEnabled,
    } = data;
    dispatch(boundDataLoadSuccess({
      [constants.IS_ONE_IDENTITY_ENABLED]: typeof oneIdentityAdminLoginEnabled === 'undefined' ? false : oneIdentityAdminLoginEnabled,
      [constants.ONE_IDENTITY_UI_URL]: typeof oneIdentityUiUrl === 'undefined' ? '' : oneIdentityUiUrl,
      loading: false,
    }));
  } catch (error) {
    const errorMsg = get(error, 'response.data.message');
    dispatch(dataChanged({
      loading: false,
    }));
    dispatch(boundError(errorMsg));
    dispatch(notifyError('errorMsg'));
  }
};

export const loader = async (pageStart, pageEnd, searchData, initializeProps) => async (dispatch, getState) => {
  await dispatch(getOrgProvisioningInfo());
  const state = getState();
  if (initializeProps) {
    state.adminManagement.page = 1;
    state.adminManagement.rbactabledata = [];
    state.adminManagement.hasNextPage = false;
    state.adminManagement.search = null;
    state.adminManagement.moreItemsLoading = false;
    dispatch(boundLoading({
      ...constants.DEFAULTS,
      searchData,
      [constants.MORE_ITEMS_LOADING]: true,
      [constants.IS_ONE_IDENTITY_ENABLED]: state.adminManagement.isOneIdentityEnabled,
      [constants.ONE_IDENTITY_UI_URL]: state.adminManagement.oneIdentityUiUrl,
    }));
    dispatch(loaderPasswordExpiration());
    if (!state.adminManagement.isOneIdentityEnabled) {
      dispatch(loaderSAML());
    }
  }

  const username = loginSelectors.usernameSelector(state);
  try {
    const { page } = state.adminManagement;
    const pageSize = 100;
    const searchText = state.adminManagement.searchData;
    const param = searchText ? { search: searchText, page, pageSize } : { page, pageSize };
    const adminManagementApi = await genericInterface(
      constants.ADMINMANAGEMENT_API_QUERY,
    ).read(null, { params: param });
    const { data } = adminManagementApi || [];
    const combinedData = [...state.adminManagement.rbactabledata, ...data.map((x) => ({
      ...x,
      isNonEditable: x.loginName === username,
    }))];
    const updatedData = uniqBy(combinedData, 'id');
    if (data.length > 0) {
      await dispatch(boundDataLoadSuccess({
        loading: false,
        [constants.DATA_TABLE]: updatedData,
        [constants.PAGE_NUMBER]: state.adminManagement.page + 1,
        [constants.HAS_NEXT_PAGE]: !(data.length < pageSize),
        [constants.MORE_ITEMS_LOADING]: false,
      }));
    } else {
      await dispatch(boundDataLoadSuccess({
        loading: false,
        [constants.DATA_TABLE]: updatedData,
        [constants.PAGE_NUMBER]: state.locations.page + 1,
        [constants.HAS_NEXT_PAGE]: false,
        [constants.MORE_ITEMS_LOADING]: false,
      }));
    }
  } catch (error) {
    const errorMsg = get(error, 'response.data.message');
    dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
    dispatch(boundError(errorMsg));
    if (isEmpty(errorMsg)) dispatch(notifyError('ERROR_LOADING_ADMIN_MANAGEMENT'));
    else dispatch(notifyError('errorMsg'));
  }
};

export const getAdminManagementData = () => (dispatch) => {
  dispatch(boundDataLoadSuccess({
    [constants.DATA]: constants.DATA,
    loading: false,
  }));
};

export const setSamlIssuers = (issuers) => (dispatch) => {
  dispatch(dataChanged({
    [constants.SAML_ISSUERS]: issuers,
    loading: false,
  }));
};

// eslint-disable-next-line max-len
export const toggleAddForm = (toggle, appData = constants.DEFAULT_FORM_DATA, modalTitle) => (dispatch) => {
  dispatch(dataChanged({
    [constants.SHOW_FORM]: toggle,
    [constants.APP_DATA]: appData,
    [constants.MODAL_TITLE]: modalTitle,
    [constants.ADD_ADMIN]: true,
    [constants.VIEW_ONLY]: false,
  }));
};

// eslint-disable-next-line max-len
export const toggleEditForm = (toggle, appData = constants.DEFAULT_FORM_DATA, modalTitle) => (dispatch, getState) => {
  const state = getState();
  const formData = selectors.dataTableSelector(state) || [];
  dispatch(dataChanged({
    [constants.SHOW_FORM]: toggle,
    [constants.APP_DATA]: { ...formData.find((x) => x.id === appData.id) },
    [constants.MODAL_TITLE]: modalTitle,
    [constants.ADD_ADMIN]: false,
    [constants.VIEW_ONLY]: false,
  }));
};

// eslint-disable-next-line max-len
export const toggleViewForm = (toggle, appData = constants.DEFAULT_FORM_DATA, modalTitle) => (dispatch, getState) => {
  const state = getState();
  const formData = selectors.dataTableSelector(state) || [];
  dispatch(dataChanged({
    [constants.SHOW_FORM]: toggle,
    [constants.APP_DATA]: { ...formData.find((x) => x.id === appData.id) },
    [constants.MODAL_TITLE]: modalTitle,
    [constants.ADD_ADMIN]: false,
    [constants.VIEW_ONLY]: true,
  }));
};

// eslint-disable-next-line max-len
export const toggleClose = () => (dispatch) => {
  dispatch(dataChanged({
    [constants.SHOW_FORM]: false,
  }));
  dispatch(destroy('adminManagement'));
};

export const toggleDeleteForm = (toggle, data) => (dispatch) => {
  dispatch(toggleClose());
  dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
  dispatch(dataChanged({
    [constants.SHOW_DELETE_FORM]: toggle,
    [constants.SELECTED_ROW_ID]: data ? data.id.toString() : null,
    loading: false,
  }));
};

export const toggleShowSamlCertificateFile = (toggle) => (dispatch) => {
  dispatch(toggleClose());
  dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
  dispatch(dataChanged({
    [constants.SHOW_SAML_CETIFICATE_MODAL]: toggle,
    loading: false,
  }));
};

export const togglePasswordExpireForm = (toggle) => (dispatch) => {
  dispatch(toggleClose());
  dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
  dispatch(dataChanged({
    [constants.SHOW_PASSWORD_EXPIRE_FORM]: toggle,
    loading: false,
  }));
};

export const toggleShowAdminExistsForm = (toggle) => (dispatch) => {
  dispatch(dataChanged({
    [constants.SHOW_ADMIN_EXISTS_CONFIRM]: toggle,
    modalLoading: false,
    loading: false,
  }));
};

export const savePasswordExpirationForm = () => async (dispatch, getState) => {
  dispatch(boundLoading({ loading: true }));
  const state = getState();
  const formData = await selectors.formDataPasswordExperationSelector(state);
  const base = selectors.baseSelector(state);
  const { samlIssuers, samlCertFileName, samlProductId } = base;
  const { passwordExpirationEnabled, expiresAfter, samlEnabled } = formData;
  const passwordExpiredValues = {
    passwordExpirationEnabled,
    passwordExpiryDays: passwordExpirationEnabled ? expiresAfter : -1,
  };
  const savePasswordExpiration = genericInterface(`${constants.ADMINMANAGEMENT_API_PASSWORD_EXPIRE_ENDPOINT}`);
  const samlEndpoint = (genericInterface(`${constants.ADMINMANAGEMENT_SAML_ENDPOINT}`)).update;
  const apiEndPoint = savePasswordExpiration.update;
  try {
    await apiEndPoint(passwordExpiredValues, {});
    await samlEndpoint({
      certFilename: samlCertFileName,
      issuers: samlIssuers,
      productId: samlProductId,
      samlEnabled,
    }, {});
  } catch (error) {
    const errorMsg = get(error, 'response.data.message');
    dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
    dispatch(boundError(errorMsg));
    if (isEmpty(errorMsg)) dispatch(notifyError('ERROR_SAVING_ADMIN_MANAGEMENT'));
    else dispatch(notifyError(errorMsg));
  
    return;
  }
  await dispatch(loaderPasswordExpiration());
  await dispatch(loaderSAML());
  dispatch(toggleClose());
  dispatch(notify('SUCCESSFULLY_SAVED'));
  dispatch(checkActivation());
  dispatch(boundModalLoading({
    loading: false,
    [constants.MODAL_LOADING]: false,
  }));
};

const beforeSave = (newAdmin, appData, formData, showAdminExistsConfirm) => {
  const {
    loginId,
    domain,
    email,
    name,
    role,
    status,
    scope,
    locationName = [],
    newLocationCreateAllowed,
    comments,
    password,
    isPasswordLoginAllowed,
  } = formData;
  const { loginName } = appData;

  const inputData = {
    loginName: newAdmin ? `${loginId}@${domain.name}` : loginName,
    userName: name,
    role: { id: role.id, name: role.name },
    email,
    comments,
    adminScopeType: scope.id,
    ...(scope.id === 'LOCATION' && { adminScopeScopeEntities: (newLocationCreateAllowed && (isNull(locationName) || isEmpty(locationName))) ? [{ id: 0, name: 'None' }] : locationName.map((x) => ({ id: x.id, name: x.name })) }),
    scope: scope.id,
    newLocationCreateAllowed,
    disabled: status.id === 'disabled',
    password,

    isNonEditable: false,
    isAuditor: false,
    isPasswordLoginAllowed,
    isPasswordExpired: false,
    isExecMobileAppEnabled: false,
    ...(showAdminExistsConfirm && { associateWithExistingAdmin: showAdminExistsConfirm }),
  };

  return (newAdmin ? inputData : { ...omit(inputData, 'loginId') });
  // return (newAdmin ? inputData : { ...omit(inputData, 'loginName') });
};

export const saveForm = (newAdmin) => async (dispatch, getState) => {
  dispatch(boundLoading({ loading: true }));
  const state = getState();
  const appData = selectors.appDataSelector(state);
  const formData = selectors.formDataValuesSelector(state);
  const showAdminExistsConfirm = selectors.showAdminExistsConfirm(state);
  const { id } = appData;
  const adminManagementValues = beforeSave(newAdmin, appData, formData, showAdminExistsConfirm);
  const queryParam = showAdminExistsConfirm ? '?associateWithExistingAdmin=true' : '';
  const saveAdminManagement = genericInterface(`${constants.ADMINMANAGEMENT_API_ENDPOINT}/${newAdmin ? queryParam : id + queryParam}`);
  const apiEndPoint = newAdmin ? saveAdminManagement.create : saveAdminManagement.update;

  try {
    await apiEndPoint(adminManagementValues, {});
  } catch (error) {
    const errorMsg = get(error, 'response.data.message');
    const errorCode = get(error, 'response.data.code');
    if (errorCode === 'ADMIN_LOGIN_NAME_ALREADY_EXISTS') {
      dispatch(dataChanged({
        [constants.SHOW_ADMIN_EXISTS_CONFIRM]: true,
        modalLoading: false,
        loading: false,
      }));
      return;
    }
    dispatch(boundModalLoading({ loading: false }));
    dispatch(boundError(errorMsg));
    if (isEmpty(errorMsg)) dispatch(notifyError('ERROR_SAVING_ADMIN_MANAGEMENT'));
    else dispatch(notifyError(errorMsg));
  
    return;
  }
  
  dispatch(toggleClose());
  dispatch(dataChanged({ [constants.SHOW_ADMIN_EXISTS_CONFIRM]: false }));
  dispatch(notify('SUCCESSFULLY_SAVED'));
  dispatch(loader(0, 0, null, true));
  dispatch(checkActivation());
  dispatch(boundModalLoading({ loading: false }));
};

export const deleteForm = () => async (dispatch, getState) => {
  dispatch(boundLoading({ loading: true }));
  const state = getState();
  const rowId = selectors.selectedRowIDSelector(state);

  const apiAdminManagement = genericInterface(`${constants.ADMINMANAGEMENT_API_ENDPOINT}/${rowId}`);
  const apiEndPoint = apiAdminManagement.del;

  try {
    await apiEndPoint(rowId, {});
  } catch (error) {
    const errorMsg = get(error, 'response.data.message');
    dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
    dispatch(boundError(errorMsg));
    if (isEmpty(errorMsg)) dispatch(notifyError('ERROR_DELETING_ADMIN_MANAGEMENT'));
    else dispatch(notifyError(errorMsg));
    
    return;
  }

  dispatch(toggleClose());
  dispatch(toggleDeleteForm(false));
  dispatch(notify('SUCCESSFULLY_DELETED'));
  dispatch(loader(0, 0, null, true));
  dispatch(checkActivation());
};

export const updateMenu = (columns) => (dispatch) => {
  dispatch(
    dataChanged({
      [constants.COLUMNS]: [],
    }),
  );
  dispatch(
    dataChanged({
      [constants.COLUMNS]: columns,
    }),
  );
};

/* SEARCH */
export const handleOnSearchFilter = (event, searchText) => (dispatch) => {
  if (event.keyCode && event.keyCode === 13 && searchText.length > 0) {
    return dispatch(dataChanged({
      [constants.SEARCH_DATA]: searchText,
    }));
  } if (searchText.length === 0) {
    return dispatch(dataChanged({
      [constants.SEARCH_DATA]: '',
    }));
  }

  return null;
};

export const tabConfiguration = [{
  value: 'ADMINISTRATORS',
  title: 'ADMINISTRATORS',
  visible: true,
  to: '?',
  handleClick: (str) => str,
}, {
//   value: 'AUDITORS',
//   title: 'Auditors',
//   visible: true,
//   to: '?',
//   handleClick: () => console.log(' click 2'),
// }, {
  value: 'ADMINISTRATORS_MANAGEMENT',
  title: 'ADMINISTRATORS_MANAGEMENT',
  visible: true,
  to: '?',
  handleClick: (str) => str,
}];

// eslint-disable-next-line max-len
export const tabChange = (tab) => (dispatch) => {
  dispatch(dataChanged({
    [constants.SELECTED_TAB]: tab,
  }));
};

export const onUploadCertificate = (fileUpload) => async (dispatch) => {
  dispatch(boundLoading({ loading: true }));
  const uploadCertificateFile = genericInterface(`${constants.ADMINMANAGEMENT_SAML_CERT_UPLOAD_ENDPOINT}`);
  const apiEndPoint = uploadCertificateFile.create;
  
  try {
    const file = new FormData();
    file.append('fileUpload', fileUpload);
    const { data } = await apiEndPoint(file, { headers: { 'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8' } });
    if (data.toUpperCase() === 'SUCCESS') {
      dispatch(notify('SUCCESSFULLY_SAVED'));
      dispatch(dataChanged({
        samlCertFileName: fileUpload.name,
      }));
    } else {
      dispatch(notifyError(data));
    }
  } catch (error) {
    const errorMsg = get(error, 'response.data.message');
    dispatch(boundModalLoading({ loading: false }));
    dispatch(boundError(errorMsg));
    if (isEmpty(errorMsg)) dispatch(notifyError('ERROR_UPLOADING_CERTIFICATE'));
    else dispatch(notifyError(errorMsg));
  }
  dispatch(toggleShowSamlCertificateFile(false));
  // dispatch(loader());
  dispatch(checkActivation());
  dispatch(boundModalLoading({ loading: false }));
};

export const onDownloadSamlXml = () => async (dispatch) => {
  dispatch(boundLoading({ loading: true }));
  const downloadFile = genericInterface(`${constants.ADMINMANAGEMENT_SAML_ENDPOINT_DOWNLOAD}`);
  const apiEndPoint = downloadFile.read();
  
  try {
    const { data } = await apiEndPoint;
    dispatch(
      boundDataLoadSuccess({
        [constants.DONWLOAD_XML]: data,
        loading: false,
        error: null,
      }),
    );
    dispatch(boundModalLoading({ loading: false }));
  
    return data;
  } catch (error) {
    const errorMsg = get(error, 'response.data.message');
    dispatch(boundModalLoading({
      [constants.DONWLOAD_XML]: 'XML ERROR',
      loading: false,
    }));
    dispatch(boundError(errorMsg));
    if (isEmpty(errorMsg)) dispatch(notifyError('ERROR_UPLOADING_CERTIFICATE'));
    else dispatch(notifyError(errorMsg));
  
    return 'XML ERROR - FAIL ON DOWNLOAD';
  }
};
