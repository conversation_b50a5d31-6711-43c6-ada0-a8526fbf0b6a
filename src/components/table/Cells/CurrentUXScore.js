// @flow
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import ScoreChart from 'components/scoreChart';
import colors from 'scss/colors.scss';
import { SCORE_STATUSES } from 'config';
import './index.scss';

class CurrentUXScore extends Component {
  static propTypes = {
    value: PropTypes.shape(),
    original: PropTypes.shape(),
    mockData: PropTypes.arrayOf(PropTypes.shape()),
  };

  static defaultProps = {
    value: {},
    original: {},
    mockData: null,
  };

  getColor = (score) => {
    switch (true) {
    case score > SCORE_STATUSES.okay:
      return colors.green2;
    case score > SCORE_STATUSES.poor:
      return colors.orange2;
    default:
      return colors.red3;
    }
  };

  render() {
    // TODO refactor when API will be ready
    const { value: { uxScore }, original: { chartData }, mockData } = this.props;
    const color = this.getColor(uxScore);

    return (
      <div className="cell-table-ux-score">
        <ScoreChart data={chartData || mockData} percentage={uxScore} color={color} />
        <span className="score-number" style={{ color }}>{`${uxScore} / 100`}</span>
      </div>
    );
  }
}

export default CurrentUXScore;
