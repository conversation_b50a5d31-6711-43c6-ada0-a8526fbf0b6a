import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { faPencil } from '@fortawesome/free-solid-svg-icons';
import { faChevronRight, faChevronDown } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import './index.scss';
  
function reviewGroup({
  children,
  contentClass,
  handleEdit,
  itemsNumber,
  styleClass,
  styleTitleClass,
  styleTitleContainerClass,
  title,
  open = true,
}) {
  const [isOpen, setIsOpen] = useState(open);

  const handleOpenClose = () => {
    setIsOpen(() => !isOpen);
  };
  
  return (
    <div className={`review-container ${styleClass}`}>
      <div className={`review-title-container ${styleTitleContainerClass}`}>
        <FontAwesomeIcon
          icon={isOpen ? faChevronDown : faChevronRight}
          className="review-title-icon"
          onClick={handleOpenClose} />
      
        <div className={`review-title ${styleTitleClass}`}>
          {title}
        </div>

        {itemsNumber && <span className="review-item-number">{`(${itemsNumber})`}</span>}
        {handleEdit && <FontAwesomeIcon icon={faPencil} className="review-title-icon" onClick={handleEdit} />}
      </div>

      {isOpen && (
        <div className={`review-container-items ${contentClass}`}>
          {children}
        </div>
      )}
    </div>
  );
}
  
reviewGroup.propTypes = {
  children: PropTypes.oneOfType([
    PropTypes.element,
    PropTypes.arrayOf(PropTypes.element),
  ]),
  contentClass: PropTypes.string,
  itemsNumber: PropTypes.string,
  handleEdit: PropTypes.func,
  title: PropTypes.string,
  styleClass: PropTypes.string,
  styleTitleClass: PropTypes.string,
  styleTitleContainerClass: PropTypes.string,
};

reviewGroup.defaultProps = {
  children: null,
  contentClass: '',
  handleEdit: null,
  itemsNumber: null,
  title: '',
  styleClass: '',
  styleTitleClass: '',
  styleTitleContainerClass: '',
};
  
export default reviewGroup;
