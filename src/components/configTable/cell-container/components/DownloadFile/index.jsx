import React, { useRef } from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faDownload, faExternalLink } from '@fortawesome/pro-regular-svg-icons';
import { faDownload as faDownloadSolid } from '@fortawesome/pro-solid-svg-icons';
import { isURL } from 'utils/validations';

function LinkCell(props) {
  const {
    value, customCellType,
  } = props;
  const {
    msiUrl32Bits, msiUrl64Bits,
    releaseNotesUrl, name32Bits, name64Bits, downloadUrl,
  } = value;
  const linkRef = useRef(null);

  const renderContent = () => (
    <div className="cca-cell-container">
      {
        customCellType === 'DOWNLOAD_FILE_32BITS' && (
          <div className="cca-download-file">
            {!msiUrl32Bits
            && (
              <div className="external-link-button">
                <FontAwesomeIcon className="disabled-input fa-external-link fa-xs" icon={faDownload} size="lg" />
              </div>
            )}
            {msiUrl32Bits && (
              <a href={msiUrl32Bits} download={name32Bits} target="_blank" rel="noopener noreferrer" className="external-link-button">
                <FontAwesomeIcon className={`${!msiUrl32Bits ? 'disabled-input' : ''} fa-external-link fa-xs`} icon={faDownload} size="lg" />
              </a>
            )}
          </div>
        )
      }
      {
        customCellType === 'DOWNLOAD_FILE_64BITS' && (
          <div className="cca-download-file">
            {!msiUrl64Bits && (
              <div className="external-link-button">
                <FontAwesomeIcon className="disabled-input fa-external-link fa-xs" icon={faDownload} size="lg" />
              </div>
            )}
            {msiUrl64Bits && (
              <a href={msiUrl64Bits} download={name64Bits} target="_blank" rel="noopener noreferrer" className="external-link-button">
                <FontAwesomeIcon className={`${!msiUrl64Bits ? 'disabled-input' : ''} fa-external-link fa-xs`} icon={faDownload} size="lg" />
              </a>
            )}
          </div>
        )
      }
      {
        customCellType === 'RELEASE_NOTES' && (
          <div className="cca-download-file">
            <a href={releaseNotesUrl} target="_blank" rel="noopener noreferrer" className="external-link-button">
              <FontAwesomeIcon className="fa-external-link fa-xs" icon={faExternalLink} size="lg" />
            </a>
          </div>
        )
      }
      {
        customCellType === 'DOWNLOAD_FILE_BC_IMAGES' && (
          <div className="cca-download-file">
            <a href={isURL(downloadUrl) === null ? new URL(downloadUrl) : '/'} target="_blank" rel="noopener noreferrer" className="external-link-button">
              <FontAwesomeIcon className="fa-external-link fa-xs" icon={faDownloadSolid} size="lg" />
            </a>
          </div>
        )
      }
    </div>
  );

  return (
    <div ref={linkRef}>
      {renderContent()}
    </div>
  );
}

LinkCell.propTypes = {
  customCellType: PropTypes.string,
  value: PropTypes.shape({
    msiUrl32Bits: PropTypes.string,
    msiUrl64Bits: PropTypes.string,
    releaseNotesUrl: PropTypes.string,
    name32Bits: PropTypes.string,
    name64Bits: PropTypes.string,
    downloadUrl: PropTypes.string,
  }),
};

LinkCell.defaultProps = {
  customCellType: '',
  value: {},
};

export default withTranslation()(LinkCell);
