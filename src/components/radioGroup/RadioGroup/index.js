import React from 'react';
import PropTypes from 'prop-types';
import { Field } from 'redux-form';
import { Trans } from 'react-i18next';
import ReactTooltip from 'react-tooltip';
import { noop } from 'utils/lodash';

import RadioButton from '../RadioButton';

function RadioGroup(props = {}) {
  const {
    name,
    options,
    label,
    disabled,
    styleClass,
    id,
    onChange,
  } = props;

  return (
    <div className={`radio-button-container ${styleClass}`}>
      <ReactTooltip place="top" type="info" effect="solid" offset={{ left: 115 }} />
      <p data-tip={`Tooltip for ${label}`}><Trans>{label}</Trans></p>
      <div className={`radio-buttons ${disabled ? 'disabled' : ''}`} id={id}>
        {options.map((item) => (
          <Field
            key={item.value}
            disabled={disabled}
            name={name}
            component={RadioButton}
            onChange={onChange}
            type="radio"
            value={item.value}
            label={item.label}
            parse={(value) => value === 'true'} />
        ))}
      </div>
    </div>
  );
}

RadioGroup.defaultProps = {
  label: null,
  disabled: false,
  styleClass: '',
  id: null,
  onChange: noop,
};

RadioGroup.propTypes = {
  label: PropTypes.string,
  name: PropTypes.string.isRequired,
  options: PropTypes.arrayOf(PropTypes.shape({
    label: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    value: PropTypes.bool.isRequired,
  })).isRequired,
  disabled: PropTypes.bool,
  styleClass: PropTypes.string,
  id: PropTypes.string,
  onChange: PropTypes.func,
};

export default RadioGroup;
