import React from 'react';
import PropTypes from 'prop-types';
import { isEmpty, noop } from 'utils/lodash';

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSortDown, faSortUp } from '@fortawesome/pro-solid-svg-icons';
import { faSort } from '@fortawesome/pro-light-svg-icons';

function SortContainer({
  column, onHandleSortBy, sortField, sortDirection,
}) {
  const {
    id,
    disableSortBy = false,
    isSorted = false,
    isSortedDesc = undefined,
    meta: { sortFieldName },
    toggleSortBy,
  } = column;

  const onToggleSortByClick = async (evt) => {
    evt.preventDefault();
    evt.stopPropagation();

    if (isEmpty(onHandleSortBy)) return toggleSortBy();

    await onHandleSortBy(sortFieldName, isSortedDesc ? 'desc' : 'asc');
  };

  const renderSortedSection = () => {
    if (sortField === sortFieldName && sortDirection === 'asc') return <FontAwesomeIcon icon={faSortUp} className="icon asc" />;
    if (sortField === sortFieldName && sortDirection === 'desc') return <FontAwesomeIcon icon={faSortDown} className="icon desc" />;

    if (isSorted) {
      return isSortedDesc ? (
        <FontAwesomeIcon icon={faSortUp} className="icon asc" />
      ) : (
        <FontAwesomeIcon icon={faSortDown} className="icon desc" />
      );
    }

    return <FontAwesomeIcon icon={faSort} className="icon sort" />;
  };

  const getSortClassName = () => {
    let className = '';

    if (isSorted || (sortField === sortFieldName && sortDirection !== '')) {
      className = 'is-active';
    }

    return className;
  };

  if (disableSortBy) {
    return null;
  }

  return (
    <div
      key={id}
      className={`table-sort-container ${getSortClassName()}`}
      aria-label="tableSort"
      onClick={onToggleSortByClick}
      onKeyPress={noop}
      role="button"
      tabIndex="0">
      {renderSortedSection()}
    </div>
  );
}

SortContainer.defaultProps = {
  column: {},
  onHandleSortBy: null,
  sortField: '',
  sortDirection: '',
};

SortContainer.propTypes = {
  column: PropTypes.objectOf(Object),
  onHandleSortBy: PropTypes.func,
  sortField: PropTypes.string,
  sortDirection: PropTypes.string,
};

export default SortContainer;
