/* eslint-disable jsx-a11y/label-has-for */
import React from 'react';
import PropTypes from 'prop-types';
import i18n from 'utils/i18n';
import { withTranslation } from 'react-i18next';
import { reduxForm } from 'redux-form';

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCheckCircle } from '@fortawesome/pro-solid-svg-icons';
import ECRadioGroup from 'components/ecRadioGroup';

function ProvInfoStatus(props) {
  const { value, row, onChangeHandler } = props;
  const { status } = value || {};

  const handleOnChange = (event) => {
    event.preventDefault();
    if (onChangeHandler) {
      onChangeHandler();
    }
  };

  if (status === 'DEPLOYED') {
    return (
      <form onSubmit={(event) => event.preventDefault()} className="">
        <div className="prov-status">
          <button type="button" className="deployed-button">
            <FontAwesomeIcon icon={faCheckCircle} />
            {i18n.t('DEPLOYED')}
          </button>
        </div>
      </form>
    );
  }
 
  if (row.original.provUrlType !== 'ONPREM_PHYSICAL') {
    return (
      <form onSubmit={(event) => event.preventDefault()} className="">
        <div className="prov-status">
          {i18n.t(status)}
        </div>
      </form>
    );
  }

  return (
    <form onSubmit={(event) => event.preventDefault()} className="">
      <div className="prov-status">
        <ECRadioGroup
          id="status"
          name="status"
          onChange={handleOnChange}
          options={[{
            name: 'STAGED', value: 'STAGED', disabled: status === 'STAGED', checked: status === 'STAGED', label: i18n.t('STAGED'),
          },
          {
            name: 'NOT_DEPLOYED', value: 'NOT_DEPLOYED', disabled: status === 'NOT_DEPLOYED', checked: status === 'NOT_DEPLOYED', label: i18n.t('NOT_DEPLOYED'),
          },
          ]} />
      </div>
    </form>
  );
}
  
ProvInfoStatus.defaultProps = {
  onChangeHandler: null,
  value: {},
  row: {
    original: {
      provUrlType: '',
    },
  },
};
  
ProvInfoStatus.propTypes = {
  onChangeHandler: PropTypes.func,
  row: PropTypes.shape({
    original: PropTypes.shape({
      provUrlType: PropTypes.string,
    }),
  }),
  value: PropTypes.shape({}),
};
  
const ProvInfoStatusForm = reduxForm({
  form: 'provInfoStatusForm',
})(ProvInfoStatus);

export default (withTranslation()(ProvInfoStatusForm));
