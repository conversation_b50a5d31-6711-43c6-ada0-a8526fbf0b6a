/* eslint-disable */
/*
Usage:
nvm use 12.11
node --experimental-modules scripts/label-diff.mjs 
Run from directly above

This file generate diff in labesl from one file to another
Should output:
Change in value
New value
*/

import fs from 'fs';

import en_US from '../src/utils/i18n/en-US.js';
// change following import per your need
import previous_en_US from '/Users/<USER>/Downloads/en-US-ZDX-Feb-13-2020.js';

const base = en_US.translation;
const oldBase = previous_en_US.translation;

// We want to find two type of changes:
// 1. entry in base that is not in oldBase
// 2. entry in base that is different from entries in oldBase

// const baseKeys = Object.keys(base);
const diff = {};
for (let [key, value] of Object.entries(base)) {
  const oldValue = oldBase[key];
  let needsReview = '';
  if(!oldValue || oldValue !== value) {
    diff[key] = value;
    needsReview = 'NEEDS_REVIEW';
  }
  console.log(`${needsReview}","${key}","${value}"`)
}
// console.log('diff', JSON.stringify(diff, null, 2));