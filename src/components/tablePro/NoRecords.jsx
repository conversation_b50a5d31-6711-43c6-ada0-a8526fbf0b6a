import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';

function NoRecords({ t }) {
  return (
    <div className="table-body-no-records">
      <div className="table-empty-message">{t('NO_ITEMS_AVAILABLE')}</div>
    </div>
  );
}

NoRecords.propTypes = {
  t: PropTypes.func,
};

NoRecords.defaultProps = {
  t: (str) => str,
};

export default withTranslation()(NoRecords);
