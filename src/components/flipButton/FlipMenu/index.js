import React from 'react';
import PropTypes from 'prop-types';

class FlipMenu extends React.Component {
  handleCheckBox = (event) => {
    const { filpMenuData } = this.props;
    const updateItem = filpMenuData;
    if (!event.target.checked) {
      updateItem[event.target.value].visible = false;
      updateItem[event.target.value].checkbox = false;
    } else {
      updateItem[event.target.value].visible = true;
      updateItem[event.target.value].checkbox = true;
    }
    return updateItem;
  };

  render() {
    const {
      filpMenuData,
      showFlipMenu,
      flipMenuApply,
      clickCallback,
      flipMenuCancelLabel,
      flipMenuApplyLabel,
      minHeight,
    } = this.props;

    const fixHeight = minHeight + 'px';
    const styler = { height: fixHeight };
    
    return (
      <div className="flip-menu">
        <div className={`flip-menu-editor ${showFlipMenu ? '' : 'hide'} `} style={styler}>
          <div className="table-column-menu-container">
            <div className="table-column-menu ui-sortable">
              <div className="menu-table-container">
                {filpMenuData.map((index, key) => (
                  <div className="form-input-row ecui-sortable-handle table-form" key={index.name}>
                    <span className="form-input -js-toggle">
                      <i className="fa fa-bars"></i>
                      <span className="check-box form-error">
                        <span>
                          <input
                            type="checkbox"
                            className="r-check-box"
                            defaultChecked={index.checkbox}
                            value={key}
                            onClick={this.handleCheckBox} />
                        </span>
                        <span className="check-box-label">
                          <li className="noLi">
                            {index.name}
                          </li>
                        </span>
                      </span>
                    </span>
                  </div>
                ))}
              </div>
            </div>
            <hr />
            <div>
              <div className="menu-cancel-div">
                <div
                  className="menu-cancel"
                  role="button"
                  aria-label="Cancel"
                  tabIndex="0"
                  onClick={() => clickCallback()}
                  onKeyPress={() => clickCallback()}>
                  {flipMenuCancelLabel}
                </div>
              </div>
              <div className="menu-apply-div">
                <div
                  className="menu-apply"
                  role="button"
                  aria-label="Apply"
                  tabIndex="0"
                  onClick={() => flipMenuApply(filpMenuData)}
                  onKeyPress={() => flipMenuApply(filpMenuData)}>
                  {flipMenuApplyLabel}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

FlipMenu.propTypes = {
  filpMenuData: PropTypes.arrayOf(PropTypes.shape({})),
  showFlipMenu: PropTypes.bool,
  flipMenuApply: PropTypes.func,
  clickCallback: PropTypes.func,
  minHeight: PropTypes.string,
  flipMenuCancelLabel: PropTypes.string,
  flipMenuApplyLabel: PropTypes.string,
};

FlipMenu.defaultProps = {
  filpMenuData: [],
  showFlipMenu: false,
  flipMenuApply: null,
  clickCallback: null,
  flipMenuCancelLabel: '',
  flipMenuApplyLabel: '',
};

export default FlipMenu;
