import React from 'react';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Trans, withTranslation } from 'react-i18next';

function DrillDownPlaceholder({ placeholder, icon }) {
  return (
    <div className="drill-down-placeholder">
      <FontAwesomeIcon icon={icon} />
      <Trans>{placeholder}</Trans>
    </div>
  );
}

DrillDownPlaceholder.propTypes = {
  placeholder: PropTypes.string,
  icon: PropTypes.shape(),
};

DrillDownPlaceholder.defaultProps = {
  placeholder: null,
  icon: null,
};

export default withTranslation()(DrillDownPlaceholder);
