import React from 'react';
import PropTypes from 'prop-types';
import { FormFieldLabel, FormSectionLabel } from 'components/label';
import { withTranslation } from 'react-i18next';
import { Field } from 'redux-form';
import Input from 'components/Input';
import {
  required,
  // ipAddressesOrRanges,
  ipAddressOnly,
} from 'utils/validations';

function BlackedListIP({ submitting, t, blackedListIpResponse }) {
  return (
    <>
      <FormSectionLabel text={t('BLACKLISTED_IP_CHECK')} />
      <div className="form-section">
        <div className="g-row">
          <div className="g-left">
            <FormFieldLabel text={t('IP_ADDRESS')} tooltip={t('TOOLTIP_HELP_BLACKLISTED_IP_IP_ADDRESS_CHECK')} />
            <Field
              id="ips"
              name="ips"
              component={Input}
              styleClass="zia"
              placeholder={t('ENTER_TEXT')}
              validate={[
                required,
                // ipAddressesOrRanges,
                ipAddressOnly,
              ]} />
          </div>
          <div className="g-left">
            <button type="submit" disabled={submitting} className="submit">{t('CHECK_BLACKLIST')}</button>
          </div>
        </div>
      </div>

      {blackedListIpResponse && (
        <>
          <FormSectionLabel text={t('BLACKLIST_LOOKUP_RESULTS')} />
          <div className="form-section">
            <div className="g-row">
              <div className="g-left">
                <FormFieldLabel text={t('IP_ADDRESS')} tooltip={t('TOOLTIP_HELP_BLACKLISTED_IP_IP_ADDRESS_RESULTS')} />
                <p className="disabled-input">---</p>
              </div>
              <div className="g-left">
                <FormFieldLabel text={t('COMMENTS')} tooltip={t('TOOLTIP_HELP_BLACKLISTED_IP_COMMENTS')} />
                <p className="disabled-input">---</p>
              </div>

            </div>
          </div>
        </>
      )}
    </>
  );
}

BlackedListIP.propTypes = {
  blackedListIpResponse: PropTypes.bool,
  submitting: PropTypes.bool,
  t: PropTypes.func,
};
  
BlackedListIP.defaultProps = {
  blackedListIpResponse: false,
  t: (str) => str,
};

export default withTranslation()(BlackedListIP);
