import React from 'react';
import { change, getFormValues } from 'redux-form';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { isEmpty } from 'utils/lodash';
import PropTypes from 'prop-types';
import { NavLink } from 'react-router-dom';

import './NavTabs.scss';

function InterfaceTab(props) {
  const {
    meta, id, disable, tabData, pageData, AddMoreComponent,
  } = props;
  const { form } = meta;
  const dispatch = useDispatch();
  const { t } = useTranslation();

  const {
    tabConfiguration, pageTabs, isPageTabFullwidth,
  } = useSelector((state) => getFormValues(form)(state)) || {};
  
  if (isEmpty(tabConfiguration) && isEmpty(tabData)) return <></>;
  
  const tab = tabData || tabConfiguration;
  const page = pageData || pageTabs;

  return (
    <div className="tab-row-container">
      <div className="tabs">
        <div className="tabs-items">
          {tab.map((item) => (
            <div key={`${item.value}-tab`} id={`${item.value}-tab`} className={`${isPageTabFullwidth ? 'max-width' : ''} ${item.hasError ? 'invalid' : ''}`}>
              <NavLink
                activeclassname="active-menu-item"
                onClick={() => {
                  dispatch(change(form, id, item.value));
                  item.handleClick();
                }}
                to={item.to}>
                <p className={`${page === item.value ? 'tabPactive' : 'tabP'} ${disable ? 'disabled-input' : ''} ${isPageTabFullwidth ? 'max-width' : ''}`}>{t(item.title)}</p>
              </NavLink>
              <div className={`highlighter ${page === item.value ? 'active' : ''}`} />
            </div>
          ))}
          <div className="tabs-highlrighte" />
        </div>
      </div>
      <div className="tabs-items-add-more">
        {AddMoreComponent}
      </div>
    </div>
  );
}

InterfaceTab.propTypes = {
  disable: PropTypes.bool,
  id: PropTypes.string,
  meta: PropTypes.shape({}),
  pageData: PropTypes.string,
  tabData: PropTypes.arrayOf(PropTypes.shape({})),
  AddMoreComponent: PropTypes.oneOfType([
    PropTypes.element,
    PropTypes.arrayOf(PropTypes.element),
  ]),
};

InterfaceTab.defaultProps = {
  disable: false,
  id: '',
  meta: {},
  pageData: '',
  tabData: [],
  AddMoreComponent: <></>,
};
export default InterfaceTab;
