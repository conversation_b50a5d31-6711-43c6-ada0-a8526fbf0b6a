@import "scss/colors.scss";
@import "scss/mixins.scss";

.ec-root-page {
.radio-tab-container {
  p {
    color: $grey1;
    font-weight: 500;
    margin: 15px 0 10px;
    text-transform: capitalize;
  }

  .radio-buttons {
    @include DisplayFlex;

    &.disabled {
      .radio-button {
        label {
          background: var(—surface-fields-disabled);
          color: var(--semantic-color-content-interactive-primary-disabled);
          border: 1px solid var(--semantic-color-border-interactive-primary-disabled);
          cursor: not-allowed;
        }

        &.checked-true {
          label {
            background: var(--semantic-color-content-interactive-primary-default);
            color: var(--semantic-color-background-primary);
          }
        }
      }
    }

    .radio-button {
      &.checked-true {
        label {
          background: var(--semantic-color-content-interactive-primary-default);
          color: var(--semantic-color-background-primary);
          transition: background .8s;
        }
      }

      &:first-child label {
        border-radius: 5px 0 0 5px;
      }

      &:last-child label {
        border-radius: 0 5px 5px 0;
      }

      &:only-child label{
        border-radius: 5px;
      }

      label {
        @include DisplayFlex;
        cursor: pointer;
        position: relative;
        padding: 4px 20px 4px 5px;
        border: 1px solid var(--semantic-color-content-interactive-primary-default);
        color: var(--semantic-color-content-interactive-primary-default);
        align-items: center;
        justify-content: center;

        .check-circle {
          width: 13px;
          font-size: 13px;
        }

        input {
          display: none;
        }
      }

      .check-circle {
        margin-right: .5rem
      }
    }
  }
}
}