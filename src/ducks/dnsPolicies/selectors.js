import { createSelector } from 'reselect';
import { getFormValues, getFormSyncErrors, getFormMeta } from 'redux-form';

import * as constants from './constants';

export const baseSelector = (state) => state[constants.REDUCER_KEY];

export const loadingSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA_LOADING],
);

export const errorSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA_ERROR],
);

export const dataSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA] || {},
);

export const appDataSelector = createSelector(
  baseSelector,
  (state) => state[constants.APP_DATA] || {},
);

export const dataTableSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA_TABLE] || {},
);

export const cachedDataTableSelector = createSelector(
  baseSelector,
  (state) => state[constants.CACHED_DATA] || {},
);

export const modalLoadingSelector = createSelector(
  baseSelector,
  (state) => state[constants.MODAL_LOADING],
);

export const featurePermissionsSelector = createSelector(
  appDataSelector,
  (state) => state[constants.FEATURE_PERMISSIONS] || {},
);

export const addPolicySelector = createSelector(
  baseSelector,
  (state) => state[constants.ADD_POLICY],
);

export const viewOnlySelector = createSelector(
  baseSelector,
  (state) => state[constants.VIEW_ONLY],
);

export const modalTitleRoleSelector = createSelector(
  baseSelector,
  (state) => state[constants.MODAL_TITLE],
);

export const selectedRowIDSelector = createSelector(
  baseSelector,
  (state) => state[constants.SELECTED_ROW_ID],
);

export const searchDataSelector = createSelector(
  baseSelector,
  (state) => state[constants.SEARCH_DATA],
);

export const ipAddressListSelector = createSelector(
  baseSelector,
  (state) => state[constants.IP_ADDRESSES_DATA] || [],
);

export const columnsSelector = createSelector(
  baseSelector,
  (state) => state[constants.COLUMNS] || [],
);

export const usersRowSelector = createSelector(
  baseSelector,
  (usersState) => usersState.data || [],
);

export const sortFieldSelector = createSelector(
  baseSelector,
  (state) => state[constants.SORT_FIELD] || '',
);
export const sortDirectionSelector = createSelector(
  baseSelector,
  (state) => state[constants.SORT_DIRECTION] || 'desc',
);

export const destinationIpAddressesSelector = createSelector(
  baseSelector,
  (state) => state[constants.DESTINATION_IP_ADDRESSES] || [],
);

export const pageNumberSelector = createSelector(
  baseSelector,
  (state) => state[constants.PAGE_NUMBER] || 1,
);

export const formValuesSelector = (state) => getFormValues('addEditDnsFilteringRules')(state);
export const formMetaSelector = (state) => getFormMeta('addEditDnsFilteringRules')(state);
export const formSyncErrorsSelector = (state) => getFormSyncErrors('addEditDnsFilteringRules')(state);

export const formDataSelector = createSelector(
  baseSelector,
  formValuesSelector,
  (data, formValues) => formValues || data,
);

// alway return top level selector
export default baseSelector;
