@import 'scss/colors.scss';
@import 'scss/mixins.scss';

$marginRight: 3;

.ec-root-page {
.selected-values {
  @include DisplayFlex;
  flex-direction: row;
  flex-wrap: wrap;
  width: 100%;
  align-items: center;

  .selected-value {
    @include DisplayFlex;
    margin-right: $marginRight * 1px;
    cursor: unset;
    color: $white;
    border: none;
    padding: 5px 8px;
    border-radius: 5px;
    background-color: var(--semantic-color-content-interactive-primary-default);
    box-shadow: 0 2px 10px 0 $blueShadow1;
    line-height: 10px;

    .fa-times {
      cursor: pointer;
      margin-left: 10px;
      display: block;
    }
  }
}

:export {
  marginRight: $marginRight;
}
}