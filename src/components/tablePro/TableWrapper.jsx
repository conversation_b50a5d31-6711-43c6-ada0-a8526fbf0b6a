import React, { useState, useEffect, useRef } from 'react';
import ReactDataGrid from 'react-data-grid';
import PropTypes from 'prop-types';
import { result } from 'utils/lodash';
import { DraggableHeader } from 'react-data-grid-addons';
import { swapObject, sortRows } from 'utils/tableHelper';
import { genericInterface } from 'utils/http';
import { withTranslation } from 'react-i18next';
import i18n from 'utils/i18n';
import ReactTooltip from 'react-tooltip';
import moment from 'moment-timezone';
import NoRecords from './NoRecords';
import './index.scss';

function ToolTipMessage({ selectedDataKey, actionToolTip }) {
  return selectedDataKey && !actionToolTip
    ? (
      <div id="tooltip" className="tooltip">
        <div className="ec-tooltip-container">
          <div id="tooltip-top7" className="tooltip-top tooltip-top-text">{selectedDataKey}</div>
          <div id="tooltip-bottom" className="tooltip-bottom tooltip-bottom-text">{i18n.t('CLICK_FOR_MORE_INFO')}</div>
        </div>
      </div>
    )
    : <></>;
}

ToolTipMessage.propTypes = {
  selectedDataKey: PropTypes.string,
  actionToolTip: PropTypes.bool,
};

function ToolTipMessageAction({ selectedDataKey, actionToolTip, DrillDownAction }) {
  return ((selectedDataKey && actionToolTip)
    ? <DrillDownAction filter={selectedDataKey} />
    : <></>);
}
ToolTipMessageAction.propTypes = {
  selectedDataKey: PropTypes.string,
  actionToolTip: PropTypes.bool,
  DrillDownAction: PropTypes.shape({}),
};
ToolTipMessageAction.defaultProps = {
  selectedDataKey: '',
  actionToolTip: false,
  DrillDownAction: {},
};

function RowRenderer(props, selectedDataKey, setSelectedDataKey, actionToolTip, setActionToolTip, DrillDownAction) {
  const handleMouseEnter = () => {
    const { row } = props || {};
    const { id } = row || {};
    if (!actionToolTip) setSelectedDataKey(id);
  };

  const handleOnClick = () => {
    setActionToolTip(!actionToolTip);
    const { row } = props || {};
    const { id } = row || {};
    setSelectedDataKey(id);
  };

  const handleMouseLeave = () => { if (!actionToolTip) setSelectedDataKey(null); };
  const row = useRef(null);
    
  return (DrillDownAction
    ? (
      <div
        role="button"
        aria-label="Select"
        tabIndex={0}
        onClick={handleOnClick}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onKeyPress={null}
        data-for="main"
        data-tip=""
        data-iscapture="true">
        <ReactDataGrid.Row ref={row} {...props} rowHeight={50} headerRowHeight={50} />
        {selectedDataKey && !actionToolTip && (
          <ReactTooltip
            id="main"
            place="top"
            type="light"
            multiline>
            <ToolTipMessage selectedDataKey={selectedDataKey} />
          </ReactTooltip>
        )}
      </div>
    )
    : <ReactDataGrid.Row ref={row} {...props} rowHeight={50} headerRowHeight={50} />
  );
}

const { DraggableContainer } = DraggableHeader;
const noop = () => undefined;

const loadNextData = (url) => {
  const nextApi = genericInterface(url);
  return nextApi.read()
    .then((response) => response.data)
    .catch((error) => error);
};

// customRowGetter will format the data according to the datatype
const customRowGetter = (data, formatTimeStamp, columns, t, showNoDataValue) => {
  const retData = {};

  if (data) {
    Object.keys(data).forEach((key) => {
      retData[key] = data[key];
      if (formatTimeStamp && formatTimeStamp.includes(key)) {
        retData[key] = moment.unix(retData[key]).format('MMM. DD, YYYY HH:mm:ss'); // Epoch converter
        // retData[key] = moment(retData[key]).format('MMM. DD, YYYY hh:mm:ss a');
      }
      if (retData[key] instanceof Array) {
        retData[key] = retData[key].map((item) => {
          return item.name ? item.name : item;
        }).join();
      } if (retData[key] instanceof Object && retData[key][0]) {
        retData[key] = retData[key][0].name;
      } if (retData[key] instanceof Object && retData[key].name) {
        retData[key] = retData[key].name;
      } if (['cltNwProtocol', 'srvNwProtocol'].includes(key)) {
        retData[key] = t(retData[key]);
      }
    });
    if (showNoDataValue) {
      columns.forEach((col) => {
        if (!(col.key in retData)) {
          retData[col.key] = t('NONE');
        }
      });
    }
  }
  return retData;
};

const customRowGetterwithKeys = (data, keysInReturn) => {
  const retData = {};
  if (data) {
    keysInReturn.forEach((item) => {
      retData[item] = result(data, item);
    });
  }
  return retData;
};

// TableWrapper is responsible for creating a draggable container and data table
function TableWrapper({
  t,
  initialRows,
  initialColumns,
  infiniteScroll,
  inScrollApi,
  updateMenu,
  keysInReturn,
  formatTimeStamp,
  height,
  showNoDataValue,
  getCellActions,
  handleOnSortClick,
  DrillDownAction,
}) {
  const [rows, setRows] = useState(initialRows);
  const [columns, setColumns] = useState(initialColumns);
  const [isFetching, setIsFetching] = useState(false);
  const [selectedDataKey, setSelectedDataKey] = useState('');
  const [actionToolTip, setActionToolTip] = useState(false);
  let custHeight = 0;
  if (height) {
    custHeight = height;
  } else if (rows && rows.length) {
    custHeight = document.documentElement.offsetHeight - 150;
  }
  
  useEffect(() => {
    setRows(initialRows);
  }, [initialRows]);

  useEffect(() => {
    setColumns(initialColumns);
  }, [initialColumns]);

  return (
    <div>
      <div
        // role="button"
        // aria-label="tooltip"
        // className="rdg-wrapper"
        // onClick={() => setActionToolTip(!actionToolTip)}
        // onKeyPress={e => e}
        className="rdg-wrapper">
        <div className="line-chart-field-tooltip-container">
          {DrillDownAction && (
            <ToolTipMessageAction
              DrillDownAction={DrillDownAction}
              selectedDataKey={selectedDataKey}
              actionToolTip={actionToolTip} />
          )}
        </div>
        <DraggableContainer
          onHeaderDrop={(source, target) => {
            const colCopy = swapObject(columns, source, target);
            updateMenu(colCopy);
          }}>
          <ReactDataGrid
            rowRenderer={(e) => RowRenderer(
              e,
              selectedDataKey,
              setSelectedDataKey,
              actionToolTip,
              setActionToolTip,
              DrillDownAction,
            )}
            columns={columns.filter((column) => column.visible === true)}
            rowGetter={keysInReturn.length
              ? ((i) => customRowGetterwithKeys(rows[i], keysInReturn))
              : ((i) => customRowGetter(rows[i], formatTimeStamp, columns, t, showNoDataValue))}
            rowsCount={rows && rows.length ? rows.length : 0}
            minHeight={custHeight}
            rowHeight={50}
            headerRowHeight={50}
            onScroll={(e) => {
              if (infiniteScroll && (rows.length - e.rowOverscanEndIdx < 5) && !isFetching) {
                let nextDataSet;
                setIsFetching(true);
                (async () => {
                  nextDataSet = await loadNextData(inScrollApi);
                  if (nextDataSet) {
                    setIsFetching(false);
                    return setRows((rowsx) => [...rowsx, ...nextDataSet]);
                  }
                  return false;
                })();
              }
            }}
            onGridSort={
              (sortColumn, sortDirection) => {
                setRows(sortRows(initialRows, sortColumn, sortDirection));
                handleOnSortClick();
              }
            }
            getCellActions={getCellActions} />

        </DraggableContainer>
        {isFetching && t('FETCHING_MORE_LIST_ITEMS')}
        {(rows && rows.length) > 0
          ? <></>
          : <NoRecords />}
      </div>
    </div>
  );
}

TableWrapper.propTypes = {
  initialRows: PropTypes.arrayOf(PropTypes.shape({})),
  initialColumns: PropTypes.arrayOf(PropTypes.shape({})),
  updateMenu: PropTypes.func,
  infiniteScroll: PropTypes.bool,
  showNoDataValue: PropTypes.bool,
  inScrollApi: PropTypes.string,
  keysInReturn: PropTypes.arrayOf(PropTypes.string),
  formatTimeStamp: PropTypes.arrayOf(PropTypes.string),
  height: PropTypes.number,
  t: PropTypes.func,
  getCellActions: PropTypes.func,
  handleOnSortClick: PropTypes.func,
  DrillDownAction: PropTypes.shape({}),
};

TableWrapper.defaultProps = {
  initialRows: [],
  initialColumns: [],
  updateMenu: noop,
  infiniteScroll: false,
  showNoDataValue: false,
  inScrollApi: '',
  keysInReturn: [],
  formatTimeStamp: [],
  height: 525,
  t: (str) => str,
  getCellActions: noop,
  handleOnSortClick: noop,
  DrillDownAction: null,
};
export default withTranslation()(TableWrapper);
