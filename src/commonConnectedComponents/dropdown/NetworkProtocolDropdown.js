import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { dropdownActions } from 'ducks/dropdowns/network-protocol';
import { EntityDropdown } from 'components/entityDropdown';

export function Types(props) {
  const dispatch = useDispatch();
  const { data } = useSelector((state) => state.networkProtocolDropdown);
  
  useEffect(
    () => {
      dispatch(dropdownActions.load());
    },
    [],
  );

  return (
    <EntityDropdown
      data={data}
      {...props} />
  );
}

export default Types;
