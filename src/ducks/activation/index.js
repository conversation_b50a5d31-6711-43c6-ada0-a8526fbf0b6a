import { genericInterface } from 'utils/http';
import { get, isEmpty, each } from 'utils/lodash';
import {
  createAction,
  loading,
  loadSuccess,
  loadError,
} from 'ducks/generics';
import { notify, notifyError } from 'ducks/notification';
import { isOneUI } from 'config';
import * as constants from './constants';
import actionTypes from './action-types';

// we want this to exported for to be used in store and other places
// from index so...elsewhere we can say import {REDUCER_KEY as widget} from 'ducks/widget'
// for exmaple
export { REDUCER_KEY } from './constants';

const actionTypeExists = Object.keys(actionTypes).reduce((hash, key) => ({
  ...hash,
  [actionTypes[key]]: true,
}), {});

// unlike other reducer, we will use
export const reducer = (state = constants.DEFAULT_STATE, action = {}) => {
  // We will merge payload from actions to the state for this widget/page/component
  // that only matches to our actions in actiontypes
  // for example once data is loaded our action below will dispatch
  // action and data
  if (actionTypeExists[action.type]) {
    return {
      ...state,
      ...action.payload,
    };
  }
  return state;
};
export default reducer;

const boundLoading = loading(actionTypes.DATA_LOADING);

const boundSuccess = loadSuccess(actionTypes.DATA_LOAD_SUCCESS);

const dataChanged = createAction(actionTypes.DATA_CHANGED);

const boundError = loadError(actionTypes.DATA_LOAD_ERROR);

const boundToggleActivationPanel = createAction(actionTypes.TOGGLE_ACTIVATION_PANEL);

export const toggleActivationPanel = (isOpen) => (dispatch) => {
  dispatch(boundToggleActivationPanel({ [constants.TOGGLE_ACTIVATION_PANEL]: isOpen }));
};

// genericInterface return an object with CRUD methods
// bound to constants.API_ENDPOINT
const activationApi = genericInterface(constants.API_ENDPOINT);

export const localData = () => (dispatch) => {
  dispatch(boundSuccess(constants.DATA));
};

export const checkActivation = () => (dispatch, getState) => {
  const state = getState();
  const { login: { data } } = state || {};
  const { isPasswordExpired } = data || {};
  if (isPasswordExpired) return null;

  // if (isOneUI) { XC-9007
  // // eslint-disable-next-line no-use-before-define
  //   return dispatch(activate(true));
  // }

  return activationApi.read(null)
    .then((response) => {
      // eslint-disable-next-line no-shadow
      const { data } = response;
      const editing = [];
      const queued = [];
      let currentlyEditing = '';
      let activationStatus = '';
      if (isEmpty(data.adminStatusMap)) {
        data.adminStatusMap = {};
      }
      each(data.adminStatusMap, (key, val) => {
        if (key === 'ADM_EDITING') {
          editing.push(val);
        }

        if (key === 'ADM_ACTV_QUEUED') {
          queued.push(val);
        }

        // setting first key as current user's status
        if (activationStatus === '') {
          activationStatus = key;
        }
      });

      // reset the values once the activation api called
      if (isEmpty(data.adminStatusMap)) {
        editing.splice(0);
        queued.splice(0);
        dispatch(dataChanged({ myActivateStatus: 'NO_ACTIVATION_PENDING' }));
      }

      // Editing users
      if (editing.length) {
        currentlyEditing = editing.join(', ');

        dispatch(dataChanged({ myActivateStatus: activationStatus }));
      }

      dispatch(boundSuccess(constants.DATA, data));

      dispatch(dataChanged({
        currentlyEditing,
        currentlyEditingLength: editing.length,
        usersQueued: queued.join(','),
        usersQueuedCount: queued.length,
      }));
    })
    .catch((error) => dispatch(boundError(error)));
};

export const activate = (forceActivate) => (dispatch) => {
  dispatch(boundLoading({
    loading: true,
  }));
  const activateApi = forceActivate
    ? genericInterface(constants.API_FORCE_ACTIVATE)
    : genericInterface(constants.API_ACTIVATE);
  
  return activateApi.update(null)
    .then((response) => {
      dispatch(boundLoading({
        loading: false,
      }));
      dispatch(boundSuccess(constants.DATA, response.data));
      if (!isOneUI) {
        dispatch(checkActivation());
        dispatch(notify('Activation complete.'));
      }
      dispatch(dataChanged({
        [constants.FORCE_ACTIVATE]: false,
      }));
    })
    .catch((error) => {
      dispatch(boundLoading({
        loading: false,
      }));
      dispatch(dataChanged({
        [constants.FORCE_ACTIVATE]: false,
      }));
      const errorMsg = get(error, 'response.data.message', 'There is a problem with activation, Please try again later.');
      dispatch(notifyError(errorMsg));
    });
};

export const updateForceActivate = (value) => (dispatch) => {
  dispatch(dataChanged({ [constants.FORCE_ACTIVATE]: value }));
};
