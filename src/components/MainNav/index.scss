@import 'scss/defaults.scss';
@import 'scss/mixins.scss';

.main-nav {
  border-right: 1px solid  var(--semantic-color-border-base-primary);
  ul {
    @include DisplayFlex;
    justify-content: space-between;
    flex-direction: column;
    width: 180px;

    li {
      background: var(--semantic-color-surface-base-primary);
      display: inline-block;
      height: 50px;
      width: 100%;
      position: relative;

      display: flex;
      height: 40px;
      min-width: 50px;
      max-width: 280px;
      align-items: center;
      align-self: stretch;
      display: flex;
      // border-bottom: 1px solid var(--semantic-color-border-base-primary);

      svg.tree-line {
        position: absolute;
        top: 50%;
        left: 0;
        transform: translateY(-50%);
      }     
      // &:first-child:before {
      //   // border-color: $transparent;
      // }
      &:last-child:before {
        // border-color: $transparent;
        border-bottom: 1px solid var(--semantic-color-border-base-primary);
      }

      button {
        background: $transparent;
        border: none;
        line-height: 40px;
        width: 100%;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        padding: 0 8px;
        &:hover:not(:disabled) {
          background-color: var(--semantic-color-surface-nav-sideBar-active);
          border-right: 2px solid var(--semantic-color-content-interactive-primary-default);
        }

        &:disabled {
          cursor: not-allowed;
        }

        .fa-layers {
          margin-right: 17px;

          .fa-circle {
            color: $grey48;
          }

          .fa-circle-dot {
            color: var(--semantic-color-surface-interactive-primary-default);
          }

          .fa-circle-check {
            color: var(--semantic-color-surface-interactive-primary-default);
          }

          .page-index {
            color: $white;
            top: -19px;
            left: 7px;
          }
        }
        .main-nav-line {
          display: flex;
          justify-content: space-between;
          width: 100%;
        }
      }

      &.valid {
        background-color: $green7;

        button {
          &:hover {
            background-color: $green7;
          }

          .fa-circle {
            color: $green2;
          }

          .fa-check-circle {
            display: block;
          }

          .name {
            color: $green2;
          }
        }
      }

      &:last-of-type {
        button:after {
          border-width: 0;
        }
      }

      &.active {
        button {
          background-color: var(--semantic-color-surface-nav-sideBar-active);
          &:hover {
            background-color: var(--semantic-color-surface-nav-sideBar-active);
            cursor: default;
          }
        }

        // button {
        //   background-color: var(--semantic-color-background-primary);
        //   z-index: 10;
        // }
      }

      .name {
        color: var(--semantic-color-content-base-primary);
        font-size: 13px;
      }
    }
  }
}
