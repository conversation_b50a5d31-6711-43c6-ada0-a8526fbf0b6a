@import "scss/colors.scss";

.ec-root-page {
.map-container {
    width: 100%;

    svg {
        width: 100%;

        .rsm-marker,
        .rsm-marker--hover,
        .rsm-marker--press {
            outline: 0;
        }
    }
}

.zoom-btn-container {
    padding-top: 15px;

    button {
        display: block;
        background: var(--semantic-color-content-interactive-primary-default);
        border: none;
        width: 20px;
        height: 20px;
        color: $white;
        font-size: 11px;
        margin-bottom: 5px;
        border-radius: 5px;
    }
}
}