// @flow
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { NavLink } from 'react-router-dom';
import { withTranslation } from 'react-i18next';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCogs } from '@fortawesome/pro-regular-svg-icons';

import {
  ADMIN_SUB_NAV_ROUTES,
  ADMIN_CONTROL_SSO_ROUTES,
  AUTHENTICATION_CONFIGURATION_SSO_ROUTES,
  FORWARDING_METHODS_SSO_ROUTES,
  NSS_SSO_ROUTES,
} from 'config';

import { toggleAdminPanel } from 'ducks/adminNav';
import * as loginSelectors from 'ducks/login/selectors';
import SSONav from 'commonConnectedComponents/SSO';
import { getPermission } from 'utils/helpers';

class AdminNav extends Component {
  static propTypes = {
    accessPermissions: PropTypes.shape({}),
    actions: PropTypes.shape({}),
    authType: PropTypes.string,
    configData: PropTypes.shape(),
    data: PropTypes.shape({}),
    location: PropTypes.shape({ pathname: PropTypes.string }),
    t: PropTypes.func,
    toggleAdminPanel: PropTypes.bool,
  };

  static defaultProps = {
    accessPermissions: {},
    actions: {},
    authType: '',
    configData: [{}],
    data: {},
    location: null,
    t: (str) => str,
    toggleAdminPanel: false,
  };

  // routes will come from top level route
  active = (prop) => {
    const { location } = this.props;
    // debugger;
    if (!location) return '';

    const { pathname } = location;

    // we will make following dynamic based on url
    if (pathname.includes(prop.path)) {
      return 'active';
    }

    return '';
  };

  getRoutes = (pageroutes) => pageroutes.map((prop) => {
    const {
      t,
      accessPermissions,
      actions: { toggleAdminPanel: toggleActivPanel },
      authType,
    } = this.props;

    let SSOElement = '';
    if (prop.title === 'ADMINISTRATION_CONTROL') { // to-do: make localization change
      SSOElement = <SSONav routes={ADMIN_CONTROL_SSO_ROUTES} />;
    } else if (prop.title === 'FORWARDING_METHODS') {
      SSOElement = <SSONav routes={FORWARDING_METHODS_SSO_ROUTES} />;
    } else if (prop.title === 'CLOUD CONFIGURATION') {
      SSOElement = <SSONav routes={NSS_SSO_ROUTES} />;
    } else if (prop.title === 'AUTHENTICATION CONFIGURATION') {
      SSOElement = <SSONav routes={AUTHENTICATION_CONFIGURATION_SSO_ROUTES} />;
    }

    return (
      <li className={`nav-menu-section ${((prop.perm && !getPermission(prop.perm, accessPermissions)) || authType === 'SUPPORT_ACCESS_PARTIAL') ? 'hide' : ''}`} key={prop.title}>
        <h3 className="nav-menu-section-header">{t(prop.title)}</h3>
        <ul>
          {
            SSOElement
          }
          {
            prop.routes.map((route) => {
              const className = this.active(route);
              if ((route.perm && !getPermission(route.perm, accessPermissions)) || authType === 'SUPPORT_ACCESS_PARTIAL') {
                return null;
              }
              return (
                <li key={route.name} className={`${className} ${route.liclass ? route.liclass : ''}`}>
                  <NavLink
                    onClick={() => toggleActivPanel(false)}
                    to={`${route.redirectTo ? route.redirectTo : (route.layout + route.path)}`}
                    activeclassname="active-menu-item">
                    <span className="nav-menu-list-item-text">
                      {t(route.name)}
                      {route.isNew && (
                        <span className="new">
                          {' '}
                          {t('NEW')}
                        </span>
                      )}
                    </span>
                  </NavLink>
                  
                </li>
              );
            })
          }
        </ul>
      </li>
    );
  });
 
  render() {
    const {
      actions: { toggleAdminPanel: toggleActivPanel },
      toggleAdminPanel: togglePanel,
      configData,
      t,
    } = this.props;

    return (
      <span
        className="custom-nav admin-nav"
        onMouseOver={() => toggleActivPanel(true)}
        onFocus={() => toggleActivPanel(true)}
        onMouseLeave={() => toggleActivPanel(false)}
        onBlur={() => toggleActivPanel(false)}>
        <span className="fa-cogs">
          <FontAwesomeIcon className="far fa-2xl" icon={faCogs} size="lg" />
        </span>
        <span>{t('ADMINISTRATION')}</span>
        {
          (togglePanel) ? (
            <div
              className="admin-panel administration"
              onMouseOver={() => toggleActivPanel(true)}
              onFocus={() => toggleActivPanel(true)}
              onMouseLeave={() => toggleActivPanel(false)}
              onBlur={() => toggleActivPanel(false)}>
              <ul>
                {this.getRoutes((ADMIN_SUB_NAV_ROUTES(configData)))}
              </ul>
            </div>
          ) : ''
        }
      </span>
    );
  }
}

const mapStateToProps = (state) => ({
  ...state.admin_nav,
  accessPermissions: loginSelectors.accessPermissionsSelector(state),
  configData: loginSelectors.configDataSelector(state),
  authType: loginSelectors.authTypeSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    toggleAdminPanel,
  }, dispatch);

  return {
    actions,
  };
};

export default withTranslation()(connect(
  mapStateToProps,
  mapDispatchToProps,
)(AdminNav));

export { AdminNav };
