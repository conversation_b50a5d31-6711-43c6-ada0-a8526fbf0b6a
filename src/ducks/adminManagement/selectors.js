import { createSelector } from 'reselect';
import { getFormValues } from 'redux-form';

import * as constants from './constants';

export const baseSelector = (state) => state[constants.REDUCER_KEY];

export const loadingSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA_LOADING],
);

export const errorSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA_ERROR],
);

export const dataTableSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA_TABLE] || {},
);

export const dataSelector = createSelector(
  baseSelector,
  (state) => state[constants.DATA] || {},
);

export const appDataSelector = createSelector(
  baseSelector,
  (state) => state[constants.APP_DATA] || {},
);

export const modalLoadingSelector = createSelector(
  baseSelector,
  (state) => state[constants.MODAL_LOADING],
);

export const addAdminSelector = createSelector(
  baseSelector,
  (state) => state[constants.ADD_ADMIN],
);

export const viewAdminSelector = createSelector(
  baseSelector,
  (state) => state[constants.VIEW_ONLY],
);

export const modalTitleAdminSelector = createSelector(
  baseSelector,
  (state) => state[constants.MODAL_TITLE],
);

export const selectedRowIDSelector = createSelector(
  baseSelector,
  (state) => state[constants.SELECTED_ROW_ID],
);

export const samlEnabled = createSelector(
  baseSelector,
  (state) => state[constants.SAML_ENABLED],
);

export const samlProductId = createSelector(
  baseSelector,
  (state) => state[constants.SAML_PRODUCTID],
);

export const samlCertFileName = createSelector(
  baseSelector,
  (state) => state[constants.SAML_CERT_FILENAME],
);

export const samlIssuers = createSelector(
  baseSelector,
  (state) => state[constants.SAML_ISSUERS],
);

export const searchDataSelector = createSelector(
  baseSelector,
  (state) => state[constants.SEARCH_DATA],
);

export const downloadXml = createSelector(
  baseSelector,
  (state) => state[constants.DONWLOAD_XML],
);

export const columnsSelector = createSelector(
  baseSelector,
  (state) => state[constants.COLUMNS] || [],
);

export const usersRowSelector = createSelector(
  baseSelector,
  (usersState) => usersState.data || [],
);

export const passwordExpirationSelector = createSelector(
  baseSelector,
  (state) => state[constants.PASSWORD_EXPIRATION],
);

export const showAdminExistsConfirm = createSelector(
  baseSelector,
  (state) => state[constants.SHOW_ADMIN_EXISTS_CONFIRM],
);

export const oneIdentityEnabledSelector = createSelector(
  baseSelector,
  (state) => state[constants.IS_ONE_IDENTITY_ENABLED],
);

export const formValuesSelector = (state) => getFormValues('adminManagementPage')(state);
export const formDataValuesSelector = (state) => getFormValues('adminManagement')(state);
export const formDataPasswordExperationSelector = (state) => getFormValues('adminManagementPasswordExperation')(state);

export const formDataSelector = createSelector(
  baseSelector,
  formValuesSelector,
  (data, formValues) => formValues || data,
);

// export const selectedTab = createSelector(
//   baseSelector,
//   state => state[constants.SELECTED_TAB],
// );

// alway return top level selector
export default baseSelector;
