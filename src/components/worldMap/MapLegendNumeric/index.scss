@import "scss/colors.scss";
@import 'scss/mixins.scss';

.ec-root-page {
.map-legend-numeric {
    height: 50px;
    width: 101.5%;
    bottom: 0;
    padding: 5px 10px;
    border-top: 1px solid $grey8;
    background-color: $grey17;
    position: absolute;
    font-size: 11px;
    color: $grey4;
    .container {
      line-height: 20px;
      height: 100%;
      @include DisplayFlex;
      justify-content: space-between;
      align-items: center;
      background: inherit;
      .data {
        height: 24px;	width: 20px;	color: var(--semantic-color-content-base-primary); 	
        font-size: 16px;	
        line-height: 24px;  
        margin-right: 10px;
      }
      .legend{
        height: 15px;	
        width: 77px;	
        color:  var(--semantic-color-content-base-primary);	
        font-size: 13px;	
        line-height: 15px;
      }
      .info{
        margin-left: 10px;
        .info-dc{
          float: left;
          margin-right: 3em;
        }
        .info-ec{
          float: right;
        }
      }
      .legend-detail{
        height: 15px;	
        width: 77px;	
        color:  var(--semantic-color-content-base-primary);	
        font-size: 13px;	
        line-height: 15px;
      }
      .statuses {
        @include DisplayFlex;
        margin-left: 10px;
        div {
          margin-left: 5px;
          @include DisplayFlex;
          align-items: center;
          .legend {
            margin-right: 20px;
          }
        }
      }
    }
  }
}