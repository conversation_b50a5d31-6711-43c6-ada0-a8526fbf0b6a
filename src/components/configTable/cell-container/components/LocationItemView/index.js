import React, { useState, useRef, useEffect } from 'react';
import ReactTooltip from 'react-tooltip';
import { isEmpty } from 'utils/lodash';
import PropTypes from 'prop-types';

function LocationItemView({ label, locations }) {
  const labelRef = useRef(null);
  const valueRef = useRef(null);
  const [isLabelEllipsisActive, setIsLabelEllipsisActive] = useState(false);
  const [isValueEllipsisActive, setIsValueEllipsisActive] = useState(false);

  useEffect(() => {
    if ((!labelRef.current) || (!valueRef.current)) return;
    
    if (labelRef.current.offsetWidth < labelRef.current.scrollWidth) {
      setIsLabelEllipsisActive(true);
    }
    if (valueRef.current.offsetWidth < valueRef.current.scrollWidth) {
      setIsValueEllipsisActive(true);
    }
  }, [labelRef, valueRef, label, locations]);

  const locationsValue = (
    <>
      {locations.map((item) => (
        <span key={item.id} className={`policy-table-criteria-item-data-item ${item.deleted ? 'strike-through' : ''}`}>
          {item.name}
        </span>
      ))}
    </>
  );
  const itemId = locations.map((item) => (item.name)).join(', ');

  return (
    <div style={{ position: 'relative' }}>
      <div className="policy-table-criteria-item">
        {!isEmpty(label) && (
          <div className="policy-table-criteria-item-label">
            <span
              ref={labelRef}
              data-html
              data-for="secondaryTooltip"
              data-tip={isLabelEllipsisActive ? label : ''}
              className={`policy-table-cell-text ${isLabelEllipsisActive ? 'has-elipsis' : ''}`}>
              {label}
            </span>
          </div>
        )}
        <div className="policy-table-criteria-item-data">
          <span
            ref={valueRef}
            data-tip
            data-for={itemId}
            className={`policy-table-cell-data ${isValueEllipsisActive ? 'has-elipsis' : ''}`}>
            {locationsValue}
            {isValueEllipsisActive && (
              <ReactTooltip
                id={itemId}
                clickable
                place="top"
                type="light"
                offset={{ top: -10 }}
                effect="solid"
                disable={(locationsValue.length === 0)}
                delayHide={30}
                border
                borderColor="#939393"
                className="form-field-tooltip-container">
                <div id={`tooltip-top-${itemId}`} className="tooltip-top tooltip-top-text">
                  <div className="help-text">{locationsValue}</div>
                </div>
              </ReactTooltip>
            )}
          </span>
        </div>
      </div>
    </div>
  );
}

LocationItemView.propTypes = {
  label: PropTypes.string,
  locations: PropTypes.arrayOf(PropTypes.shape()),
};
  
LocationItemView.defaultProps = {
  label: '',
  locations: [],
};

export default LocationItemView;
