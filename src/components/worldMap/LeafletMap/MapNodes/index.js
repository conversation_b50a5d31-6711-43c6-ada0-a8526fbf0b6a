/* eslint-disable max-len */
/* eslint-disable react/prop-types */
// @flow
import React from 'react';
import PropTypes from 'prop-types';
import { renderToStaticMarkup } from 'react-dom/server';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faBuilding } from '@fortawesome/pro-regular-svg-icons';
import AWS from 'images/aws.png';
import awsActive from 'images/awsActive.png';
import awsInactive from 'images/awsInactive.png';
import azure from 'images/azure.png';
import azureActive from 'images/azureActive.png';
import azureInactive from 'images/azureInactive.png';
import gcp from 'images/gcp.png';
import gcpActive from 'images/gcpActive.png';
import gcpInactive from 'images/gcpInactive.png';
import { divIcon } from 'leaflet';
import { Marker, Popup } from 'react-leaflet';
import ConnectorTooltip from 'commonConnectedComponents/GeoPopup/ConnectorTooltip';
import { WrappedTrafficTooltip } from '../TrafficTooltip';

// DEPLOYED || DEFAULT
const awsImg = renderToStaticMarkup(
  <img src={AWS} className="cluster-child-icon" alt="AWS" />,
);
  
const awsIcon = divIcon({
  html: awsImg,
});
  
// AWS ACTIVE
const awsImgActive = renderToStaticMarkup(
  <img src={awsActive} className="cluster-child-icon-active" alt="AWS active" />,
);
  
const awsIconActive = divIcon({
  html: awsImgActive,
});
  
// AWS INACTIVE
const awsImgInactive = renderToStaticMarkup(
  <img src={awsInactive} className="cluster-child-icon-inactive" alt="AWS Inactive" />,
);
  
const awsIconInactive = divIcon({
  html: awsImgInactive,
});
  
// Azure Default
const azureDefImg = renderToStaticMarkup(
  <img src={azure} className="cluster-child-icon-active" alt="Azure" />,
);
  
const azureDefIcon = divIcon({
  html: azureDefImg,
});
  
// Azure ACTIVE
const azureImgActive = renderToStaticMarkup(
  <img src={azureActive} className="cluster-child-icon-active" alt="Azure Active" />,
);
  
const azureIconActive = divIcon({
  html: azureImgActive,
});
  
// Azure INACTIVE
const azureImgInactive = renderToStaticMarkup(
  <img src={azureInactive} className="cluster-child-icon-inactive" alt="Azure Inactive" />,
);
  
const azureIconInactive = divIcon({
  html: azureImgInactive,
});

// GCP Default
const gcpDefImg = renderToStaticMarkup(
  <img src={gcp} className="cluster-child-icon-active" alt="GCP" />,
);
  
const gcpDefIcon = divIcon({
  html: gcpDefImg,
});
  
// GCP ACTIVE
const gcpImgActive = renderToStaticMarkup(
  <img src={gcpActive} className="cluster-child-icon-active" alt="GCP Active" />,
);
  
const gcpIconActive = divIcon({
  html: gcpImgActive,
});
  
// GCP INACTIVE
const gcpImgInactive = renderToStaticMarkup(
  <img src={gcpInactive} className="cluster-child-icon-inactive" alt="GCP Inactive" />,
);
  
const gcpIconInactive = divIcon({
  html: gcpImgInactive,
});

// DEPLOYED || DEFAULT Branch
const branchDefImg = renderToStaticMarkup(
  <FontAwesomeIcon icon={faBuilding} className="fontStyle fas map-icon-branch" alt="Branch" />,
);
  
const branchDefaultIcon = divIcon({
  html: branchDefImg,
});
  
// Branch ACTIVE
const branchImgActive = renderToStaticMarkup(
  <FontAwesomeIcon icon={faBuilding} className="fontStyle fas map-icon-branch-active" alt="Branch Active" />,
);
  
const branchIconActive = divIcon({
  html: branchImgActive,
});
  
// Branch INACTIVE
const branchImgInactive = renderToStaticMarkup(
  <FontAwesomeIcon icon={faBuilding} className="fontStyle fas map-icon-branch-inactive" alt="Branch Inactive logo" />,
);
  
const branchIconInactive = divIcon({
  html: branchImgInactive,
});

const formatter = (coOrd) => {
  const formatCoOrd = coOrd / 10000000;
  return formatCoOrd.toFixed(6);
};

function MapNodes({
  data, showConnectorToolTip, showTrafficTooltip, isZeroTrustGateway, actions,
}) {
  return data.map((item, index) => {
    const {
      geoLocation,
      deploymentType,
      deviceType,
      status,
    } = item;
    // const [loading, setLoading] = useEffect(false);
    let icon = awsIcon;
    const lat = (geoLocation && 'latitude' in geoLocation) ? formatter(geoLocation.latitude) : 0;
    const log = (geoLocation && 'longitude' in geoLocation) ? formatter(geoLocation.longitude) : 0;
      
    if (deploymentType && deploymentType === 'AWS') {
      if (status && status === 'Active') {
        icon = awsIconActive;
      } else if (status && status === 'Inactive') {
        icon = awsIconInactive;
      } else {
        icon = awsIcon;
      }
    } else if (deploymentType && deploymentType === 'AZURE') {
      if (status && status === 'Active') {
        icon = azureIconActive;
      } else if (status && status === 'Inactive') {
        icon = azureIconInactive;
      } else {
        icon = azureDefIcon;
      }
    } else if (deploymentType && deploymentType === 'GCP') {
      if (status && status === 'Active') {
        icon = gcpIconActive;
      } else if (status && status === 'Inactive') {
        icon = gcpIconInactive;
      } else {
        icon = gcpDefIcon;
      }
    } else if (deviceType === 'PHYSICAL' || (deploymentType
                && (deploymentType === 'CENTOS'
                  || deploymentType === 'REDHAT_LINUX'
                  || deploymentType === 'MICROSOFT_HYPER_V'
                  || deploymentType === 'VMWARE_ESXI'))) {
      if (status && status === 'Active') {
        icon = branchIconActive;
      } else if (status && status === 'Inactive') {
        icon = branchIconInactive;
      } else {
        icon = branchDefaultIcon;
      }
    } // CENTOS REDHAT_LINUX VMWARE_ESXI
      
    return (
      <Marker
      // eslint-disable-next-line react/no-array-index-key
        key={`${geoLocation}${index}`}
        position={[
          lat,
          log,
        ]}
        icon={icon}>
        <Popup
          minWidth={250}
          closeButton={false}
          onOpen={async () => {
            await actions.popupOpen(item);
          }}
          maxHeight="auto">
          <WrappedTrafficTooltip {...item} showTrafficTooltip={showTrafficTooltip} />
          <ConnectorTooltip
            isZeroTrustGateway={isZeroTrustGateway}
            showConnectorToolTip={showConnectorToolTip}
            connectorCloudInfo={item}
            cloudInfo={item} />
        </Popup>
      </Marker>
    );
  });
}

MapNodes.propTypes = {
  isZeroTrustGateway: PropTypes.bool,
  showConnectorToolTip: PropTypes.bool,
  showTrafficTooltip: PropTypes.bool,
  data: PropTypes.arrayOf(PropTypes.shape({})),
  actions: PropTypes.shape({}),
};

MapNodes.defaultProps = {
  isZeroTrustGateway: false,
  showConnectorToolTip: false,
  showTrafficTooltip: false,
  data: [],
  actions: {
    handlePopupOpen: null,
    handlePopupClose: null,
  },
};

export default MapNodes;
