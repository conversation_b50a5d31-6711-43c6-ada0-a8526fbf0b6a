// @flow

import React from 'react';
import PropTypes from 'prop-types';

function DrawPath(props) {
  const {
    p,
    markerStart,
    markerEnd,
    key,
  } = props;
  return (
    <g
      fill="none"
      className="traffic-path"
      stroke="#D6D7DB"
      strokeWidth="2"
      markerStart={markerStart}
      markerEnd={markerEnd}
      key={key}>
      <path d={p} stroke="#D6D7DB" strokeWidth="1" fill="transparent" />
    </g>
  );
}

DrawPath.propTypes = {
  p: PropTypes.string,
  markerStart: PropTypes.string,
  markerEnd: PropTypes.string,
  key: PropTypes.string,
};

DrawPath.defaultProps = {
  p: '',
  markerStart: '',
  markerEnd: '',
  key: '',
};

export default DrawPath;
