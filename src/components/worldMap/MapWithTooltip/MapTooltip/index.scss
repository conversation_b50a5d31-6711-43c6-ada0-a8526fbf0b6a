@import "scss/colors.scss";

.ec-root-page {
.world-map-tooltip {
    box-shadow: 0 1px 8px 0 $grey16;
    background: $white;
    opacity: 1;
    padding: 12px;
    line-height: 19px;
    .location-name {
      padding-bottom: 3px;
      font-size: 10px;
      color: $grey4;
      opacity: .5;
      > span {
        padding-left: 5px;
        font-size: 11px;
        font-weight: bold;
        letter-spacing:  0.06px;
        line-height: 12px;
      }
    }
    span.good {
      color: $green2;
    }
    span.okay {
      color: $orange2;
    }
    span.poor {
      color: $red3;
    }
  }


.fontStyle {
  height: 14px;	width: 13px;	color: var(--semantic-color-content-interactive-primary-default);	font-size: 14px;	line-height: 14px;
}

.__react_component_tooltip { 
  padding: 0;
}

.mask {
  height: 138px;
  width: 150px;
  border-radius: 4px;
  background-color: $white; // var(--semantic-color-surface-base-primary);
  box-shadow: 0 3px 8px 0 $blackShadow2;// rgba(176,186,197,0.6);
 }

.line {	height: .05em; border: .5px solid $grey26; } // #DDDDDD; }

// bule2 var(--semantic-color-content-interactive-primary-default)
.tooltipHeader {	
  height: 15.36px;	
  width: 70px;	
  color: var(--semantic-color-content-interactive-primary-default);	
  font-size: 13px;	
  line-height: 15px;
  padding-left: .5em;
}
.tooltip-content {
  padding: 0.1em;
  > span {
    display: block;
    padding-bottom: .5em;
  }
  .info {	
    height: 20px;	
    width: 125px;	
    color: var(--semantic-color-content-base-primary); 	
    font-size: 11px;	
    font-weight: 500;	
    letter-spacing: 1px;	
    line-height: 20px;
  }
}
}