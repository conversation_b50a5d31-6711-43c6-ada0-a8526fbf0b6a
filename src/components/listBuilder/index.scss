@import 'scss/pages.scss';
@import "scss/colors.scss";
@import 'scss/mixins.scss';

.ec-root-page {
.list-builder {
    display: block;
    height: auto;
    max-height: none;
    width: auto;
    .list-builder-header{
      position: relative;
      white-space: nowrap;
      display: flex;
      align-items: center;
      .list-builder-input-textarea {
        width: 436px;
        border: 1px solid var(--semantic-color-border-base-primary);
        border-radius: 5px 5px 0 0;
        box-shadow: none;
        color: var(--semantic-color-content-base-primary);
        line-height: 15px;
        background: var(--semantic-color-background-primary);
        resize: none;
        cursor: text;
        display: inline-block;
        padding: 9px 8px;
        text-align: left;
        min-width: 412px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        height: 32px;
        &:focus{
          height: 150px;        
        }
        &.non-empty {
          border: 1px solid $button-bkgrd-color;
        }        
      }
      .list-builder-input-textarea.invalid {
        border: 1px solid var(--semantic-color-border-status-danger-active);
        box-shadow: none;
      }
      .list-builder-add {
        margin-left: 5px;
        display: inline-block;
        min-width: 105px;
      }         
    }

    .list-builder-body {
      width: 436px;
      background: var(--semantic-color-background-primary);
      border: 1px solid var(--semantic-color-border-base-primary);
      border-radius: 0 0 5px 5px;
      border-top: 0;

      .list-builder-search-container{
        padding: 12px;
        .search-input-text{
          width: calc(100% - 30px);
        }
        .search-container {
          background: var(--semantic-color-surface-base-primary);
          border-radius: 8px;
          width: 100%;
          display: flex;
          align-items: center;
        }
      }
      .list-builder-list-overflow {
        max-height: 170px;
        overflow-x: hidden;
        overflow-y: auto;
        .list-builder-list {
          background: var(--semantic-color-background-primary);
          color: var(--semantic-color-content-interactive-primary-default);
          .list-builder-list-item {
            padding: 10px;
            padding-right: 30px;
            position: relative;
            vertical-align: top;
            width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;  
            &:hover {
              background-color: var(--semantic-color-surface-fields-hover);
              cursor: default;
            }
            .list-builder-remove {
              color: var(--semantic-color-content-status-info-primary);
              font-size: 12px;
              position: absolute;
              right: 10px;
              width: 12px;
            }
          }
          .list-builder-list-item.list-builder-invalid {
            color: var(--semantic-color-content-status-danger-primary);
          }
        }
      }
    }    
    .list-builder-footer-container {
      // box-shadow: 0px 2px 4px rgba(42, 44, 8, 0.15) inset;
      padding: 8px 12px;
      background: var(--semantic-color-background-primary);
      color:  var(--semantic-color-content-base-primary);
      border: 1px solid var(--semantic-color-border-base-primary);      
      border-radius: 0 0 5px 5px;
      .list-builder-footer-left {
        display: inline-block;
        vertical-align: middle;
        width: 50%;
      }
      .list-builder-footer-right {
        display: inline-block;
        vertical-align: middle;
        width: 50%;
        text-align: right;
        .list-builder-remove-all {
          background: none;
          color: var(--semantic-color-content-interactive-primary-default);
          cursor: pointer !important;
          font-weight: 500;
          padding: 9px 0px;
          padding-left: 0px;
          text-align: left;
        }
      }
    }    
    .list-builder-tooltip-container {
		min-width: 260px;
		min-height: 30px;
		overflow: hidden;
		padding: 12px 16px;
		word-wrap: break-word;
	}
  }
}