# AI Assistant Prompt: React App to Dual-Purpose Library Converter

**COPY AND PASTE THIS ENTIRE CONTENT INTO ANOTHER VSCODE AUGMENT CODE SESSION TO AUTOMATICALLY CONVERT ANY REACT APPLICATION INTO A DUAL-PURPOSE LIBRARY**

---

## INSTRUCTIONS FOR AI ASSISTANT

You are tasked with analyzing an existing React application and converting it into a dual-purpose library that can function both as a standalone application and as a reusable npm package. Follow this systematic approach based on the iam-admin-ui reference implementation.

## PHASE 1: AUTOMATIC ANALYSIS

### Step 1: Project Structure Analysis
Execute this analysis automatically:

```bash
# Run these commands to understand the project structure
find src -type d -maxdepth 3 | sort
find src -name "*.jsx" -o -name "*.js" -o -name "*.ts" -o -name "*.tsx" | head -30
ls -la src/
cat package.json | grep -A 20 '"dependencies"'
cat package.json | grep -A 10 '"scripts"'
```

Then examine and document:
1. **Entry Points**: Locate `src/index.js`, `src/main.js`, or equivalent
2. **App Component**: Find the main App component
3. **Pages/Views**: Identify top-level route components (usually in `/pages`, `/views`, `/screens`)
4. **Components**: Catalog reusable components (usually in `/components`)
5. **State Management**: Find Redux/Zustand/Context setup (usually in `/store`, `/state`, `/ducks`)
6. **Routing**: Locate router configuration
7. **Styles**: Find main stylesheet and styling approach
8. **Utils**: Identify utility functions and helpers

### Step 2: Dependency Analysis
Examine `package.json` and categorize dependencies:

```javascript
// Automatically categorize dependencies into:
const dependencyCategories = {
  // These should become peer dependencies (consumer provides)
  peerDependencies: [
    'react', 'react-dom', 'react-router-dom',
    '@reduxjs/toolkit', 'react-redux', 'i18next', 'react-i18next',
    '@emotion/react', '@emotion/styled', '@mui/material',
    'styled-components', 'framer-motion'
  ],

  // These should be bundled with the library
  bundledDependencies: [
    'axios', 'lodash-es', 'dayjs', 'prop-types', 'classnames',
    'uuid', 'date-fns', 'validator', 'crypto-js'
  ]
};
```

### Step 3: Exportable Component Identification
Scan the codebase and identify components suitable for export by examining:
- All files in `/pages` or `/views` directories
- Key components in `/components` directory
- Provider components
- Utility functions in `/utils` or `/helpers`
- Custom hooks in `/hooks`

## PHASE 2: AUTOMATIC IMPLEMENTATION

### Step 1: Create Library Webpack Configuration

Create `webpack.library.config.js`:

```javascript
const path = require('path');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const webpack = require('webpack');

module.exports = (env, argv) => {
  const isProdMode = argv.mode === 'production';

  return {
    mode: isProdMode ? 'production' : 'development',
    entry: {
      index: './src/lib-entry.js',
    },
    devtool: 'source-map',
    output: {
      path: path.resolve(__dirname, 'dist'),
      filename: '[name].js',
      library: {
        name: 'ReactLibrary',
        type: 'umd',
      },
      globalObject: 'this',
      clean: true,
    },
    externals: [
      /^react($|\/)/,
      /^react-dom($|\/)/,
      /^react-router-dom($|\/)/,
      /^@reduxjs\/toolkit($|\/)/,
      /^react-redux($|\/)/,
      /^i18next($|\/)/,
      /^react-i18next($|\/)/,
      /^@emotion\/react($|\/)/,
      /^@emotion\/styled($|\/)/,
      /^@mui\/material($|\/)/,
      /^styled-components($|\/)/,
      /^framer-motion($|\/)/,
    ],
    module: {
      rules: [
        {
          test: /\.(js|jsx|ts|tsx)$/,
          use: 'swc-loader',
          exclude: /node_modules/,
        },
        {
          test: /\.(css|scss|sass)$/,
          use: [
            isProdMode && MiniCssExtractPlugin.loader,
            !isProdMode && 'style-loader',
            'css-loader',
            'resolve-url-loader',
            {
              loader: 'sass-loader',
              options: {
                sourceMap: true,
              },
            },
          ].filter(Boolean),
        },
        {
          test: /\.(svg|png|jpg|jpeg|gif|woff|woff2|ttf|eot)$/,
          type: 'asset/resource',
        },
      ],
    },
    resolve: {
      extensions: ['.js', '.jsx', '.ts', '.tsx', '...'],
    },
    plugins: [
      new MiniCssExtractPlugin({
        filename: '[name].css',
      }),
      new webpack.DefinePlugin({
        'process.env.IS_LIBRARY_MODE': true,
      }),
    ],
    optimization: {
      minimize: isProdMode,
      minimizer: [
        new TerserPlugin({
          minify: TerserPlugin.swcMinify,
          terserOptions: {
            compress: {
              drop_console: true,
              drop_debugger: true,
            },
          },
        }),
      ],
    },
  };
};
```

### Step 2: Create Library Entry Point

Create `src/lib-entry.js` - This file will export all components that should be available when the library is consumed:

```javascript
// Import library-specific styles
import './styles/library.scss';

// Export all pages (scan pages directory and export all components)
// Example exports - replace with actual page components found in analysis
export { default as DashboardPage } from './pages/Dashboard/DashboardPage';
export { default as HomePage } from './pages/Home/HomePage';
export { default as ProfilePage } from './pages/Profile/ProfilePage';
export { default as SettingsPage } from './pages/Settings/SettingsPage';
export { default as LoginPage } from './pages/Auth/LoginPage';
export { default as RegisterPage } from './pages/Auth/RegisterPage';

// Export key reusable components (scan components directory)
export { default as Header } from './components/Header/Header';
export { default as Sidebar } from './components/Sidebar/Sidebar';
export { default as Navigation } from './components/Navigation/Navigation';
export { default as Footer } from './components/Footer/Footer';
export { default as Modal } from './components/Modal/Modal';
export { default as Button } from './components/Button/Button';
export { default as Input } from './components/Input/Input';
export { default as Table } from './components/Table/Table';
export { default as Card } from './components/Card/Card';
export { default as Form } from './components/Form/Form';

// Export providers
export { default as AppProvider } from './providers/AppProvider';

// Export utilities and hooks
export { default as httpClient } from './utils/http';
export { initializeHttpClient } from './utils/http';
export { default as authUtils } from './utils/auth';
export { default as storageUtils } from './utils/storage';

// Export custom hooks if they exist
export { default as useAuth } from './hooks/useAuth';
export { default as useApi } from './hooks/useApi';
export { default as useLocalStorage } from './hooks/useLocalStorage';

// Export types if using TypeScript
export type { User, UserRole, ApiResponse } from './types';
```

### Step 3: Create Library Provider

Create `src/providers/AppProvider.jsx`:

```javascript
'use client';

import React, { useEffect } from 'react';
import PropTypes from 'prop-types';

// Import existing providers based on what's found in the project
import { Provider } from 'react-redux';
import { ThemeProvider } from '@mui/material/styles'; // or styled-components, emotion, etc.
import { I18nextProvider } from 'react-i18next';

// Import existing store and configuration
import store from '../store'; // or wherever the store is located
import theme from '../theme'; // or wherever theme is defined
import i18n from '../i18n'; // or wherever i18n is configured
import { initializeHttpClient } from '../utils/http';

const APP_CONTAINER_ID = 'react-library-container';
const PORTAL_ROOT_ID = 'react-library-portal';

const AppProvider = ({ children, config = {} }) => {
  // Initialize library-specific configuration
  useEffect(() => {
    if (config.httpClient) {
      initializeHttpClient(config.httpClient);
    }

    if (config.theme) {
      // Apply custom theme if provided
    }

    if (config.i18n) {
      // Apply custom i18n configuration if provided
    }
  }, [config]);

  return (
    <>
      <div id={APP_CONTAINER_ID}>
        <Provider store={store}>
          <ThemeProvider theme={config.theme || theme}>
            <I18nextProvider i18n={i18n}>
              {children}
            </I18nextProvider>
          </ThemeProvider>
        </Provider>
      </div>
      <div id={PORTAL_ROOT_ID} />
    </>
  );
};

AppProvider.propTypes = {
  children: PropTypes.node.isRequired,
  config: PropTypes.shape({
    httpClient: PropTypes.shape({
      baseURL: PropTypes.string.isRequired,
      getToken: PropTypes.func,
      timeout: PropTypes.number,
      headers: PropTypes.object,
    }),
    theme: PropTypes.object,
    i18n: PropTypes.object,
  }),
};

export default AppProvider;
```

### Step 4: Create Library-Specific Styles

Create `src/styles/library.scss`:

```scss
// Container scoping for library mode to prevent CSS conflicts
#react-library-container,
#react-library-portal {
  // Import all existing styles within scoped container
  @import '../index.css'; // or main stylesheet path found in analysis
  @import '../App.css'; // if exists

  // Ensure proper scoping and CSS custom properties
  color: var(--text-primary, #333333);
  background-color: var(--bg-primary, #ffffff);
  font-family: var(--font-family, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif);

  // Reset any global styles that might conflict with consumer app
  * {
    box-sizing: border-box;
  }

  // Override potentially conflicting global styles
  body, html {
    margin: unset !important;
    padding: unset !important;
    font-family: unset !important;
    background: unset !important;
  }

  // Ensure all component styles are scoped
  .app {
    // Scoped app styles
  }

  .header {
    // Scoped header styles
  }

  .sidebar {
    // Scoped sidebar styles
  }

  .main-content {
    // Scoped main content styles
  }

  // Define CSS custom properties for theming
  :root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;

    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.25rem;
    --font-size-xl: 1.5rem;

    --border-radius: 0.375rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);

    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 3rem;
  }
}
```

### Step 5: Adapt HTTP Client

Create or modify `src/utils/http.js` to support both standalone and library modes:

```javascript
import axios from 'axios';

const http = axios.create({
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

let libraryConfig = null;

export function initializeHttpClient(config) {
  libraryConfig = config;

  // Apply configuration immediately
  if (config.baseURL) {
    http.defaults.baseURL = config.baseURL;
  }

  if (config.timeout) {
    http.defaults.timeout = config.timeout;
  }

  if (config.headers) {
    Object.assign(http.defaults.headers, config.headers);
  }
}

// Add conditional logic for library mode
if (process.env.IS_LIBRARY_MODE) {
  http.interceptors.request.use((config) => {
    if (!libraryConfig) {
      throw new Error(
        'HTTP client not initialized. Call initializeHttpClient() first.'
      );
    }

    // Apply library-specific configuration
    config.baseURL = libraryConfig.baseURL;

    if (libraryConfig.getToken) {
      const token = libraryConfig.getToken();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }

    return config;
  });

  http.interceptors.response.use(
    (response) => response,
    (error) => {
      // Library-specific error handling
      console.error('HTTP Error in library mode:', error);
      return Promise.reject(error);
    }
  );
} else {
  // Standalone mode - use existing configuration
  http.defaults.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:3001';
  http.defaults.withCredentials = true;

  // Add existing interceptors for standalone mode
  http.interceptors.response.use(
    (response) => response,
    (error) => {
      // Standalone error handling
      if (error.response?.status === 401) {
        // Handle unauthorized access
        window.location.href = '/login';
      }
      return Promise.reject(error);
    }
  );
}

export default http;
```

### Step 6: Update Package.json Configuration

Modify `package.json` to support dual-purpose builds:

```json
{
  "name": "@your-org/your-library-name",
  "version": "1.0.0",
  "description": "Dual-purpose React library - standalone app and reusable components",
  "main": "dist/index.js",
  "module": "dist/index.js",
  "types": "dist/index.d.ts",
  "files": [
    "dist",
    "README.md"
  ],
  "scripts": {
    "start": "webpack serve --mode development",
    "build": "webpack --mode production",
    "build-lib": "webpack --config webpack.library.config.js --mode production",
    "prepublishOnly": "npm run build-lib",
    "test-lib": "npm run build-lib && npm run validate",
    "validate": "node scripts/validate-build.js",
    "analyze": "npm run build-lib && npx webpack-bundle-analyzer dist/index.js"
  },
  "peerDependencies": {
    "react": ">=18.0.0",
    "react-dom": ">=18.0.0",
    "react-router-dom": ">=6.0.0",
    "@reduxjs/toolkit": ">=1.9.0",
    "react-redux": ">=8.0.0",
    "i18next": ">=22.0.0",
    "react-i18next": ">=12.0.0"
  },
  "peerDependenciesMeta": {
    "react-router-dom": {
      "optional": true
    },
    "i18next": {
      "optional": true
    },
    "react-i18next": {
      "optional": true
    }
  },
  "dependencies": {
    "axios": "^1.6.0",
    "prop-types": "^15.8.1",
    "classnames": "^2.3.2",
    "lodash-es": "^4.17.21"
  },
  "devDependencies": {
    "@swc/core": "^1.3.0",
    "swc-loader": "^0.2.3",
    "webpack": "^5.89.0",
    "webpack-cli": "^5.1.4",
    "webpack-dev-server": "^4.15.1",
    "mini-css-extract-plugin": "^2.7.6",
    "css-loader": "^6.8.1",
    "sass-loader": "^13.3.2",
    "resolve-url-loader": "^5.0.0",
    "terser-webpack-plugin": "^5.3.9"
  },
  "publishConfig": {
    "registry": "https://registry.npmjs.org/"
  },
  "keywords": [
    "react",
    "library",
    "components",
    "ui",
    "dual-purpose"
  ],
  "author": "Your Name",
  "license": "MIT"
}
```

### Step 7: Create Build Validation Script

Create `scripts/validate-build.js`:

```javascript
const fs = require('fs');
const path = require('path');

console.log('🔍 Validating library build...');

// Check if dist files exist
const requiredFiles = ['dist/index.js', 'dist/index.css', 'dist/index.js.map'];
let allFilesExist = true;

requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ Found: ${file}`);
  } else {
    console.error(`❌ Missing required file: ${file}`);
    allFilesExist = false;
  }
});

if (!allFilesExist) {
  process.exit(1);
}

// Check bundle sizes
const jsStats = fs.statSync('dist/index.js');
const cssStats = fs.statSync('dist/index.css');

console.log(`📦 Bundle sizes:`);
console.log(`   JS: ${(jsStats.size / 1024 / 1024).toFixed(2)} MB`);
console.log(`   CSS: ${(cssStats.size / 1024).toFixed(2)} KB`);

// Warn if bundles are too large
if (jsStats.size > 2 * 1024 * 1024) { // 2MB
  console.warn(`⚠️  JS bundle is large (${(jsStats.size / 1024 / 1024).toFixed(2)} MB). Consider optimization.`);
}

if (cssStats.size > 500 * 1024) { // 500KB
  console.warn(`⚠️  CSS bundle is large (${(cssStats.size / 1024).toFixed(2)} KB). Consider optimization.`);
}

// Test imports
try {
  const lib = require('./dist/index.js');
  console.log('✅ Library imports successfully');

  const exports = Object.keys(lib);
  console.log(`📋 Available exports (${exports.length}):`, exports.slice(0, 10).join(', ') + (exports.length > 10 ? '...' : ''));

  // Check for required exports
  const requiredExports = ['AppProvider'];
  const missingExports = requiredExports.filter(exp => !exports.includes(exp));

  if (missingExports.length > 0) {
    console.error('❌ Missing required exports:', missingExports);
    process.exit(1);
  }

} catch (error) {
  console.error('❌ Library import failed:', error.message);
  process.exit(1);
}

console.log('🎉 Validation complete!');
```

## PHASE 3: AUTOMATIC VALIDATION AND TESTING

### Step 1: Create Usage Documentation

Create `README-LIBRARY.md`:

```markdown
# React Dual-Purpose Library

A React library that can function both as a standalone application and as reusable components.

## Installation

```bash
npm install @your-org/your-library-name
```

## Usage as a Library

### Basic Setup

```javascript
import { AppProvider, DashboardPage, HomePage } from '@your-org/your-library-name';
import '@your-org/your-library-name/dist/index.css';

// Initialize HTTP client for API calls
import { initializeHttpClient } from '@your-org/your-library-name';

initializeHttpClient({
  baseURL: 'https://api.example.com',
  getToken: () => localStorage.getItem('authToken'),
  timeout: 30000,
  headers: {
    'X-Custom-Header': 'value'
  }
});

function MyApp() {
  return (
    <AppProvider config={{
      theme: customTheme,
      i18n: customI18nConfig
    }}>
      <DashboardPage />
      <HomePage />
    </AppProvider>
  );
}
```

### NextJS Integration

```javascript
// app/layout.tsx
import { AppProvider } from '@your-org/your-library-name';
import '@your-org/your-library-name/dist/index.css';

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body>
        <AppProvider>
          {children}
        </AppProvider>
      </body>
    </html>
  );
}

// app/dashboard/page.tsx
import { DashboardPage } from '@your-org/your-library-name';

export default function Dashboard() {
  return <DashboardPage />;
}
```

### Available Components

- **Pages**: DashboardPage, HomePage, ProfilePage, SettingsPage, LoginPage, RegisterPage
- **Components**: Header, Sidebar, Navigation, Footer, Modal, Button, Input, Table, Card, Form
- **Providers**: AppProvider
- **Utilities**: httpClient, initializeHttpClient, authUtils, storageUtils
- **Hooks**: useAuth, useApi, useLocalStorage

### Configuration Options

```javascript
const config = {
  httpClient: {
    baseURL: 'https://api.example.com',
    getToken: () => localStorage.getItem('token'),
    timeout: 30000,
    headers: {}
  },
  theme: {
    // Custom theme object
  },
  i18n: {
    // Custom i18n configuration
  }
};
```

## Development

### Build Commands

```bash
# Build standalone application
npm run build

# Build library
npm run build-lib

# Test library build
npm run test-lib

# Analyze bundle size
npm run analyze
```

### Peer Dependencies

This library requires the following peer dependencies:

- react >=18.0.0
- react-dom >=18.0.0
- react-router-dom >=6.0.0 (optional)
- @reduxjs/toolkit >=1.9.0
- react-redux >=8.0.0
- i18next >=22.0.0 (optional)
- react-i18next >=12.0.0 (optional)

## License

MIT
```

## EXECUTION CHECKLIST FOR AI ASSISTANT

When implementing this conversion, follow this systematic checklist:

### Phase 1: Analysis
- [ ] ✅ Run project structure analysis commands
- [ ] ✅ Examine `package.json` dependencies and categorize them
- [ ] ✅ Identify all exportable components in `/pages`, `/components`, `/hooks` directories
- [ ] ✅ Locate main entry point (`src/index.js` or equivalent)
- [ ] ✅ Find main App component and provider hierarchy
- [ ] ✅ Identify main stylesheet and styling approach
- [ ] ✅ Locate HTTP client or API utilities
- [ ] ✅ Document existing build configuration

### Phase 2: Implementation
- [ ] ✅ Create `webpack.library.config.js` with proper externals based on analysis
- [ ] ✅ Create `src/lib-entry.js` with all identified exportable components
- [ ] ✅ Create `src/providers/AppProvider.jsx` wrapping existing providers
- [ ] ✅ Create `src/styles/library.scss` with scoped styles
- [ ] ✅ Create or modify `src/utils/http.js` for dual-mode support
- [ ] ✅ Update `package.json` with library configuration and scripts
- [ ] ✅ Create `scripts/validate-build.js` validation script
- [ ] ✅ Create `README-LIBRARY.md` usage documentation

### Phase 3: Validation
- [ ] ✅ Run `npm run build-lib` to test library build
- [ ] ✅ Run `npm run validate` to check build output
- [ ] ✅ Verify all exports are available and working
- [ ] ✅ Test CSS scoping prevents conflicts
- [ ] ✅ Ensure both standalone and library modes work
- [ ] ✅ Check bundle sizes are reasonable
- [ ] ✅ Validate peer dependencies are correctly externalized

### Phase 4: Final Steps
- [ ] ✅ Test library consumption in a separate project
- [ ] ✅ Verify HTTP client initialization works
- [ ] ✅ Confirm CSS imports work correctly
- [ ] ✅ Test all exported components render properly
- [ ] ✅ Validate provider configuration options work

## IMPORTANT IMPLEMENTATION NOTES

1. **Replace Placeholders**: Update all `@your-org/your-library-name` references with actual package name
2. **Adapt Imports**: Modify import paths in `lib-entry.js` based on actual project structure
3. **Provider Adaptation**: Adjust `AppProvider.jsx` based on existing provider hierarchy found in analysis
4. **Style Scoping**: Update container IDs and style imports based on actual project structure
5. **HTTP Client**: Adapt HTTP client configuration based on existing API setup
6. **Dependency Management**: Use package managers (npm/yarn/pnpm) for dependency updates, not manual editing

## TROUBLESHOOTING COMMON ISSUES

### Build Errors
- **Missing dependencies**: Add missing packages to `devDependencies`
- **Import errors**: Check file paths in `lib-entry.js` match actual structure
- **CSS errors**: Verify SCSS imports and loader configuration

### Runtime Errors
- **Provider errors**: Ensure all required providers are included in `AppProvider.jsx`
- **HTTP client errors**: Verify `initializeHttpClient` is called before making API requests
- **Style conflicts**: Check CSS scoping is working correctly

### Bundle Size Issues
- **Large bundles**: Review externals in webpack config, consider code splitting
- **Missing externals**: Add commonly used libraries to externals array

---

**FINAL INSTRUCTION**: After completing all steps, provide a summary of:
1. All files created/modified
2. Build commands to use
3. How to consume the library
4. Any manual adjustments needed

This guide ensures consistent, reliable conversion of React applications to dual-purpose libraries following the proven iam-admin-ui pattern.