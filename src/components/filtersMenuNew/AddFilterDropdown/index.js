import { noop } from 'utils/lodash';
import PropTypes from 'prop-types';
import React from 'react';
import { withTranslation } from 'react-i18next';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlus } from '@fortawesome/pro-solid-svg-icons';

import ToolTip from '../../tooltip/ToolTipPane';

class AddFilterDropdown extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      search: '',
      openList: false,
      isTooltipActive: false,
      tooltipValue: '',
    };
    this.wrapperRef = null;
  }

  componentDidMount() {
    document.addEventListener('mousedown', this.handleMousedown);
  }

  componentWillUnmount() {
    document.removeEventListener('mousedown', this.handleMousedown);
  }

  setWrapperRef = (node) => {
    this.wrapperRef = node;
  };

  handleChangeValue = (item) => {
    const { setValue } = this.props;
    this.setState({
      openList: false,
    });
    setValue(item);
  };

  handleMousedown = (e) => {
    const containRef = this.wrapperRef.contains(e.target);
    if (!containRef) {
      this.setState({ openList: false });
    }
  };

  handleOnMouseOverTooltip = (tooltip, element) => {
    const { isTooltipActive } = this.state;
    if (!isTooltipActive && tooltip) {
      this.setState(
        (prevState) => ({
          isTooltipActive: !prevState.isTooltipActive,
          tooltipValue: tooltip,
        }),
        () => {
          this.setPosition(element, { top: -30, left: 70 });
        },
      );
    }
  };

  handleOnMouseOutTooltip = () => {
    this.setState((prevState) => ({
      isTooltipActive: !prevState.isTooltipActive,
      tooltipValue: '',
    }));
  };

  handleOpenList = () => {
    const { openList } = this.state;
    this.setState({ openList: !openList, search: '' });
  };

  handleChangeInput = (event) => {
    this.setState({ search: event.target.value });
  };

  render() {
    let { items } = this.props;
    const { hasSearch, t, tooltip } = this.props;
    const {
      search, openList, isTooltipActive, tooltipValue,
    } = this.state;
    let labelRef = React.createRef();

    if (hasSearch && search) {
      items = items.filter(
        (item) => t(item.label).toLowerCase().indexOf(search.trim().toLowerCase()) !== -1,
      );
    }
    items.sort((a, b) => {
      return a.label.localeCompare(b.label);
    });
    return (
      <div>
        <div
          className="drop-down-container"
          ref={this.setWrapperRef}>
          <button
            className="drop-down-selected-value add-filter-value"
            onClick={this.handleOpenList}
            type="button">
            {tooltip && (
              <ToolTip
                tooltip={tooltipValue}
                isActive={isTooltipActive}
                tooltipOptions={{
                  isValidation: true,
                }}
                // eslint-disable-next-line no-return-assign
                setPosition={(hover) => (this.setPosition = hover)}
                // eslint-disable-next-line react/jsx-handler-names
                closeTooltip={this.handleOnMouseOutTooltip} />
            )}
            <span
              className={`${tooltip ? 'cursorHelp' : ''} toggleTooltip tooltip-right`}
              ref={(node) => {
                labelRef = node;
              }}
              onMouseOver={() => this.handleOnMouseOverTooltip(tooltip, labelRef)}
              onMouseOut={() => this.handleOnMouseOutTooltip()}
              onBlur={noop}
              onFocus={noop}>
              {/* <span className="fa-thin fa-plus"></span> */}
              <FontAwesomeIcon icon={faPlus} />
            </span>
          </button>
          <div className="dropdown-box">
            <div className={`drop-down-list add-filter-list ${openList ? 'open' : ''}`}>
              {hasSearch && (
                <div className="dropdown-list-header">
                  <div className="dropdown-search">
                    <input
                      className="dropdown-input-search"
                      type="text"
                      value={search}
                      placeholder="Search ..."
                      onChange={this.handleChangeInput} />
                    <span className="search-icon fa fa-search"></span>
                  </div>
                </div>
              )}
              <div className="dropdown-list-content">
                {items.map((item) => (
                  <div key={item.id} className="dropdown-list-item">
                    <button
                      onClick={() => {
                        this.handleChangeValue(item);
                      }}
                      type="button">
                      <span>{t(item.label)}</span>
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

AddFilterDropdown.propTypes = {
  hasSearch: PropTypes.bool,
  t: PropTypes.func,
  items: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.oneOfType([
        PropTypes.string,
        PropTypes.arrayOf(PropTypes.string),
        PropTypes.shape({}),
      ]),
      label: PropTypes.string,
    }),
  ),
  setValue: PropTypes.func,
  tooltip: PropTypes.string,
};

AddFilterDropdown.defaultProps = {
  hasSearch: false,
  t: (str) => str,
  items: [],
  setValue: noop,
  tooltip: '',
};

export default withTranslation()(AddFilterDropdown);
