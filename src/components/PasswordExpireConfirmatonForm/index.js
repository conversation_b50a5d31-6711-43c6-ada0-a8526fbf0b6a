// @flow
import React from 'react';
import Loading from 'components/spinner/Loading';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';

import './index.scss';
 
export function PasswordExpireConfirmationForm({
  valid,
  t,
  modalLoading,
  handleCancel,
  handleSubmit,
}) {
  const onSubmit = (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    handleSubmit();
    handleCancel(false);
  };

  return (
    <form className="add-custom-app-form password-expire-confirm-form">
      <Loading loading={modalLoading} />
      <div className="form-sections-container">
        <div className="form-section">
          <p className="modal-text">
            {t('PASSWORD_EXPIRATION_CHANGE_WARNING_MESSAGE')}
          </p>
        </div>
      </div>
      <div className="dialog-footer">
        <div className="dialog-footer-left">
          <button type="submit" onClick={(e) => onSubmit(e)} className={`submit ${!valid ? 'disabled' : ''}`}>{t('OK')}</button>
          <button type="button" onClick={() => handleCancel(false)} className="cancel">{t('CANCEL')}</button>
        </div>
      </div>
    </form>
  );
}

PasswordExpireConfirmationForm.propTypes = {
  handleCancel: PropTypes.func,
  handleSubmit: PropTypes.func,
  t: PropTypes.func,
  modalLoading: PropTypes.bool,
  valid: PropTypes.bool,
  
};

PasswordExpireConfirmationForm.defaultProps = {
  handleCancel: noop,
  handleSubmit: noop,
  t: (str) => str,
  modalLoading: false,
  valid: true,
};

export default (withTranslation()(PasswordExpireConfirmationForm));
