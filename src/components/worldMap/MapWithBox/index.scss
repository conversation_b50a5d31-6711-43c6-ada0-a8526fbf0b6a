@import 'scss/colors.scss';
@import 'scss/mixins.scss';

.ec-root-page {
.box-map-container {
    height:  700px;
    position: relative;
    border-top: 2px solid $blue16;
    border-bottom: 2px solid $blue16;

    .left-boxes,
    .right-boxes {
        position: absolute;
        top: 0;
        bottom: 0;
        z-index: 9;
        @include DisplayFlex;
        flex-direction: column;
        align-items: flex-start;
        flex: 1;

        .map-box:last-child {
            border-bottom: none;

            .separator {
                display: none;
            }
        }
    }

    .right-boxes {
        right: 0;
        border-left: 1px solid var(--semantic-color-border-base-primary);
        border-right: 1px solid var(--semantic-color-border-base-primary);
    }

    .left-boxes {
        left: 0;
        border-right: 1px solid var(--semantic-color-border-base-primary);
        border-left: 1px solid var(--semantic-color-border-base-primary);
    }

    .container {
        background: radial-gradient(circle, $white 0%, $white2 100%);
        position: relative;
        justify-content: center;
        height: 100%;

        .title {
            padding: 24px 0;
            text-align: center;

            p:nth-child(1) {
                color: $blue18;
                font-size: 20px;
                font-weight: bold;
                line-height: 30px;
            }

            p:nth-child(2) {

                color: $grey10;
                font-size: 12px;
                line-height: 30px;
            }
        }

        svg {
            fill: $grey24;
        }

        .map-legend {
            width: 230px;
            margin: 0 auto;
            left: 0;
            right: 0;
        }
    }
}
}