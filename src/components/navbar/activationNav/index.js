// @flow
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCircle } from '@fortawesome/pro-regular-svg-icons';
import { faCircle as faCircleSolid, faUpload } from '@fortawesome/pro-solid-svg-icons';

import { toggleActivationPanel } from 'ducks/activation';
import Activation from 'commonConnectedComponents/Activation';

class ActivationNav extends PureComponent {
  static propTypes = {
    actions: PropTypes.shape({}),
    data: PropTypes.shape({}),
    t: PropTypes.func,
    currentlyEditingLength: PropTypes.number,
  };

  static defaultProps = {
    actions: {},
    data: {},
    t: (str) => str,
    currentlyEditingLength: 0,
  };

  render() {
    const {
      actions: { toggleActivationPanel: toggleActivPanel },
      data: { status },
      currentlyEditingLength,
      t,
    } = this.props;

    return (
      <span
        className="custom-nav activation-nav"
        onMouseOver={() => toggleActivPanel(true)}
        onFocus={() => toggleActivPanel(true)}
        onMouseLeave={() => toggleActivPanel(false)}
        onBlur={() => toggleActivPanel(false)}>
        <span className={`nav-super-label ${currentlyEditingLength ? '' : 'no-value'}`}>{currentlyEditingLength}</span>
        <span className="fa-stack">
          <FontAwesomeIcon className="far fa-stack-2x" icon={faCircle} size="lg" />
          <FontAwesomeIcon className="fas fa-stack-1x" icon={faUpload} size="lg" />
          {status && status === 'pending' && (
            <FontAwesomeIcon className="fas fa-stack-1x" icon={faCircleSolid} />
          )}
        </span>
        <span>{t('ACTIVATION')}</span>
        <Activation />
      </span>
    );
  }
}

const mapStateToProps = (state) => ({
  ...state.activation,
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    toggleActivationPanel,
  }, dispatch);

  return {
    actions,
  };
};

export default withTranslation()(connect(
  mapStateToProps,
  mapDispatchToProps,
)(ActivationNav));

export { ActivationNav };
