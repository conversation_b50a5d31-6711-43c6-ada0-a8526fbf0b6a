import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import i18n from 'utils/i18n';
import PolicyItemView from '../PolicyItemView';
  
function Description({ value, valueObj, skipTranslation }) {
  const { description } = value || {};
  
  const renderContent = () => (
    <PolicyItemView
      label={i18n.t('DESCRIPTION')}
      valueObj={valueObj}
      value={skipTranslation ? description : i18n.t(description)} />
  );
    
  return (
    <div
      onKeyPress={null}
      // aria-label="description item"
      role="button"
      tabIndex="0">
      {renderContent()}
    </div>
  );
}
  
Description.defaultProps = {
  value: '',
  valueObj: null,
  skipTranslation: false,
};
  
Description.propTypes = {
  value: PropTypes.shape({}),
  valueObj: PropTypes.objectOf(Object),
  skipTranslation: PropTypes.bool,
};
  
export default withTranslation()(Description);
