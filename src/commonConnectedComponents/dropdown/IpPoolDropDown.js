import React, { useEffect } from 'react';
import { EntityDropdown } from 'components/entityDropdown';
import { useDispatch, useSelector } from 'react-redux';
import { dropdownActions } from 'ducks/dropdowns/ip-pool';
import { isEmpty } from 'utils/lodash';

export function IpPoolDropdown(props) {
  const dispatch = useDispatch();
  const { data } = useSelector((state) => state.ipPoolDropdown);

  useEffect(() => {
    dispatch(dropdownActions.load());
  }, []);

  return (isEmpty(data)) ? <></> : <EntityDropdown data={data} {...props} />;
}

export default IpPoolDropdown;
